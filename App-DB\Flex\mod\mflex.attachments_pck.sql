PROMPT Creating package specification script for package ATTACHMENTS_PCK
CREATE OR REPLACE
PACKAGE MFLEX.ATTACHMENTS_PCK
  IS
    pkgCtxId constant varchar2(100) := '/App-DB/Flex/Attachments';

    PROCEDURE addAttachment(pExtId mcore.attachments.ext_id%TYPE,
                            pService mcore.attachments.service%TYPE,
                            pDescription mcore.attachments.description%TYPE,
                            pContent BLOB,
                            pMimeType mcore.attachments.mime_type%TYPE,
                            pFileName mcore.attachments.filename%TYPE);
END;
/
show error

Prompt GRANT EXECUTE ON MFLEX.ATTACHMENTS_PCK TO MFLEX_USER
GRANT EXECUTE ON MFLEX.ATTACHMENTS_PCK TO MFLEX_USER
/
Prompt CREATE OR REPLACE SYNONYM MFLEX_USER.ATTACHMENTS_PCK FOR MFLEX.ATTACHMENTS_PCK
CREATE OR REPLACE SYNONYM MFLEX_USER.ATTACHMENTS_PCK FOR MFLEX.ATTACHMENTS_PCK
/
