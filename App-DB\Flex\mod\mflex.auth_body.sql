PROMPT Creating package body script for package MFLEX.AUTH
CREATE OR REPLACE
PACKAGE BODY MFLEX.AUTH AS

	/*
	/App-DB/Flex/Auth/err/noIPAddress
	/App-DB/Flex/Auth/err/IPChanged
	*/
	cERR_noIPAddress CONSTANT VARCHAR2(33) := pkgCtxId || '/err/noIPAddress';
	cERR_IPChanged   CONSTANT VARCHAR2(31) := pkgCtxId || '/err/IPChanged';
	
	/* get Session var 4 user */
	FUNCTION getSApp RETURN VARCHAR2 AS
	BEGIN
	RETURN mcauth.auth.getSApp;
	END;
	
	
	/* get Session var 4 user */
	FUNCTION getSUser RETURN VARCHAR2 AS
	BEGIN
		RETURN mcauth.auth.getSUser;
	END;
	
	--True IF login PROCEDURE IS fully and successfully completed (bASic login and extENDed login IF needed) */
	
	FUNCTION ISLogged RETURN BOOLEAN AS
	BEGIN
		RETURN getSCID IS NOT NULL;
	<PERSON><PERSON> ISLogged;

	/* Return TRUE IF username IS locked, and dates when lock IS ISsued and when will expire */
	FUNCTION ISUsernameLocked(vusername VARCHAR2, sinceDate OUT DATE, untilDate OUT DATE) RETURN BOOLEAN AS
	BEGIN
	RETURN mcauth.auth.ISUsernameLocked(vusername, sinceDate, untilDate);
	END;
	
	/* Return TRUE IF username IS locked */
	FUNCTION ISUsernameLocked(vusername VARCHAR2) RETURN BOOLEAN AS
	BEGIN
	RETURN mcauth.auth.ISUsernameLocked(vusername);
	END;
	
	/* Return TRUE IF gsm IS locked, and dates when lock IS ISsued and when will expire */
	FUNCTION ISGsmLocked(vgsm VARCHAR2, sinceDate OUT DATE, untilDate OUT DATE) RETURN BOOLEAN AS
	BEGIN
	RETURN mcauth.auth.ISGsmLocked(vgsm, sinceDate, untilDate);
	END;
	
	/* Return TRUE IF gsm IS locked, and dates when lock IS ISsued and when will expire */
	FUNCTION ISGsmLocked(vgsm VARCHAR2) RETURN BOOLEAN AS
	BEGIN
	RETURN mcauth.auth.ISGsmLocked(vgsm);
	END;

	/* get Session var 4 client ID */
	FUNCTION getSCID RETURN VARCHAR2 AS
	BEGIN
		RETURN mcauth.auth.getSCID;
	END;
	
/*	 get IP address used during logong */
	FUNCTION getIPAddress RETURN VARCHAR2 AS
	BEGIN
		RETURN mcauth.auth.getIPAddress;
	END;

	/*  */
	FUNCTION getDeviceAuthorizationStatus RETURN VARCHAR2 AS
	BEGIN
		RETURN mcauth.auth.getDeviceAuthorizationStatus;
	END;
	
	/* Return lASt login status code. NULL IS fine, otherwISe there IS error code */
	FUNCTION getLoginStatusCode RETURN VARCHAR2 AS
	BEGIN
		RETURN mcauth.auth.getLoginStatusCode;
	END;
	
	/* Return lASt status message */
	FUNCTION getLoginStatusDetails RETURN VARCHAR2 AS
	BEGIN
		RETURN mcauth.auth.getLoginStatusDetails;
	END;
	
	PROCEDURE write(varName VARCHAR2, varValue VARCHAR2) AS
	myunit CONSTANT VARCHAR2(5) := 'write';
	BEGIN
		slog.debug(pkgCtxId, myUnit, 'Execute for : ' || varName || ':' || varValue);
		mcauth.auth.write(varName, varValue);
	END;
	
	FUNCTION read(varName VARCHAR2) RETURN VARCHAR2 AS
		myunit CONSTANT VARCHAR2(4) := 'read';
	BEGIN
		slog.debug(pkgCtxId, myUnit, 'Execute for ' || varName);
		RETURN mcauth.auth.read(varName);
	END;
	
	FUNCTION readMDate(varName VARCHAR2) RETURN DATE AS
		myunit CONSTANT VARCHAR2(9) := 'readMDate';
	BEGIN
		slog.debug(pkgCtxId, myUnit, 'Execute for ' || varName);
		RETURN mcauth.auth.readMDate(varName);
	END;
	
	FUNCTION hash(var VARCHAR2) RETURN VARCHAR2 AS
	BEGIN
		RETURN mcore.common_pck.hash(var);
	END;
	
	FUNCTION clientOTPType RETURN VARCHAR2 AS
		myunit CONSTANT VARCHAR2(13) := 'clientOTPType';
	BEGIN
		slog.debug(pkgCtxId, myUnit, 'Execute');
		RETURN mcauth.auth.clientOTPType;
	END clientOTPType;

	FUNCTION clientRequirePKILogin(pUsername VARCHAR2, pDeviceId VARCHAR2, pApplicationId VARCHAR2) RETURN BOOLEAN AS
	BEGIN
		RETURN mcauth.auth.clientRequirePKILogin(vusername=>pUsername, pDeviceId=>pDeviceId, pApplicationId=>pApplicationId);
	END;
	
	FUNCTION clientRequirePKILogin RETURN BOOLEAN AS
	BEGIN
		RETURN mcauth.auth.clientRequirePKILogin();
	END;
	
	FUNCTION clientSignatureMethod RETURN VARCHAR2 AS
	myunit CONSTANT VARCHAR2(21) := 'clientSignatureMethod';
	BEGIN
		slog.debug(pkgCtxId, myUnit, 'Execute');
		RETURN NVL(mcauth.auth.clientSignatureMethod, 'NULL');
	END clientSignatureMethod;
	
	FUNCTION clientSignatureMethod(pUsername VARCHAR2) RETURN VARCHAR2 AS
	myunit CONSTANT VARCHAR2(22) := 'clientSignatureMethod2';
	
	vDeviceId VARCHAR2(1000 CHAR) := NULL;  
	vClientExtAuthId VARCHAR2(40 CHAR) := mcauth.auth.getAppExtAuthId;
	BEGIN
		slog.debug(pkgCtxId, myUnit, pUsername || ':' || mcauth.auth.getAppExtAuthId);
		
		IF vClientExtAuthId IS NULL THEN
			vClientExtAuthId := mcauth.auth.clientExtAuthID(pUsername, vDeviceId, mcore.common_pck.cAPP_THIN);
			slog.debug(pkgCtxId, myUnit, 'vClientExtAuthId:' || vClientExtAuthId);
		END IF;
		
		RETURN NVL(mcauth.auth.clientSignatureMethod(pClientExtAuthId=> vClientExtAuthId), 'NULL');
	END clientSignatureMethod;

	FUNCTION clientSignatureOtpType RETURN VARCHAR2 AS
		myunit VARCHAR2(22) := 'clientSignatureOtpType';
	BEGIN
		slog.debug(pkgCtxId, myUnit, 'Execute');
		RETURN mcauth.auth.clientSignatureOtpType;
	END clientSignatureOtpType;

	FUNCTION clientSignatureOtpType(pUsername VARCHAR2) 
	RETURN VARCHAR2 AS
		myunit VARCHAR2(23) := 'clientSignatureOtpType2';
		vClientExtAuthId VARCHAR2(40 CHAR) := mcauth.auth.getAppExtAuthId;
		vDeviceId VARCHAR2(1000 CHAR) := NULL; 
	BEGIN
		slog.debug(pkgCtxId, myUnit, 'Execute for :' || pUsername || ':' || vClientExtAuthId);
		
		IF vClientExtAuthId IS NULL THEN
			vClientExtAuthId := mcauth.auth.clientExtAuthID(pUsername, vDeviceId, mcore.common_pck.cAPP_THIN);
			slog.debug(pkgCtxId, myUnit, 'vClientExtAuthId:' || vClientExtAuthId);
		END IF;
	
		RETURN mcauth.auth.clientSignatureOtpType(
				pUsername => pUsername, 
				pDeviceId => vDeviceId, 
				pApplicationId => mcore.common_pck.cAPP_THIN, 
				pClientExtAuthId => vClientExtAuthId);
				
	END clientSignatureOtpType;
	
	FUNCTION basicLogin(vusername VARCHAR2, vpassword VARCHAR2, ipAddress VARCHAR2, host VARCHAR2,
						certIFicate VARCHAR2, application_id VARCHAR2, otpType OUT VARCHAR2,
						sessionId OUT VARCHAR2, sessLogLevel integer := NULL, langId VARCHAR2 := NULL,
						pInstallationId VARCHAR2 := NULL, pDeviceId VARCHAR2 := NULL, pPasswordExpireInDays OUT PLS_INTEGER, pNOnce IN VARCHAR2 DEFAULT NULL,
						pExtAuthTag OUT VARCHAR2, pUserId OUT PLS_INTEGER)
						RETURN BOOLEAN IS
		myunit CONSTANT VARCHAR2(10) := 'basicLogin';
		funcResult BOOLEAN:= FALSE;
		vSessionId VARCHAR2(4000);
		vpasswordExpireInDays PLS_INTEGER;
	BEGIN
		slog.debug(pkgCtxId, myUnit, 'Execute for ' || vusername || ':' || ipAddress || ':' || host || ':' || application_id || ':' || sessLogLevel || ':' || langId || ':' || pInstallationId);
		
		funcResult :=   mcauth.auth.basicLogin(
				vusername=>substr(vusername,1,40), 
				vpassword=>substr(vpassword,1,40), 
				ipAddress=>ipAddress, host=>host, certIFicate=>certIFicate, otpType=>otpType, 
				sessionId=>vSessionId, sessLogLevel=>sessLogLevel, 
				langId=>langId, pApplicationId=>application_id, pInstallationId=>pInstallationId, pDeviceId=>pDeviceId, pPasswordExpireInDays=>vpasswordExpireInDays,
				pNOnce => pNOnce, pUserId => pUserId, pPasswordHashed => NULL);
				
			pPasswordExpireInDays := vpasswordExpireInDays;
				
			slog.debug(pkgCtxId, myUnit, 'Core BASic login routine RETURNs ' || mcore.util.bool2char(funcResult));
		
		IF funcResult then
			IF vSessionId IS NULL THEN
				sspkg.raiseError('/Core/Auth/err/InternalError', 'mcauth.auth.basicLogin signaled that login process wAS successfull, but RETURNed no session ID!', pkgCtxId, myunit);
			END IF;
		
			IF otpType IS NULL THEN
				sspkg.raiseError('/Core/Auth/err/InternalError', 'mcauth.auth.basicLogin signaled that login process wAS successfull, but RETURNed no otpType!', pkgCtxId, myunit);
			END IF;
		
			slog.debug(pkgCtxId, myUnit, 'Restore session ' || nvl(vSessionId, '<MISSING SESSION ID>') || ':' || nvl(otpType, '<MISSING OTP TYPE>'));
			opens(vSessionId, ipAddress);
			
			pExtAuthTag := mcauth.auth.getExtAuthTag();
		
			slog.debug(pkgCtxId, myUnit, 'Session ' || nvl(vSessionId, '<MISSING SESSION ID>') || ' restored!');
		END IF;
		
		sessionId := vSessionId;
		slog.debug(pkgCtxId, myUnit, 'Wrapper BASic login routine RETURNs ' || mcore.util.bool2char(funcResult) || ', session id: ' || vSessionId);
		
		RETURN funcResult;
	END basicLogin;
	
	FUNCTION basicLogin(vusername VARCHAR2, vpassword VARCHAR2, ipAddress VARCHAR2, host VARCHAR2,
						certIFicate VARCHAR2, application_id VARCHAR2, otpType OUT VARCHAR2,
						sessionId OUT VARCHAR2,
						pLicenses OUT sys_refcursor, pAccountOwners OUT sys_refcursor,
						sessLogLevel integer := NULL, langId VARCHAR2 := NULL,
						pInstallationId VARCHAR2 := NULL, pDeviceId VARCHAR2 := NULL, pPasswordExpireInDays OUT PLS_INTEGER, pNOnce IN VARCHAR2 DEFAULT NULL,
						pExtAuthTag OUT VARCHAR2, pUserId OUT PLS_INTEGER, pAppVersionId IN VARCHAR2 DEFAULT NULL, pOSVersionId IN VARCHAR2 DEFAULT NULL)
						RETURN BOOLEAN IS
		myunit CONSTANT VARCHAR2(11) := 'basicLogin2';
		funcResult BOOLEAN:= FALSE;
		vpasswordExpireInDays PLS_INTEGER;
	BEGIN
		slog.debug(pkgCtxId, myUnit, 'Execute for :' || pInstallationId || ':' || vusername || ':' || ipAddress || ':' || host || ':' || application_id || ':' || sessLogLevel || ':' || langId || ':' || pInstallationId);
		-- run_id := dbms_profiler.start_profiler(pkgCtxId || '.' || myUnit || '-' || TO_CHAR(SYSDATE,'DD.MM.YYYY HH24:MI:SS'));
		
		funcResult := mcauth.auth.basicLogin(
			vusername=>substr(vusername,1,40), 
			vpassword=>substr(vpassword,1,40), 
			ipAddress=>ipAddress, host=>host, certIFicate=>certIFicate, otpType=>otpType, 
			sessionId=>sessionId, sessLogLevel=>sessLogLevel, 
			langId=>langId, pApplicationId=>application_id, pInstallationId=>pInstallationId, pDeviceId=>pDeviceId, pPasswordExpireInDays=>vpasswordExpireInDays,
			pNOnce => pNOnce, pUserId => pUserId, pPasswordHashed => NULL, pAppVersionId => pAppVersionId, pOSVersionId => pOSVersionId);
	
		slog.debug(pkgCtxId, myUnit, 'Core BASic login routine RETURNs ' || mcore.util.bool2char(funcResult));
	
		OPEN pLicenses FOR SELECT NULL FROM dual WHERE 1 = 2;
		OPEN pAccountOwners FOR SELECT NULL FROM dual WHERE 1 = 2;
	
		pPasswordExpireInDays := vpasswordExpireInDays;
	
		IF funcResult then
			slog.debug(pkgCtxId, myUnit, 'Restore session ' || sessionId);
			opens(sessionId, ipAddress);
		
			IF otpType = 'NULL' THEN
				IF pLicenses%ISOPEN THEN
					CLOSE pLicenses;
				END IF;
				IF pAccountOwners%ISOPEN THEN
					CLOSE pAccountOwners;
				END IF;
				pLicenses := accounts_pck.getAssignedLicences;
				pAccountOwners := accounts_pck.getAccOwnersList;
			END IF;
			
			pExtAuthTag := mcauth.auth.getExtAuthTag();
		
			slog.debug(pkgCtxId, myUnit, 'Session ' || sessionId || ' restored!');
		END IF;
		
		RETURN funcResult;
	END basicLogin;
	
	FUNCTION extendedLogin(sessionId VARCHAR2, otp VARCHAR2, challenge VARCHAR2 := NULL) RETURN BOOLEAN AS
		myunit CONSTANT VARCHAR2(13) := 'extendedLogin';
		vLoginResult BOOLEAN;
	BEGIN
	slog.debug(pkgCtxId, myUnit, 'Execute for ' || sessionId || ':' || otp || ':' || challenge);
	vLoginResult := mcauth.auth.extendedLogin(sessionId, otp, challenge);
	
	RETURN vLoginResult;
	END extendedLogin;
	
	FUNCTION extendedLogin(sessionId VARCHAR2, otp VARCHAR2, challenge VARCHAR2 := NULL,
					pLicenses OUT sys_refcursor, pAccountOwners OUT sys_refcursor) RETURN BOOLEAN AS
		myunit CONSTANT VARCHAR2(14) := 'extendedLogin2';
		vLoginResult BOOLEAN;
	BEGIN
	slog.debug(pkgCtxId, myUnit, 'Execute for ' || sessionId || ':' || otp || ':' || challenge);
	vLoginResult := mcauth.auth.extendedLogin(sessionId, otp, challenge);
	
	OPEN pLicenses FOR SELECT NULL FROM dual WHERE 1 = 2;
	OPEN pAccountOwners FOR SELECT NULL FROM dual WHERE 1 = 2;
	
	IF vLoginResult then
		mcauth.auth.opens(sessionId);
		IF pLicenses%ISOPEN THEN
			CLOSE pLicenses;
		END IF;
		IF pAccountOwners%ISOPEN THEN
			CLOSE pAccountOwners;
		END IF;
		pLicenses := accounts_pck.getAssignedLicences;
		pAccountOwners := accounts_pck.getAccOwnersList;
	
	END IF;
	
	RETURN vLoginResult;
	END extendedLogin;
	
	PROCEDURE opens(sessionId VARCHAR2) AS
	BEGIN
		mcauth.auth.opens(sessionId);
	END opens;
	
	PROCEDURE opens(sessionId VARCHAR2, ipaddress VARCHAR2 DEFAULT NULL) AS
		myunit VARCHAR2(5) := 'opens';
		vIpChangeAllowed BOOLEAN := FALSE;
	BEGIN
		slog.debug(pkgCtxId, myUnit, substr(sessionId,1,10) || ':' || ipaddress);
		vIpChangeAllowed := sspkg.readBool(pkgCtxId || '/IPChangeAllowed');
	
		IF vIpChangeAllowed THEN
		slog.debug(pkgCtxId, myUnit, 'Allow IP change!');
		END IF;
	
		IF (NOT vIpChangeAllowed) AND (ipaddress IS NULL) THEN
			sspkg.raiseError(cERR_noIPAddress, NULL, pkgCtxId, myunit);
		END IF;
	
		mcauth.auth.opens(sessionId);
		slog.debug(pkgCtxId, myUnit, 'Session opened');
	
		IF (NOT vIpChangeAllowed) AND (getIPAddress <> ipaddress) THEN
			slog.debug(pkgCtxId, myUnit, 'IP change dISallowed and saved address ' || getIPAddress || ' dIFferes from actual one : ' || ipaddress || '. Logout!');
			logout;
			sspkg.raiseError(cERR_IPChanged, substr(sessionId,1,10) || ':' || ipaddress, pkgCtxId, myunit);
		END IF;
	
	END opens;
	
	PROCEDURE closes AS
		myunit CONSTANT VARCHAR2(6) := 'closes';
	BEGIN
		slog.debug(pkgCtxId, myUnit);
		mcauth.auth.closes;
	END closes;
	
	PROCEDURE logout AS
		myunit CONSTANT VARCHAR2(6) := 'logout';
	BEGIN
		slog.info(pkgCtxId, myUnit);
		mcauth.auth.logout;
	END logout;
	
	FUNCTION resetPasswordRQ(contactClient VARCHAR2, secQuestionID VARCHAR2, secQuestionAnswer VARCHAR2) RETURN BOOLEAN AS
		myunit CONSTANT VARCHAR2(15) := 'resetPasswordRQ';
	BEGIN
		slog.info(pkgCtxId, myUnit);
		RETURN mcauth.auth.resetPasswordRQ(contactClient, secQuestionID, secQuestionAnswer);
	END resetPasswordRQ;
	
	FUNCTION changePassword(oldPassword VARCHAR2, newPassword VARCHAR2, otp VARCHAR2 := NULL, challenge VARCHAR2 := NULL, pNOnce IN VARCHAR2 := NULL) RETURN BOOLEAN AS
		myunit CONSTANT VARCHAR2(14) := 'changePassword';
	BEGIN
		slog.info(pkgCtxId, myUnit);
		RETURN mcauth.auth.changePassword(oldPassword, newPassword, otp, challenge, pNOnce);
	END changePassword;
	
	FUNCTION checkOTP(otp VARCHAR2) RETURN BOOLEAN AS
		myunit CONSTANT VARCHAR2(8) := 'checkOTP';
	BEGIN
		slog.info(pkgCtxId, myUnit);
		RETURN mcauth.auth.checkOTP(otp);
	END checkOTP;
	
	PROCEDURE genSENDOTP  AS
		myunit CONSTANT VARCHAR2(10) := 'genSENDOTP';
	BEGIN
		slog.info(pkgCtxId, myUnit);
		mcauth.auth.genSENDOTP;
	END genSENDOTP;
	
	FUNCTION genChallenge(sourceData VARCHAR2 := dbms_random.string('A', 24)) RETURN VARCHAR2 AS
		myunit CONSTANT VARCHAR2(12) := 'genChallenge';
	BEGIN
		slog.info(pkgCtxId, myUnit);
		RETURN mcauth.auth.genChallenge(sourceData);
	END;
	
	FUNCTION checkSignature(sourceData VARCHAR2, signature VARCHAR2) RETURN BOOLEAN AS
		myunit CONSTANT VARCHAR2(14) := 'checkSignature';
	BEGIN
		slog.info(pkgCtxId, myUnit);
		RETURN mcauth.auth.checkSignature(sourceData, signature);
	END checkSignature;
	
	FUNCTION checkCHRESP(challenge VARCHAR2, response VARCHAR2) RETURN BOOLEAN AS
		myunit CONSTANT VARCHAR2(9) := 'checkRESP';
	BEGIN
	slog.info(pkgCtxId, myUnit);
		RETURN mcauth.auth.checkCHRESP(challenge, response);
	END checkCHRESP;

	FUNCTION getGsmSCID RETURN NUMBER AS
	BEGIN
		RETURN mcauth.auth.getGsmSCID;
	END;
	
	FUNCTION getGsmSPhone RETURN VARCHAR2 AS
	BEGIN
		RETURN mcauth.auth.getGsmSPhone;
	END;

	FUNCTION getGsmForCID RETURN VARCHAR2 AS
	BEGIN
		RETURN mcauth.auth.getGsmForCID(getSCID);
	END;
	
	PROCEDURE gsmLogout AS
		myunit CONSTANT VARCHAR2(9) := 'gsmLogout';
	BEGIN
		slog.debug(pkgCtxId, myUnit, getGsmSCID);
		mcauth.auth.gsmLogout;
	END;
	
	FUNCTION gsmLogin(vgsm VARCHAR2, vpassword VARCHAR2, vapplication VARCHAR2) RETURN BOOLEAN AS
		myunit CONSTANT VARCHAR2(8) := 'gsmLogin';
	
	BEGIN
		slog.debug(pkgCtxId, myUnit, vgsm || ':' || vapplication);
		RETURN mcauth.auth.gsmLogin(vgsm, vpassword, vapplication);
	END;

	-- ThIS FUNCTION RETURN extauth id. If NULL IS RETURNed then user does not use additional OTP.
	-- Extauth_id are configured IN branch /Core/Auth/ExtAuth/<ExtAuthID>
	
	FUNCTION getExtAuthId RETURN VARCHAR2 AS
	myunit CONSTANT VARCHAR2(10) := 'getExtAuth';
	BEGIN
	slog.debug(pkgCtxId, myUnit);
	RETURN mcauth.auth.getExtAuthId;
	END;
	
	-- Set language
	PROCEDURE setLang(langId VARCHAR2) AS
	myunit CONSTANT VARCHAR2(7) := 'setLang';
	BEGIN
	slog.debug(pkgCtxId, myUnit, langId);
	mcauth.auth.setLang(langId);
	END;
	
	-- Get language
	FUNCTION getLang RETURN VARCHAR2 AS
	BEGIN
	RETURN mcauth.auth.getLang;
	END;
	
	-- Set account owner
	PROCEDURE setAccountOwner(pAccOwnerId IN VARCHAR2)
	IS
	BEGIN
		mcauth.auth.setAccountOwner(pAccOwnerId => pAccOwnerId);
	END setAccountOwner;
	
	FUNCTION getAccountOwner
	RETURN VARCHAR2 IS
	BEGIN
		RETURN mcauth.auth.getAccountOwner;
	END getAccountOwner;

	FUNCTION getPrimaryAccountOwner
	RETURN VARCHAR2 IS
	BEGIN
		RETURN mcauth.auth.getPrimaryAccountOwner;
	END getPrimaryAccountOwner;
	
	/* Return previous login */
	FUNCTION getPreviousLoginInfo RETURN SYS_REFCURSOR IS
	BEGIN
		RETURN mcauth.auth.getPreviousLoginInfo;
	END;
	
	/* Return previous logins */
	FUNCTION getPreviousLogins
	RETURN SYS_REFCURSOR
	IS
		rez SYS_REFCURSOR;
		vClientId mcauth.client.id%TYPE := getSCID;
	BEGIN
		rez := mcauth.auth.getPreviousLogins(vClientId);
	
		RETURN rez;
	END getPreviousLogins;
	
	FUNCTION ISSessionExpired
	RETURN BOOLEAN
	IS
	BEGIN
		RETURN mcauth.auth.ISSessionExpired;
	END ISSessionExpired;
	
	FUNCTION p_ISSessionExpired
	RETURN PLS_INTEGER
	IS
	BEGIN
		IF ISSessionExpired THEN
			RETURN 1;
		END IF;
		RETURN 0;
	END p_ISSessionExpired;
	
	FUNCTION getDayOfWeeks
	RETURN sys_refcursor IS
		rez sys_refcursor;
	BEGIN
		OPEN rez FOR
		SELECT dow, dow_name FROM mcore.vw$day_of_weeks a
		ORDER BY dow ASC;
		RETURN rez;
	END getDayOfWeeks;
	
	FUNCTION generateTanList(
		pThrId OUT PLS_INTEGER,
		pTanDim OUT PLS_INTEGER, pOtpLength OUT PLS_INTEGER,
		pIndexD1Count OUT PLS_INTEGER, pIndexD1Type OUT VARCHAR2,
		pIndexD2Count OUT PLS_INTEGER, pIndexD2Type OUT VARCHAR2
		)
	RETURN sys_refcursor
	IS
		myunit CONSTANT VARCHAR2(15) := 'generateTanList';
	BEGIN
		slog.debug(pkgCtxId, myUnit, getSCID);
		RETURN mcauth.tanlISt_plugin.generateTanList(getSCID, pThrId, pTanDim, pOtpLength, pIndexD1Count, pIndexD1Type, pIndexD2Count, pIndexD2Type);
	END generateTanList;
	
	PROCEDURE activateNewTanList
	IS
		myunit CONSTANT VARCHAR2(18) := 'activateNewTanList';
	BEGIN
		slog.debug(pkgCtxId, myUnit);
		mcauth.tanlISt_plugin.activateNewTanList(getSCID);
	END activateNewTanList;
	
	FUNCTION ISAlmostDepleted
	RETURN VARCHAR2
	IS
	BEGIN
		RETURN mcauth.auth.ISAlmostDepleted(getSCID);
	END ISAlmostDepleted;
	
	PROCEDURE generateNewActivationKey(pActKey OUT VARCHAR2, pValidUntil OUT DATE)
	IS	
	BEGIN
		mcauth.auth.generateNewActivationKey(pActKey, pValidUntil);
	END generateNewActivationKey;
	
	FUNCTION getExIStingActivationKey
	RETURN DATE
	IS
	BEGIN
		RETURN mcauth.auth.getExIStingActivationKey();
	END getExIStingActivationKey;
	
	FUNCTION getListOfAuthorizedDevices RETURN sys_refcursor
	IS
	BEGIN
		RETURN mcauth.auth.getListOfAuthorizedDevices();
	END getListOfAuthorizedDevices;
	
	PROCEDURE unregISterDevice(pAppExtAuthId NUMBER, pComment VARCHAR2 := NULL) IS
	BEGIN
		mcauth.auth.unregISterDevice(pAppExtAuthId);
	END unregISterDevice; 
	
	PROCEDURE sendSMSMessage (pMessage VARCHAR2)
	IS
		myunit CONSTANT VARCHAR2(14) := 'sendSMSMessage';
	BEGIN
		slog.debug(pkgCtxId, myUnit, getSCID);
		mcauth.auth.sendSMSMessage(clientID => getSCID, pMessage => pMessage);
	END sendSMSMessage;
END AUTH;
/
show error

alter package MFLEX.AUTH compile debug
/

alter package MFLEX.AUTH compile debug body
/