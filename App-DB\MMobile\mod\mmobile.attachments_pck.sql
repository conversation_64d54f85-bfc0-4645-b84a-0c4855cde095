PROMPT Creating package specification script for package AT<PERSON>CHMENTS_PCK
CREATE OR R<PERSON>LACE
PACKAGE MMOBILE.ATTACHMENTS_PCK
  IS
    pkgCtxId constant varchar2(100) := '/App-DB/MMobile/Attachments';

    PROCEDURE addAttachment(pExtId mcore.attachments.ext_id%TYPE,
                            pService mcore.attachments.service%TYPE,
                            pDescription mcore.attachments.description%TYPE,
                            pContent BLOB,
                            pMimeType mcore.attachments.mime_type%TYPE,
                            pFileName mcore.attachments.filename%TYPE);
END;
/
show error

Prompt GRANT EXECUTE ON MMOBILE.ATTACHMENTS_PCK TO MMOBILE_USER
GRANT EXECUTE ON MMOBILE.ATTACHMENTS_PCK TO MMOBILE_USER
/
Prompt CREATE OR REPLACE SYNONYM MMOBILE_USER.ATTACHMENTS_PCK FOR MMOBILE.ATTACHMENTS_PCK
CREATE OR <PERSON><PERSON>LACE SYNONYM MMOBILE_USER.ATTACHMENTS_PCK FOR MMOBILE.ATTACHMENTS_PCK
/
