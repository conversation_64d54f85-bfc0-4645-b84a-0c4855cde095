PROMPT Creating package body script for package ATTACHMENTS_PCK
CREATE OR REPLACE
PACKAGE BODY MMOBILE.ATTACHMENTS_PCK
IS

    PROCEDURE addAttachment(pExtId mcore.attachments.ext_id%TYPE,
                            pService mcore.attachments.service%TYPE,
                            pDescription mcore.attachments.description%TYPE,
                            pContent BLOB,
                            pMimeType mcore.attachments.mime_type%TYPE,
                            pFileName mcore.attachments.filename%TYPE)
    IS
        myunit CONSTANT VARCHAR2(13) := 'addAttachment';
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pExtId || ':' || pService || ':' || SUBSTR(pDescription, 1, 30) || ':' || pMimeType ||':'|| pFileName);
        mcore.attachments_pck.addAttachment(pExtId, pService, pDescription, pContent, pMimeType, pFileName);
    <PERSON>ND addAttachment;
END;
/
show error

