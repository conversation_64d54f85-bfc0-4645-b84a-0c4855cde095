Prompt CREATE OR REPLACE PACKAGE mmobile.invoice_mgmt_pck

CREATE OR REPLACE
PACKAGE mmobile.invoice_mgmt_pck IS
/**
* Paket za upravljanje pretplatama kod izdavatelja ra�una i samim ra�unima izdatim od strane izdavatelja
* @headcom
*/
	pkgCtxId CONSTANT VARCHAR2(33) := '/App-DB/MMobile/InvoiceManagement';

	/* Procesi relevantni za pretplate */

	/** Registracija pretplate klijenta na ra�une odre�enog izdavatelja.
	* Koristi se od strane klijentske aplikacije za registraciju klijenta pri odre�enom izdavatelju ra�una
	*
	* @param pInvoiceProviderId Interni ID izdavatelja ra�una (odgovara vrijednosti kolone mcore.invoice_providers.id za izdavatelja) -> o�ekuje se vrijednost kolone ipr_id iz rezultantnog ref. cursora funkcije getActiveSubscriptions (za update)
	* @param pUserReference Referenca ra�una koju klijent unosi za sopstvenu identifikaciju prema izdavatelju -> o�ekuje se vrijednost kolone user_reference iz rezultantnog ref. cursora funkcije getActiveSubscriptions (za update)
	* @param pUserComment Opcionalni komentar korisnika za lak�e prepoznavanje pretplate
	* @param pUserAgreementId ID uslova kori�tenja koje je korisnik prihvatio (invoice_provider_clients.uat_id -> user_agreement.id). Verifikacija u odnosu na trenutnu vrijednost invoice_providers.uat_id za dati pInvoiceProviderId
	* @param pResponse Signature related data
	* @param pOtp Signature related data
	* @param pSourceData Signature related data
	* @param pSignature Signature related data
	* @return Integer ID pretplate ukoliko je proces bio uspje�an (invoice_provider_clients.id) */
	FUNCTION RegisterConsumer(
		pInvoiceProviderId PLS_INTEGER,
		pUserReference VARCHAR2,
		pUserComment VARCHAR2,
		pUserAgreementId PLS_INTEGER := NULL,
		pResponse VARCHAR2 := NULL,
        pOtp VARCHAR2 := NULL,
        pSourceData VARCHAR2 := NULL,
        pSignature VARCHAR2 := NULL,
		pInvoiceTemplate CLOB DEFAULT NULL)
	RETURN NUMBER;

	/** Deregistracija pretplate korisnika.
	* @param pId ID pretplate (invoice_provider_clients.id) */
	PROCEDURE UnregisterConsumer(pId IN PLS_INTEGER);

	FUNCTION RegisterConsumer(
		pInvoiceProviderName VARCHAR2,
		pUserReference VARCHAR2,
		pUserAgreementId PLS_INTEGER := NULL)
	RETURN NUMBER;

    PROCEDURE UnregisterConsumer(pInvoiceProviderName VARCHAR2);

	/** Odre�uje da li za trenutno prijavljenog user-a postoji aktivna pretplata.	*/
	FUNCTION hasActiveSubscriptions	RETURN BOOLEAN;

	/** Vra�a listu aktivnih pretplata za trenutno prijavljenog korisnika za tip CONSUMER.
    * <br/><br/>Rezultantni ref-cursor ima opis<br/>
	* 	<code>id</code> (INTEGER) ID pretplate (invoice_provider_clients.id)<br/>
	* 	<code>ipr_id</code> (INTEGER) ID izdavatelja ra�una (invoice_provider_clients.ipr_id -> invoice_providers.id za izdavatelja)<br/>
	* 	<code>ipr_name</code> (VARCHAR2) Naziv izdavatelja ra�una (invoice_providers.name za izdavatelja)<br/>
	* 	<code>user_reference</code> (VARCHAR2) Referenca korisnika koju je klijent prethodno unio (prilikom registracije) (invoice_provider_clients.user_reference)<br/>
	* 	<code>user_comment</code> (VARCHAR2) Opcionalni komentar uz pretplatu unesen od strane klijenta (prilikom registracije) (invoice_provider_clients.user_comment)<br/>
	* @return SYS_REFCURSOR	*/
	FUNCTION getActiveSubscriptions RETURN SYS_REFCURSOR;

	FUNCTION checkATMCardlessSubscription RETURN BOOLEAN;
	FUNCTION checkQRPaySubscription RETURN BOOLEAN;

	/** Lista va�e�ih izdavaoca ra�una.
	* <br/><br/>Rezultantni ref-cursor ima opis<br/>
	* 	<code>ipr_id</code> (INTEGER) Interni ID izdavatelja ra�una (invoice_providers.id za izdavatelja)<br/>
	* 	<code>ipr_name</code> (VARCHAR2) Naziv izdavatelja ra�una (invoice_providers.name za izdavatelja)<br/>
	* 	<code>ipr_description</code> (VARCHAR2) Opis izdavatelja ra�una (dodatne informacije o istim) (invoice_providers.description)<br/>
	* 	<code>blob_data</code> (BLOB) Slika primjera ra�una u cilju lak�eg pronala�enja reference ra�una (BLOB) (invoice_providers.blob_data)<br/>
	* 	<code>mime_type</code> (VARCHAR2) Mime-type BLOB-a (invoice_providers.mime_type)<br/>
  * @return SYS_REFCURSOR*/
	FUNCTION getProviders RETURN SYS_REFCURSOR;

	/* Invoice related */

	/** Vra�a broj ra�una za trenutno prijavljenog user-a i status.
	* @param pStatus Status ra�una. Dozvoljene vrijednosti 0,1,2,3,4,5 ili %
	* @return Broj ra�una za trenutno prijavljenog user-a u statusu pStatus */
	FUNCTION getInvoicesCount(pStatus IN VARCHAR2 DEFAULT '%')
	RETURN PLS_INTEGER;

	/** Vra�a broj pla�enih ra�una za trenutno prijavljenog user-a.
	* @return () Broj ra�una za trenutno prijavljenog user-a u statusu 2
	* */
	FUNCTION getProcessedInvoicesCount
	RETURN PLS_INTEGER;

	/** Vra�a listu ra�una za trenutno prijavljenog korisnika.
	* <br/><br/>Rezultantni ref-cursor ima opis<br/>
	* 	<code>invoice_id</code><br/> (INTEGER)
	* 	<code>invoice_provider_id</code><br/> (INTEGER)
	* 	<code>invoice_provider_name</code><br/> (VARCHAR2)
	* 	<code>payment_type</code><br/>
	* 	<code>reference</code><br/> (VARCHAR2)
	* 	<code>status</code><br/> (VARCHAR2)
	* 	<code>description</code><br/> (VARCHAR2)
	* 	<code>tranval</code><br/>
	* 	<code>currency_id</code><br/>
	* 	<code>tranpay_id</code><br/> (INTEGER)
	* 	<code>date_of_processing</code><br/> (DATE)
	* 	<code>billing_period</code><br/>
	* 	<code>billing_date</code><br/> (DATE)
	* 	<code>payment_due_date</code><br/> (DATE)
	* 	<code>hitno</code><br/> (VARCHAR2)
	* 	<code>naziv_primaoca</code><br/> (VARCHAR2)
	* 	<code>racun_primaoca</code><br/> (VARCHAR2)
	* 	<code>naziv_posiljaoca</code><br/> (VARCHAR2)
	* 	<code>javni_prihodi</code><br/> (VARCHAR2)
	* 	<code>referenca_placanja</code><br/> (VARCHAR2)
	* 	<code>idporobv</code><br/> (VARCHAR2)
	* 	<code>vrsta_uplate</code><br/> (VARCHAR2)
	* 	<code>vrsta_prihoda</code><br/> (VARCHAR2)
	* 	<code>porperiod_od</code><br/> (VARCHAR2)
	* 	<code>porperiod_do</code><br/> (VARCHAR2)
	* 	<code>opcina</code><br/> (VARCHAR2)
	* 	<code>poziv_na_broj</code><br/> (VARCHAR2)
	* 	<code>budzetska_organizacija</code><br/> (VARCHAR2)
	* 	<code>fiksno</code><br/> (VARCHAR2)
	* 	<code>iznos_kupovine</code><br/>
	* 	<code>racun</code><br/> (VARCHAR2)
	* 	<code>racun_valuta</code><br/> (VARCHAR2)
	* 	<code>message</code><br/> (VARCHAR2)
	* 	<code>display_on_overview</code><br/>
	* 	<code>group_id</code><br/>
	* 	<code>tl_type</code><br/>
    * 	<code>group_id_translated</code><br/>
	* @param pStatus Status ra�una koji se �ele prikazati. Dozvoljene vrijednosti % - Svi ra�un, 0 - Nepla�eni ra�uni
	* @return SYS_REFCURSOR */
    FUNCTION getInvoices(pStatus IN VARCHAR2 DEFAULT '%')
    RETURN sys_refcursor;

	/** Vra�a listu pla�enih ra�una za trenutno prijavljenog korisnika.
	* <br/><br/>Rezultantni ref-cursor ima opis<br/>
	* 	<code>invoice_id</code><br/> (INTEGER)
	* 	<code>invoice_provider_id</code><br/> (INTEGER)
	* 	<code>invoice_provider_name</code><br/> (VARCHAR2)
	* 	<code>payment_type</code><br/>
	* 	<code>reference</code><br/> (VARCHAR2)
	* 	<code>status</code><br/> (VARCHAR2)
	* 	<code>description</code><br/> (VARCHAR2)
	* 	<code>tranval</code><br/>
	* 	<code>currency_id</code><br/>
	* 	<code>tranpay_tranval</code><br/>
	* 	<code>tranpay_tranval_currency_id</code><br/>
	* 	<code>tranpay_id</code><br/> (INTEGER)
	* 	<code>date_of_processing</code><br/> (DATE)
	* 	<code>billing_period</code><br/>
	* 	<code>billing_date</code><br/> (DATE)
	* 	<code>payment_due_date</code><br/> (DATE)
	* 	<code>hitno</code><br/> (VARCHAR2)
	* 	<code>naziv_primaoca</code><br/> (VARCHAR2)
	* 	<code>racun_primaoca</code><br/> (VARCHAR2)
	* 	<code>naziv_posiljaoca</code><br/> (VARCHAR2)
	* 	<code>javni_prihodi</code><br/> (VARCHAR2)
	* 	<code>referenca_placanja</code><br/> (VARCHAR2)
	* 	<code>idporobv</code><br/> (VARCHAR2)
	* 	<code>vrsta_uplate</code><br/> (VARCHAR2)
	* 	<code>vrsta_prihoda</code><br/> (VARCHAR2)
	* 	<code>porperiod_od</code><br/> (VARCHAR2)
	* 	<code>porperiod_do</code><br/> (VARCHAR2)
	* 	<code>opcina</code><br/> (VARCHAR2)
	* 	<code>poziv_na_broj</code><br/> (VARCHAR2)
	* 	<code>budzetska_organizacija</code><br/> (VARCHAR2)
	* 	<code>fiksno</code><br/> (VARCHAR2)
	* 	<code>iznos_kupovine</code><br/>
	* 	<code>racun</code><br/> (VARCHAR2)
	* 	<code>racun_valuta</code><br/> (VARCHAR2)
	* 	<code>message</code><br/> (VARCHAR2)
	* 	<code>display_on_overview</code><br/>
	* 	<code>group_id</code><br/>
	* 	<code>tl_type</code><br/>
    * 	<code>group_id_translated</code><br/>
	* @param pStatus Status ra�una koji se �ele prikazati. Dozvoljene vrijednosti % - Svi ra�un, 0 - Nepla�eni ra�uni
	* @return SYS_REFCURSOR */
    FUNCTION getProcessedInvoices
    RETURN sys_refcursor;
   
    /** Vra�a ra�un za trenutno prijavljenog korisnika (ili racun koji nije nikome dodjeljen).
	* <br/><br/>Rezultantni ref-cursor ima opis<br/>
	* 	<code>invoice_id</code><br/> (INTEGER)
	* 	<code>invoice_provider_id</code><br/> (INTEGER)
	* 	<code>invoice_provider_name</code><br/> (VARCHAR2)
	* 	<code>payment_type</code><br/>
	* 	<code>reference</code><br/> (VARCHAR2)
	* 	<code>status</code><br/> (VARCHAR2)
	* 	<code>description</code><br/> (VARCHAR2)
	* 	<code>tranval</code><br/>
	* 	<code>currency_id</code><br/>
	* 	<code>tranpay_id</code><br/> (INTEGER)
	* 	<code>date_of_processing</code><br/> (DATE)
	* 	<code>billing_period</code><br/>
	* 	<code>billing_date</code><br/> (DATE)
	* 	<code>payment_due_date</code><br/> (DATE)
	* 	<code>hitno</code><br/> (VARCHAR2)
	* 	<code>naziv_primaoca</code><br/> (VARCHAR2)
	* 	<code>racun_primaoca</code><br/> (VARCHAR2)
	* 	<code>naziv_posiljaoca</code><br/> (VARCHAR2)
	* 	<code>javni_prihodi</code><br/> (VARCHAR2)
	* 	<code>referenca_placanja</code><br/> (VARCHAR2)
	* 	<code>idporobv</code><br/> (VARCHAR2)
	* 	<code>vrsta_uplate</code><br/> (VARCHAR2)
	* 	<code>vrsta_prihoda</code><br/> (VARCHAR2)
	* 	<code>porperiod_od</code><br/> (VARCHAR2)
	* 	<code>porperiod_do</code><br/> (VARCHAR2)
	* 	<code>opcina</code><br/> (VARCHAR2)
	* 	<code>poziv_na_broj</code><br/> (VARCHAR2)
	* 	<code>budzetska_organizacija</code><br/> (VARCHAR2)
	* 	<code>fiksno</code><br/> (VARCHAR2)
	* 	<code>iznos_kupovine</code><br/>
	* 	<code>racun</code><br/> (VARCHAR2)
	* 	<code>racun_valuta</code><br/> (VARCHAR2)
	* 	<code>message</code><br/> (VARCHAR2)
	* 	<code>display_on_overview</code><br/>
	* 	<code>group_id</code><br/>
	* 	<code>tl_type</code><br/>
    * 	<code>group_id_translated</code><br/>
	* @param pInvoiceId ID ra�una
	* @return SYS_REFCURSOR */
    FUNCTION getInvoice(pInvoiceId IN PLS_INTEGER)
    RETURN sys_refcursor;

  /** Mijenja status invoice-a.
	* @param pInvoiceId ID invoice-a
	* @param pAction Novi status
	* @param pTranpayId	ID Elba naloga */
    PROCEDURE processInvoice(pInvoiceID IN PLS_INTEGER, pAction IN PLS_INTEGER, pTranpayId IN NUMBER DEFAULT NULL, pExtrefName IN mcore.tranpay_extref.extref_name%TYPE DEFAULT NULL);

	/** Progla�ava invoice otkazanim.
	* @param pInvoiceID ID invoice-a*/
    PROCEDURE cancelInvoice(pInvoiceID IN PLS_INTEGER);


	/* Invoice provider app related */
	/** Vra�a listu izdavaoca ra�una za koje je trenutno prijavljeni korisnik kao administrator prijavljen.
	* <br/><br/>Rezultantni ref-cursor ima opis<br/>
	* 	<code>ipr_id</code> (INTEGER) Interni ID izdavatelja ra�una (odgovara vrijednosti kolone mcore.invoice_provider_clients.ipr_id -> mcore.invoice_providers.id za izdavatelja)<br/>
	* 	<code>ipr_name</code> (VARCHAR2) Naziv izdavatelja ra�una (odgovara vrijednosti kolone mcore.invoice_providers.name za izdavatelja)<br/>
	* 	<code>ipr_description</code> (VARCHAR2) Opis izdavatelja ra�una (dodatne informacije o istim)<br/>
	* 	<code>blob_data</code> (BLOB) Slika primjera ra�una u cilju lak�eg pronala�enja reference ra�una (BLOB)<br/>
	* 	<code>mime_type</code> (VARCHAR2) Mime-type BLOB-a (invoice_providers.mime_type)<br/>
	* 	<code>agreement_text</code> (VARCHAR2) Tekst ugovora o prihvatanju primanja ra�una putem aplikacije (CLOB) */
	FUNCTION getManagedProviders RETURN SYS_REFCURSOR;

	/** Vra�a listu pretpla�enih klijenata za ID izdavatelja ra�una.
	* <br/><br/>Rezultantni ref-cursor ima opis<br/>
	* 	<code>id</code> (INTEGER) ID pretplate (invoice_provider_clients.id)<br/>
	* 	<code>user_reference</code> (VARCHAR2) Referenca korisnika (invoice_provider_clients.user_reference)<br/>
	* 	<code>date_subscribed</code> (DATE) Datum pretplate (invoice_provider_clients.date_subscribed)<br/>
	* 	<code>end_users_id</code> (INTEGER) ID Elba korisnika (invoice_provider_clients.end_users_id)<br/>
	* 	<code>user_email</code> (VARCHAR2) Email adresa ELBA korisnika (end_users.contact_email)<br/>
	* 	<code>user_agreement_id</code> (INTEGER) ID prihva�enih uslova kori�tenja (invoice_provider_clients.uat_id)<br/>
	* @param pInvoiceProviderId ID izdavatelja ra�una
	* @return SYS_REFCURSOR */
	FUNCTION getClients(pInvoiceProviderId IN PLS_INTEGER)
	RETURN SYS_REFCURSOR;

	/** U�itavanje ra�una.
	* @param pInvoiceProviderId ID izdavatelja ra�una<br/>
	* @param pBatchName Naziv batch-a<br/>
	* @param pInvoiceList Lista ra�una<br/>
	* @param pInvoiceBatchId (OUT) ID generisanog batch-a<br/>
	* @param pImportResult (OUT) Lista s rezultatima importa pojedina�nih ra�una<br/>
	* @return BOOLEAN Indicira da li je do�lo do gre�ke kod import-a */
	FUNCTION UploadInvoices(
		pInvoiceProviderId IN PLS_INTEGER,
		pBatchName IN VARCHAR2,
		pInvoiceList IN mcore.invoice_list,
		pInvoiceBatchId OUT PLS_INTEGER,
		pImportResult OUT mcore.invoice_import_result_list)
	RETURN BOOLEAN;

	/** Vra�a list posoje�ih batch-eva u�itavanja ra�una.
	* <br/><br/>Rezultantni ref-cursor ima opis<br/>
	* 	<code>id</code> (INTEGER) ID batch-a (invoice_batches.id)<br/>
	* 	<code>name</code> (VARCHAR2) Naziv batch-a (invoice_batches.name)<br/>
	* 	<code>batch_date</code> (DATE) Datum batch-a (invoice_batches.batch_date)<br/>
	* @param pInvoiceProviderId
	* @return SYS_REFCURSOR */
	FUNCTION getBatches(pInvoiceProviderId IN PLS_INTEGER)
	RETURN sys_refcursor;

	/** Odstranjuje batch.
	* @param pInvoiceBatchId ID Batch-a */
	PROCEDURE RemoveBatch(pInvoiceBatchId IN PLS_INTEGER);

	/** Vra�a listu u�itanih ra�una za dati ID batch-a.
	* @param pInvoiceBatchId ID Batch-a
	* @return SYS_REFCURSOR */
	FUNCTION getUploadedInvoices(pInvoiceBatchId IN PLS_INTEGER)
	RETURN sys_refcursor;

	/** Mijenja status batch-a.
	* @param pInvoiceBatchId ID Batch-a
	* @param pValid Novi status batch-a */
	PROCEDURE ChangeBatchStatus(pInvoiceBatchId IN PLS_INTEGER, pValid IN PLS_INTEGER);

	/** Vra�a aktivne uslove kori�tenje za dati ID izdavatelja ra�una.
	* <br/><br/>Rezultantni ref-cursor ima opis<br/>
	* <code>id</code> (INTEGER) ID uslova kori�tenja<br/>
	* <code>agreement_text</code> (CLOB) Tekst uslova kori�tenja<br/>
	* <code>valid_from</code> (DATE) Datum po�etka va�enja uslova kori�tenja<br/>
	* @param pId ID izdavatelja ra�una
	* @return SYS_REFCURSOR	*/
	FUNCTION getActiveUserAgreement(pInvoiceProviderId IN NUMBER)
	RETURN SYS_REFCURSOR;

	FUNCTION getActiveUserAgreement(pInvoiceProviderName IN VARCHAR2)
	RETURN SYS_REFCURSOR;

	/** Kreira nalog za dati invoice, identificiran bilo internim ID-em (pInvoiceId) ili nazivom izdavatelja ra�una (pInvoiceProviderName) i brojem ra�una (pInvoiceNo)
	* @param pInvoiceId ID invoice-a (obavezan ukoliko se ne proslijedi pInvoiceProviderName i pInvoiceNo). Ima prednost u odnosu na pInvoiceProviderName i pInvoiceNo
	* @param pInvoiceProviderName Naziv izdavatelja ra�una
	* @param pInvoiceNo Broj ra�una
	* @param pSenderBankAccountId ID ra�una koji se tereti
	* @param pSenderName Naziv po�iljaoca
	* @param pTranval Iznos naloga
	* @param pTranpayDescription Opis naloga
	* @param pReference Referenca iz vanjskog sistema
	* @param pResponse Signature related data
	* @param pOtp Signature related data
	* @param pSourceData Signature related data
	* @param pSignature Signature related data
	* @return TranpayId kreiranog ELBA naloga */
	FUNCTION CreateTranpayForInvoice(
		pInvoiceId IN NUMBER,
		pInvoiceProviderName IN VARCHAR2,
		pInvoiceNo IN VARCHAR2,
		pSenderBankAccountId IN VARCHAR2,
		pSenderName IN VARCHAR2,
		pTranval IN NUMBER,
		pTranpayDescription IN VARCHAR2,
		pReference IN VARCHAR2,
		pResponse VARCHAR2 := NULL,
        pOtp VARCHAR2 := NULL,
        pSourceData VARCHAR2 := NULL,
        pSignature VARCHAR2 := NULL)
	RETURN NUMBER;

	FUNCTION checkInvoiceAccess(pInvoiceId NUMBER) 
        RETURN BOOLEAN;
		
	PROCEDURE checkInvoiceAmount(pInvoiceId NUMBER, pInvoiceAmount NUMBER);
	
	PROCEDURE getInvoiceProvidersForDetail(pIprId IN NUMBER DEFAULT NULL,
										   pIprAttribId IN VARCHAR2 DEFAULT '%', 
										   pIprDetailValue IN VARCHAR2 DEFAULT '%',  
										   pAllInvoiceProviders OUT sys_refcursor,
									       pAllIprDetails OUT sys_refcursor);

										   
	PROCEDURE getInvoiceProvidersForService(pIprId IN NUMBER DEFAULT NULL,
											pAdditionalServiceType IN VARCHAR2 DEFAULT NULL,
											pAllInvoiceProviders OUT sys_refcursor,
											pAllIprDetails OUT sys_refcursor);
	
	FUNCTION getActiveSubscriptsForReqType(pReqTypeId IN VARCHAR2 DEFAULT NULL) RETURN SYS_REFCURSOR;
	
	FUNCTION UploadInvoice_v2(
		pInvoiceId IN OUT NUMBER,
		pInvoiceProviderId IN NUMBER,
		pSubscriptionId IN NUMBER,
		pInvoiceNo IN VARCHAR2,
		pPaymentType IN VARCHAR2,
		pDescription IN VARCHAR2,
		pTranval IN NUMBER,
		pCurrencyId IN VARCHAR2,
		pBeneficiaryName IN VARCHAR2,
		pBeneficiaryAccount IN VARCHAR2,
		pBillingDate IN DATE
	) RETURN BOOLEAN;
	
	PROCEDURE UpdateConsumer(pIpcId NUMBER,
							 pUserReference VARCHAR2,
							 pUserComment VARCHAR2,
		                     pInvoiceTemplate CLOB,
		                     pAutoPaymentEnabled NUMBER DEFAULT NULL,
		                     pPaymentAccountId VARCHAR2 DEFAULT NULL);
							 
	FUNCTION getInvoiceProviderForReqType(pReqTypeId mcore.request_types.id%TYPE)
	RETURN sys_refcursor;

END invoice_mgmt_pck;
/

show errors

Prompt GRANT EXECUTE ON mmobile.invoice_mgmt_pck TO mmobile_user
GRANT EXECUTE ON mmobile.invoice_mgmt_pck TO mmobile_user
/

Prompt CREATE OR REPLACE SYNONYM mmobile_user.invoice_mgmt_pck FOR mmobile.invoice_mgmt_pck
CREATE OR REPLACE SYNONYM mmobile_user.invoice_mgmt_pck FOR mmobile.invoice_mgmt_pck
/