Prompt CREATE OR REPLACE PACKAGE BODY mmobile.invoice_mgmt_pck

CREATE OR REPLACE
PACKAGE BODY mmobile.invoice_mgmt_pck IS


	-- Published
	FUNCTION RegisterConsumer(
		pInvoiceProviderId PLS_INTEGER,
		pUserReference VARCHAR2,
		pUserComment VARCHAR2,
		pUserAgreementId PLS_INTEGER := NULL,
		pResponse VARCHAR2 := NULL,
        pOtp VARCHAR2 := NULL,
        pSourceData VARCHAR2 := NULL,
        pSignature VARCHAR2 := NULL,
		pInvoiceTemplate CLOB DEFAULT NULL)
	RETURN NUMBER IS
		myunit CONSTANT VARCHAR2(16) := 'RegisterConsumer';
	BEGIN
		slog.debug(pkgCtxId, myunit, pInvoiceProviderId || ':' || pUserReference || pUserAgreementId);
		common_pck.CommonSecurityChecks;

		RETURN mcore.invoice_mgmt_pck.RegisterConsumer(
			pInvoiceProviderId => pInvoiceProviderId,
			pEndUserId => auth.getSCID,
			pType => mcore.common_pck.cINVPRVCLTYPECONSUMER,
			pUserReference => pUserReference,
			pUserComment => pUserComment,
			pValid => 1,
			pUserAgreementId => pUserAgreementId,
			pResponse => pResponse,
			pOtp => pOtp,
			pSourceData => pSourceData,
			pSignature => pSignature,
			pInvoiceTemplate => pInvoiceTemplate);

	END RegisterConsumer;

	-- Overloaded version with auto payment parameters
	FUNCTION RegisterConsumer(
		pInvoiceProviderId PLS_INTEGER,
		pUserReference VARCHAR2,
		pUserComment VARCHAR2,
		pUserAgreementId PLS_INTEGER,
		pResponse VARCHAR2,
        pOtp VARCHAR2,
        pSourceData VARCHAR2,
        pSignature VARCHAR2,
		pInvoiceTemplate CLOB,
		pAutoPaymentEnabled NUMBER,
		pPaymentAccountId VARCHAR2)
	RETURN NUMBER IS
		myunit CONSTANT VARCHAR2(16) := 'RegisterConsumer';
	BEGIN
		slog.debug(pkgCtxId, myunit, pInvoiceProviderId || ':' || pUserReference || pUserAgreementId);
		common_pck.CommonSecurityChecks;

		RETURN mcore.invoice_mgmt_pck.RegisterConsumer(
			pInvoiceProviderId => pInvoiceProviderId,
			pEndUserId => auth.getSCID,
			pType => mcore.common_pck.cINVPRVCLTYPECONSUMER,
			pUserReference => pUserReference,
			pUserComment => pUserComment,
			pValid => 1,
			pUserAgreementId => pUserAgreementId,
			pResponse => pResponse,
			pOtp => pOtp,
			pSourceData => pSourceData,
			pSignature => pSignature,
			pInvoiceTemplate => pInvoiceTemplate,
			pAutoPaymentEnabled => pAutoPaymentEnabled,
			pPaymentAccountId => pPaymentAccountId);

	END RegisterConsumer;

	-- Published
	PROCEDURE UnregisterConsumer(pId IN PLS_INTEGER)
	IS
	BEGIN
		mcore.invoice_mgmt_pck.UnregisterConsumer(
			pId => pId,
			pEndUserId => auth.getSCID,
			pType => mcore.common_pck.cINVPRVCLTYPECONSUMER);
	END UnregisterConsumer;

	FUNCTION RegisterConsumer(
		pInvoiceProviderName VARCHAR2,
		pUserReference VARCHAR2,
		pUserAgreementId PLS_INTEGER := NULL)
	RETURN NUMBER IS
		myunit CONSTANT VARCHAR2(16) := 'RegisterConsumer';
	BEGIN
		slog.debug(pkgCtxId, myunit, pInvoiceProviderName || ':' || pUserReference || pUserAgreementId);
		common_pck.CommonSecurityChecks;

		RETURN mcore.invoice_mgmt_pck.RegisterConsumer(
			pInvoiceProviderName => pInvoiceProviderName,
			pEndUserId => auth.getSCID,
			pType => mcore.common_pck.cINVPRVCLTYPECONSUMER,
			pUserReference => pUserReference,
			pValid => 1,
			pUserAgreementId => pUserAgreementId);

	END RegisterConsumer;

    PROCEDURE UnregisterConsumer(pInvoiceProviderName VARCHAR2)
    IS
    BEGIN

		common_pck.CommonSecurityChecks;

        mcore.invoice_mgmt_pck.UnregisterConsumer(pInvoiceProviderName => pInvoiceProviderName,
			pEndUserId => auth.getSCID,
			pType => mcore.common_pck.cINVPRVCLTYPECONSUMER);

    END UnregisterConsumer;

	-- Published
	FUNCTION hasActiveSubscriptions
	RETURN BOOLEAN IS
		myunit CONSTANT VARCHAR2(22) := 'hasActiveSubscriptions';
	BEGIN
		slog.debug(pkgCtxId, myunit);
		RETURN mcore.invoice_mgmt_pck.hasActiveSubscriptions(pEndUserId => auth.getSCID);
	END hasActiveSubscriptions;

	-- Published
	FUNCTION getActiveSubscriptions
	RETURN SYS_REFCURSOR IS
		myunit CONSTANT VARCHAR2(22) := 'getActiveSubscriptions';
	BEGIN
		slog.debug(pkgCtxId, myunit);
        common_pck.CommonSecurityChecks;

		RETURN mcore.invoice_mgmt_pck.getActiveSubscriptions(pEndUserId => auth.getSCID);
	END getActiveSubscriptions;

	FUNCTION checkATMCardlessSubscription
	RETURN BOOLEAN IS
		myunit CONSTANT VARCHAR2(28) := 'checkATMCardlessSubscription';
	BEGIN
		slog.debug(pkgCtxId, myunit);
        common_pck.CommonSecurityChecks;

		RETURN mcore.invoice_mgmt_pck.checkATMCardlessSubscription();
	END checkATMCardlessSubscription;

	FUNCTION checkQRPaySubscription
	RETURN BOOLEAN IS
		myunit CONSTANT VARCHAR2(22) := 'checkQRPaySubscription';
	BEGIN
		slog.debug(pkgCtxId, myunit);
        common_pck.CommonSecurityChecks;

		RETURN mcore.invoice_mgmt_pck.checkQRPaySubscription();
	END checkQRPaySubscription;

	-- Published
	FUNCTION getProviders
	RETURN SYS_REFCURSOR IS
		myunit CONSTANT VARCHAR2(12) := 'getProviders';
	BEGIN
		slog.debug(pkgCtxId, myunit);
        --common_pck.CommonSecurityChecks;

		RETURN mcore.invoice_mgmt_pck.getProviders();
	END getProviders;

	-- Published
	FUNCTION getManagedProviders
	RETURN SYS_REFCURSOR IS
		myunit CONSTANT VARCHAR2(19) := 'getManagedProviders';
	BEGIN
		slog.debug(pkgCtxId, myunit);
        common_pck.CommonSecurityChecks;

		RETURN mcore.invoice_mgmt_pck.getManagedProviders(pEndUserId => auth.getSCID);
	END getManagedProviders;

	-- Published
	FUNCTION getClients(pInvoiceProviderId IN PLS_INTEGER)
	RETURN SYS_REFCURSOR IS
		myunit CONSTANT VARCHAR2(10) := 'getClients';
	BEGIN
		slog.debug(pkgCtxId, myunit, pInvoiceProviderId);
        common_pck.CommonSecurityChecks;

		RETURN mcore.invoice_mgmt_pck.getClients(
			pInvoiceProviderId => pInvoiceProviderId,
			pAdminUserId => auth.getSCID);
	END getClients;

    -- Published
	FUNCTION getInvoicesCount(pStatus IN VARCHAR2 DEFAULT '%')
	RETURN PLS_INTEGER IS
		myunit CONSTANT VARCHAR2(16) := 'getInvoicesCount';
	BEGIN
		slog.debug(pkgCtxId, myunit, pStatus);
		RETURN mcore.invoice_mgmt_pck.getInvoicesCount(pEndUserId => auth.getSCID, pStatus => pStatus);
	END getInvoicesCount;

	FUNCTION getProcessedInvoicesCount
	RETURN PLS_INTEGER IS
		myunit CONSTANT VARCHAR2(25) := 'getProcessedInvoicesCount';
	BEGIN
		slog.debug(pkgCtxId, myunit);
		RETURN mcore.invoice_mgmt_pck.getProcessedInvoicesCount(pEndUserId => auth.getSCID);
	END getProcessedInvoicesCount;

	FUNCTION getInvoices(pStatus IN VARCHAR2 DEFAULT '%')
    RETURN sys_refcursor
    IS
		myunit CONSTANT VARCHAR2(11) := 'getInvoices';
    BEGIN
		slog.debug(pkgCtxId, myunit, pStatus);
        RETURN mcore.invoice_mgmt_pck.getInvoices(pEndUserId => auth.getSCID, pStatus => pStatus);
    END getInvoices;

	FUNCTION getProcessedInvoices
    RETURN sys_refcursor
    IS
		myunit CONSTANT VARCHAR2(20) := 'getProcessedInvoices';
    BEGIN
		slog.debug(pkgCtxId, myunit);
        RETURN mcore.invoice_mgmt_pck.getProcessedInvoices(pEndUserId => auth.getSCID);
    END getProcessedInvoices;
   
    -- Published
    FUNCTION getInvoice(pInvoiceId IN PLS_INTEGER)
    RETURN sys_refcursor
    IS
		myunit CONSTANT VARCHAR2(20) := 'getInvoice';
    BEGIN
		slog.debug(pkgCtxId, myunit);
        RETURN mcore.invoice_mgmt_pck.getInvoice(pInvoiceId);
    END getInvoice;

	-- Published
	PROCEDURE processInvoice(pInvoiceID IN PLS_INTEGER, pAction IN PLS_INTEGER, pTranpayId IN NUMBER DEFAULT NULL, pExtrefName IN mcore.tranpay_extref.extref_name%TYPE DEFAULT NULL)
	IS
		myunit CONSTANT VARCHAR2(14) := 'processInvoice';
    BEGIN
		slog.debug(pkgCtxId, myunit, pInvoiceID || ':' || pAction || ':' || pTranpayId  || ':' || pExtrefName);
		mcore.invoice_mgmt_pck.processInvoice(pInvoiceID, pAction, pTranpayId, pExtrefName);
	END processInvoice;

	-- Published
	PROCEDURE cancelInvoice(pInvoiceID IN PLS_INTEGER)
	IS
	BEGIN
		processInvoice(pInvoiceID => pInvoiceID, pAction => 3);
	END cancelInvoice;

	-- Published
	FUNCTION UploadInvoices(
		pInvoiceProviderId IN PLS_INTEGER,
		pBatchName IN VARCHAR2,
		pInvoiceList IN mcore.invoice_list,
		pInvoiceBatchId OUT PLS_INTEGER,
		pImportResult OUT mcore.invoice_import_result_list)
	RETURN BOOLEAN IS
		myunit CONSTANT VARCHAR2(14) := 'UploadInvoices';
	BEGIN
		slog.debug(pkgCtxId, myunit, pInvoiceProviderId || ':' || pBatchName || ':' || pInvoiceList.COUNT);
        common_pck.CommonSecurityChecks;

		RETURN mcore.invoice_mgmt_pck.UploadInvoices (
			pInvoiceProviderId => pInvoiceProviderId,
			pEndUserId => auth.getSCID,
			pBatchName => pBatchName,
			pInvoiceList => pInvoiceList,
			pInvoiceBatchId => pInvoiceBatchId,
			pImportResult => pImportResult);

	END UploadInvoices;

	-- Published
	FUNCTION getBatches(pInvoiceProviderId IN PLS_INTEGER)
	RETURN sys_refcursor IS
		myunit CONSTANT VARCHAR2(10) := 'getBatches';
	BEGIN
		slog.debug(pkgCtxId, myunit, pInvoiceProviderId);
        common_pck.CommonSecurityChecks;

		RETURN mcore.invoice_mgmt_pck.getBatches(pInvoiceProviderId => pInvoiceProviderId, pEndUserId => auth.getSCID);
	END getBatches;

	-- Published
	PROCEDURE RemoveBatch(pInvoiceBatchId IN PLS_INTEGER)
	IS
		myunit CONSTANT VARCHAR2(11) := 'RemoveBatch';
	BEGIN
		slog.debug(pkgCtxId, myunit, pInvoiceBatchId);
        common_pck.CommonSecurityChecks;

		mcore.invoice_mgmt_pck.RemoveBatch(pEndUserId => auth.getSCID, pInvoiceBatchId => pInvoiceBatchId);
	END RemoveBatch;

	-- Published
	PROCEDURE ChangeBatchStatus(pInvoiceBatchId IN PLS_INTEGER, pValid IN PLS_INTEGER)
	IS
		myunit CONSTANT VARCHAR2(17) := 'ChangeBatchStatus';
	BEGIN
		slog.debug(pkgCtxId, myunit, pInvoiceBatchId || ':' || pValid);
        common_pck.CommonSecurityChecks;

		mcore.invoice_mgmt_pck.ChangeBatchStatus(pEndUserId => auth.getSCID, pInvoiceBatchId => pInvoiceBatchId, pValid => pValid);
	END ChangeBatchStatus;

	-- Published
	FUNCTION getUploadedInvoices(pInvoiceBatchId IN PLS_INTEGER)
	RETURN sys_refcursor IS
		myunit CONSTANT VARCHAR2(19) := 'getUploadedInvoices';
	BEGIN
		slog.debug(pkgCtxId, myunit, pInvoiceBatchId);
        common_pck.CommonSecurityChecks;

		RETURN mcore.invoice_mgmt_pck.getUploadedInvoices(pInvoiceBatchId => pInvoiceBatchId, pAdminUserId => auth.getSCID);

	END getUploadedInvoices;

	/*FUNCTION getUserAgreementText(pID IN NUMBER)
	RETURN CLOB
	IS
		myunit CONSTANT VARCHAR2(20) := 'getUserAgreementText';
	BEGIN
		slog.debug(pkgCtxId, myunit, pId);
		RETURN mcore.invoice_mgmt_pck.getUserAgreementText(pID);
	END getUserAgreementText;*/

	FUNCTION getActiveUserAgreement(pInvoiceProviderId IN NUMBER)
	RETURN SYS_REFCURSOR
	IS
		myunit CONSTANT VARCHAR2(22) := 'getActiveUserAgreement';
	BEGIN
		slog.debug(pkgCtxId, myunit, pInvoiceProviderId);
		RETURN mcore.invoice_mgmt_pck.getActiveUserAgreement(pInvoiceProviderId);
	END getActiveUserAgreement;

	FUNCTION getActiveUserAgreement(pInvoiceProviderName IN VARCHAR2)
	RETURN SYS_REFCURSOR
	IS
		myunit CONSTANT VARCHAR2(23) := 'getActiveUserAgreement1';
	BEGIN
		slog.debug(pkgCtxId, myunit, pInvoiceProviderName);
		RETURN mcore.invoice_mgmt_pck.getActiveUserAgreement(pInvoiceProviderName);
	END getActiveUserAgreement;

	FUNCTION CreateTranpayForInvoice(
		pInvoiceId IN NUMBER,
		pInvoiceProviderName IN VARCHAR2,
		pInvoiceNo IN VARCHAR2,
		pSenderBankAccountId IN VARCHAR2,
		pSenderName IN VARCHAR2,
		pTranval IN NUMBER,
		pTranpayDescription IN VARCHAR2,
		pReference IN VARCHAR2,
		pResponse VARCHAR2 := NULL,
        pOtp VARCHAR2 := NULL,
        pSourceData VARCHAR2 := NULL,
        pSignature VARCHAR2 := NULL)
	RETURN NUMBER IS
		vTranpayId NUMBER;

	BEGIN
		vTranpayId := mcore.invoice_mgmt_pck.CreateTranpayForInvoice(
			pInvoiceId => pInvoiceId,
			pInvoiceProviderName => pInvoiceProviderName,
			pInvoiceNo => pInvoiceNo,
			pSenderBankAccountId => pSenderBankAccountId,
			pSenderName => pSenderName,
			pTranval => pTranval,
			pTranpayDescription => pTranpayDescription,
			pReference => pReference,
			pResponse => pResponse,
			pOtp => pOtp,
			pSourceData => pSourceData,
			pSignature => pSignature);
		RETURN vTranpayId;
	END CreateTranpayForInvoice;


    FUNCTION checkInvoiceAccess(pInvoiceId NUMBER) 
    RETURN BOOLEAN 
    IS
    BEGIN
        return mcore.invoice_mgmt_pck.checkInvoiceAccess(pInvoiceId);
    END;
	
	PROCEDURE checkInvoiceAmount(pInvoiceId NUMBER, pInvoiceAmount NUMBER)
	IS
	BEGIN
		mcore.invoice_mgmt_pck.checkInvoiceAmount(pInvoiceId => pInvoiceId, pInvoiceAmount => pInvoiceAmount);
	END;
	
	PROCEDURE getInvoiceProvidersForDetail(pIprId IN NUMBER DEFAULT NULL,
										   pIprAttribId IN VARCHAR2 DEFAULT '%', 
										   pIprDetailValue IN VARCHAR2 DEFAULT '%',  
										   pAllInvoiceProviders OUT sys_refcursor,
									       pAllIprDetails OUT sys_refcursor)
	IS 
	BEGIN 
		mcore.invoice_mgmt_pck.getInvoiceProvidersForDetail(pIprId => pIprId,
															pIprAttribId => pIprAttribId, 
															pIprDetailValue => pIprDetailValue,
															pAllInvoiceProviders => pAllInvoiceProviders,														
															pAllIprDetails => pAllIprDetails);
	END;
		
	PROCEDURE getInvoiceProvidersForService(pIprId IN NUMBER DEFAULT NULL,
											pAdditionalServiceType IN VARCHAR2 DEFAULT NULL,
											pAllInvoiceProviders OUT sys_refcursor,
											pAllIprDetails OUT sys_refcursor)
	IS
		myunit CONSTANT VARCHAR2(30) := 'getInvoiceProvidersForService';
	BEGIN
		slog.debug(pkgCtxId, myunit);
        common_pck.CommonSecurityChecks;

	    mcore.invoice_mgmt_pck.getInvoiceProvidersForService(pIprId => pIprId,
															pAdditionalServiceType => pAdditionalServiceType,
															pAllInvoiceProviders =>  pAllInvoiceProviders,
															pAllIprDetails =>  pAllIprDetails);
	END getInvoiceProvidersForService;
	

	
	FUNCTION getActiveSubscriptsForReqType(pReqTypeId IN VARCHAR2 DEFAULT NULL)
	RETURN SYS_REFCURSOR IS
		myunit CONSTANT VARCHAR2(30) := 'getActiveSubscriptsForReqType';
	BEGIN
		slog.debug(pkgCtxId, myunit);
        common_pck.CommonSecurityChecks;

		RETURN mcore.invoice_mgmt_pck.getActiveSubscriptsForReqType(pReqTypeId => pReqTypeId);
	END getActiveSubscriptsForReqType;
	
	FUNCTION UploadInvoice_v2(
		pInvoiceId IN OUT NUMBER,
		pInvoiceProviderId IN NUMBER,
		pSubscriptionId IN NUMBER,
		pInvoiceNo IN VARCHAR2,
		pPaymentType IN VARCHAR2,
		pDescription IN VARCHAR2,
		pTranval IN NUMBER,
		pCurrencyId IN VARCHAR2,
		pBeneficiaryName IN VARCHAR2,
		pBeneficiaryAccount IN VARCHAR2,
		pBillingDate IN DATE
	) RETURN BOOLEAN
	IS
		myunit CONSTANT VARCHAR2(30) := 'UploadInvoice_v2';
		pImportResult mcore.invoice_import_result;
	BEGIN 
		slog.debug(pkgCtxId, myunit);
		RETURN mcore.invoice_mgmt_pck.UploadInvoice_v2(pInvoiceId => pInvoiceId,		
														pInvoiceBatchId => NULL,
														pInvoiceProviderId  => pInvoiceProviderId,
														pSubscriptionId  => pSubscriptionId,
														pUserReference => NULL,
														pExternalClientId => NULL,
														pInvoiceNo => pInvoiceNo,
														pPaymentType => pPaymentType,
														pDescription => pDescription,
														pTranval => pTranval,
														pCurrencyId => pCurrencyId,
														pBeneficiaryName => pBeneficiaryName,
														pBeneficiaryAccount => pBeneficiaryAccount,
														pMessage => NULL,
														pBILLING_PERIOD => NULL,
														pBILLING_DATE => pBillingDate,
														pPAYMENT_DUE_DATE => NULL,
														pImportResult => pImportResult);
	END;
	
	PROCEDURE UpdateConsumer(pIpcId NUMBER,
							 pUserReference VARCHAR2,
							 pUserComment VARCHAR2,
		                     pInvoiceTemplate CLOB)
	IS 
		myunit CONSTANT VARCHAR2(30) := 'UpdateConsumer';
	BEGIN
		slog.debug(pkgCtxId, myunit, pIpcId);
		
		mcore.invoice_mgmt_pck.UpdateConsumer(pIpcId => pIpcId,
											  pUserReference => pUserReference,
							                  pUserComment => pUserComment,
		                                      pInvoiceTemplate => pInvoiceTemplate);
	END;

	-- Overloaded version with auto payment parameters
	PROCEDURE UpdateConsumer(pIpcId NUMBER,
							 pUserReference VARCHAR2,
							 pUserComment VARCHAR2,
		                     pInvoiceTemplate CLOB,
		                     pAutoPaymentEnabled NUMBER,
		                     pPaymentAccountId VARCHAR2)
	IS
		myunit CONSTANT VARCHAR2(30) := 'UpdateConsumer';
	BEGIN
		slog.debug(pkgCtxId, myunit, pIpcId);

		mcore.invoice_mgmt_pck.UpdateConsumer(pIpcId => pIpcId,
											  pUserReference => pUserReference,
							                  pUserComment => pUserComment,
		                                      pInvoiceTemplate => pInvoiceTemplate,
		                                      pAutoPaymentEnabled => pAutoPaymentEnabled,
		                                      pPaymentAccountId => pPaymentAccountId);
	END;
	
	FUNCTION getInvoiceProviderForReqType(pReqTypeId mcore.request_types.id%TYPE)
	RETURN sys_refcursor
	IS
		myunit CONSTANT VARCHAR2(30) := 'getInvoiceProviderForReqType';
	BEGIN 
		slog.debug(pkgCtxId, myunit, pReqTypeId);
		RETURN mcore.invoice_mgmt_pck.getInvoiceProviderForReqType(pReqTypeId => pReqTypeId);
	END;

END invoice_mgmt_pck;
/
show errors
