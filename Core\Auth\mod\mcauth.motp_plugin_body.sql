PROMPT Creating package body script for package MOTP_PLUGIN
CREATE OR REPLACE
PACKAGE BODY MCAUTH.MOTP_PLUGIN AS

  /* mOTP Plugin */
  mcsmCH_Var CONSTANT VARCHAR2(100) := sspkg.readvchar(pkgCtxId || '/SessVar4CH');
  challengeLen CONSTANT PLS_INTEGER := sspkg.readInt(pkgCtxId || '/ChallengeLen');
  respLen CONSTANT PLS_INTEGER := sspkg.readInt(pkgCtxId || '/ResponseLen');
  valid4TimeSecs constant integer := sspkg.readInt(pkgCtxId || '/Valid4TimeSecs');
  cERR_InternalError CONSTANT VARCHAR2(80) := '/Core/Auth/err/InternalError';

 function convertToLetters(pValue NUMBER, pMaxChars PLS_INTEGER)
 return varchar2 is
	numVal NUMBER := pValue;
	result VARCHAR2(1024);
	i pls_integer := 0;
 begin
    
	WHILE (numVal>0 AND i<pMaxChars) LOOP
		result := result || CHR(65+MOD(numVal,25));
		
		numVal := TRUNC(numVal/10);
		i :=i+1;
	END LOOP;
	return result;
 end convertToLetters;
 
	FUNCTION hex2dec (hexnum IN VARCHAR2) RETURN NUMBER IS
	BEGIN
		RETURN mcore.util.hex2dec(hexnum);
	END hex2dec;
	
  FUNCTION genExpectedResponse (pSourceData IN VARCHAR2)
  RETURN VARCHAR2 IS
  BEGIN
	RETURN convertToLetters(
			mcore.util.hex2dec(
				SUBSTR(
					rawtohex(
						dbms_crypto.hash(
							src => 
							utl_raw.cast_to_raw(pSourceData), 
							typ => dbms_crypto.HASH_SH1)
					),
				1,32)
			),
			respLen
		);
  END genExpectedResponse;

  function checkCHRESP(clientID CLIENT.ID%TYPE, challenge varchar2, response varchar2) return integer
  is
	myunit CONSTANT VARCHAR2(30) := 'checkCHRESP';
	vSecKey varchar2(1024);
	vApplicationId APP_EXTAUTH.application_id%TYPE;
	vExtAuth mcauth.app_extauth.extauth_id%TYPE;

	vClientLoginMethod varchar2(200);
	vDevId APP_EXTAUTH.dev_id%TYPE;
	vExpectedResponse varchar2(50);
	vChallenge varchar2(1024);
	vEvent VARCHAR2(30);
	vLoginType VARCHAR2(30);
	vSignatureType VARCHAR2(30);
	vPin VARCHAR2(40);
	vSourceData VARCHAR2(400);
	the_rowid rowid;
	failed_attempts PLS_INTEGER;
	maxAttemptsBeforeLogout PLS_INTEGER;
        vMOTP_USE_PIN VARCHAR2(10);
  begin
    slog.debug(pkgCtxId, myUnit, clientID || ':'||challenge||':'||response);

	-- #9090
	if (mcsm.readMDate(mcsmCH_Var) + valid4TimeSecs/(24*60*60) < sysdate) then
		-- No OTP generated (should not be) or staled OTP
		slog.warn(pkgCtxId, myUnit, 'Staled response for client_id="'||clientID||'"');
		return 0;
	end if;

	vChallenge := mcsm.read(mcsmCH_Var);
	slog.debug(pkgCtxId, myUnit, 'Read challenge from session memory ' || vChallenge);

	-- Vrijednosti su upisane u kontekst kod odgovarajuceg dogadaja (npr. login, potpis i sl.)
	vApplicationId := auth.getApplicationId();
	vDevId := auth.getDeviceId();
	vEvent := auth.getEvent();

	vExtAuth := auth.clientExtAuthID(pUsername => auth.getSUser(), pDeviceId => vDevId, pApplicationId => vApplicationId);

	vClientLoginMethod := auth.clientLoginMethod(pUsername => auth.getSUser(), pDeviceId => auth.getDeviceId(), pApplicationId => auth.getApplicationId(), pClientExtAuthId => vExtAuth);

	IF NVL(vClientLoginMethod, common_pck.cOTPTYPE_NULL) = 'NULL' THEN
		vLoginType := common_pck.cOTPTYPE_NULL;
	ELSE
		vLoginType := auth.clientOTPType(pClientExtAuthId => vExtAuth);
	END IF;

	vSignatureType := auth.clientSignatureMethod(pClientExtAuthId => vExtAuth);

	vSecKey := auth.getSecKey(pclientId => clientID, papplication_id => vApplicationId, pdev_id => vDevId);
	slog.debug(pkgCtxId, myUnit, 'Data:'|| vApplicationId||':'||vDevId||':'||vExtAuth || ':' || vEvent|| ':' || vLoginType || ':' || vSignatureType || ':' || vSecKey);

	vMOTP_USE_PIN := mcsm.getMOTP_USE_PIN;
    mcsm.setMOTP_USE_PIN(NULL);

	 IF (vEvent = common_pck.cAUTHEVT_LOGIN AND vLoginType = common_pck.cOTPTYPE_CHRESPPIN AND (vMOTP_USE_PIN IS NULL OR vMOTP_USE_PIN = 'true')) 
	         OR (vEvent = common_pck.cAUTHEVT_SIGN AND vSignatureType = common_pck.cOTPTYPE_CHRESPPIN AND (vMOTP_USE_PIN IS NULL OR vMOTP_USE_PIN = 'true')) THEN

		BEGIN
			SELECT rowid, ph3 AS pin, TO_NUMBER(NVL(ph4, '0')) AS failed_attempts
			  INTO the_rowid, vPin, failed_attempts
			  FROM app_extauth
			 WHERE valid = 1
			   AND application_id = common_pck.cAPP_MOBILE
			   AND client_id = clientID
			   AND dev_id = vDevId;
		EXCEPTION
			WHEN NO_DATA_FOUND THEN
				slog.error(pkgCtxId, myUnit, 'NO-DATA-FOUND:' || clientID || ':' || vDevId);
				sspkg.raiseError(pkgCtxId || '/err/ExtAuthProblem', NULL, pkgCtxId, myunit);
			WHEN TOO_MANY_ROWS THEN
				slog.error(pkgCtxId, myUnit, 'TOO_MANY_ROWS:' || clientID || ':' || vDevId);
				sspkg.raiseError(pkgCtxId || '/err/ExtAuthProblem', NULL, pkgCtxId, myunit);
		END;

		IF vPin IS NOT NULL THEN
			slog.debug(pkgCtxId, myUnit, 'Expecting response encoded with PIN');
			vSourceData := vSecKey || vDevId || vChallenge || vPin;

		ELSE
			slog.debug(pkgCtxId, myUnit, 'Expecting response encoded without PIN');
			vSourceData := vSecKey || vDevId || vChallenge;

		END IF;
	ELSE
		slog.debug(pkgCtxId, myUnit, 'Expecting response encoded without PIN');
		vSourceData := vSecKey || vDevId || vChallenge;
	END IF;

    slog.debug(pkgCtxId, myUnit, 'Source data: '|| vSourceData);

	vExpectedResponse := genExpectedResponse ( vSourceData );

	slog.debug(pkgCtxId, myUnit, 'Expected response: '|| vExpectedResponse);

	if (response = vExpectedResponse) then
		slog.debug(pkgCtxId, myUnit, 'Response Ok!');
		IF ((vEvent = common_pck.cAUTHEVT_LOGIN AND vLoginType = common_pck.cOTPTYPE_CHRESPPIN)
			OR (vEvent = common_pck.cAUTHEVT_SIGN AND vSignatureType = common_pck.cOTPTYPE_CHRESPPIN))
			AND failed_attempts > 0
		THEN
			auth.setVerificationAttempt (pExtAuthRowId => the_rowid, pAttempt# => 0);
		END IF;

		return 1;
	end if;

	slog.debug(pkgCtxId, myUnit, 'Response NOT Ok!');
	IF (vEvent = common_pck.cAUTHEVT_LOGIN AND vLoginType = common_pck.cOTPTYPE_CHRESPPIN) OR (vEvent = common_pck.cAUTHEVT_SIGN AND vSignatureType = common_pck.cOTPTYPE_CHRESPPIN) THEN
		maxAttemptsBeforeLogout := NVL(sspkg.readInt('/Core/Auth/Plugin/MPIN/maxAttemptsBeforeLogout'), 3);

		failed_attempts := failed_attempts + 1;

		IF failed_attempts >= maxAttemptsBeforeLogout THEN
			auth.setVerificationAttempt (pExtAuthRowId => the_rowid, pAttempt# => 0);
			mcsm.destroys;
		ELSE
			auth.setVerificationAttempt (pExtAuthRowId => the_rowid, pAttempt# => failed_attempts);
		END IF;
	END IF;

	return 0;

  end checkCHRESP;

  function checkCHRESP_WithPWDHash(clientID CLIENT.ID%TYPE, challenge varchar2, response varchar2) return integer
  is
    myunit CONSTANT VARCHAR2(30) := 'checkCHRESP_WithPWDHash';
	vSecKey varchar2(1024);
	vApplicationId APP_EXTAUTH.application_id%TYPE;
	vExtAuth mcauth.app_extauth.extauth_id%TYPE;

	vClientLoginMethod varchar2(200);
	vDevId APP_EXTAUTH.dev_id%TYPE;
	vExpectedResponse varchar2(50);
	vChallenge varchar2(1024);
	vEvent VARCHAR2(30);	
	vLoginType VARCHAR2(30);
	vSignatureType VARCHAR2(30);
	vPin VARCHAR2(40);
	vSourceData VARCHAR2(400);	
    vPwdHash client.password%TYPE;
    the_rowid rowid;
    failed_attempts PLS_INTEGER;
	maxAttemptsBeforeLogout PLS_INTEGER;
    vMOTP_USE_PIN VARCHAR2(10);
	vPasswordCISHash client.password%TYPE;
	vPasswordCSHash client.password_cs%TYPE;
	vUsePwdIndicator VARCHAR2(3);
  begin
    slog.debug(pkgCtxId, myUnit, clientID || ':'||challenge||':'||response);

	-- #9090
	if (mcsm.readMDate(mcsmCH_Var) + valid4TimeSecs/(24*60*60) < sysdate) then
		-- No OTP generated (should not be) or staled OTP
		slog.warn(pkgCtxId, myUnit, 'Staled response for client_id="'||clientID||'"');
		return 0;
	end if;

   vChallenge := mcsm.read(mcsmCH_Var);
	slog.debug(pkgCtxId, myUnit, 'Read challenge from session memory ' || vChallenge);

	-- Vrijednosti su upisane u kontekst kod odgovarajuceg dogadaja (npr. login, potpis i sl.)
	vApplicationId := auth.getApplicationId();
	vDevId := auth.getDeviceId();
	vEvent := auth.getEvent();

	vExtAuth := auth.clientExtAuthID(pUsername => auth.getSUser(), pDeviceId => vDevId, pApplicationId => vApplicationId);

	vClientLoginMethod := auth.clientLoginMethod(pUsername => auth.getSUser(), pDeviceId => auth.getDeviceId(), pApplicationId => auth.getApplicationId(), pClientExtAuthId => vExtAuth);

	IF NVL(vClientLoginMethod, common_pck.cOTPTYPE_NULL) = 'NULL' THEN
		vLoginType := common_pck.cOTPTYPE_NULL;
	ELSE
		vLoginType := auth.clientOTPType(pClientExtAuthId => vExtAuth);
	END IF;

	vSignatureType := auth.clientSignatureMethod(pClientExtAuthId => vExtAuth);

	vSecKey := auth.getSecKey(pclientId => clientID, papplication_id => vApplicationId, pdev_id => vDevId);
	
	BEGIN
        auth.getPwdHash ( 
            	pclientId => clientID, 
            	pUsePwdIndicator => vUsePwdIndicator,
            	pPwdCaseInsensitiveHash => vPasswordCISHash,
            	pPwdCaseSensitiveHash => vPasswordCSHash);
            	
        IF vUsePwdIndicator = common_pck.cPwdCaseInSensitiveIndicator THEN            
            vPwdHash := vPasswordCISHash;
        ELSE
            vPwdHash := vPasswordCSHash;
        END IF;
    
    EXCEPTION
        WHEN sspkg.sysException THEN
				RAISE;
		WHEN OTHERS THEN
			slog.error(pkgCtxId, myUnit, sqlcode||':'||sqlerrm);
            sspkg.raiseError(cERR_InternalError, NULL, pkgCtxId, myunit);
    END;
	
	slog.debug(pkgCtxId, myUnit, 'Data:'|| vApplicationId||':'||vDevId||':'||vEvent|| ':' || vLoginType || ':' || vSignatureType || ':' || vSecKey || ':' || vPwdHash);

	vMOTP_USE_PIN := mcsm.getMOTP_USE_PIN;

	IF (vMOTP_USE_PIN IS NOT NULL AND vMOTP_USE_PIN = 'true') OR (vMOTP_USE_PIN IS NULL AND vSignatureType = common_pck.cOTPTYPE_CHRESPPIN) THEN

		BEGIN
			SELECT rowid, ph3 AS pin, TO_NUMBER(NVL(ph4, '0')) AS failed_attempts
			  INTO the_rowid, vPin, failed_attempts
			  FROM app_extauth
			 WHERE valid = 1
			   AND application_id = common_pck.cAPP_MOBILE
			   AND client_id = clientID
			   AND dev_id = vDevId;
		EXCEPTION
			WHEN NO_DATA_FOUND THEN
				slog.error(pkgCtxId, myUnit, 'NO-DATA-FOUND:' || clientID || ':' || vDevId);
				sspkg.raiseError(pkgCtxId || '/err/ExtAuthProblem', NULL, pkgCtxId, myunit);
			WHEN TOO_MANY_ROWS THEN
				slog.error(pkgCtxId, myUnit, 'TOO_MANY_ROWS:' || clientID || ':' || vDevId);
				sspkg.raiseError(pkgCtxId || '/err/ExtAuthProblem', NULL, pkgCtxId, myunit);
		END;

		IF vPin IS NOT NULL THEN
			slog.debug(pkgCtxId, myUnit, 'Expecting response encoded with PIN');
			vSourceData := vSecKey || vDevId || vChallenge || vPwdHash || vPin;

		ELSE
			slog.debug(pkgCtxId, myUnit, 'Expecting response encoded without PIN');
			vSourceData := vSecKey || vDevId || vChallenge || vPwdHash;

		END IF;
	 ELSE
		slog.debug(pkgCtxId, myUnit, 'Expecting response encoded without PIN');
		vSourceData := vSecKey || vDevId || vChallenge;
	 END IF;
	
         vMOTP_USE_PIN := NULL;

	
    slog.debug(pkgCtxId, myUnit, 'Source data: '|| vSourceData);

	vExpectedResponse := genExpectedResponse ( vSourceData );

	slog.debug(pkgCtxId, myUnit, 'Expected response: '|| vExpectedResponse);

	if (response = vExpectedResponse) then
		slog.debug(pkgCtxId, myUnit, 'Response Ok!');
		IF ((vEvent = common_pck.cAUTHEVT_LOGIN AND vLoginType = common_pck.cOTPTYPE_CHRESPPIN)
			OR (vEvent = common_pck.cAUTHEVT_SIGN AND vSignatureType = common_pck.cOTPTYPE_CHRESPPIN))
			AND failed_attempts > 0 
		THEN
			auth.setVerificationAttempt (pExtAuthRowId => the_rowid, pAttempt# => 0);
		END IF;

		return 1;
	end if;

	slog.debug(pkgCtxId, myUnit, 'Response NOT Ok!');
	IF (vEvent = common_pck.cAUTHEVT_LOGIN AND vLoginType = common_pck.cOTPTYPE_CHRESPPIN) OR (vEvent = common_pck.cAUTHEVT_SIGN AND vSignatureType = common_pck.cOTPTYPE_CHRESPPIN) THEN
		maxAttemptsBeforeLogout := NVL(sspkg.readInt('/Core/Auth/Plugin/MPIN/maxAttemptsBeforeLogout'), 3);
		
		failed_attempts := failed_attempts + 1;
		
		IF failed_attempts >= maxAttemptsBeforeLogout THEN 
			auth.setVerificationAttempt (pExtAuthRowId => the_rowid, pAttempt# => 0);
			mcsm.destroys;			
		ELSE
			auth.setVerificationAttempt (pExtAuthRowId => the_rowid, pAttempt# => failed_attempts);
		END IF;        
	END IF;
	
	return 0;

  end checkCHRESP_WithPWDHash; 

  /* Generate challenge and send sms with response */
  function genChallenge(clientID CLIENT.ID%TYPE, sourceData varchar2) return varchar2
  is
    myunit CONSTANT VARCHAR2(30) := 'genChallenge';
    challenge varchar2(1024);
  begin
    slog.debug(pkgCtxId, myUnit, clientID||':'||sourceData||':'||challengeLen);
	challenge := dbms_random.string('l', challengeLen);
	slog.debug(pkgCtxId, myUnit, challenge);
	-- HSAFET: #9090
    mcsm.write(mcsmCH_Var, challenge);
	slog.debug(pkgCtxId, myUnit, 'Session data saved!');
    return challenge;

  end genChallenge;

END MOTP_PLUGIN;
/

show errors
