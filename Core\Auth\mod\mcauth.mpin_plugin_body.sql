Prompt
Prompt CREATE OR REPLACE PACKAGE BODY mcauth.mpin_plugin
CREATE OR REPLACE PACKAGE BODY mcauth.mpin_plugin
AS
  minPINLength PLS_INTEGER;
  maxPINLength PLS_INTEGER;
  minDifferentDigits PLS_INTEGER;
  maxAttemptsBeforeLogout PLS_INTEGER;
  
  cERR_UnauthorizedDevice CONSTANT VARCHAR2(40) := '/Core/Auth/err/UnauthorizedDevice';
  cERR_InternalError CONSTANT VARCHAR2(40) := '/Core/Auth/err/cERR_InternalError';
 
  PROCEDURE checkComplexity(pin IN varchar2)
  AS
    myunit CONSTANT VARCHAR2(30) := 'checkComplexity';
   
    differentDigits varchar2(50) := '';
    digit VARCHAR2(100);
    hasDigit boolean;
  BEGIN
	slog.debug(pkgCtxId, myunit);
	
	minPINLength := NVL(sspkg.readInt(pkgCtxId || '/minLength'), 4);
    maxPINLength := NVL(sspkg.readInt(pkgCtxId || '/maxLength'), 4);
    minDifferentDigits := NVL(sspkg.readInt(pkgCtxId || '/minDifferentDigits'), 2);
    maxAttemptsBeforeLogout := NVL(sspkg.readInt(pkgCtxId || '/maxAttemptsBeforeLogout'), 3);
	
    IF LENGTH(pin) < minPINLength THEN
     sspkg.raiseError(pkgCtxId || '/err/pinTooShort', mlang.trans(NVL(auth.getLang(), 'bs'), pkgCtxId || '/err/pinTooShort'), pkgCtxId, myUnit);
    END IF;
   
    IF LENGTH(pin) > maxPINLength THEN
     sspkg.raiseError(pkgCtxId || '/err/pinTooLong', mlang.trans(NVL(auth.getLang(), 'bs'), pkgCtxId || '/err/pinTooLong'), pkgCtxId, myUnit);
    END IF;
   
    -- check minimum number of different digits
    FOR i IN 1..LENGTH(pin) 
    LOOP 
      digit := substr(pin, i, 1);
      
      hasDigit := FALSE;      
	  IF differentDigits IS NOT NULL THEN
      FOR j IN 1..LENGTH(differentDigits) LOOP
        IF digit = substr(differentDigits, j, 1) THEN
           hasDigit := TRUE;
        END IF;
      END LOOP;
	  END IF;
      
      IF NOT hasDigit THEN
          differentDigits := differentDigits || digit;
      END IF;
     
    END LOOP; 
   
    IF LENGTH(differentDigits) < minDifferentDigits THEN 
       sspkg.raiseError(pkgCtxId || '/err/minDifferentDigits', mlang.trans(NVL(auth.getLang(), 'bs'), pkgCtxId || '/err/minDifferentDigits'), pkgCtxId, myUnit);
    END IF;
   
  END checkComplexity;
 
 
  PROCEDURE checkPIN(clientID IN CLIENT.ID%TYPE, deviceID IN APP_EXTAUTH.DEV_ID%TYPE, pin IN varchar2)
  AS
   myunit CONSTANT VARCHAR2(30) := 'checkPIN';

   CURSOR c_extauth IS
    SELECT rowid as the_rowid, id, ph3 AS pin, TO_NUMBER(NVL(ph4, '0')) AS failed_attempts
      FROM app_extauth
     WHERE valid = 1
       AND application_id = common_pck.cAPP_MOBILE
       AND client_id = clientID
       AND dev_id = deviceID;

   extauth_rec c_extauth%ROWTYPE;
   
   pragma autonomous_transaction;

  BEGIN
    slog.debug(pkgCtxId, myunit, clientID);

    IF clientID IS NULL THEN
     slog.error(pkgCtxId, myunit, 'No clientID provided!');
	 ROLLBACK;
	 sspkg.raiseError(cERR_InternalError, mlang.trans(NVL(auth.getLang(), 'bs'), cERR_InternalError), pkgCtxId, myUnit);
    END IF;

    IF deviceID IS NULL THEN
		slog.error(pkgCtxId, myunit, 'No deviceID provided!');
		ROLLBACK;
		sspkg.raiseError(cERR_InternalError, mlang.trans(NVL(auth.getLang(), 'bs'), cERR_InternalError), pkgCtxId, myUnit);
    END IF;
  
    IF pin IS NULL THEN
		slog.error(pkgCtxId, myunit, 'No PIN provided!');
		ROLLBACK;
		sspkg.raiseError(cERR_InternalError, mlang.trans(NVL(auth.getLang(), 'bs'), cERR_InternalError), pkgCtxId, myUnit);
    END IF;

    OPEN c_extauth;
    FETCH c_extauth into extauth_rec;
    IF c_extauth%NOTFOUND THEN
        CLOSE c_extauth;
		slog.error(pkgCtxId, myunit, 'ExtAuth problem: device not registered for user!');
		ROLLBACK;
        sspkg.raiseError(cERR_UnauthorizedDevice, mlang.trans(NVL(auth.getLang(), 'bs'), cERR_UnauthorizedDevice), pkgCtxId, myUnit);
    END IF;
    CLOSE c_extauth;

    IF common_pck.hash(pin) = extauth_rec.pin THEN
        slog.debug(pkgCtxId, myUnit, 'PIN OK for client_id="'||clientID||'"');
        
        IF extauth_rec.failed_attempts > 0 THEN
		
            auth.setVerificationAttempt (pExtAuthRowId => extauth_rec.the_rowid, pAttempt# => 0);
			  
        END IF;
        
		COMMIT;
        RETURN;
    END IF;
	
	extauth_rec.failed_attempts := extauth_rec.failed_attempts + 1;
              
    IF extauth_rec.failed_attempts >= maxAttemptsBeforeLogout THEN 
        
		auth.setVerificationAttempt (pExtAuthRowId => extauth_rec.the_rowid, pAttempt# => 0);
        
        mcsm.destroys;
		COMMIT;
		sspkg.raiseError('/Core/SessMgmt/err/expired', mlang.trans(NVL(auth.getLang(), 'bs'), '/Core/SessMgmt/err/expired'), pkgCtxId, myUnit);
		
	ELSE
		auth.setVerificationAttempt (pExtAuthRowId => extauth_rec.the_rowid, pAttempt# => extauth_rec.failed_attempts);
				
		COMMIT;
		sspkg.raiseError(pkgCtxId || '/err/wrongPIN', mlang.trans(NVL(auth.getLang(), 'bs'), pkgCtxId || '/err/wrongPIN'), pkgCtxId, myUnit);
    END IF;


  EXCEPTION
    WHEN sspkg.sysException THEN
	  ROLLBACK;
      RAISE;
    WHEN others THEN
		ROLLBACK;
		sspkg.raiseOraError(pkgCtxId, myUnit);
  END checkPIN;
  
  PROCEDURE addPIN(clientID IN CLIENT.ID%TYPE, deviceID IN APP_EXTAUTH.DEV_ID%TYPE, pin IN varchar2)
  AS
   myunit CONSTANT VARCHAR2(30) := 'addPIN';

   CURSOR c_extauth IS
    SELECT rowid as the_rowid, id, ph3 AS pin
      FROM app_extauth
     WHERE valid = 1
       AND application_id = common_pck.cAPP_MOBILE
       AND client_id = clientID
       AND dev_id = deviceID;

   extauth_rec c_extauth%ROWTYPE;

  BEGIN
    slog.debug(pkgCtxId, myunit, clientID || ':' || deviceID || ':' || pin);

    IF clientID IS NULL THEN
     slog.error(pkgCtxId, myunit, 'No clientID provided!');
	 sspkg.raiseError(cERR_InternalError, mlang.trans(NVL(auth.getLang(), 'bs'), cERR_InternalError), pkgCtxId, myUnit);
    END IF;

    IF deviceID IS NULL THEN
		slog.error(pkgCtxId, myunit, 'No deviceID provided!');
		sspkg.raiseError(cERR_InternalError, mlang.trans(NVL(auth.getLang(), 'bs'), cERR_InternalError), pkgCtxId, myUnit);
    END IF;
  
    IF pin IS NULL THEN
		slog.error(pkgCtxId, myunit, 'No PIN provided!');
		sspkg.raiseError(cERR_InternalError, mlang.trans(NVL(auth.getLang(), 'bs'), cERR_InternalError), pkgCtxId, myUnit);
    END IF;
   
    checkComplexity(pin);

    OPEN c_extauth;
    FETCH c_extauth into extauth_rec;
    IF c_extauth%NOTFOUND THEN
        CLOSE c_extauth;
		slog.error(pkgCtxId, myunit, 'ExtAuth problem: device not registered for user!');        
		sspkg.raiseError(cERR_UnauthorizedDevice, mlang.trans(NVL(auth.getLang(), 'bs'), cERR_UnauthorizedDevice), pkgCtxId, myUnit);
    END IF;
    CLOSE c_extauth;
	
	IF extauth_rec.pin IS NOT NULL THEN
		slog.error(pkgCtxId, myunit, 'ExtAuth problem: PIN already set!');
		sspkg.raiseError(pkgCtxId || '/err/pinAlreadySet', mlang.trans(NVL(auth.getLang(), 'bs'), pkgCtxId || '/err/pinAlreadySet'), pkgCtxId, myUnit);
	END IF;

    UPDATE app_extauth 
       SET ph3 = common_pck.hash(pin)
     WHERE rowid = extauth_rec.the_rowid;
           
  EXCEPTION
    WHEN sspkg.sysException THEN
      RAISE;
    WHEN others THEN
      sspkg.raiseOraError(pkgCtxId, myUnit);
  END addPIN; 
 
  PROCEDURE changePIN(clientID IN CLIENT.ID%TYPE, deviceID IN APP_EXTAUTH.DEV_ID%TYPE, newPIN IN varchar2, oldPIN IN varchar2)
  AS
   myunit CONSTANT VARCHAR2(30) := 'changePIN';

   CURSOR c_extauth IS
    SELECT rowid as the_rowid, id, ph3 AS pin
      FROM app_extauth
     WHERE valid = 1
       AND application_id = common_pck.cAPP_MOBILE
       AND client_id = clientID
       AND dev_id = deviceID;

   extauth_rec c_extauth%ROWTYPE;

  BEGIN
    slog.debug(pkgCtxId, myunit, clientID || ':' || deviceID || ':' || oldPIN || ':' || newPIN);

    IF clientID IS NULL THEN
     slog.error(pkgCtxId, myunit, 'No clientID provided!');
	 sspkg.raiseError(cERR_InternalError, mlang.trans(NVL(auth.getLang(), 'bs'), cERR_InternalError), pkgCtxId, myUnit);
    END IF;

    IF deviceID IS NULL THEN
		slog.error(pkgCtxId, myunit, 'No deviceID provided!');
		sspkg.raiseError(cERR_InternalError, mlang.trans(NVL(auth.getLang(), 'bs'), cERR_InternalError), pkgCtxId, myUnit);
    END IF;
  
    IF newPin IS NULL THEN
		slog.error(pkgCtxId, myunit, 'No new PIN provided!');
		sspkg.raiseError(cERR_InternalError, mlang.trans(NVL(auth.getLang(), 'bs'), cERR_InternalError), pkgCtxId, myUnit);
    END IF;
	
	IF oldPIN IS NULL THEN
		slog.error(pkgCtxId, myunit, 'No old PIN provided!');
		sspkg.raiseError(cERR_InternalError, mlang.trans(NVL(auth.getLang(), 'bs'), cERR_InternalError), pkgCtxId, myUnit);
    END IF;
	
	checkPIN(clientID, deviceID, oldPIN);
     
    checkComplexity(newPIN);

    OPEN c_extauth;
    FETCH c_extauth into extauth_rec;
    IF c_extauth%NOTFOUND THEN
        CLOSE c_extauth;
		slog.error(pkgCtxId, myunit, 'ExtAuth problem: device not registered for user!');
		sspkg.raiseError(cERR_UnauthorizedDevice, mlang.trans(NVL(auth.getLang(), 'bs'), cERR_UnauthorizedDevice), pkgCtxId, myUnit);
    END IF;
    CLOSE c_extauth;

    IF common_pck.hash(oldPIN) <> extauth_rec.pin THEN
		slog.error(pkgCtxId, myunit, 'OldPIN dont match!');		
		sspkg.raiseError(pkgCtxId || '/err/wrongPIN', mlang.trans(NVL(auth.getLang(), 'bs'), pkgCtxId || '/err/wrongPIN'), pkgCtxId, myUnit);
    END IF;
   
    UPDATE app_extauth 
       SET ph3 = common_pck.hash(newPIN)
     WHERE rowid = extauth_rec.the_rowid;
           
  EXCEPTION
    WHEN sspkg.sysException THEN
      raise;
    WHEN others THEN
      sspkg.raiseOraError(pkgCtxId, myUnit);
  END changePIN;
 
 
END MPIN_PLUGIN;
/

show errors