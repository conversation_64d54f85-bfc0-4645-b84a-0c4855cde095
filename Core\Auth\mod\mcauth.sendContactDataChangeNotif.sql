prompt
PROMPT mcauth.sendContactDataChangeNotif.sql: CREATE OR REPLACE PROCEDURE mcauth.sendContactDataChangeNotif

create or replace procedure          mcauth.sendContactDataChangeNotif(pEvent mcore.obj$eventchangecontactdata) IS
	
    pkgCtxId CONSTANT VARCHAR2(10) := '/Core/Auth';
    myunit CONSTANT VARCHAR2(26) := 'sendContactDataChangeNotif';

    vLang VARCHAR2(10 CHAR):='bs';
    vSubject VARCHAR2(100);
    vBodySMS  VARCHAR2(400);
    vBodyMAIL  VARCHAR2(400);
    vSMSBodySMS  VARCHAR2(400);
    vSMSBodyMAIL  VARCHAR2(400);
    vFromAddr VARCHAR2(100);
    channelId VARCHAR2(100);
    vEmail mcore.end_users.contact_email%TYPE;
    vGSM mcauth.client.gsm%TYPE;

    BEGIN
	
	vSubject := mlang.trans(vLang,'/Core/Auth/ChangeContactDataNotification/Subject');
    vBodySMS := mlang.trans(vLang,'/Core/Auth/ChangeContactDataNotification/BodySMS');
    vBodyMAIL  := mlang.trans(vLang,'/Core/Auth/ChangeContactDataNotification/BodyMail');
    vSMSBodySMS := mlang.trans(vLang,'/Core/Auth/ChangeContactDataNotification/SMS_BodySMS');
    vSMSBodyMAIL  := mlang.trans(vLang,'/Core/Auth/ChangeContactDataNotification/SMS_BodyMail');
    vFromAddr := sspkg.readvchar('/Core/Main/TranPays/Notifications/FromAddr');
    channelId := sspkg.readvchar('/Core/Main/TranPays/Notifications/MsgChannelId');
	
	--ako je dogadjaj izmjena GSM broja
      IF pEvent.OLD_GSM is not null AND pEvent.NEW_GSM is not null AND pEvent.OLD_GSM <> pEvent.NEW_GSM THEN
        
        BEGIN
          select contact_email
          into vEmail
          from mcore.end_users 
          where id = pEvent.CLIENT_ID;
        EXCEPTION
          WHEN no_data_found THEN
              slog.error(pkgCtxId, myUnit, 'Failed to send email because client does not have contact email:' || pEvent.CLIENT_ID );
        END;
         
        IF vEmail is not null THEN 
          mcore.send_mail(
                  fromAddr => vFromAddr,
                  toAddr => vEmail,
                  subject => vSubject,
                  bodyMsg => vBodySMS,
                  MC_ID => channelId,
                  sendAfter => NULL,
                  sendBefore => NULL
                          );
        END IF;
    
        mcauth.smsotp_plugin.sendMessage(contactInformation => pEvent.OLD_GSM, pMessage => vSMSBodySMS);
      END IF;
      	
	--ako je dogadjaj izmjena emaila
      IF pEvent.OLD_CONTACT_EMAIL is not null AND pEvent.OLD_CONTACT_EMAIL <> '<EMAIL>' AND pEvent.NEW_CONTACT_EMAIL is not null AND pEvent.OLD_CONTACT_EMAIL <> pEvent.NEW_CONTACT_EMAIL THEN

          mcore.send_mail(
                  fromAddr => vFromAddr,
                  toAddr => pEvent.OLD_CONTACT_EMAIL,
                  subject => vSubject,
                  bodyMsg => vBodyMAIL,
                  MC_ID => channelId,
                  sendAfter => NULL,
                  sendBefore => NULL
                          );
        BEGIN
          select gsm
          into vGSM
          from mcauth.client
          where id = pEvent.CLIENT_ID;
        EXCEPTION
          WHEN no_data_found THEN
              slog.error(pkgCtxId, myUnit, 'Failed to send SMS because client does not have GSM number:' || pEvent.CLIENT_ID );
        END;
          
          IF vGSM is not null THEN
            mcauth.smsotp_plugin.sendMessage(contactInformation => vGSM, pMessage => vSMSBodyMAIL);
          END IF;
          
      END IF;

END sendContactDataChangeNotif;
/
show error

Prompt
Prompt	grant EXECUTE on mcauth.sendContactDataChangeNotif to STRMADMIN
GRANT EXECUTE ON mcauth.sendContactDataChangeNotif TO STRMADMIN
/

