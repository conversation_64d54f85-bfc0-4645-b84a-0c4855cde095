PROMPT Creating Table mcore.invoice_provider_clients
CREATE TABLE mcore.invoice_provider_clients
 (id NUMBER(*,0) NOT NULL
 ,ipr_id NUMBER(*,0) NOT NULL
 ,end_users_id NUMBER NOT NULL
 ,user_reference VARCHAR2(400) NOT NULL
 ,type VARCHAR2(1) DEFAULT 'C' NOT NULL
 ,valid NUMBER(1,0) DEFAULT 1 NOT NULL
 ,date_subscribed DATE DEFAULT SYSDATE NOT NULL
 ,user_comment VARCHAR2(400)
 )
/

COMMENT ON TABLE mcore.invoice_provider_clients IS 'Associative entity linking invoice providers and Elba users (introduced with eRacuni - RedMine #12219)'
/

COMMENT ON COLUMN mcore.invoice_provider_clients.id IS 'Internal unique record identifier'
/

COMMENT ON COLUMN mcore.invoice_provider_clients.ipr_id IS 'Invoice provider ID - FK on INVOICE_PROVIDERS'
/

COMMENT ON COLUMN mcore.invoice_provider_clients.end_users_id IS 'Internal end user id used to identify end user subscribed to invoices - FK on END_USERS.ID'
/

COMMENT ON COLUMN mcore.invoice_provider_clients.type IS 'User type (A - Invoice provider admin, C - Client)'
/

COMMENT ON COLUMN mcore.invoice_provider_clients.user_comment IS 'User provided custom name for subscription'
/

COMMENT ON COLUMN mcore.invoice_provider_clients.date_subscribed IS 'Date when customer subscribed'
/

COMMENT ON COLUMN mcore.invoice_provider_clients.user_reference IS 'Invoice reference provided by user, used by provider to uniquely identify client'
/

PROMPT Creating Table mcore.invoice_provider_clients_jn
CREATE TABLE mcore.invoice_provider_clients_jn
 (jn_operation CHAR(3) NOT NULL
 ,jn_oracle_user VARCHAR2(30) NOT NULL
 ,jn_datetime DATE NOT NULL
 ,jn_notes VARCHAR2(240)
 ,jn_appln VARCHAR2(35)
 ,jn_session NUMBER(38)
 ,id NUMBER(*,0) NOT NULL
 ,ipr_id NUMBER(*,0)
 ,end_users_id NUMBER
 ,type VARCHAR2(1)
 ,user_comment VARCHAR2(400)
 ,valid NUMBER(1,0)
 ,date_subscribed DATE
 ,user_reference VARCHAR2(400) 
 )
/

Prompt COMMENT ON TABLE mcore.invoice_provider_clients_jn IS 'Created by Oracle Designer Server Generator'
COMMENT ON TABLE mcore.invoice_provider_clients_jn IS 'Created by Oracle Designer Server Generator'
/

PROMPT Creating Primary Key on mcore.invoice_provider_clients
ALTER TABLE mcore.invoice_provider_clients 
ADD (CONSTRAINT ipt_pk PRIMARY KEY (id))
/

PROMPT Creating Unique Key on 'INVOICE_PROVIDER_CLIENTS'
ALTER TABLE mcore.invoice_provider_clients 
ADD (CONSTRAINT ipt_uk UNIQUE (ipr_id, end_users_id, user_reference, type))
/

PROMPT Creating Check Constraint on mcore.invoice_provider_clients
ALTER TABLE mcore.invoice_provider_clients
ADD (CONSTRAINT AVCON_1453821572_TYPE_000 CHECK (type IN ('C', 'A')))
/

PROMPT Creating Check Constraint on 'INVOICE_PROVIDER_CLIENTS'
ALTER TABLE mcore.invoice_provider_clients
ADD (CONSTRAINT AVCON_1454536110_VALID_001 CHECK (valid IN (1, 0)))
/

PROMPT Creating Foreign Key on mcore.invoice_provider_clients
ALTER TABLE mcore.invoice_provider_clients 
ADD (CONSTRAINT ipt_ipr_fk FOREIGN KEY (ipr_id) REFERENCES mcore.invoice_providers (id))
/

PROMPT Creating Foreign Key on mcore.invoice_provider_clients
ALTER TABLE mcore.invoice_provider_clients 
ADD (CONSTRAINT ipt_eur_fk FOREIGN KEY (end_users_id) REFERENCES mcore.end_users (id))
/

PROMPT Creating Index mcore.ipt_eur_fk_idx
CREATE INDEX mcore.ipt_eur_fk_idx ON mcore.invoice_provider_clients (end_users_id)
/

PROMPT Creating Index mcore.ipt_ipr_fk_idx
CREATE INDEX mcore.ipt_ipr_fk_idx ON mcore.invoice_provider_clients (ipr_id)
/

/*
PROMPT Creating Index 'IPT_USER_REFERENCE_IDX'
CREATE INDEX mcore.ipt_user_reference_idx ON mcore.invoice_provider_clients (user_reference)
/
*/

PROMPT Creating Sequence 'INVOICE_PROVIDER_CLIENTS_SEQ'
CREATE SEQUENCE mcore.invoice_provider_clients_seq
/

PROMPT 
PROMPT Altering Table 'INVOICE_PROVIDER_CLIENTS_JN' 
ALTER TABLE mcore.invoice_provider_clients_jn 
 ADD (uat_id NUMBER(*,0)
 ,signature_id NUMBER
 ,signature CLOB
 )
/

PROMPT 
PROMPT Altering Table 'INVOICE_PROVIDER_CLIENTS' 
ALTER TABLE mcore.invoice_provider_clients 
 ADD (uat_id NUMBER(*,0)
 ,signature_id NUMBER
 ,signature CLOB
 )
/

PROMPT 
PROMPT COMMENT ON COLUMN mcore.invoice_provider_clients.id IS 'Internal unique record identifier (receiver ID)'
COMMENT ON COLUMN mcore.invoice_provider_clients.id IS 'Internal unique record identifier (receiver ID)'
/

PROMPT 
PROMPT COMMENT ON COLUMN mcore.invoice_provider_clients.uat_id
COMMENT ON COLUMN mcore.invoice_provider_clients.uat_id IS 'Actual accepted agreement text ID - Ref. on USER_AGREEMENT.ID. Updated every time user accepts service'
/

PROMPT 
PROMPT COMMENT ON COLUMN mcore.invoice_provider_clients.signature_id IS 'Signature ID - Ref. on SIGNATURES.ID'
COMMENT ON COLUMN mcore.invoice_provider_clients.signature_id IS 'Signature ID - Ref. on SIGNATURES.ID'
/

PROMPT 
PROMPT COMMENT ON COLUMN mcore.invoice_provider_clients.signature IS 'Agreement text signature'
COMMENT ON COLUMN mcore.invoice_provider_clients.signature IS 'Agreement text signature'
/

PROMPT 
PROMPT Creating Foreign Key on 'INVOICE_PROVIDER_CLIENTS'
ALTER TABLE mcore.invoice_provider_clients 
ADD (CONSTRAINT ipt_sie_fk FOREIGN KEY (signature_id) REFERENCES mcore.signatures (id))
/

Prompt
PROMPT Creating Foreign Key on 'INVOICE_PROVIDER_CLIENTS'
ALTER TABLE mcore.invoice_provider_clients 
ADD (CONSTRAINT ipt_uat_fk FOREIGN KEY (uat_id) REFERENCES mcore.user_agreement (id))
/

Prompt
PROMPT Creating Index 'IPT_UAT_FK_IDX'
CREATE INDEX mcore.ipt_uat_fk_idx ON mcore.invoice_provider_clients (uat_id)
/

Prompt
Prompt mcore.invoice_providers.sql
PROMPT Altering Table 'INVOICE_PROVIDER_CLIENTS_JN' 
ALTER TABLE mcore.invoice_provider_clients_jn  ADD (external_client_id VARCHAR2(255 CHAR)
 )
/

Prompt
Prompt mcore.invoice_providers.sql
PROMPT Altering Table 'INVOICE_PROVIDER_CLIENTS' 
ALTER TABLE mcore.invoice_provider_clients ADD (external_client_id VARCHAR2(255 CHAR)
 )
/

Prompt
Prompt mcore.invoice_providers.sql
PROMPT COMMENT ON COLUMN invoice_provider_clients.external_client_id
COMMENT ON COLUMN mcore.invoice_provider_clients.external_client_id IS 'Unique subscription identifier assigned by invoice provider'
/

PROMPT
PROMPT Altering Table 'MCORE.INVOICE_PROVIDER_CLIENTS_JN' 
ALTER TABLE MCORE.INVOICE_PROVIDER_CLIENTS_JN 
 ADD (ACC_OWNER_ID VARCHAR2(40)
 )
/

PROMPT
PROMPT Altering Table 'MCORE.INVOICE_PROVIDER_CLIENTS' 
ALTER TABLE MCORE.INVOICE_PROVIDER_CLIENTS 
 ADD (ACC_OWNER_ID VARCHAR2(40)
 )
/

PROMPT
PROMPT COMMENT ON COLUMN 'MCORE.INVOICE_PROVIDER_CLIENTS.VALID'
COMMENT ON COLUMN MCORE.INVOICE_PROVIDER_CLIENTS.VALID IS 'Indicate if subscription is valid'
/

PROMPT
PROMPT Altering Table 'INVOICE_PROVIDER_CLIENTS' - Add AUTO_PAYMENT_ENABLED column
ALTER TABLE INVOICE_PROVIDER_CLIENTS
ADD (AUTO_PAYMENT_ENABLED NUMBER(1) DEFAULT 0)
/

PROMPT
PROMPT Altering Table 'INVOICE_PROVIDER_CLIENTS_JN' - Add AUTO_PAYMENT_ENABLED column
ALTER TABLE INVOICE_PROVIDER_CLIENTS_JN
ADD (AUTO_PAYMENT_ENABLED NUMBER(1))
/

PROMPT
PROMPT Altering Table 'INVOICE_PROVIDER_CLIENTS' - Add PAYMENT_ACCOUNT_ID column
ALTER TABLE INVOICE_PROVIDER_CLIENTS
ADD (PAYMENT_ACCOUNT_ID VARCHAR2(40))
/

PROMPT
PROMPT Altering Table 'INVOICE_PROVIDER_CLIENTS_JN' - Add PAYMENT_ACCOUNT_ID column
ALTER TABLE INVOICE_PROVIDER_CLIENTS_JN
ADD (PAYMENT_ACCOUNT_ID VARCHAR2(40))
/

PROMPT
PROMPT COMMENT ON COLUMN 'MCORE.INVOICE_PROVIDER_CLIENTS.AUTO_PAYMENT_ENABLED'
COMMENT ON COLUMN MCORE.INVOICE_PROVIDER_CLIENTS.AUTO_PAYMENT_ENABLED IS 'Indicates if automatic payment is enabled for this subscription (0=disabled, 1=enabled)'
/

PROMPT
PROMPT COMMENT ON COLUMN 'MCORE.INVOICE_PROVIDER_CLIENTS.PAYMENT_ACCOUNT_ID'
COMMENT ON COLUMN MCORE.INVOICE_PROVIDER_CLIENTS.PAYMENT_ACCOUNT_ID IS 'Account ID from which automatic payments will be made'
/

PROMPT
PROMPT Creating Check Constraint on 'INVOICE_PROVIDER_CLIENTS.AUTO_PAYMENT_ENABLED'
ALTER TABLE INVOICE_PROVIDER_CLIENTS
ADD (CONSTRAINT AVCON_AUTO_PAYMENT_ENABLED CHECK (AUTO_PAYMENT_ENABLED IN (1, 0)))
/

PROMPT
PROMPT Altering Table 'INVOICE_PROVIDER_CLIENTS_JN' 
ALTER TABLE INVOICE_PROVIDER_CLIENTS_JN 
 ADD (DATE_SBSCRPTREQ_FORWARDED DATE
 ,DATE_UNSBSCRPTREQ_FORWARDED DATE
 )
/

PROMPT
PROMPT Altering Table 'INVOICE_PROVIDER_CLIENTS' 
ALTER TABLE INVOICE_PROVIDER_CLIENTS 
 ADD (DATE_SBSCRPTREQ_FORWARDED DATE
 ,DATE_UNSBSCRPTREQ_FORWARDED DATE
 )
/