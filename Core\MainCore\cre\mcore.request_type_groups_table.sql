PROMPT DROP TABLE MCORE.REQUEST_TYPE_GROUPS...
DROP TABLE MCORE.REQUEST_TYPE_GROUPS
/
PROMPT CREATING TABLE MCORE.REQUEST_TYPE_GROUPS...
CREATE TABLE MCORE.REQUEST_TYPE_GROUPS
(
ID VARCHAR2(100) NOT NULL PRIMARY KEY,
NAME CLOB NOT NULL,
DESCRIPTION CLOB,
IMAGE VARCHAR2(4000),
SORT_ORDER NUMBER(38),
PARENT_REQ_TYPE_GROUP_ID VARCHAR2(100 CHAR),
VALID NUMBER(38) NOT NULL,
APPLICATION_ID VARCHAR2(40),
AO_TYPE VARCHAR2(40 CHAR),
BASIC_TYPE VARCHAR2(10 CHAR),
VISIBLE NUMBER(38),
CONSTRAINT REQ_TYPE_GROUP_PK PRIMARY <PERSON> (ID),
CONSTRAINT REQ_TYP_GRP_CK_BASIC_TYP CHECK (BASIC_TYPE IN ('ORD' , 'REQ' , 'TRAN', 'WUT', 'INSURANCE')), 
CHECK (VALID IN ('0' , '1' ))
)
/
PROMPT GRANT SELECT, INSERT, UPDATE, DELETE ON REQUEST_TYPE_GROUPS TO MCADMIN_USER
GRANT SELECT, INSERT, UPDATE, DELETE ON REQUEST_TYPE_GROUPS TO MCADMIN_USER
/
show error


PROMPT alter table mcore.REQUEST_TYPE_GROUPS ADDING COLUMN REQ_TYPE_GROUP_STRUCT...
ALTER TABLE MCORE.REQUEST_TYPE_GROUPS ADD REQ_TYPE_GROUP_STRUCT CLOB
/

PROMPT ALTER TABLE mcore.request_type_groups DROP CONSTRAINT
ALTER TABLE mcore.request_type_groups
	DROP CONSTRAINT req_typ_grp_ck_basic_typ
/
