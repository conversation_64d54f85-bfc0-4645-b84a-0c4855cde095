--------------------------------------------------------
--  DDL for Table REQUEST_TYPES
--------------------------------------------------------

  CREATE TABLE MCORE.REQUEST_TYPES 
   (	ID VARCHAR2(40 CHAR), 
	DESCRIPTION VARCHAR2(4000 CHAR), 
	BASIC_TYPE VARCHAR2(29 CHAR), 
	VALID NUMBER(*,0), 
	DOC_PATH VARCHAR2(2000 CHAR), 
	AO_TYPE VARCHAR2(40 CHAR), 
	ORG_ID VARCHAR2(40 CHAR)
   ) TABLESPACE USERS
   /
 
   COMMENT ON COLUMN MCORE.REQUEST_TYPES.DESCRIPTION IS 'Description';
 
   COMMENT ON COLUMN MCORE.REQUEST_TYPES.BASIC_TYPE IS 'Is this TRANPAY order or request?';
 
   COMMENT ON COLUMN MCORE.REQUEST_TYPES.VALID IS 'This type can be used for typing transactions, payments and orders';
 
   COMMENT ON COLUMN MCORE.REQUEST_TYPES.DOC_PATH IS 'Path to the document on file system';
 
   COMMENT ON COLUMN MCORE.REQUEST_TYPES.ORG_ID IS 'Bank org. id for which admin has to have admin privileges';
 
   COMMENT ON TABLE MCORE.REQUEST_TYPES  IS 'Payment, Transfer and REQUEST types';

   --------------------------------------------------------
--  DDL for Index IDX$$_2C800003
--------------------------------------------------------

  CREATE INDEX MCORE.IDX$$_2C800003 ON MCORE.REQUEST_TYPES (BASIC_TYPE, ID) 
  TABLESPACE USERS ;
--------------------------------------------------------
--  DDL for Index REQUEST_TYPE_PK
--------------------------------------------------------

  CREATE UNIQUE INDEX MCORE.REQUEST_TYPE_PK ON MCORE.REQUEST_TYPES (ID) 
  TABLESPACE USERS ;
--------------------------------------------------------
--  Constraints for Table REQUEST_TYPES
--------------------------------------------------------

  ALTER TABLE MCORE.REQUEST_TYPES ADD CONSTRAINT REQUEST_TYPE_PK PRIMARY KEY (ID)
  USING INDEX TABLESPACE USERS  ENABLE;
 
  ALTER TABLE MCORE.REQUEST_TYPES MODIFY (ID NOT NULL ENABLE);
 
  ALTER TABLE MCORE.REQUEST_TYPES MODIFY (DESCRIPTION NOT NULL ENABLE);
 
  ALTER TABLE MCORE.REQUEST_TYPES MODIFY (BASIC_TYPE NOT NULL ENABLE);
 
  ALTER TABLE MCORE.REQUEST_TYPES MODIFY (VALID NOT NULL ENABLE);
 
  ALTER TABLE MCORE.REQUEST_TYPES ADD CHECK ( VALID IN ('0' , '1' )) ENABLE;
 
  ALTER TABLE MCORE.REQUEST_TYPES ADD CHECK ( BASIC_TYPE IN ('ORD' , 'REQ' , 'TRAN', 'WUT' )) ENABLE;

  ALTER TABLE MCORE.REQUEST_TYPES ADD CONSTRAINT REQ_TYP_BAO_FK FOREIGN KEY (ORG_ID)
  REFERENCES MCORE.BANK_ORG_UNITS (ID) ENABLE
  /

  PROMPT Altering Table 'REQUEST_TYPES' 
  ALTER TABLE mcore.request_types ADD (parent_req_type_id VARCHAR2(40))
  /
    
  PROMPT Creating Foreign Key on 'REQUEST_TYPES'
  ALTER TABLE mcore.request_types ADD (CONSTRAINT rte_rte_fk FOREIGN KEY (parent_req_type_id) REFERENCES mcore.request_types (ID))
  /
	
  PROMPT Creating Index 'RTE_RTE_FK_IDX'
  CREATE INDEX mcore.rte_rte_fk_idx ON mcore.request_types (parent_req_type_id)
  /
	
  COMMENT ON COLUMN mcore.request_types.parent_req_type_id IS 'Ref. on parent RequestTypeId'
  /

  PROMPT Altering Table 'REQUEST_TYPES' 
  ALTER TABLE MCORE.REQUEST_TYPES 
  ADD (VISIBLE NUMBER DEFAULT 1 NOT NULL)
  /
  PROMPT ALTER TABLE MCORE.REQUEST_TYPES...
  ALTER TABLE MCORE.REQUEST_TYPES
  ADD (REQUEST_TYPE_GROUP_ID VARCHAR2(100 CHAR),
	  SORT_ORDER NUMBER(38),
	  CONSTRAINT REQ_TYPE_GROUP_FK FOREIGN KEY(REQUEST_TYPE_GROUP_ID) REFERENCES MCORE.REQUEST_TYPE_GROUPS(ID))
  /
 
COMMENT ON COLUMN mcore.request_types.visible IS 'Request type visibility on application'
  /
  
 PROMPT alter table mcore.request_types add column req_report_struct...
ALTER TABLE mcore.request_types add req_report_struct CLOB
/

PROMPT ALTER TABLE mcore.request_types ADD NEW COLUMN
ALTER TABLE mcore.request_types 
	ADD is_public NUMBER DEFAULT 0 NOT NULL CONSTRAINT rt_is_public CHECK (is_public IN (0,1))
/

PROMPT ALTER TABLE mcore.request_types DROP CONSTRAINT
ALTER TABLE mcore.request_types 
	DROP CONSTRAINT req_typ_ck_basic_typ
/