PROMPT CREATING TABLE MCORE.ATTACHMENTS
CREATE TABLE MCORE.ATTACHMENTS (	
	ID NUMBER NOT NULL ENABLE, 
	DESCRIPTION VARCHAR2(1000 CHAR), 
	ERR_REPORT_ID NUMBER, 
	TRANPAY_ID NUMBER, 
	CONTENT BFILE NOT NULL ENABLE, 
	MIME_TYPE VARCHAR2(400 CHAR), 
	FILENAME VARCHAR2(400 CHAR), 
	USER_ID NUMBER, 
	DATE_CREATED DATE DEFAULT SYSDATE NOT NULL ENABLE, 
	DELIVERED NUMBER(1,0) DEFAULT 0 NOT NULL ENABLE, 
	EXT_ID NUMBER NOT NULL ENABLE, 
	SERVICE VARCHAR2(100 BYTE) NOT NULL ENABLE, 
	CONSTRAINT ATTACHMENT_PK PRIMARY KEY (ID),
	CONSTRAINT ATT_USER_FK FOREIGN KEY (USER_ID)
		REFERENCES MCORE.END_USERS (ID) ENABLE
);

PROMPT COMMENT ON TABLE MCORE.ATTACHMENTS
COMMENT ON COLUMN MCORE.ATTACHMENTS.CONTENT IS 'Content of attachment';
COMMENT ON COLUMN MCORE.ATTACHMENTS.MIME_TYPE IS 'Mime type of content';
COMMENT ON COLUMN MCORE.ATTACHMENTS.FILENAME IS 'Name of attached file';
COMMENT ON TABLE MCORE.ATTACHMENTS  IS 'Attachments for error report';
   
PROMPT CREATING INDEX MCORE.ATTACHMENT_TRANPAY_FKX  
CREATE INDEX MCORE.ATTACHMENT_TRANPAY_FKX ON MCORE.ATTACHMENTS (TRANPAY_ID) ;

PROMPT CREATING INDEX MCORE.ATTACHMENT_USER_FKX 
CREATE INDEX MCORE.ATTACHMENT_USER_FKX ON MCORE.ATTACHMENTS (USER_ID) ;

PROMPT GRANT ON MCORE.ATTACHMENTS  
GRANT ALL ON MCORE.ATTACHMENTS TO MCADMIN WITH GRANT OPTION;
GRANT SELECT, INSERT, DELETE ON MCORE.ATTACHMENTS TO MFLEX;
GRANT SELECT, INSERT, DELETE ON MCORE.ATTACHMENTS TO MMOBILE;
GRANT SELECT ON MCORE.ATTACHMENTS TO MC_BASIC;
GRANT SELECT ON MCORE.ATTACHMENTS TO MC_SUPER;
GRANT SELECT ON MCORE.ATTACHMENTS TO MC_REQUESTS;
GRANT SELECT ON MCORE.ATTACHMENTS TO MC_HELPDESK;
GRANT SELECT ON MCORE.ATTACHMENTS TO MC_USERMANAGER;
GRANT SELECT ON MCORE.ATTACHMENTS TO UNITTEST;
GRANT SELECT ON MCORE.ATTACHMENTS TO MCADMIN_USER;
GRANT SELECT ON MCORE.ATTACHMENTS TO MC_NEWS;
