# Implementacija Automatskog Plaćanja eRačuna

## Pregled

Ova implementacija omogućava automatsko plaćanje eRačuna kada se oni učitaju u sistem, pod uslovom da je korisnik aktivirao ovu funkcionalnost i podesio odgovarajući račun za plaćanje.

## Bazne Izmjene

### 1. <PERSON>šire<PERSON><PERSON> `mcore.invoice_provider_clients`

Dodane su sljedeće kolone:

- `AUTO_PAYMENT_ENABLED` (NUMBER(1) DEFAULT 0) - označava da li je automatsko plaćanje omogućeno (0=onemogućeno, 1=omogućeno)
- `PAYMENT_ACCOUNT_ID` (VARCHAR2(40)) - ID računa sa kojeg će biti izvršeno automatsko plaćanje

### 2. Ažuriranje CG Paketa

Ažurirani su sljedeći CG paketi da uključe nove kolone:
- `mcore.cg_invoice_provider_clients.sql`
- `mcore.cg_invoice_provider_clients_body.sql`

## Proceduralne Izmjene

### 1. Overloaded `RegisterConsumer` Procedure

Kreiran je **overloaded** (preopterećen) set procedura `RegisterConsumer` koji ne mijenja postojeće pozive:

**Postojeća verzija** (ostaje nepromijenjena):
```sql
FUNCTION RegisterConsumer(pInvoiceProviderId, pEndUserId, pType, pUserReference,
                         pUserComment, pValid, pUserAgreementId, pResponse,
                         pOtp, pSourceData, pSignature, pInvoiceTemplate)
```

**Nova overloaded verzija** sa parametrima za automatsko plaćanje:
```sql
FUNCTION RegisterConsumer(pInvoiceProviderId, pEndUserId, pType, pUserReference,
                         pUserComment, pValid, pUserAgreementId, pResponse,
                         pOtp, pSourceData, pSignature, pInvoiceTemplate,
                         pAutoPaymentEnabled, pPaymentAccountId)
```

### 2. Overloaded `UpdateConsumer` Procedure

Kreiran je **overloaded** set procedura `UpdateConsumer`:

**Postojeća verzija** (ostaje nepromijenjena):
```sql
PROCEDURE UpdateConsumer(pIpcId, pUserReference, pUserComment, pInvoiceTemplate)
```

**Nova overloaded verzija** sa parametrima za automatsko plaćanje:
```sql
PROCEDURE UpdateConsumer(pIpcId, pUserReference, pUserComment, pInvoiceTemplate,
                        pAutoPaymentEnabled, pPaymentAccountId)
```

**Prednosti ovog pristupa:**
- Postojeći kod nastavlja da radi bez izmjena
- Novi kod može koristiti proširene funkcionalnosti
- Nema breaking changes u postojećim aplikacijama

### 3. Nova `ProcessAutoPayment` Procedura

Kreirana je nova procedura `mcore.invoice_mgmt_pck.ProcessAutoPayment` koja:
- Provjerava da li je automatsko plaćanje globalno omogućeno
- Provjerava da li je automatsko plaćanje omogućeno za konkretnu pretplatu
- Kreira tranpay za automatsko plaćanje
- Obrađuje račun sa kreiranim tranpay-om
- Šalje odgovarajuće notifikacije

### 4. Nova `SendAutoPaymentNotification` Procedura

Kreirana je procedura za slanje email notifikacija o:
- Uspješnom automatskom plaćanju
- Neuspješnom automatskom plaćanju sa razlogom greške

### 5. Nova `ProcessBatchAutoPayments` Procedura

Procedura za obradu automatskih plaćanja na nivou batch-a, koja se poziva nakon učitavanja svih računa u batch-u.

## Job za Automatsko Plaćanje

### 1. Novi Paket `mcore.auto_payment_job_pck`

Kreiran je novi paket koji sadrži:
- `ProcessPendingAutoPayments` - obrađuje račune koji čekaju automatsko plaćanje
- `ScheduleAutoPaymentJob` - kreira job koji se pokreće svakih 5 minuta
- `RemoveAutoPaymentJob` - uklanja job

### 2. Scheduler Job

Job `AUTO_PAYMENT_JOB` se pokreće svakih 5 minuta i obrađuje račune koji:
- Imaju status 0 (novi)
- Imaju omogućeno automatsko plaćanje
- Imaju podešen račun za plaćanje
- Kreirani su u poslednja 24 sata
- Nemaju već kreiran tranpay za automatsko plaćanje

## Konfiguracija

### Globalne Postavke

Automatsko plaćanje se kontroliše sljedećim postavkama u sspkg:

- `/Core/Main/InvoiceManagement/AutoPayment/enabled` (BOOLEAN) - globalno omogućava/onemogućava automatsko plaćanje
- `/Core/Main/InvoiceManagement/AutoPayment/fromAddress` (VARCHAR2) - email adresa pošaljitelja notifikacija

### Postavke po Korisniku

Svaki korisnik može podesiti automatsko plaćanje kroz:
- `AUTO_PAYMENT_ENABLED` kolonu u `invoice_provider_clients` tabeli
- `PAYMENT_ACCOUNT_ID` kolonu za specifikaciju računa za plaćanje

## Tok Izvršavanja

1. **Učitavanje Računa**: Kada se račun učita kroz `mcore.cg_invoices_body.sql`
2. **Poziv Auto Payment**: Poziva se `ProcessAutoPayment` procedura
3. **Provjera Uslova**: Provjeravaju se svi uslovi za automatsko plaćanje
4. **Kreiranje Tranpay**: Kreira se tranpay za automatsko plaćanje
5. **Obrada Računa**: Poziva se `processInvoice` procedura
6. **Notifikacija**: Šalje se email notifikacija o rezultatu

## Hendliranje Grešaka

- Sve greške se loguju kroz slog sistem
- Greške u automatskom plaćanju ne prekidaju normalan tok učitavanja računa
- Korisnici dobijaju notifikacije o neuspješnim automatskim plaćanjima
- Računi ostaju dostupni za ručno plaćanje u slučaju greške

## Testiranje

Kreiran je test script `test_auto_payment.sql` koji testira:
- Registraciju korisnika sa automatskim plaćanjem
- Ažuriranje postavki automatskog plaćanja
- Obradu automatskog plaćanja
- Kreiranje i uklanjanje job-a

## Sigurnost

- Automatsko plaćanje se izvršava samo za račune sa statusom 0 (novi)
- Provjeravaju se svi uslovi prije kreiranja tranpay-a
- Greške se loguju ali ne prekidaju normalan tok
- Korisnici dobijaju notifikacije o svim automatskim plaćanjima

## Održavanje

- Job se može zaustaviti/pokrenuti kroz `auto_payment_job_pck` paket
- Logovi se čuvaju u slog sistemu
- Postavke se mogu mijenjati kroz sspkg konfiguraciju
- Statistike se mogu pratiti kroz tranpays tabelu
