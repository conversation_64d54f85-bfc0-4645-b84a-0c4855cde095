PROMPT Creating package specification script for package ATTACHMENTS_PCK
CREATE OR REPLACE
PACKAGE MCORE.ATTACHMENTS_PCK
  IS
    pkgCtxId constant varchar2(100) := '/Core/Main/Attachments';

    PROCEDURE addAttachment(pExtId mcore.attachments.ext_id%TYPE,
                            pService mcore.attachments.service%TYPE,
                            pDescription mcore.attachments.description%TYPE,
                            pContent BLOB,
                            pMimeType mcore.attachments.mime_type%TYPE,
                            pFileName mcore.attachments.filename%TYPE);
END;
/
show error

Prompt GRANT EXECUTE ON MCORE.ATTACHMENTS_PCK TO MFLEX
GRANT EXECUTE ON MCORE.ATTACHMENTS_PCK TO MFLEX
/

Prompt GRANT EXECUTE ON MCORE.ATTACHMENTS_PCK TO MMOBILE
GRANT EXECUTE ON MCORE.ATTACHMENTS_PCK TO MMOBILE
/
