PROMPT Creating package body script for package ATTACHMENTS_PCK
CREATE OR REPLACE
PACKAGE BODY MCORE.ATTACHMENTS_PCK
IS

    PROCEDURE addAttachment(pExtId mcore.attachments.ext_id%TYPE,
                            pService mcore.attachments.service%TYPE,
                            pDescription mcore.attachments.description%TYPE,
                            pContent BLOB,
                            pMimeType mcore.attachments.mime_type%TYPE,
                            pFileName mcore.attachments.filename%TYPE)
    IS
        myunit CONSTANT VARCHAR2(13) := 'addAttachment';

		vPom PLS_INTEGER;
		vFileName VARCHAR2(1024);
		vFileHandle BFILE;
		vId mcore.attachments.id%TYPE;
    BEGIN
        slog.debug(pkgCtxId, myUnit, 'Execute ' || myunit);

		IF dbms_lob.getlength(pContent) = 0 THEN
			sspkg.raiseError(mcore.common_pck.cERR_FilesizeZero, null, pkgCtxId, myunit);
		END IF;

		vId := mcore.ATTACHMENTS_ID_SEQ.nextval;

		vPom := instr(pFileName, '.', -1);
		IF vPom > 0 THEN
			vFileName := vId || substr(pFileName, vPom);
		ELSE
			vFileName := vId;
		END IF;

		vFileHandle := util.WriteBLOBToFILE (pDestDirectory => mcore.common_pck.cDIR_ATTACHMENTS, pFileName => vFileName, sourceData => pContent);

		INSERT INTO mcore.attachments(id, ext_id, service, description, content, filename, mime_Type, user_id)
		VALUES (vId, pExtId, pService, pDescription, vFileHandle, pFileName, pMimeType, mcauth.auth.getClientId);

    END addAttachment;

END;
/
show error