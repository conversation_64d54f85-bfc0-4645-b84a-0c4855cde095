CREATE OR REPLACE PACKAGE BODY mcore.auto_payment_job_pck
AS
	pkgCtxId CONSTANT VARCHAR2(100) := '/Core/Main/InvoiceManagement/AutoPayment';
	
	PROCEDURE ProcessPendingAutoPayments
	IS
		myunit CONSTANT VARCHAR2(30) := 'ProcessPendingAutoPayments';
		
		CURSOR cPendingInvoices IS
			SELECT i.id
			FROM invoices i
			JOIN invoice_provider_clients ipc ON i.client_id = ipc.id
			WHERE i.status = 0 -- Only new invoices
			  AND ipc.auto_payment_enabled = 1
			  AND ipc.payment_account_id IS NOT NULL
			  AND i.date_created >= SYSDATE - 1 -- Only invoices from last 24 hours
			  AND NOT EXISTS (
			      SELECT 1 FROM tranpays t 
			      WHERE t.invoice_id = i.id 
			      AND t.reference LIKE 'AUTO_PAY_%'
			  ); -- Only invoices that don't already have auto payment
			  
		vProcessedCount PLS_INTEGER := 0;
		vErrorCount PLS_INTEGER := 0;
		
	BEGIN
		slog.debug(pkgCtxId, myunit, 'Starting processing of pending auto payments');
		
		-- Check if auto payment is globally enabled
		IF NOT sspkg.readBool('/Core/Main/InvoiceManagement/AutoPayment/enabled') THEN
			slog.debug(pkgCtxId, myunit, 'Auto payment globally disabled');
			RETURN;
		END IF;
		
		-- Process each pending invoice
		FOR rec IN cPendingInvoices LOOP
			BEGIN
				mcore.invoice_mgmt_pck.ProcessAutoPayment(pInvoiceId => rec.id);
				vProcessedCount := vProcessedCount + 1;
				
			EXCEPTION
				WHEN OTHERS THEN
					vErrorCount := vErrorCount + 1;
					slog.error(pkgCtxId, myunit, 'Error processing auto payment for invoice: ' || rec.id || ', Error: ' || SQLERRM);
			END;
		END LOOP;
		
		slog.debug(pkgCtxId, myunit, 'Pending auto payment processing completed. Processed: ' || vProcessedCount || ', Errors: ' || vErrorCount);
		
	EXCEPTION
		WHEN OTHERS THEN
			slog.error(pkgCtxId, myunit, 'Unexpected error in pending auto payment processing: ' || SQLERRM);
	END ProcessPendingAutoPayments;
	
	PROCEDURE ScheduleAutoPaymentJob
	IS
		myunit CONSTANT VARCHAR2(30) := 'ScheduleAutoPaymentJob';
		vJobName VARCHAR2(100) := 'AUTO_PAYMENT_JOB';
		vJobExists PLS_INTEGER := 0;
		
	BEGIN
		slog.debug(pkgCtxId, myunit, 'Scheduling auto payment job');
		
		-- Check if job already exists
		SELECT COUNT(*)
		INTO vJobExists
		FROM user_scheduler_jobs
		WHERE job_name = vJobName;
		
		IF vJobExists > 0 THEN
			-- Drop existing job
			DBMS_SCHEDULER.DROP_JOB(job_name => vJobName, force => TRUE);
			slog.debug(pkgCtxId, myunit, 'Dropped existing auto payment job');
		END IF;
		
		-- Create new job
		DBMS_SCHEDULER.CREATE_JOB(
			job_name => vJobName,
			job_type => 'PLSQL_BLOCK',
			job_action => 'BEGIN mcore.auto_payment_job_pck.ProcessPendingAutoPayments; END;',
			start_date => SYSTIMESTAMP,
			repeat_interval => 'FREQ=MINUTELY; INTERVAL=5', -- Run every 5 minutes
			enabled => TRUE,
			comments => 'Job for processing automatic payments of eInvoices'
		);
		
		slog.debug(pkgCtxId, myunit, 'Auto payment job scheduled successfully');
		
	EXCEPTION
		WHEN OTHERS THEN
			slog.error(pkgCtxId, myunit, 'Error scheduling auto payment job: ' || SQLERRM);
			RAISE;
	END ScheduleAutoPaymentJob;
	
	PROCEDURE RemoveAutoPaymentJob
	IS
		myunit CONSTANT VARCHAR2(30) := 'RemoveAutoPaymentJob';
		vJobName VARCHAR2(100) := 'AUTO_PAYMENT_JOB';
		vJobExists PLS_INTEGER := 0;
		
	BEGIN
		slog.debug(pkgCtxId, myunit, 'Removing auto payment job');
		
		-- Check if job exists
		SELECT COUNT(*)
		INTO vJobExists
		FROM user_scheduler_jobs
		WHERE job_name = vJobName;
		
		IF vJobExists > 0 THEN
			-- Drop job
			DBMS_SCHEDULER.DROP_JOB(job_name => vJobName, force => TRUE);
			slog.debug(pkgCtxId, myunit, 'Auto payment job removed successfully');
		ELSE
			slog.debug(pkgCtxId, myunit, 'Auto payment job does not exist');
		END IF;
		
	EXCEPTION
		WHEN OTHERS THEN
			slog.error(pkgCtxId, myunit, 'Error removing auto payment job: ' || SQLERRM);
			RAISE;
	END RemoveAutoPaymentJob;
	
END auto_payment_job_pck;
/
