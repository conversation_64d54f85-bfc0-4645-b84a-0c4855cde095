
PROMPT Creating API Package Specification for Table 'INVOICE_PROVIDER_CLIENTS'
--------------------------------------------------------------------------------
-- Name:        cg$INVOICE_PROVIDER_CLIENTS
-- Description: INVOICE_PROVIDER_CLIENTS table API package declarations
--------------------------------------------------------------------------------
CREATE OR REPLACE PACKAGE MCORE.CG$INVOICE_PROVIDER_CLIENTS IS

called_from_package BOOLEAN := FALSE;

--  Repository User-Defined Error Messages
IPT_PK CONSTANT VARCHAR2(240) := '';
IPT_UK CONSTANT VARCHAR2(240) := '';
IPT_IPR_FK CONSTANT VARCHAR2(240) := '';
IPT_UAT_FK CONSTANT VARCHAR2(240) := '';
IPT_SIE_FK CONSTANT VARCHAR2(240) := '';
IPT_EUR_FK CONSTANT VARCHAR2(240) := '';

--  Column default prompts. Format PSEQNO_COL
P20ID CONSTANT VARCHAR2(240) := 'Id';
P30IPR_ID CONSTANT VARCHAR2(240) := 'Ipr Id';
P70END_USERS_ID CONSTANT VARCHAR2(240) := 'End Users Id';
P80USER_REFERENCE CONSTANT VARCHAR2(240) := 'User Reference';
P90TYPE CONSTANT VARCHAR2(240) := 'Type';
P120VALID CONSTANT VARCHAR2(240) := 'Valid';
P130DATE_SUBSCRIBED CONSTANT VARCHAR2(240) := 'Date subscribed';
P140USER_COMMENT CONSTANT VARCHAR2(240) := 'User Comment';
P150UAT_ID CONSTANT VARCHAR2(240) := 'Iat Id';
P160SIGNATURE_ID CONSTANT VARCHAR2(240) := 'Signature Id';
P170SIGNATURE CONSTANT VARCHAR2(240) := 'Signature';
P180EXTERNAL_CLIENT_ID CONSTANT VARCHAR2(240) := 'External Client Id';
P190ACC_OWNER_ID CONSTANT VARCHAR2(240) := 'Acc Owner Id';
P200DATE_SBSCRPTREQ_FORWARDED CONSTANT VARCHAR2(240) := 'Date Sbscrptreq Forwarded';
P210DATE_UNSBSCRPTREQ_FORWARDE CONSTANT VARCHAR2(240) := 'Date Unsbscrptreq Forwarded';
P220INVOICE_TEMPLATE CONSTANT VARCHAR2(240) := 'Invoice template';

cg$row INVOICE_PROVIDER_CLIENTS%ROWTYPE;

--  INVOICE_PROVIDER_CLIENTS row type variable 
TYPE cg$row_type IS RECORD
(ID cg$row.ID%TYPE
,IPR_ID cg$row.IPR_ID%TYPE
,END_USERS_ID cg$row.END_USERS_ID%TYPE
,USER_REFERENCE cg$row.USER_REFERENCE%TYPE
,TYPE cg$row.TYPE%TYPE
,VALID cg$row.VALID%TYPE
,DATE_SUBSCRIBED cg$row.DATE_SUBSCRIBED%TYPE
,USER_COMMENT cg$row.USER_COMMENT%TYPE
,UAT_ID cg$row.UAT_ID%TYPE
,SIGNATURE_ID cg$row.SIGNATURE_ID%TYPE
,SIGNATURE cg$row.SIGNATURE%TYPE
,EXTERNAL_CLIENT_ID cg$row.EXTERNAL_CLIENT_ID%TYPE
,ACC_OWNER_ID cg$row.ACC_OWNER_ID%TYPE
,DATE_SBSCRPTREQ_FORWARDED cg$row.DATE_SBSCRPTREQ_FORWARDED%TYPE
,DATE_UNSBSCRPTREQ_FORWARDED cg$row.DATE_UNSBSCRPTREQ_FORWARDED%TYPE
,INVOICE_TEMPLATE cg$row.INVOICE_TEMPLATE%TYPE
,AUTO_PAYMENT_ENABLED cg$row.AUTO_PAYMENT_ENABLED%TYPE
,PAYMENT_ACCOUNT_ID cg$row.PAYMENT_ACCOUNT_ID%TYPE
,the_rowid ROWID
,JN_NOTES VARCHAR2(240))
;

--  INVOICE_PROVIDER_CLIENTS indicator type variable
TYPE cg$ind_type IS RECORD
(ID BOOLEAN DEFAULT FALSE
,IPR_ID BOOLEAN DEFAULT FALSE
,END_USERS_ID BOOLEAN DEFAULT FALSE
,USER_REFERENCE BOOLEAN DEFAULT FALSE
,TYPE BOOLEAN DEFAULT FALSE
,VALID BOOLEAN DEFAULT FALSE
,DATE_SUBSCRIBED BOOLEAN DEFAULT FALSE
,USER_COMMENT BOOLEAN DEFAULT FALSE
,UAT_ID BOOLEAN DEFAULT FALSE
,SIGNATURE_ID BOOLEAN DEFAULT FALSE
,SIGNATURE BOOLEAN DEFAULT FALSE
,EXTERNAL_CLIENT_ID BOOLEAN DEFAULT FALSE
,ACC_OWNER_ID BOOLEAN DEFAULT FALSE
,DATE_SBSCRPTREQ_FORWARDED BOOLEAN DEFAULT FALSE
,DATE_UNSBSCRPTREQ_FORWARDED BOOLEAN DEFAULT FALSE
,INVOICE_TEMPLATE BOOLEAN DEFAULT FALSE
,AUTO_PAYMENT_ENABLED BOOLEAN DEFAULT FALSE
,PAYMENT_ACCOUNT_ID BOOLEAN DEFAULT FALSE);

cg$ind_true cg$ind_type;

--  INVOICE_PROVIDER_CLIENTS primary key type variable
TYPE cg$pk_type IS RECORD
(ID cg$row.ID%TYPE
,the_rowid ROWID
,JN_NOTES VARCHAR2(240))
;

--  PL/SQL Table Type variable for triggers              

TYPE cg$tab_row_type IS RECORD 
(ID INVOICE_PROVIDER_CLIENTS.ID%TYPE
,IPR_ID INVOICE_PROVIDER_CLIENTS.IPR_ID%TYPE
,END_USERS_ID INVOICE_PROVIDER_CLIENTS.END_USERS_ID%TYPE
,USER_REFERENCE INVOICE_PROVIDER_CLIENTS.USER_REFERENCE%TYPE
,TYPE INVOICE_PROVIDER_CLIENTS.TYPE%TYPE
,VALID INVOICE_PROVIDER_CLIENTS.VALID%TYPE
,DATE_SUBSCRIBED INVOICE_PROVIDER_CLIENTS.DATE_SUBSCRIBED%TYPE
,USER_COMMENT INVOICE_PROVIDER_CLIENTS.USER_COMMENT%TYPE
,UAT_ID INVOICE_PROVIDER_CLIENTS.UAT_ID%TYPE
,SIGNATURE_ID INVOICE_PROVIDER_CLIENTS.SIGNATURE_ID%TYPE
,SIGNATURE INVOICE_PROVIDER_CLIENTS.SIGNATURE%TYPE
,EXTERNAL_CLIENT_ID INVOICE_PROVIDER_CLIENTS.EXTERNAL_CLIENT_ID%TYPE
,ACC_OWNER_ID INVOICE_PROVIDER_CLIENTS.ACC_OWNER_ID%TYPE
,DATE_SBSCRPTREQ_FORWARDED INVOICE_PROVIDER_CLIENTS.DATE_SBSCRPTREQ_FORWARDED%TYPE
,DATE_UNSBSCRPTREQ_FORWARDED INVOICE_PROVIDER_CLIENTS.DATE_UNSBSCRPTREQ_FORWARDED%TYPE
,INVOICE_TEMPLATE INVOICE_PROVIDER_CLIENTS.INVOICE_TEMPLATE%TYPE
,action_performed VARCHAR2(4)
) ;


TYPE cg$table_type IS TABLE OF cg$tab_row_type
INDEX BY BINARY_INTEGER;

cg$table cg$table_type;

TYPE cg$tableind_type IS TABLE OF cg$ind_type
     INDEX BY BINARY_INTEGER;
cg$tableind cg$tableind_type;
idx BINARY_INTEGER := 1;

PROCEDURE   ins(cg$rec IN OUT cg$row_type,
                cg$ind IN OUT cg$ind_type,
                do_ins IN BOOLEAN DEFAULT TRUE
               );
PROCEDURE   upd(cg$rec             IN OUT cg$row_type,
                cg$ind             IN OUT cg$ind_type,
                do_upd             IN BOOLEAN     DEFAULT TRUE,
                cg$pk              IN cg$row_type DEFAULT NULL      
               );
PROCEDURE   del(cg$pk  IN cg$pk_type,
                do_del IN BOOLEAN DEFAULT TRUE
               );
PROCEDURE   lck(cg$old_rec  IN cg$row_type,
                cg$old_ind  IN cg$ind_type,
                nowait_flag IN BOOLEAN DEFAULT TRUE
               );
PROCEDURE   insert_jn(cg$rec    IN cg$row_type,
                      operation IN VARCHAR2 DEFAULT 'INS'
                     );
PROCEDURE   slct(cg$sel_rec IN OUT cg$row_type);

PROCEDURE   validate_arc(cg$rec IN OUT cg$row_type);

PROCEDURE   validate_domain(cg$rec IN OUT cg$row_type,
                            cg$ind IN cg$ind_type DEFAULT cg$ind_true);

PROCEDURE   validate_foreign_keys_ins(cg$rec IN cg$row_type);
PROCEDURE   validate_foreign_keys_upd(cg$rec IN cg$row_type, 
                                      cg$old_rec IN cg$row_type, 
                                      cg$ind IN cg$ind_type);
PROCEDURE   validate_foreign_keys_del(cg$rec IN cg$row_type);

PROCEDURE   validate_domain_cascade_delete(cg$old_rec IN cg$row_type);        
PROCEDURE   validate_domain_cascade_update(cg$old_rec IN cg$row_type);        

PROCEDURE   cascade_update(cg$new_rec IN OUT cg$row_type,
                           cg$old_rec IN cg$row_type );
PROCEDURE   domain_cascade_update(cg$new_rec IN OUT cg$row_type,              
                                  cg$new_ind IN OUT cg$ind_type,
                                  cg$old_rec IN     cg$row_type);
PROCEDURE   domain_cascade_upd( cg$rec     IN OUT cg$row_type,                
                                cg$ind     IN OUT cg$ind_type,
                                cg$old_rec IN     cg$row_type);

PROCEDURE   cascade_delete(cg$old_rec IN OUT cg$row_type);
PROCEDURE   domain_cascade_delete(cg$old_rec IN cg$row_type);

PROCEDURE   upd_denorm2( cg$rec IN cg$row_type,
                         cg$ind IN cg$ind_type );
PROCEDURE   upd_oper_denorm2( cg$rec IN cg$row_type,
                              cg$old_rec IN cg$row_type,
                              cg$ind IN cg$ind_type,
                              operation IN VARCHAR2 DEFAULT 'UPD' );

-- Used for custom DATA QUALITY ENSURANCE                                                                               
cg$columID constant cg$colum_type := cg$colum_type('ID','NUMBER', 22, NULL, 0);                                         
cg$columIPR_ID constant cg$colum_type := cg$colum_type('IPR_ID','NUMBER', 22, NULL, 0);                                 
cg$columEND_USERS_ID constant cg$colum_type := cg$colum_type('END_USERS_ID','NUMBER', 22, NULL, NULL);                  
cg$columUSER_REFERENCE constant cg$colum_type := cg$colum_type('USER_REFERENCE','VARCHAR2', 400, NULL, NULL);           
cg$columTYPE constant cg$colum_type := cg$colum_type('TYPE','VARCHAR2', 1, NULL, NULL);                                 
cg$columVALID constant cg$colum_type := cg$colum_type('VALID','NUMBER', 22, 1, 0);                                      
cg$columDATE_SUBSCRIBED constant cg$colum_type := cg$colum_type('DATE_SUBSCRIBED','DATE', 7, NULL, NULL);               
cg$columUSER_COMMENT constant cg$colum_type := cg$colum_type('USER_COMMENT','VARCHAR2', 400, NULL, NULL);               
cg$columUAT_ID constant cg$colum_type := cg$colum_type('UAT_ID','NUMBER', 22, NULL, 0);                                 
cg$columSIGNATURE_ID constant cg$colum_type := cg$colum_type('SIGNATURE_ID','NUMBER', 22, NULL, NULL);                  
cg$columSIGNATURE constant cg$colum_type := cg$colum_type('SIGNATURE','RAW', 20, NULL, NULL);                           
cg$columEXTERNAL_CLIENT_ID constant cg$colum_type := cg$colum_type('EXTERNAL_CLIENT_ID','VARCHAR2', 255, NULL, NULL);   
cg$columACC_OWNER_ID constant cg$colum_type := cg$colum_type('ACC_OWNER_ID','VARCHAR2', 40, NULL, NULL);   
cg$columDATE_SBSCRPTREQ_FORW constant cg$colum_type := cg$colum_type('DATE_SBSCRPTREQ_FORWARDED','DATE', 7, NULL, NULL);   
cg$columDATE_UNSBSCRPTREQ_FORW constant cg$colum_type := cg$colum_type('DATE_UNSBSCRPTREQ_FORWARDED','DATE', 7, NULL, NULL);   
cg$columINVOICE_TEMPLATE constant cg$colum_type := cg$colum_type('INVOICE_TEMPLATE','CLOB', 4000, NULL, NULL); 

cg$table_columns cg$common.cg$column_table_type;                                                                        
                                                                                                                        
/* op$isEqual(...) checks if two given records are equal or not! If they are equal                                      
	TRUE is returned, else FALSE is returned. Works only with basic datatypes!                                             
*/                                                                                                                      
FUNCTION op$isEqual (cg$rec_1 IN cg$row_type, cg$rec_2 IN cg$row_type, cg$ind IN cg$ind_type DEFAULT NULL, lastEvaluated
 OUT VARCHAR2)                                                                                                          
                                                                                                                        
RETURN BOOLEAN;                                                                                                         
                                                                                                                        
PROCEDURE val$length (                                                                                                  
pID IN NUMBER                                                                                                           
  ,pIPR_ID IN NUMBER                                                                                                    
  ,pEND_USERS_ID IN NUMBER                                                                                              
  ,pUSER_REFERENCE IN VARCHAR2                                                                                          
  ,pTYPE IN VARCHAR2                                                                                                    
  ,pVALID IN NUMBER                                                                                                     
  ,pDATE_SUBSCRIBED IN DATE                                                                                             
  ,pUSER_COMMENT IN VARCHAR2                                                                                            
  ,pUAT_ID IN NUMBER                                                                                                    
  ,pSIGNATURE_ID IN NUMBER                                                                                              
  ,pSIGNATURE IN RAW                                                                                                    
  ,pEXTERNAL_CLIENT_ID IN VARCHAR2
  ,pACC_OWNER_ID IN VARCHAR2                                                                                      
  ,pDATE_SBSCRPTREQ_FORWARDED IN DATE DEFAULT NULL
  ,pDATE_UNSBSCRPTREQ_FORWARDED IN DATE DEFAULT NULL
  ,pINVOICE_TEMPLATE IN CLOB DEFAULT NULL
  );                                                                                                                    
-- Used for custom DATA QUALITY ENSURANCE -- END                                                                        

END cg$INVOICE_PROVIDER_CLIENTS;
/

