
PROMPT Creating API Package Body for Table 'INVOICE_PROVIDER_CLIENTS'
--------------------------------------------------------------------------------
-- Name:        cg$INVOICE_PROVIDER_CLIENTS
-- Description: INVOICE_PROVIDER_CLIENTS table API package definitions
--------------------------------------------------------------------------------
CREATE OR REPLACE PACKAGE BODY mcore.cg$INVOICE_PROVIDER_CLIENTS IS

pkgCtxId CONSTANT VARCHAR2(28) := '/Core/Main/InvoiceManagement';

/* op$isEqual(...) checks if two given records are equal or not! If they are equal                                                                                                                      
	TRUE is returned, else FALSE is returned. Works only with basic datatypes!                                                                                                                             
*/                                                                                                                                                                                                      
FUNCTION op$isEqual (cg$rec_1 IN cg$row_type, cg$rec_2 IN cg$row_type, cg$ind IN cg$ind_type DEFAULT NULL, lastEvaluated OUT VARCHAR2)                                                                  
RETURN BOOLEAN IS                                                                                                                                                                                       
BEGIN                                                                                                                                                                                                   
	lastEvaluated := 'id';                                                                                                                                                                                 
	if cg$ind.id is null or cg$ind.id then                                                                                                                                                                 
		if ((cg$rec_1.id is not null) or (cg$rec_2.id is not null)) then                                                                                                                                      
			if cg$rec_1.id = cg$rec_2.id then null; else return false; end if;                                                                                                                                   
		end if;                                                                                                                                                                                               
	end if;                                                                                                                                                                                                
                                                                                                                                                                                                        
	lastEvaluated := 'ipr_id';                                                                                                                                                                             
	if cg$ind.ipr_id is null or cg$ind.ipr_id then                                                                                                                                                         
		if ((cg$rec_1.ipr_id is not null) or (cg$rec_2.ipr_id is not null)) then                                                                                                                              
			if cg$rec_1.ipr_id = cg$rec_2.ipr_id then null; else return false; end if;                                                                                                                           
		end if;                                                                                                                                                                                               
	end if;                                                                                                                                                                                                
                                                                                                                                                                                                        
	lastEvaluated := 'end_users_id';                                                                                                                                                                       
	if cg$ind.end_users_id is null or cg$ind.end_users_id then                                                                                                                                             
		if ((cg$rec_1.end_users_id is not null) or (cg$rec_2.end_users_id is not null)) then                                                                                                                  
			if cg$rec_1.end_users_id = cg$rec_2.end_users_id then null; else return false; end if;                                                                                                               
		end if;                                                                                                                                                                                               
	end if;                                                                                                                                                                                                
                                                                                                                                                                                                        
	lastEvaluated := 'user_reference';                                                                                                                                                                     
	if cg$ind.user_reference is null or cg$ind.user_reference then                                                                                                                                         
		if ((cg$rec_1.user_reference is not null) or (cg$rec_2.user_reference is not null)) then                                                                                                              
			if cg$rec_1.user_reference = cg$rec_2.user_reference then null; else return false; end if;                                                                                                           
		end if;                                                                                                                                                                                               
	end if;                                                                                                                                                                                                
                                                                                                                                                                                                        
	lastEvaluated := 'type';                                                                                                                                                                               
	if cg$ind.type is null or cg$ind.type then                                                                                                                                                             
		if ((cg$rec_1.type is not null) or (cg$rec_2.type is not null)) then                                                                                                                                  
			if cg$rec_1.type = cg$rec_2.type then null; else return false; end if;                                                                                                                               
		end if;                                                                                                                                                                                               
	end if;                                                                                                                                                                                                
                                                                                                                                                                                                        
	lastEvaluated := 'valid';                                                                                                                                                                              
	if cg$ind.valid is null or cg$ind.valid then                                                                                                                                                           
		if ((cg$rec_1.valid is not null) or (cg$rec_2.valid is not null)) then                                                                                                                                
			if cg$rec_1.valid = cg$rec_2.valid then null; else return false; end if;                                                                                                                             
		end if;                                                                                                                                                                                               
	end if;                                                                                                                                                                                                
                                                                                                                                                                                                        
	lastEvaluated := 'date_subscribed';                                                                                                                                                                    
	if cg$ind.date_subscribed is null or cg$ind.date_subscribed then                                                                                                                                       
		if ((cg$rec_1.date_subscribed is not null) or (cg$rec_2.date_subscribed is not null)) then                                                                                                            
			if cg$rec_1.date_subscribed = cg$rec_2.date_subscribed then null; else return false; end if;                                                                                                         
		end if;                                                                                                                                                                                               
	end if;                                                                                                                                                                                                
                                                                                                                                                                                                        
	lastEvaluated := 'user_comment';                                                                                                                                                                       
	if cg$ind.user_comment is null or cg$ind.user_comment then                                                                                                                                             
		if ((cg$rec_1.user_comment is not null) or (cg$rec_2.user_comment is not null)) then                                                                                                                  
			if cg$rec_1.user_comment = cg$rec_2.user_comment then null; else return false; end if;                                                                                                               
		end if;                                                                                                                                                                                               
	end if;                                                                                                                                                                                                
                                                                                                                                                                                                        
	lastEvaluated := 'uat_id';                                                                                                                                                                             
	if cg$ind.uat_id is null or cg$ind.uat_id then                                                                                                                                                         
		if ((cg$rec_1.uat_id is not null) or (cg$rec_2.uat_id is not null)) then                                                                                                                              
			if cg$rec_1.uat_id = cg$rec_2.uat_id then null; else return false; end if;                                                                                                                           
		end if;                                                                                                                                                                                               
	end if;                                                                                                                                                                                                
                                                                                                                                                                                                        
	lastEvaluated := 'signature_id';                                                                                                                                                                       
	if cg$ind.signature_id is null or cg$ind.signature_id then                                                                                                                                             
		if ((cg$rec_1.signature_id is not null) or (cg$rec_2.signature_id is not null)) then                                                                                                                  
			if cg$rec_1.signature_id = cg$rec_2.signature_id then null; else return false; end if;                                                                                                               
		end if;                                                                                                                                                                                               
	end if;                                                                                                                                                                                                
                                                                                                                                                                                                        
	lastEvaluated := 'signature';                                                                                                                                                                          
	if cg$ind.signature is null or cg$ind.signature then                                                                                                                                                   
		if ((cg$rec_1.signature is not null) or (cg$rec_2.signature is not null)) then                                                                                                                        
			if cg$rec_1.signature = cg$rec_2.signature then null; else return false; end if;
		end if;                                                                                                                                                                                               
	end if;          

	lastEvaluated := 'external_client_id';                                                                                                                                                                 
	if cg$ind.external_client_id is null or cg$ind.external_client_id then                                                                                                                                 
		if ((cg$rec_1.external_client_id is not null) or (cg$rec_2.external_client_id is not null)) then                                                                                                      
			if cg$rec_1.external_client_id = cg$rec_2.external_client_id then null; else return false; end if;                                                                                                   
		end if;                                                                                                                                                                                               
	end if;

	lastEvaluated := 'acc_owner_id';                                                                                                                                                                 
	if cg$ind.acc_owner_id is null or cg$ind.acc_owner_id then                                                                                                                                 
		if ((cg$rec_1.acc_owner_id is not null) or (cg$rec_2.acc_owner_id is not null)) then                                                                                                      
			if cg$rec_1.acc_owner_id = cg$rec_2.acc_owner_id then null; else return false; end if;                                                                                                   
		end if;                                                                                                                                                                                               
	end if;                                                                                                                                                                                                
	
	lastEvaluated := 'date_sbscrptreq_forwarded';                                                                                                                                                                 
	if cg$ind.DATE_SBSCRPTREQ_FORWARDED is null or cg$ind.DATE_SBSCRPTREQ_FORWARDED then                                                                                                                                 
		if ((cg$rec_1.DATE_SBSCRPTREQ_FORWARDED is not null) or (cg$rec_2.DATE_SBSCRPTREQ_FORWARDED is not null)) then                                                                                                      
			if cg$rec_1.DATE_SBSCRPTREQ_FORWARDED = cg$rec_2.DATE_SBSCRPTREQ_FORWARDED then null; else return false; end if;                                                                                                   
		end if;                                                                                                                                                                                               
	end if;                                                                                                                                                                                                

	lastEvaluated := 'date_unsbscrptreq_forwarded';                                                                                                                                                                 
	if cg$ind.DATE_UNSBSCRPTREQ_FORWARDED is null or cg$ind.DATE_UNSBSCRPTREQ_FORWARDED then                                                                                                                                 
		if ((cg$rec_1.DATE_UNSBSCRPTREQ_FORWARDED is not null) or (cg$rec_2.DATE_UNSBSCRPTREQ_FORWARDED is not null)) then                                                                                                      
			if cg$rec_1.DATE_UNSBSCRPTREQ_FORWARDED = cg$rec_2.DATE_UNSBSCRPTREQ_FORWARDED then null; else return false; end if;                                                                                                   
		end if;                                                                                                                                                                                               
	end if;
	
	lastEvaluated := 'invoice_template';
	if cg$ind.INVOICE_TEMPLATE is null or cg$ind.INVOICE_TEMPLATE then
		if ((cg$rec_1.INVOICE_TEMPLATE is not null) or (cg$rec_2.INVOICE_TEMPLATE is not null)) then
			if cg$rec_1.INVOICE_TEMPLATE = cg$rec_2.INVOICE_TEMPLATE then null; else return false; end if;
		end if;
	end if;

	lastEvaluated := 'auto_payment_enabled';
	if cg$ind.AUTO_PAYMENT_ENABLED is null or cg$ind.AUTO_PAYMENT_ENABLED then
		if ((cg$rec_1.AUTO_PAYMENT_ENABLED is not null) or (cg$rec_2.AUTO_PAYMENT_ENABLED is not null)) then
			if cg$rec_1.AUTO_PAYMENT_ENABLED = cg$rec_2.AUTO_PAYMENT_ENABLED then null; else return false; end if;
		end if;
	end if;

	lastEvaluated := 'payment_account_id';
	if cg$ind.PAYMENT_ACCOUNT_ID is null or cg$ind.PAYMENT_ACCOUNT_ID then
		if ((cg$rec_1.PAYMENT_ACCOUNT_ID is not null) or (cg$rec_2.PAYMENT_ACCOUNT_ID is not null)) then
			if cg$rec_1.PAYMENT_ACCOUNT_ID = cg$rec_2.PAYMENT_ACCOUNT_ID then null; else return false; end if;
		end if;
	end if;

	lastEvaluated := NULL;
	return true;
END op$isEqual;                                                                                                                                                                                          
                

PROCEDURE   validate_mandatory(cg$val_rec IN cg$row_type,
                               loc        IN VARCHAR2 DEFAULT '');
PROCEDURE   up_autogen_columns(cg$rec    IN OUT cg$row_type,
                               cg$ind    IN OUT cg$ind_type,
                               operation IN VARCHAR2 DEFAULT 'INS',
                               do_denorm IN BOOLEAN DEFAULT TRUE);
PROCEDURE   err_msg(msg  IN VARCHAR2,
                    type IN INTEGER,
                    loc  IN VARCHAR2 DEFAULT '');

--------------------------------------------------------------------------------
-- Name:        raise_uk_not_updateable
--
-- Description: Raise appropriate error when unique key updated
--
-- Parameters:  none
--------------------------------------------------------------------------------
PROCEDURE raise_uk_not_updateable(uk IN VARCHAR2) IS   
BEGIN
    cg$errors.push(cg$errors.MsgGetText(cg$errors.API_UNIQUE_KEY_UPDATE, cg$errors.ERR_UK_UPDATE, uk),
                   'E',
                   'API',
                   cg$errors.API_UNIQUE_KEY_UPDATE,
                   'cg$INVOICE_PROVIDER_CLIENTS.raise_uk_not_updateable');
                   cg$errors.raise_failure;
END raise_uk_not_updateable;


--------------------------------------------------------------------------------
-- Name:        raise_fk_not_transferable
--
-- Description: Raise appropriate error when foreign key updated
--
-- Parameters:  none
--------------------------------------------------------------------------------
PROCEDURE raise_fk_not_transferable(fk IN VARCHAR2) IS 
BEGIN
    cg$errors.push(cg$errors.MsgGetText(cg$errors.API_FOREIGN_KEY_TRANS, cg$errors.ERR_FK_TRANS, fk),
                   'E',
                   'API',
                   cg$errors.API_FOREIGN_KEY_TRANS,
                   'cg$INVOICE_PROVIDER_CLIENTS.raise_fk_not_transferable');
    cg$errors.raise_failure;
END raise_fk_not_transferable;


--------------------------------------------------------------------------------
-- Name:        up_autogen_columns
--
-- Description: Specific autogeneration of column values and conversion to 
--              uppercase
--
-- Parameters:  cg$rec    Record of row to be manipulated
--              cg$ind    Indicators for row
--              operation Procedure where this procedure was called
--------------------------------------------------------------------------------
PROCEDURE up_autogen_columns(cg$rec IN OUT cg$row_type,
                             cg$ind IN OUT cg$ind_type,
                             operation IN VARCHAR2 DEFAULT 'INS',
                             do_denorm IN BOOLEAN DEFAULT TRUE) IS
BEGIN
  IF (operation = 'INS') THEN
    BEGIN
			IF (cg$ind.ID = FALSE
      OR  cg$rec.ID is NULL) THEN 
				SELECT INVOICE_PROVIDER_CLIENTS_SEQ.nextval
				INTO   cg$rec.ID
				FROM   DUAL;
				cg$ind.ID := TRUE;
			END IF;
    EXCEPTION WHEN others THEN
      cg$errors.push(SQLERRM, 'E', 'ORA', SQLCODE,
                     'cg$INVOICE_PROVIDER_CLIENTS.up_autogen.ID.OTHERS');
      cg$errors.raise_failure;
    END;
    NULL;
  ELSE      -- (operation = 'UPD')
    NULL;
  END IF;   -- (operation = 'INS') ELSE (operation = 'UPD')

  -- Statements executed for both 'INS' and 'UPD'


EXCEPTION
  WHEN no_data_found THEN
    NULL;
  WHEN others THEN
    cg$errors.push( SQLERRM, 'E', 'ORA', SQLCODE, 
                    'cg$INVOICE_PROVIDER_CLIENTS.up_autogen_columns');
    cg$errors.raise_failure;
END up_autogen_columns;


--------------------------------------------------------------------------------
-- Name:        validate_mandatory
--
-- Description: Checks all mandatory columns are not null and raises appropriate
--              error if not satisfied
--
-- Parameters:  cg$val_rec Record of row to be checked
--              loc        Place where this procedure was called for error 
--                         trapping
--------------------------------------------------------------------------------
PROCEDURE validate_mandatory(cg$val_rec IN cg$row_type,
                             loc        IN VARCHAR2 DEFAULT '') IS
BEGIN
    IF (cg$val_rec.ID IS NULL) THEN
        cg$errors.push(cg$errors.MsgGetText(cg$errors.API_MAND_COLUMN_ISNULL, cg$errors.VAL_MAND, P20ID),
                       'E',
                       'API',
                       cg$errors.API_MAND_COLUMN_ISNULL,
                       loc);
    END IF;
    IF (cg$val_rec.IPR_ID IS NULL) THEN
        cg$errors.push(cg$errors.MsgGetText(cg$errors.API_MAND_COLUMN_ISNULL, cg$errors.VAL_MAND, P30IPR_ID),
                       'E',
                       'API',
                       cg$errors.API_MAND_COLUMN_ISNULL,
                       loc);
    END IF;
    IF (cg$val_rec.END_USERS_ID IS NULL) THEN
        cg$errors.push(cg$errors.MsgGetText(cg$errors.API_MAND_COLUMN_ISNULL, cg$errors.VAL_MAND, P70END_USERS_ID),
                       'E',
                       'API',
                       cg$errors.API_MAND_COLUMN_ISNULL,
                       loc);
    END IF;
    IF (cg$val_rec.USER_REFERENCE IS NULL) THEN
        cg$errors.push(cg$errors.MsgGetText(cg$errors.API_MAND_COLUMN_ISNULL, cg$errors.VAL_MAND, P80USER_REFERENCE),
                       'E',
                       'API',
                       cg$errors.API_MAND_COLUMN_ISNULL,
                       loc);
    END IF;
    IF (cg$val_rec.TYPE IS NULL) THEN
        cg$errors.push(cg$errors.MsgGetText(cg$errors.API_MAND_COLUMN_ISNULL, cg$errors.VAL_MAND, P90TYPE),
                       'E',
                       'API',
                       cg$errors.API_MAND_COLUMN_ISNULL,
                       loc);
    END IF;
    IF (cg$val_rec.VALID IS NULL) THEN
        cg$errors.push(cg$errors.MsgGetText(cg$errors.API_MAND_COLUMN_ISNULL, cg$errors.VAL_MAND, P120VALID),
                       'E',
                       'API',
                       cg$errors.API_MAND_COLUMN_ISNULL,
                       loc);
    END IF;
    IF (cg$val_rec.DATE_SUBSCRIBED IS NULL) THEN
        cg$errors.push(cg$errors.MsgGetText(cg$errors.API_MAND_COLUMN_ISNULL, cg$errors.VAL_MAND, P130DATE_SUBSCRIBED),
                       'E',
                       'API',
                       cg$errors.API_MAND_COLUMN_ISNULL,
                       loc);
    END IF;
    NULL;
END validate_mandatory;


--------------------------------------------------------------------------------
-- Name:        validate_foreign_keys
--
-- Description: Checks all mandatory columns are not null and raises appropriate
--              error if not satisfied
--
-- Parameters:  cg$rec Record of row to be checked
--------------------------------------------------------------------------------
PROCEDURE validate_foreign_keys_ins(cg$rec IN cg$row_type) IS
    fk_check INTEGER;
BEGIN
NULL;
END;

PROCEDURE validate_foreign_keys_upd( cg$rec IN cg$row_type, 
                                     cg$old_rec IN cg$row_type, 
                                     cg$ind IN cg$ind_type) IS
    fk_check INTEGER;
BEGIN
NULL;
END;

PROCEDURE validate_foreign_keys_del(cg$rec IN cg$row_type) IS
    fk_check INTEGER;
BEGIN
NULL;
END;


--------------------------------------------------------------------------------
-- Name:        slct
--
-- Description: Selects into the given parameter all the attributes for the row
--              given by the primary key
--
-- Parameters:  cg$sel_rec  Record of row to be selected into using its PK
--------------------------------------------------------------------------------
PROCEDURE slct(cg$sel_rec IN OUT cg$row_type) IS

BEGIN

    IF cg$sel_rec.the_rowid is null THEN
       SELECT    ID
       ,         IPR_ID
       ,         END_USERS_ID
       ,         USER_REFERENCE
       ,         TYPE
       ,         VALID
       ,         DATE_SUBSCRIBED
       ,         USER_COMMENT
       ,         UAT_ID
       ,         SIGNATURE_ID
       ,         SIGNATURE
       ,         EXTERNAL_CLIENT_ID
       ,         ACC_OWNER_ID
       ,         DATE_SBSCRPTREQ_FORWARDED
       ,         DATE_UNSBSCRPTREQ_FORWARDED
	   ,		 INVOICE_TEMPLATE
	   ,		 AUTO_PAYMENT_ENABLED
	   ,		 PAYMENT_ACCOUNT_ID
       , rowid
       INTO      cg$sel_rec.ID
       ,         cg$sel_rec.IPR_ID
       ,         cg$sel_rec.END_USERS_ID
       ,         cg$sel_rec.USER_REFERENCE
       ,         cg$sel_rec.TYPE
       ,         cg$sel_rec.VALID
       ,         cg$sel_rec.DATE_SUBSCRIBED
       ,         cg$sel_rec.USER_COMMENT
       ,         cg$sel_rec.UAT_ID
       ,         cg$sel_rec.SIGNATURE_ID
       ,         cg$sel_rec.SIGNATURE
       ,         cg$sel_rec.EXTERNAL_CLIENT_ID
       ,         cg$sel_rec.ACC_OWNER_ID
       ,         cg$sel_rec.DATE_SBSCRPTREQ_FORWARDED
       ,         cg$sel_rec.DATE_UNSBSCRPTREQ_FORWARDED
	   ,         cg$sel_rec.INVOICE_TEMPLATE
	   ,         cg$sel_rec.AUTO_PAYMENT_ENABLED
	   ,         cg$sel_rec.PAYMENT_ACCOUNT_ID
       ,cg$sel_rec.the_rowid
       FROM   INVOICE_PROVIDER_CLIENTS
       WHERE        ID = cg$sel_rec.ID;
    ELSE
       SELECT    ID
       ,         IPR_ID
       ,         END_USERS_ID
       ,         USER_REFERENCE
       ,         TYPE
       ,         VALID
       ,         DATE_SUBSCRIBED
       ,         USER_COMMENT
       ,         UAT_ID
       ,         SIGNATURE_ID
       ,         SIGNATURE
       ,         EXTERNAL_CLIENT_ID
       ,         ACC_OWNER_ID
       ,         DATE_SBSCRPTREQ_FORWARDED
       ,         DATE_UNSBSCRPTREQ_FORWARDED
	   ,		 INVOICE_TEMPLATE
	   ,		 AUTO_PAYMENT_ENABLED
	   ,		 PAYMENT_ACCOUNT_ID
       , rowid
       INTO      cg$sel_rec.ID
       ,         cg$sel_rec.IPR_ID
       ,         cg$sel_rec.END_USERS_ID
       ,         cg$sel_rec.USER_REFERENCE
       ,         cg$sel_rec.TYPE
       ,         cg$sel_rec.VALID
       ,         cg$sel_rec.DATE_SUBSCRIBED
       ,         cg$sel_rec.USER_COMMENT
       ,         cg$sel_rec.UAT_ID
       ,         cg$sel_rec.SIGNATURE_ID
       ,         cg$sel_rec.SIGNATURE
       ,         cg$sel_rec.EXTERNAL_CLIENT_ID
       ,         cg$sel_rec.ACC_OWNER_ID
       ,         cg$sel_rec.DATE_SBSCRPTREQ_FORWARDED
       ,         cg$sel_rec.DATE_UNSBSCRPTREQ_FORWARDED
	   ,         cg$sel_rec.INVOICE_TEMPLATE
	   ,         cg$sel_rec.AUTO_PAYMENT_ENABLED
	   ,         cg$sel_rec.PAYMENT_ACCOUNT_ID
       ,cg$sel_rec.the_rowid
       FROM   INVOICE_PROVIDER_CLIENTS
       WHERE  rowid = cg$sel_rec.the_rowid;
    END IF;

EXCEPTION WHEN OTHERS THEN
    cg$errors.push(SQLERRM,
                   'E',
                   'ORA',
                   SQLCODE,
                   'cg$INVOICE_PROVIDER_CLIENTS.slct.others');
    cg$errors.raise_failure;

END slct;


--------------------------------------------------------------------------------
-- Name:        cascade_update
--
-- Description: Updates all child tables affected by a change to INVOICE_PROVIDER_CLIENTS 
--
-- Parameters:  cg$rec     Record of INVOICE_PROVIDER_CLIENTS current values
--              cg$old_rec Record of INVOICE_PROVIDER_CLIENTS previous values
--------------------------------------------------------------------------------
PROCEDURE cascade_update(cg$new_rec IN OUT cg$row_type,
                         cg$old_rec IN     cg$row_type) IS
BEGIN
  NULL;
END cascade_update;


--------------------------------------------------------------------------------
-- Name:        validate_domain_cascade_update
--
-- Description: Implement the Domain Key Constraint Cascade Updates Resticts rule
--              of each child table that references this tableINVOICE_PROVIDER_CLIENTS 
--
-- Parameters:  cg$old_rec     Record of INVOICE_PROVIDER_CLIENTS current values
--------------------------------------------------------------------------------
PROCEDURE validate_domain_cascade_update( cg$old_rec IN cg$row_type ) IS
  dk_check INTEGER;
BEGIN
  NULL;
END validate_domain_cascade_update;


-----------------------------------------------------------------------------------------
-- Name:        domain_cascade_update
--
-- Description: Implement the Domain Key Constraint Cascade Updates rules of each
--              child table that references this table INVOICE_PROVIDER_CLIENTS 
--
-- Parameters:  cg$new_rec  New values for INVOICE_PROVIDER_CLIENTS's domain key constraint columns 
--              cg$new_ind  Indicates changed INVOICE_PROVIDER_CLIENTS's domain key constraint columns
--              cg$old_rec  Current values for INVOICE_PROVIDER_CLIENTS's domain key constraint columns
-----------------------------------------------------------------------------------------
PROCEDURE domain_cascade_update(cg$new_rec IN OUT cg$row_type,
                                cg$new_ind IN OUT cg$ind_type,
                                cg$old_rec IN     cg$row_type) IS
BEGIN
  NULL;
END domain_cascade_update;


--------------------------------------------------------------------------------
-- Name:        cascade_delete
--
-- Description: Delete all child tables affected by a delete to INVOICE_PROVIDER_CLIENTS 
--
-- Parameters:  cg$rec     Record of INVOICE_PROVIDER_CLIENTS current values
--------------------------------------------------------------------------------
PROCEDURE cascade_delete(cg$old_rec IN OUT cg$row_type)
IS
BEGIN
  NULL;
END cascade_delete;

--------------------------------------------------------------------------------
-- Name:        domain_cascade_delete
--
-- Description: Implement the Domain Key Constraint Cascade Delete rules of each
--              child table that references this tableINVOICE_PROVIDER_CLIENTS 
--
-- Parameters:  cg$old_rec     Record of INVOICE_PROVIDER_CLIENTS current values
--------------------------------------------------------------------------------
PROCEDURE domain_cascade_delete( cg$old_rec IN cg$row_type )
IS
BEGIN
  NULL;
END domain_cascade_delete;


--------------------------------------------------------------------------------
-- Name:        validate_domain_cascade_delete
--
-- Description: Implement the Domain Key Constraint Cascade Delete Restricts rule
--              of each child table that references this tableINVOICE_PROVIDER_CLIENTS 
--
-- Parameters:  cg$old_rec     Record of INVOICE_PROVIDER_CLIENTS current values
--------------------------------------------------------------------------------
PROCEDURE validate_domain_cascade_delete(cg$old_rec IN cg$row_type)
IS
    dk_check INTEGER;
BEGIN
  NULL;
END validate_domain_cascade_delete;



--------------------------------------------------------------------------------
-- Name:        validate_arc
--
-- Description: Checks for adherence to arc relationship 
--
-- Parameters:  cg$rec     Record of INVOICE_PROVIDER_CLIENTS current values
--------------------------------------------------------------------------------
PROCEDURE validate_arc(cg$rec IN OUT cg$row_type) IS
i NUMBER;
BEGIN
    NULL;
END validate_arc;


--------------------------------------------------------------------------------
-- Name:        validate_domain
--
-- Description: Checks against reference table for values lying in a domain 
--
-- Parameters:  cg$rec     Record of INVOICE_PROVIDER_CLIENTS current values
--------------------------------------------------------------------------------
PROCEDURE validate_domain(cg$rec IN OUT cg$row_type,
                          cg$ind IN cg$ind_type DEFAULT cg$ind_true)
IS
  dummy NUMBER;
  found BOOLEAN;
  no_tabview EXCEPTION;
  PRAGMA EXCEPTION_INIT(no_tabview, -942); 
BEGIN

















    NULL;

EXCEPTION
    WHEN cg$errors.cg$error THEN 
        cg$errors.raise_failure;
    WHEN no_tabview THEN
        cg$errors.push(cg$errors.MsgGetText(cg$errors.API_RV_TAB_NOT_FOUND,
                                            cg$errors.APIMSG_RV_TAB_NOT_FOUND,
                                            'CG_REF_CODES','INVOICE_PROVIDER_CLIENTS'),
                       'E',
                       'API',
                       cg$errors.API_RV_TAB_NOT_FOUND,
                       'cg$INVOICE_PROVIDER_CLIENTS.v_domain.no_reftable_found');
        cg$errors.raise_failure;
    WHEN OTHERS THEN
        cg$errors.push(SQLERRM,
                       'E',
                       'ORA',
                       SQLCODE,
                       'cg$INVOICE_PROVIDER_CLIENTS.v_domain.others');
        cg$errors.raise_failure;
END validate_domain;


--------------------------------------------------------------------------------
-- Name:        err_msg
--
-- Description: Pushes onto stack appropriate user defined error message
--              depending on the rule violated
--
-- Parameters:  msg     Oracle error message
--              type    Type of violation e.g. check_constraint: ERR_CHECK_CON
--              loc     Place where this procedure was called for error 
--                      trapping
--------------------------------------------------------------------------------
PROCEDURE err_msg(msg   IN VARCHAR2,
                  type  IN INTEGER,
                  loc   IN VARCHAR2 DEFAULT '') IS
con_name VARCHAR2(240);
BEGIN
    con_name := cg$errors.parse_constraint(msg, type);
    IF (con_name = 'IPT_PK') THEN
        cg$errors.push(nvl(IPT_PK 
                  ,cg$errors.MsgGetText(cg$errors.API_PK_CON_VIOLATED
					                 ,cg$errors.APIMSG_PK_VIOLAT
                                     ,'IPT_PK' 
                                     ,'INVOICE_PROVIDER_CLIENTS')),
                       'E',
                       'API',
                       cg$errors.API_PK_CON_VIOLATED,
                       loc);
    ELSIF (con_name = 'IPT_UK') THEN
        cg$errors.push(nvl(IPT_UK 
                  ,cg$errors.MsgGetText(cg$errors.API_UQ_CON_VIOLATED
					                 ,cg$errors.APIMSG_UK_VIOLAT
                                     ,'IPT_UK' 
                                     ,'INVOICE_PROVIDER_CLIENTS')),
                       'E',
                       'API',
                       cg$errors.API_UQ_CON_VIOLATED,
                       loc);

    ELSIF (con_name = 'IPT_IPR_FK') THEN
        cg$errors.push(nvl(IPT_IPR_FK 
                      ,cg$errors.MsgGetText(cg$errors.API_FK_CON_VIOLATED
					                 ,cg$errors.APIMSG_FK_VIOLAT
                                     ,'IPT_IPR_FK' 
                                     ,'INVOICE_PROVIDER_CLIENTS')),
                       'E',
                       'API',
                       cg$errors.API_FK_CON_VIOLATED,
                       loc);
    ELSIF (con_name = 'IPT_UAT_FK') THEN
        cg$errors.push(nvl(IPT_UAT_FK 
                      ,cg$errors.MsgGetText(cg$errors.API_FK_CON_VIOLATED
					                 ,cg$errors.APIMSG_FK_VIOLAT
                                     ,'IPT_UAT_FK' 
                                     ,'INVOICE_PROVIDER_CLIENTS')),
                       'E',
                       'API',
                       cg$errors.API_FK_CON_VIOLATED,
                       loc);
    ELSIF (con_name = 'IPT_SIE_FK') THEN
        cg$errors.push(nvl(IPT_SIE_FK 
                      ,cg$errors.MsgGetText(cg$errors.API_FK_CON_VIOLATED
					                 ,cg$errors.APIMSG_FK_VIOLAT
                                     ,'IPT_SIE_FK' 
                                     ,'INVOICE_PROVIDER_CLIENTS')),
                       'E',
                       'API',
                       cg$errors.API_FK_CON_VIOLATED,
                       loc);
    ELSIF (con_name = 'IPT_EUR_FK') THEN
        cg$errors.push(nvl(IPT_EUR_FK 
                      ,cg$errors.MsgGetText(cg$errors.API_FK_CON_VIOLATED
					                 ,cg$errors.APIMSG_FK_VIOLAT
                                     ,'IPT_EUR_FK' 
                                     ,'INVOICE_PROVIDER_CLIENTS')),
                       'E',
                       'API',
                       cg$errors.API_FK_CON_VIOLATED,
                       loc);
    ELSE
        cg$errors.push(SQLERRM,
                       'E',
                       'ORA',
                       SQLCODE,
                       loc);
    END IF;
END err_msg;


--------------------------------------------------------------------------------
-- Name:        insert_jn
--
-- Description: Insert a record into the journal table for auditing purposes
--
-- Parameters:  cg$rec    Record of row to be journalled
--              operation Type of action to be journalled for this record
--------------------------------------------------------------------------------
PROCEDURE insert_jn(cg$rec    IN cg$row_type,
                    operation IN VARCHAR2 DEFAULT 'INS') IS
BEGIN
    INSERT INTO INVOICE_PROVIDER_CLIENTS_JN
        (JN_OPERATION
        ,JN_ORACLE_USER
        ,JN_DATETIME
        ,JN_NOTES
        ,JN_APPLN
        ,JN_SESSION
        ,ID
        ,IPR_ID
        ,END_USERS_ID
        ,USER_REFERENCE
        ,TYPE
        ,VALID
        ,DATE_SUBSCRIBED
        ,USER_COMMENT
        ,UAT_ID
        ,SIGNATURE_ID
        ,SIGNATURE
        ,EXTERNAL_CLIENT_ID
        ,ACC_OWNER_ID
        ,DATE_SBSCRPTREQ_FORWARDED
        ,DATE_UNSBSCRPTREQ_FORWARDED
		,INVOICE_TEMPLATE)
    VALUES
        (operation
        ,NVL(SYS_CONTEXT('APISESSMGMT', 'APICALL_USERNAME'), USER)
        ,SYSDATE
        ,cg$rec.jn_notes
        ,'CG$INVOICE_PROVIDER_CLIENTS.'||operation
        ,userenv('sessionid')
        ,cg$rec.ID
        ,cg$rec.IPR_ID
        ,cg$rec.END_USERS_ID
        ,cg$rec.USER_REFERENCE
        ,cg$rec.TYPE
        ,cg$rec.VALID
        ,cg$rec.DATE_SUBSCRIBED
        ,cg$rec.USER_COMMENT
        ,cg$rec.UAT_ID
        ,cg$rec.SIGNATURE_ID
        ,cg$rec.SIGNATURE
        ,cg$rec.EXTERNAL_CLIENT_ID
        ,cg$rec.ACC_OWNER_ID
        ,cg$rec.DATE_SBSCRPTREQ_FORWARDED
        ,cg$rec.DATE_UNSBSCRPTREQ_FORWARDED
		,cg$rec.INVOICE_TEMPLATE);
EXCEPTION
    WHEN OTHERS THEN
        cg$errors.push(SQLERRM,
                      'E',
                      'ORA',
                      SQLCODE,
                      'cg$INVOICE_PROVIDER_CLIENTS.insert_jn_'||operation||'.others');
        cg$errors.raise_failure;
END insert_jn;


--------------------------------------------------------------------------------
-- Name:        doLobs
--
-- Description: This function is updating lob columns
--
-- Parameters:  cg$rec  Record of row to be inserted
--              cg$ind  Record of columns specifically set
--------------------------------------------------------------------------------
PROCEDURE doLobs(cg$rec IN OUT cg$row_type,
                 cg$ind IN OUT cg$ind_type) IS
BEGIN
   NULL;
END doLobs;


--------------------------------------------------------------------------------
-- Name:        ins
--
-- Description: API insert procedure
--
-- Parameters:  cg$rec  Record of row to be inserted
--              cg$ind  Record of columns specifically set
--              do_ins  Whether we want the actual INSERT to occur
--------------------------------------------------------------------------------
PROCEDURE ins(cg$rec IN OUT cg$row_type,
              cg$ind IN OUT cg$ind_type,
              do_ins IN BOOLEAN DEFAULT TRUE) IS
cg$tmp_rec cg$row_type;

--  Constant default values

D90_TYPE CONSTANT INVOICE_PROVIDER_CLIENTS.TYPE%TYPE := 'C';
D120_VALID CONSTANT INVOICE_PROVIDER_CLIENTS.VALID%TYPE := 1;
D130_DATE_SUBSCRIBED CONSTANT INVOICE_PROVIDER_CLIENTS.DATE_SUBSCRIBED%TYPE := SYSDATE;

BEGIN
--  Application_logic Pre-Insert <<Start>>
--  Application_logic Pre-Insert << End >>

--  Defaulted

    IF NOT (cg$ind.TYPE) THEN cg$rec.TYPE := D90_TYPE; END IF;
    IF NOT (cg$ind.VALID) THEN cg$rec.VALID := D120_VALID; END IF;
    IF NOT (cg$ind.DATE_SUBSCRIBED) THEN cg$rec.DATE_SUBSCRIBED := D130_DATE_SUBSCRIBED; END IF;
--  Auto-generated and uppercased columns

    up_autogen_columns(cg$rec, cg$ind, 'INS', do_ins);

    called_from_package := TRUE;

    IF (do_ins) THEN 
        validate_foreign_keys_ins(cg$rec);
        validate_arc(cg$rec);
        validate_domain(cg$rec);

        INSERT INTO INVOICE_PROVIDER_CLIENTS
            (ID
            ,IPR_ID
            ,END_USERS_ID
            ,USER_REFERENCE
            ,TYPE
            ,VALID
            ,DATE_SUBSCRIBED
            ,USER_COMMENT
            ,UAT_ID
            ,SIGNATURE_ID
            ,SIGNATURE
            ,EXTERNAL_CLIENT_ID
            ,ACC_OWNER_ID
            ,DATE_SBSCRPTREQ_FORWARDED
            ,DATE_UNSBSCRPTREQ_FORWARDED
			,INVOICE_TEMPLATE)
        VALUES
            (cg$rec.ID
            ,cg$rec.IPR_ID
            ,cg$rec.END_USERS_ID
            ,cg$rec.USER_REFERENCE
            ,cg$rec.TYPE
            ,cg$rec.VALID
            ,cg$rec.DATE_SUBSCRIBED
            ,cg$rec.USER_COMMENT
            ,cg$rec.UAT_ID
            ,cg$rec.SIGNATURE_ID
            ,cg$rec.SIGNATURE
            ,cg$rec.EXTERNAL_CLIENT_ID
            ,cg$rec.ACC_OWNER_ID
            ,cg$rec.DATE_SBSCRPTREQ_FORWARDED
            ,cg$rec.DATE_UNSBSCRPTREQ_FORWARDED
			,cg$rec.INVOICE_TEMPLATE
);
        doLobs(cg$rec, cg$ind);
        slct(cg$rec);

        upd_oper_denorm2(cg$rec, cg$tmp_rec, cg$ind, 'INS');
    END IF;

    called_from_package := FALSE;


    insert_jn(cg$rec, 'INS');


--  Application logic Post-Insert <<Start>>
--  Application logic Post-Insert << End >>

EXCEPTION
    WHEN cg$errors.cg$error THEN 
        called_from_package := FALSE;
        cg$errors.raise_failure;
    WHEN cg$errors.mandatory_missing THEN
        validate_mandatory(cg$rec, 'cg$INVOICE_PROVIDER_CLIENTS.ins.mandatory_missing');
        called_from_package := FALSE;
        cg$errors.raise_failure;
    WHEN cg$errors.check_violation THEN
        err_msg(SQLERRM, cg$errors.ERR_CHECK_CON, 'cg$INVOICE_PROVIDER_CLIENTS.ins.check_violation');
        called_from_package := FALSE;
        cg$errors.raise_failure;
    WHEN cg$errors.fk_violation THEN
        err_msg(SQLERRM, cg$errors.ERR_FOREIGN_KEY, 'cg$INVOICE_PROVIDER_CLIENTS.ins.fk_violation');
        called_from_package := FALSE;
        cg$errors.raise_failure;
    WHEN cg$errors.uk_violation THEN
        err_msg(SQLERRM, cg$errors.ERR_UNIQUE_KEY, 'cg$INVOICE_PROVIDER_CLIENTS.ins.uk_violation');
        called_from_package := FALSE;
        cg$errors.raise_failure;
    WHEN OTHERS THEN
        cg$errors.push(SQLERRM,
                       'E',
                       'ORA',
                       SQLCODE,
                       'cg$INVOICE_PROVIDER_CLIENTS.ins.others');
        called_from_package := FALSE;
        cg$errors.raise_failure;
END ins;


--------------------------------------------------------------------------------
-- Name:        upd
--
-- Description: API update procedure
--
-- Parameters:  cg$rec  Record of row to be updated
--              cg$ind  Record of columns specifically set
--              do_upd  Whether we want the actual UPDATE to occur
--------------------------------------------------------------------------------
PROCEDURE upd(cg$rec             IN OUT cg$row_type,
              cg$ind             IN OUT cg$ind_type,
              do_upd             IN BOOLEAN DEFAULT TRUE,
              cg$pk              IN cg$row_type DEFAULT NULL )    
IS
  cg$upd_rec    cg$row_type;
  cg$old_rec    cg$row_type;
  RECORD_LOGGED BOOLEAN := FALSE;
  myunit CONSTANT VARCHAR2(31) := 'cg$invoice_provider_clients.upd';
BEGIN
--  Application_logic Pre-Update <<Start>>
--  Application_logic Pre-Update << End >>
	slog.debug(pkgCtxId, myunit, NVL(cg$pk.ID, cg$rec.ID));
 
    IF ( cg$pk.ID IS NULL ) THEN          
      cg$upd_rec.ID := cg$rec.ID;
    ELSE
      cg$upd_rec.ID := cg$pk.ID;
    END IF;
    cg$old_rec.ID := cg$upd_rec.ID;

    IF ( cg$pk.the_rowid IS NULL ) THEN             
      cg$upd_rec.the_rowid := cg$rec.the_rowid;
    ELSE
      cg$upd_rec.the_rowid := cg$pk.the_rowid;
    END IF;
    cg$old_rec.the_rowid := cg$upd_rec.the_rowid;

    IF ( do_upd ) THEN
		slog.debug(pkgCtxId, myunit, 'Do update');
        slct(cg$upd_rec);
		slog.debug(pkgCtxId, myunit, 'Current data fetched');

        --  Report error if attempt to update non updateable Primary Key IPT_PK
        IF (cg$ind.ID AND cg$rec.ID != cg$upd_rec.ID) THEN
            raise_uk_not_updateable('IPT_PK');
        END IF;
        IF NOT (cg$ind.ID) THEN
            cg$rec.ID := cg$upd_rec.ID;
        END IF;
        IF NOT (cg$ind.IPR_ID) THEN
            cg$rec.IPR_ID := cg$upd_rec.IPR_ID;
        END IF;
        IF NOT (cg$ind.END_USERS_ID) THEN
            cg$rec.END_USERS_ID := cg$upd_rec.END_USERS_ID;
        END IF;
        IF NOT (cg$ind.USER_REFERENCE) THEN
            cg$rec.USER_REFERENCE := cg$upd_rec.USER_REFERENCE;
        END IF;
        IF NOT (cg$ind.TYPE) THEN
            cg$rec.TYPE := cg$upd_rec.TYPE;
        END IF;
        IF NOT (cg$ind.VALID) THEN
            cg$rec.VALID := cg$upd_rec.VALID;
        END IF;
        IF NOT (cg$ind.DATE_SUBSCRIBED) THEN
            cg$rec.DATE_SUBSCRIBED := cg$upd_rec.DATE_SUBSCRIBED;
        END IF;
        IF NOT (cg$ind.USER_COMMENT) THEN
            cg$rec.USER_COMMENT := cg$upd_rec.USER_COMMENT;
        END IF;
        IF NOT (cg$ind.UAT_ID) THEN
            cg$rec.UAT_ID := cg$upd_rec.UAT_ID;
        END IF;
        IF NOT (cg$ind.SIGNATURE_ID) THEN
            cg$rec.SIGNATURE_ID := cg$upd_rec.SIGNATURE_ID;
        END IF;
        IF NOT (cg$ind.SIGNATURE) THEN
            cg$rec.SIGNATURE := cg$upd_rec.SIGNATURE;
        END IF;
        IF NOT (cg$ind.EXTERNAL_CLIENT_ID) THEN
            cg$rec.EXTERNAL_CLIENT_ID := cg$upd_rec.EXTERNAL_CLIENT_ID;
        END IF;
        IF NOT (cg$ind.ACC_OWNER_ID) THEN
            cg$rec.ACC_OWNER_ID := cg$upd_rec.ACC_OWNER_ID;
        END IF;
        IF NOT (cg$ind.DATE_SBSCRPTREQ_FORWARDED) THEN
            cg$rec.DATE_SBSCRPTREQ_FORWARDED := cg$upd_rec.DATE_SBSCRPTREQ_FORWARDED;
        END IF;
        IF NOT (cg$ind.DATE_UNSBSCRPTREQ_FORWARDED) THEN
            cg$rec.DATE_UNSBSCRPTREQ_FORWARDED := cg$upd_rec.DATE_UNSBSCRPTREQ_FORWARDED;
        END IF;
		IF NOT (cg$ind.INVOICE_TEMPLATE) THEN
            cg$rec.INVOICE_TEMPLATE := cg$upd_rec.INVOICE_TEMPLATE;
        END IF;
    ELSE
		slog.debug(pkgCtxId, myunit, 'Called from trigger. DO NOT UPDATE');
	     -- Perform checks if called from a trigger
	     -- Indicators are only set on changed values
	     null;
        --  Report error if attempt to update non updateable Primary Key IPT_PK
        IF ( cg$ind.ID ) THEN
          raise_uk_not_updateable('IPT_PK');
        END IF;
    END IF;

	slog.debug(pkgCtxId, myunit, 'Autogen columns ...');
    up_autogen_columns(cg$rec, cg$ind, 'UPD', do_upd);  --  Auto-generated and uppercased columns
	slog.debug(pkgCtxId, myunit, 'Autogen columns ... OK');

--  Now do update if updateable columns exist
    IF (do_upd) THEN
		slog.debug(pkgCtxId, myunit, 'do update if updateable columns exist');
        DECLARE
            called_from BOOLEAN := called_from_package;
        BEGIN
          called_from_package := TRUE;
		  slog.debug(pkgCtxId, myunit, 'Retrieve old data');
          slct(cg$old_rec);                          
		  slog.debug(pkgCtxId, myunit, 'Old data fetch');
          validate_foreign_keys_upd(cg$rec, cg$old_rec, cg$ind);
		  slog.debug(pkgCtxId, myunit, 'FKs validated');
          validate_arc(cg$rec);
		  slog.debug(pkgCtxId, myunit, 'ARC validated');
          validate_domain(cg$rec, cg$ind);
		  slog.debug(pkgCtxId, myunit, 'Domain validated');
          validate_domain_cascade_update(cg$old_rec);
		  slog.debug(pkgCtxId, myunit, 'Domain casadade validated');

		  slog.debug(pkgCtxId, myunit, 'Execute UPDATE DML');
          IF cg$rec.the_rowid is null THEN
			
            UPDATE INVOICE_PROVIDER_CLIENTS
            SET
              IPR_ID = cg$rec.IPR_ID
              ,END_USERS_ID = cg$rec.END_USERS_ID
              ,USER_REFERENCE = cg$rec.USER_REFERENCE
              ,TYPE = cg$rec.TYPE
              ,VALID = cg$rec.VALID
              ,DATE_SUBSCRIBED = cg$rec.DATE_SUBSCRIBED
              ,USER_COMMENT = cg$rec.USER_COMMENT
              ,UAT_ID = cg$rec.UAT_ID
              ,SIGNATURE_ID = cg$rec.SIGNATURE_ID
              ,SIGNATURE = cg$rec.SIGNATURE
              ,EXTERNAL_CLIENT_ID = cg$rec.EXTERNAL_CLIENT_ID
              ,ACC_OWNER_ID = cg$rec.ACC_OWNER_ID
              ,DATE_SBSCRPTREQ_FORWARDED = cg$rec.DATE_SBSCRPTREQ_FORWARDED
              ,DATE_UNSBSCRPTREQ_FORWARDED = cg$rec.DATE_UNSBSCRPTREQ_FORWARDED
			  ,INVOICE_TEMPLATE = cg$rec.INVOICE_TEMPLATE
            WHERE  ID = cg$rec.ID;
            null;
          ELSE
            UPDATE INVOICE_PROVIDER_CLIENTS
            SET
              IPR_ID = cg$rec.IPR_ID
              ,END_USERS_ID = cg$rec.END_USERS_ID
              ,USER_REFERENCE = cg$rec.USER_REFERENCE
              ,TYPE = cg$rec.TYPE
              ,VALID = cg$rec.VALID
              ,DATE_SUBSCRIBED = cg$rec.DATE_SUBSCRIBED
              ,USER_COMMENT = cg$rec.USER_COMMENT
              ,UAT_ID = cg$rec.UAT_ID
              ,SIGNATURE_ID = cg$rec.SIGNATURE_ID
              ,SIGNATURE = cg$rec.SIGNATURE
              ,EXTERNAL_CLIENT_ID = cg$rec.EXTERNAL_CLIENT_ID
              ,ACC_OWNER_ID = cg$rec.ACC_OWNER_ID
              ,DATE_SBSCRPTREQ_FORWARDED = cg$rec.DATE_SBSCRPTREQ_FORWARDED
              ,DATE_UNSBSCRPTREQ_FORWARDED = cg$rec.DATE_UNSBSCRPTREQ_FORWARDED
			  ,INVOICE_TEMPLATE = cg$rec.INVOICE_TEMPLATE
            WHERE rowid = cg$rec.the_rowid;

            null;
          END IF;
		  slog.debug(pkgCtxId, myunit, 'Execute UPDATE DML ... OK');

          slct(cg$rec);
		  slog.debug(pkgCtxId, myunit, 'Refresh data');
          upd_denorm2(cg$rec, cg$ind);
		  slog.debug(pkgCtxId, myunit, 'upd_denorm2');
          upd_oper_denorm2(cg$rec, cg$old_rec, cg$ind, 'UPD');
		  slog.debug(pkgCtxId, myunit, 'upd_oper_denorm2');
          cascade_update(cg$rec, cg$old_rec);
		  slog.debug(pkgCtxId, myunit, 'cascade_update');
          domain_cascade_update(cg$rec, cg$ind, cg$old_rec);             
		  slog.debug(pkgCtxId, myunit, 'domain_cascade_update');
          called_from_package := called_from;
        END;
    END IF;

    slog.debug(pkgCtxId, myunit, 'Insert journal ...');
    insert_jn(cg$rec, 'UPD');
	slog.debug(pkgCtxId, myunit, 'Insert journal ... OK');

    IF NOT (do_upd) THEN
        cg$table(idx).ID := cg$rec.ID;
        cg$tableind(idx).ID := cg$ind.ID;
        cg$table(idx).IPR_ID := cg$rec.IPR_ID;
        cg$tableind(idx).IPR_ID := cg$ind.IPR_ID;
        cg$table(idx).END_USERS_ID := cg$rec.END_USERS_ID;
        cg$tableind(idx).END_USERS_ID := cg$ind.END_USERS_ID;
        cg$table(idx).USER_REFERENCE := cg$rec.USER_REFERENCE;
        cg$tableind(idx).USER_REFERENCE := cg$ind.USER_REFERENCE;
        cg$table(idx).TYPE := cg$rec.TYPE;
        cg$tableind(idx).TYPE := cg$ind.TYPE;
        cg$table(idx).VALID := cg$rec.VALID;
        cg$tableind(idx).VALID := cg$ind.VALID;
        cg$table(idx).DATE_SUBSCRIBED := cg$rec.DATE_SUBSCRIBED;
        cg$tableind(idx).DATE_SUBSCRIBED := cg$ind.DATE_SUBSCRIBED;
        cg$table(idx).USER_COMMENT := cg$rec.USER_COMMENT;
        cg$tableind(idx).USER_COMMENT := cg$ind.USER_COMMENT;
        cg$table(idx).UAT_ID := cg$rec.UAT_ID;
        cg$tableind(idx).UAT_ID := cg$ind.UAT_ID;
        cg$table(idx).SIGNATURE_ID := cg$rec.SIGNATURE_ID;
        cg$tableind(idx).SIGNATURE_ID := cg$ind.SIGNATURE_ID;
        cg$table(idx).SIGNATURE := cg$rec.SIGNATURE;
        cg$tableind(idx).SIGNATURE := cg$ind.SIGNATURE;
        cg$table(idx).EXTERNAL_CLIENT_ID := cg$rec.EXTERNAL_CLIENT_ID;
        cg$tableind(idx).EXTERNAL_CLIENT_ID := cg$ind.EXTERNAL_CLIENT_ID;
        cg$table(idx).ACC_OWNER_ID := cg$rec.ACC_OWNER_ID;
        cg$tableind(idx).ACC_OWNER_ID := cg$ind.ACC_OWNER_ID;
        cg$table(idx).DATE_SBSCRPTREQ_FORWARDED := cg$rec.DATE_SBSCRPTREQ_FORWARDED;
        cg$tableind(idx).DATE_SBSCRPTREQ_FORWARDED := cg$ind.DATE_SBSCRPTREQ_FORWARDED;
        cg$table(idx).DATE_UNSBSCRPTREQ_FORWARDED := cg$rec.DATE_UNSBSCRPTREQ_FORWARDED;
        cg$tableind(idx).DATE_UNSBSCRPTREQ_FORWARDED := cg$ind.DATE_UNSBSCRPTREQ_FORWARDED;
		cg$tableind(idx).INVOICE_TEMPLATE := cg$ind.INVOICE_TEMPLATE;

        cg$table(idx).action_performed := 'UPD';
        idx := idx + 1;
    END IF;

--  Application_logic Post-Update <<Start>>
--  Application_logic Post-Update << End >>
            
EXCEPTION
    WHEN cg$errors.cg$error THEN 
        called_from_package := FALSE;
        cg$errors.raise_failure;
    WHEN cg$errors.upd_mandatory_null THEN
        validate_mandatory(cg$rec, 'cg$INVOICE_PROVIDER_CLIENTS.upd.upd_mandatory_null');
        called_from_package := FALSE;
        cg$errors.raise_failure;
    WHEN cg$errors.check_violation THEN
        err_msg(SQLERRM, cg$errors.ERR_CHECK_CON, 'cg$INVOICE_PROVIDER_CLIENTS.upd.check_violation');
        called_from_package := FALSE;
        cg$errors.raise_failure;
    WHEN cg$errors.fk_violation THEN
        err_msg(SQLERRM, cg$errors.ERR_FOREIGN_KEY, 'cg$INVOICE_PROVIDER_CLIENTS.upd.fk_violation');
        called_from_package := FALSE;
        cg$errors.raise_failure;
    WHEN cg$errors.uk_violation THEN
        err_msg(SQLERRM, cg$errors.ERR_UNIQUE_KEY, 'cg$INVOICE_PROVIDER_CLIENTS.upd.uk_violation');
        called_from_package := FALSE;
        cg$errors.raise_failure;
    WHEN OTHERS THEN
        cg$errors.push(SQLERRM,
                       'E',
                       'ORA',
                       SQLCODE,
                       'cg$INVOICE_PROVIDER_CLIENTS.upd.others');
        called_from_package := FALSE;
        cg$errors.raise_failure;
END upd;


----------------------------------------------------------------------------------------
-- Name:        domain_cascade_upd
--
-- Description: Update the Domain Constraint Key columns of INVOICE_PROVIDER_CLIENTS when the
--              Cascade Update rule is Cascades and the domain table has been
--              updated. Called from <Domain Table pkg>.domain_cascade_update().
--
-- Parameters:  cg$rec      New values for INVOICE_PROVIDER_CLIENTS's domain key constraint columns 
--              cg$ind      Indicates changed INVOICE_PROVIDER_CLIENTS's domain key constraint columns
--              cg$old_rec  Current values for INVOICE_PROVIDER_CLIENTS's domain key constraint columns
----------------------------------------------------------------------------------------
PROCEDURE   domain_cascade_upd( cg$rec     IN OUT cg$row_type,
                                cg$ind     IN OUT cg$ind_type,
                                cg$old_rec IN     cg$row_type )
IS
  called_from BOOLEAN := called_from_package;
BEGIN

  null;
END domain_cascade_upd;


--------------------------------------------------------------------------------
-- Name:        upd_denorm
--
-- Description: API procedure for simple denormalization
--
-- Parameters:  cg$rec  Record of row to be updated
--              cg$ind  Record of columns specifically set
--              do_upd  Whether we want the actual UPDATE to occur
--------------------------------------------------------------------------------
PROCEDURE upd_denorm2( cg$rec IN cg$row_type,
                       cg$ind IN cg$ind_type ) IS
BEGIN
  NULL;
END upd_denorm2;


--------------------------------------------------------------------------------
-- Name:        upd_oper_denorm
--
-- Description: API procedure for operation denormalization
--
-- Parameters:  cg$rec  Record of row to be updated
--              cg$ind  Record of columns specifically set
--              do_upd  Whether we want the actual UPDATE to occur
--------------------------------------------------------------------------------
PROCEDURE upd_oper_denorm2( cg$rec IN cg$row_type,
                            cg$old_rec IN cg$row_type,
                            cg$ind IN cg$ind_type,
                            operation IN VARCHAR2 DEFAULT 'UPD'
					           )
IS
BEGIN















NULL;
END upd_oper_denorm2;

--------------------------------------------------------------------------------
-- Name:        del
--
-- Description: API delete procedure
--
-- Parameters:  cg$pk  Primary key record of row to be deleted
--------------------------------------------------------------------------------
PROCEDURE del(cg$pk IN cg$pk_type,
              do_del IN BOOLEAN DEFAULT TRUE) IS
cg$tmp_rec cg$row_type;
BEGIN
--  Application_logic Pre-Delete <<Start>>
--  Application_logic Pre-Delete << End >>
    cg$tmp_rec.ID := cg$pk.ID;
    cg$tmp_rec.jn_notes := cg$pk.jn_notes;

--  Delete the record

    called_from_package := TRUE;

    IF (do_del) THEN
        DECLARE
           cg$rec cg$row_type;
           cg$old_rec cg$row_type;
           cg$ind cg$ind_type;
        BEGIN
           cg$rec.ID := cg$pk.ID;
           slct(cg$rec);

           validate_foreign_keys_del(cg$rec);
           validate_domain_cascade_delete(cg$rec);    

           IF cg$pk.the_rowid is null THEN
              DELETE INVOICE_PROVIDER_CLIENTS
              WHERE                    ID = cg$pk.ID;
           ELSE
              DELETE INVOICE_PROVIDER_CLIENTS
              WHERE  rowid = cg$pk.the_rowid;
           END IF;

           upd_oper_denorm2(cg$rec, cg$old_rec, cg$ind, 'DEL');
           cascade_delete(cg$rec);
           domain_cascade_delete(cg$rec);             
        END;
    END IF;

    called_from_package := FALSE;

    insert_jn(cg$tmp_rec, 'DEL');

--  Application_logic Post-Delete <<Start>>
--  Application_logic Post-Delete << End >>

EXCEPTION
    WHEN cg$errors.cg$error THEN 
        called_from_package := FALSE;
        cg$errors.raise_failure;
    WHEN cg$errors.delete_restrict THEN
        err_msg(SQLERRM, cg$errors.ERR_DELETE_RESTRICT, 'cg$INVOICE_PROVIDER_CLIENTS.del.delete_restrict');
        called_from_package := FALSE;
        cg$errors.raise_failure;
    WHEN no_data_found THEN
        cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_DEL, cg$errors.ROW_DEL),
                       'E',
                       'ORA',
                       SQLCODE,
                       'cg$INVOICE_PROVIDER_CLIENTS.del.no_data_found');
        called_from_package := FALSE;
        cg$errors.raise_failure;
    WHEN OTHERS THEN
        cg$errors.push(SQLERRM,
                       'E',
                       'ORA',
                       SQLCODE,
                       'cg$INVOICE_PROVIDER_CLIENTS.del.others');
        called_from_package := FALSE;
        cg$errors.raise_failure;
END del;


--------------------------------------------------------------------------------
-- Name:        lck
--
-- Description: API lock procedure
--
-- Parameters:  cg$old_rec  Calling apps view of record of row to be locked
--              cg$old_ind  Record of columns to raise error if modified
--              nowait_flag TRUE lock with NOWAIT, FALSE don't fail if busy
--------------------------------------------------------------------------------
PROCEDURE lck(cg$old_rec IN cg$row_type,
              cg$old_ind IN cg$ind_type,
              nowait_flag IN BOOLEAN DEFAULT TRUE) IS
cg$tmp_rec cg$row_type;
any_modified BOOLEAN := FALSE;

BEGIN
--  Application_logic Pre-Lock <<Start>>
--  Application_logic Pre-Lock << End >>

--  Do the row lock

    BEGIN
        IF (nowait_flag) THEN
            IF cg$old_rec.the_rowid is null THEN
               SELECT       ID
               ,            IPR_ID
               ,            END_USERS_ID
               ,            USER_REFERENCE
               ,            TYPE
               ,            VALID
               ,            DATE_SUBSCRIBED
               ,            USER_COMMENT
               ,            UAT_ID
               ,            SIGNATURE_ID
               ,            SIGNATURE
               ,            EXTERNAL_CLIENT_ID
               ,            ACC_OWNER_ID
               ,            DATE_SBSCRPTREQ_FORWARDED
               ,            DATE_UNSBSCRPTREQ_FORWARDED
			   ,			INVOICE_TEMPLATE
               INTO         cg$tmp_rec.ID
               ,            cg$tmp_rec.IPR_ID
               ,            cg$tmp_rec.END_USERS_ID
               ,            cg$tmp_rec.USER_REFERENCE
               ,            cg$tmp_rec.TYPE
               ,            cg$tmp_rec.VALID
               ,            cg$tmp_rec.DATE_SUBSCRIBED
               ,            cg$tmp_rec.USER_COMMENT
               ,            cg$tmp_rec.UAT_ID
               ,            cg$tmp_rec.SIGNATURE_ID
               ,            cg$tmp_rec.SIGNATURE
               ,            cg$tmp_rec.EXTERNAL_CLIENT_ID
               ,            cg$tmp_rec.ACC_OWNER_ID
               ,            cg$tmp_rec.DATE_SBSCRPTREQ_FORWARDED
               ,            cg$tmp_rec.DATE_UNSBSCRPTREQ_FORWARDED
			   ,            cg$tmp_rec.INVOICE_TEMPLATE
               FROM      INVOICE_PROVIDER_CLIENTS
               WHERE              ID = cg$old_rec.ID
               FOR UPDATE NOWAIT;
            ELSE
               SELECT       ID
               ,            IPR_ID
               ,            END_USERS_ID
               ,            USER_REFERENCE
               ,            TYPE
               ,            VALID
               ,            DATE_SUBSCRIBED
               ,            USER_COMMENT
               ,            UAT_ID
               ,            SIGNATURE_ID
               ,            SIGNATURE
               ,            EXTERNAL_CLIENT_ID
               ,            ACC_OWNER_ID
               ,            DATE_SBSCRPTREQ_FORWARDED
               ,            DATE_UNSBSCRPTREQ_FORWARDED
			   ,			INVOICE_TEMPLATE
               INTO         cg$tmp_rec.ID
               ,            cg$tmp_rec.IPR_ID
               ,            cg$tmp_rec.END_USERS_ID
               ,            cg$tmp_rec.USER_REFERENCE
               ,            cg$tmp_rec.TYPE
               ,            cg$tmp_rec.VALID
               ,            cg$tmp_rec.DATE_SUBSCRIBED
               ,            cg$tmp_rec.USER_COMMENT
               ,            cg$tmp_rec.UAT_ID
               ,            cg$tmp_rec.SIGNATURE_ID
               ,            cg$tmp_rec.SIGNATURE
               ,            cg$tmp_rec.EXTERNAL_CLIENT_ID
               ,            cg$tmp_rec.ACC_OWNER_ID
               ,            cg$tmp_rec.DATE_SBSCRPTREQ_FORWARDED
               ,            cg$tmp_rec.DATE_UNSBSCRPTREQ_FORWARDED
			   ,            cg$tmp_rec.INVOICE_TEMPLATE
               FROM      INVOICE_PROVIDER_CLIENTS
               WHERE rowid = cg$old_rec.the_rowid
               FOR UPDATE NOWAIT;
            END IF;
        ELSE
            IF cg$old_rec.the_rowid is null THEN
               SELECT       ID
               ,            IPR_ID
               ,            END_USERS_ID
               ,            USER_REFERENCE
               ,            TYPE
               ,            VALID
               ,            DATE_SUBSCRIBED
               ,            USER_COMMENT
               ,            UAT_ID
               ,            SIGNATURE_ID
               ,            SIGNATURE
               ,            EXTERNAL_CLIENT_ID
               ,            ACC_OWNER_ID
               ,            DATE_SBSCRPTREQ_FORWARDED
               ,            DATE_UNSBSCRPTREQ_FORWARDED
			   ,			INVOICE_TEMPLATE
               INTO         cg$tmp_rec.ID
               ,            cg$tmp_rec.IPR_ID
               ,            cg$tmp_rec.END_USERS_ID
               ,            cg$tmp_rec.USER_REFERENCE
               ,            cg$tmp_rec.TYPE
               ,            cg$tmp_rec.VALID
               ,            cg$tmp_rec.DATE_SUBSCRIBED
               ,            cg$tmp_rec.USER_COMMENT
               ,            cg$tmp_rec.UAT_ID
               ,            cg$tmp_rec.SIGNATURE_ID
               ,            cg$tmp_rec.SIGNATURE
               ,            cg$tmp_rec.EXTERNAL_CLIENT_ID
               ,            cg$tmp_rec.ACC_OWNER_ID
               ,            cg$tmp_rec.DATE_SBSCRPTREQ_FORWARDED
               ,            cg$tmp_rec.DATE_UNSBSCRPTREQ_FORWARDED
			   ,            cg$tmp_rec.INVOICE_TEMPLATE
               FROM      INVOICE_PROVIDER_CLIENTS
               WHERE              ID = cg$old_rec.ID
               FOR UPDATE;
            ELSE
               SELECT       ID
               ,            IPR_ID
               ,            END_USERS_ID
               ,            USER_REFERENCE
               ,            TYPE
               ,            VALID
               ,            DATE_SUBSCRIBED
               ,            USER_COMMENT
               ,            UAT_ID
               ,            SIGNATURE_ID
               ,            SIGNATURE
               ,            EXTERNAL_CLIENT_ID
               ,            ACC_OWNER_ID
               ,            DATE_SBSCRPTREQ_FORWARDED
               ,            DATE_UNSBSCRPTREQ_FORWARDED
			   ,			INVOICE_TEMPLATE
               INTO         cg$tmp_rec.ID
               ,            cg$tmp_rec.IPR_ID
               ,            cg$tmp_rec.END_USERS_ID
               ,            cg$tmp_rec.USER_REFERENCE
               ,            cg$tmp_rec.TYPE
               ,            cg$tmp_rec.VALID
               ,            cg$tmp_rec.DATE_SUBSCRIBED
               ,            cg$tmp_rec.USER_COMMENT
               ,            cg$tmp_rec.UAT_ID
               ,            cg$tmp_rec.SIGNATURE_ID
               ,            cg$tmp_rec.SIGNATURE
               ,            cg$tmp_rec.EXTERNAL_CLIENT_ID
               ,            cg$tmp_rec.ACC_OWNER_ID
               ,            cg$tmp_rec.DATE_SBSCRPTREQ_FORWARDED
               ,            cg$tmp_rec.DATE_UNSBSCRPTREQ_FORWARDED
               ,            cg$tmp_rec.INVOICE_TEMPLATE			   
               FROM      INVOICE_PROVIDER_CLIENTS
               WHERE rowid = cg$old_rec.the_rowid
               FOR UPDATE;
            END IF;
        END IF;

    EXCEPTION 
        WHEN cg$errors.cg$error THEN 
            cg$errors.raise_failure;
        WHEN cg$errors.resource_busy THEN
            cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_LCK, cg$errors.ROW_LCK),
                           'E',
                           'ORA',
                           SQLCODE,
                           'cg$INVOICE_PROVIDER_CLIENTS.lck.resource_busy');
            cg$errors.raise_failure;
        WHEN no_data_found THEN
            cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_DEL, cg$errors.ROW_DEL),
                           'E',
                           'ORA',
                           SQLCODE,
                           'cg$INVOICE_PROVIDER_CLIENTS.lck.no_data_found');
            cg$errors.raise_failure;
        WHEN OTHERS THEN
            cg$errors.push(SQLERRM,
                           'E',
                           'ORA',
                           SQLCODE,
                           'cg$INVOICE_PROVIDER_CLIENTS.lck.others');
            cg$errors.raise_failure;
    END;

-- Optional Columns

    IF (cg$old_ind.USER_COMMENT) THEN
        IF (cg$tmp_rec.USER_COMMENT IS NOT NULL
        AND cg$old_rec.USER_COMMENT IS NOT NULL) THEN
            IF (cg$tmp_rec.USER_COMMENT NOT LIKE cg$old_rec.USER_COMMENT) THEN
                cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P140USER_COMMENT
                    ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICE_PROVIDER_CLIENTS.lck');
                any_modified := TRUE;
            END IF;
        ELSIF (cg$tmp_rec.USER_COMMENT IS NOT NULL
        OR cg$old_rec.USER_COMMENT IS NOT NULL) THEN
            cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P140USER_COMMENT
                ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICE_PROVIDER_CLIENTS.lck');
            any_modified := TRUE;
        END IF;
    END IF;
    IF (cg$old_ind.UAT_ID) THEN
        IF (cg$tmp_rec.UAT_ID IS NOT NULL
        AND cg$old_rec.UAT_ID IS NOT NULL) THEN
            IF (cg$tmp_rec.UAT_ID NOT LIKE cg$old_rec.UAT_ID) THEN
                cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P150UAT_ID
                    ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICE_PROVIDER_CLIENTS.lck');
                any_modified := TRUE;
            END IF;
        ELSIF (cg$tmp_rec.UAT_ID IS NOT NULL
        OR cg$old_rec.UAT_ID IS NOT NULL) THEN
            cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P150UAT_ID
                ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICE_PROVIDER_CLIENTS.lck');
            any_modified := TRUE;
        END IF;
    END IF;
    IF (cg$old_ind.SIGNATURE_ID) THEN
        IF (cg$tmp_rec.SIGNATURE_ID IS NOT NULL
        AND cg$old_rec.SIGNATURE_ID IS NOT NULL) THEN
            IF (cg$tmp_rec.SIGNATURE_ID NOT LIKE cg$old_rec.SIGNATURE_ID) THEN
                cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P160SIGNATURE_ID
                    ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICE_PROVIDER_CLIENTS.lck');
                any_modified := TRUE;
            END IF;
        ELSIF (cg$tmp_rec.SIGNATURE_ID IS NOT NULL
        OR cg$old_rec.SIGNATURE_ID IS NOT NULL) THEN
            cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P160SIGNATURE_ID
                ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICE_PROVIDER_CLIENTS.lck');
            any_modified := TRUE;
        END IF;
    END IF;
    IF (cg$old_ind.SIGNATURE) THEN
        IF (cg$tmp_rec.SIGNATURE IS NOT NULL
        AND cg$old_rec.SIGNATURE IS NOT NULL) THEN
            IF (cg$tmp_rec.SIGNATURE NOT LIKE cg$old_rec.SIGNATURE) THEN
                cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P170SIGNATURE
                    ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICE_PROVIDER_CLIENTS.lck');
                any_modified := TRUE;
            END IF;
        ELSIF (cg$tmp_rec.SIGNATURE IS NOT NULL
        OR cg$old_rec.SIGNATURE IS NOT NULL) THEN
            cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P170SIGNATURE
                ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICE_PROVIDER_CLIENTS.lck');
            any_modified := TRUE;
        END IF;
    END IF;
    IF (cg$old_ind.EXTERNAL_CLIENT_ID) THEN
        IF (cg$tmp_rec.EXTERNAL_CLIENT_ID IS NOT NULL
        AND cg$old_rec.EXTERNAL_CLIENT_ID IS NOT NULL) THEN
            IF (cg$tmp_rec.EXTERNAL_CLIENT_ID NOT LIKE cg$old_rec.EXTERNAL_CLIENT_ID) THEN
                cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P180EXTERNAL_CLIENT_ID
                    ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICE_PROVIDER_CLIENTS.lck');
                any_modified := TRUE;
            END IF;
        ELSIF (cg$tmp_rec.EXTERNAL_CLIENT_ID IS NOT NULL
        OR cg$old_rec.EXTERNAL_CLIENT_ID IS NOT NULL) THEN
            cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P180EXTERNAL_CLIENT_ID
                ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICE_PROVIDER_CLIENTS.lck');
            any_modified := TRUE;
        END IF;
    END IF;
    IF (cg$old_ind.ACC_OWNER_ID) THEN
        IF (cg$tmp_rec.ACC_OWNER_ID IS NOT NULL
        AND cg$old_rec.ACC_OWNER_ID IS NOT NULL) THEN
            IF (cg$tmp_rec.ACC_OWNER_ID NOT LIKE cg$old_rec.ACC_OWNER_ID) THEN
                cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P190ACC_OWNER_ID
                    ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICE_PROVIDER_CLIENTS.lck');
                any_modified := TRUE;
            END IF;
        ELSIF (cg$tmp_rec.ACC_OWNER_ID IS NOT NULL
        OR cg$old_rec.ACC_OWNER_ID IS NOT NULL) THEN
            cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P190ACC_OWNER_ID
                ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICE_PROVIDER_CLIENTS.lck');
            any_modified := TRUE;
        END IF;
    END IF;
    IF (cg$old_ind.DATE_SBSCRPTREQ_FORWARDED) THEN
        IF (cg$tmp_rec.DATE_SBSCRPTREQ_FORWARDED IS NOT NULL
        AND cg$old_rec.DATE_SBSCRPTREQ_FORWARDED IS NOT NULL) THEN
            IF (cg$tmp_rec.DATE_SBSCRPTREQ_FORWARDED NOT LIKE cg$old_rec.DATE_SBSCRPTREQ_FORWARDED) THEN
                cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P200DATE_SBSCRPTREQ_FORWARDED
                    ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICE_PROVIDER_CLIENTS.lck');
                any_modified := TRUE;
            END IF;
        ELSIF (cg$tmp_rec.DATE_SBSCRPTREQ_FORWARDED IS NOT NULL
        OR cg$old_rec.DATE_SBSCRPTREQ_FORWARDED IS NOT NULL) THEN
            cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P200DATE_SBSCRPTREQ_FORWARDED
                ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICE_PROVIDER_CLIENTS.lck');
            any_modified := TRUE;
        END IF;
    END IF;
    IF (cg$old_ind.DATE_UNSBSCRPTREQ_FORWARDED) THEN
        IF (cg$tmp_rec.DATE_UNSBSCRPTREQ_FORWARDED IS NOT NULL
        AND cg$old_rec.DATE_UNSBSCRPTREQ_FORWARDED IS NOT NULL) THEN
            IF (cg$tmp_rec.DATE_UNSBSCRPTREQ_FORWARDED NOT LIKE cg$old_rec.DATE_UNSBSCRPTREQ_FORWARDED) THEN
                cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P210DATE_UNSBSCRPTREQ_FORWARDE
                    ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICE_PROVIDER_CLIENTS.lck');
                any_modified := TRUE;
            END IF;
        ELSIF (cg$tmp_rec.DATE_UNSBSCRPTREQ_FORWARDED IS NOT NULL
        OR cg$old_rec.DATE_UNSBSCRPTREQ_FORWARDED IS NOT NULL) THEN
            cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P210DATE_UNSBSCRPTREQ_FORWARDE
                ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICE_PROVIDER_CLIENTS.lck');
            any_modified := TRUE;
        END IF;
    END IF;
	 IF (cg$old_ind.INVOICE_TEMPLATE) THEN
        IF (cg$tmp_rec.INVOICE_TEMPLATE IS NOT NULL
        AND cg$old_rec.INVOICE_TEMPLATE IS NOT NULL) THEN
            IF (cg$tmp_rec.INVOICE_TEMPLATE NOT LIKE cg$old_rec.INVOICE_TEMPLATE) THEN
                cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P220INVOICE_TEMPLATE
                    ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICE_PROVIDER_CLIENTS.lck');
                any_modified := TRUE;
            END IF;
        ELSIF (cg$tmp_rec.INVOICE_TEMPLATE IS NOT NULL
        OR cg$old_rec.INVOICE_TEMPLATE IS NOT NULL) THEN
            cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P220INVOICE_TEMPLATE
                ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICE_PROVIDER_CLIENTS.lck');
            any_modified := TRUE;
        END IF;
    END IF;

-- Mandatory Columns

    IF (cg$old_ind.ID) THEN
        IF (cg$tmp_rec.ID != cg$old_rec.ID) THEN
            cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P20ID
                ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICE_PROVIDER_CLIENTS.lck');
            any_modified := TRUE;
        END IF;
    END IF;
    IF (cg$old_ind.IPR_ID) THEN
        IF (cg$tmp_rec.IPR_ID != cg$old_rec.IPR_ID) THEN
            cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P30IPR_ID
                ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICE_PROVIDER_CLIENTS.lck');
            any_modified := TRUE;
        END IF;
    END IF;
    IF (cg$old_ind.END_USERS_ID) THEN
        IF (cg$tmp_rec.END_USERS_ID != cg$old_rec.END_USERS_ID) THEN
            cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P70END_USERS_ID
                ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICE_PROVIDER_CLIENTS.lck');
            any_modified := TRUE;
        END IF;
    END IF;
    IF (cg$old_ind.USER_REFERENCE) THEN
        IF (cg$tmp_rec.USER_REFERENCE != cg$old_rec.USER_REFERENCE) THEN
            cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P80USER_REFERENCE
                ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICE_PROVIDER_CLIENTS.lck');
            any_modified := TRUE;
        END IF;
    END IF;
    IF (cg$old_ind.TYPE) THEN
        IF (cg$tmp_rec.TYPE != cg$old_rec.TYPE) THEN
            cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P90TYPE
                ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICE_PROVIDER_CLIENTS.lck');
            any_modified := TRUE;
        END IF;
    END IF;
    IF (cg$old_ind.VALID) THEN
        IF (cg$tmp_rec.VALID != cg$old_rec.VALID) THEN
            cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P120VALID
                ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICE_PROVIDER_CLIENTS.lck');
            any_modified := TRUE;
        END IF;
    END IF;
    IF (cg$old_ind.DATE_SUBSCRIBED) THEN
        IF (cg$tmp_rec.DATE_SUBSCRIBED != cg$old_rec.DATE_SUBSCRIBED) THEN
            cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P130DATE_SUBSCRIBED
                ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICE_PROVIDER_CLIENTS.lck');
            any_modified := TRUE;
        END IF;
    END IF;

    IF (any_modified) THEN
        cg$errors.raise_failure;
    END IF;

--  Application_logic Post-Lock <<Start>>
--  Application_logic Post-Lock << End >>

END lck;

PROCEDURE val$length (                                                                                                                                                                                  
   pID IN NUMBER                                                                                                                                                                                        
  ,pIPR_ID IN NUMBER                                                                                                                                                                                    
  ,pEND_USERS_ID IN NUMBER                                                                                                                                                                              
  ,pUSER_REFERENCE IN VARCHAR2                                                                                                                                                                          
  ,pTYPE IN VARCHAR2                                                                                                                                                                                    
  ,pVALID IN NUMBER                                                                                                                                                                                     
  ,pDATE_SUBSCRIBED IN DATE                                                                                                                                                                             
  ,pUSER_COMMENT IN VARCHAR2                                                                                                                                                                            
  ,pUAT_ID IN NUMBER                                                                                                                                                                                    
  ,pSIGNATURE_ID IN NUMBER                                                                                                                                                                              
  ,pSIGNATURE IN RAW                                                                                                                                                                                    
  ,pEXTERNAL_CLIENT_ID IN VARCHAR2
  ,pACC_OWNER_ID IN VARCHAR2
  ,pDATE_SBSCRPTREQ_FORWARDED IN DATE DEFAULT NULL
  ,pDATE_UNSBSCRPTREQ_FORWARDED IN DATE DEFAULT NULL
  ,pINVOICE_TEMPLATE IN CLOB DEFAULT NULL
  ) IS                                                                                                                                                                                                  
  vLoc VARCHAR2(60) := 'cg$invoice_provider_clients.val$length';                                                                                                                                        
BEGIN                                                                                                                                                                                                   
		cg$common.val$length(pcg$table_columns => cg$table_columns, pLoc => vLoc, pColumnName => cg$table_columns('ID').COLUMN_NAME, pValue => pID);                                                          
		cg$common.val$length(pcg$table_columns => cg$table_columns, pLoc => vLoc, pColumnName => cg$table_columns('IPR_ID').COLUMN_NAME, pValue => pIPR_ID);                                                  
		cg$common.val$length(pcg$table_columns => cg$table_columns, pLoc => vLoc, pColumnName => cg$table_columns('END_USERS_ID').COLUMN_NAME, pValue => pEND_USERS_ID);                                      
		cg$common.val$length(pcg$table_columns => cg$table_columns, pLoc => vLoc, pColumnName => cg$table_columns('USER_REFERENCE').COLUMN_NAME, pValue => pUSER_REFERENCE);                                  
		cg$common.val$length(pcg$table_columns => cg$table_columns, pLoc => vLoc, pColumnName => cg$table_columns('TYPE').COLUMN_NAME, pValue => pTYPE);                                                      
		cg$common.val$length(pcg$table_columns => cg$table_columns, pLoc => vLoc, pColumnName => cg$table_columns('VALID').COLUMN_NAME, pValue => pVALID);                                                    
		cg$common.val$length(pcg$table_columns => cg$table_columns, pLoc => vLoc, pColumnName => cg$table_columns('DATE_SUBSCRIBED').COLUMN_NAME, pValue => pDATE_SUBSCRIBED);                                
		cg$common.val$length(pcg$table_columns => cg$table_columns, pLoc => vLoc, pColumnName => cg$table_columns('USER_COMMENT').COLUMN_NAME, pValue => pUSER_COMMENT);                                      
		cg$common.val$length(pcg$table_columns => cg$table_columns, pLoc => vLoc, pColumnName => cg$table_columns('UAT_ID').COLUMN_NAME, pValue => pUAT_ID);                                                  
		cg$common.val$length(pcg$table_columns => cg$table_columns, pLoc => vLoc, pColumnName => cg$table_columns('SIGNATURE_ID').COLUMN_NAME, pValue => pSIGNATURE_ID);                                      
		cg$common.val$length(pcg$table_columns => cg$table_columns, pLoc => vLoc, pColumnName => cg$table_columns('SIGNATURE').COLUMN_NAME, pValue => pSIGNATURE);                                            
		cg$common.val$length(pcg$table_columns => cg$table_columns, pLoc => vLoc, pColumnName => cg$table_columns('EXTERNAL_CLIENT_ID').COLUMN_NAME, pValue => pEXTERNAL_CLIENT_ID);
        cg$common.val$length(pcg$table_columns => cg$table_columns, pLoc => vLoc, pColumnName => cg$table_columns('ACC_OWNER_ID').COLUMN_NAME, pValue => pACC_OWNER_ID);
        cg$common.val$length(pcg$table_columns => cg$table_columns, pLoc => vLoc, pColumnName => cg$table_columns('DATE_SBSCRPTREQ_FORWARDED').COLUMN_NAME, pValue => pDATE_SBSCRPTREQ_FORWARDED);
        cg$common.val$length(pcg$table_columns => cg$table_columns, pLoc => vLoc, pColumnName => cg$table_columns('DATE_UNSBSCRPTREQ_FORWARDED').COLUMN_NAME, pValue => pDATE_UNSBSCRPTREQ_FORWARDED);
		cg$common.val$length(pcg$table_columns => cg$table_columns, pLoc => vLoc, pColumnName => cg$table_columns('INVOICE_TEMPLATE').COLUMN_NAME, pValue => pINVOICE_TEMPLATE);
END val$length;    

BEGIN
      cg$ind_true.ID := TRUE;
      cg$ind_true.IPR_ID := TRUE;
      cg$ind_true.END_USERS_ID := TRUE;
      cg$ind_true.USER_REFERENCE := TRUE;
      cg$ind_true.TYPE := TRUE;
      cg$ind_true.VALID := TRUE;
      cg$ind_true.DATE_SUBSCRIBED := TRUE;
      cg$ind_true.USER_COMMENT := TRUE;
      cg$ind_true.UAT_ID := TRUE;
      cg$ind_true.SIGNATURE_ID := TRUE;
      cg$ind_true.SIGNATURE := TRUE;
      cg$ind_true.EXTERNAL_CLIENT_ID := TRUE;
      cg$ind_true.ACC_OWNER_ID := TRUE;
      cg$ind_true.DATE_SBSCRPTREQ_FORWARDED := TRUE;
      cg$ind_true.DATE_UNSBSCRPTREQ_FORWARDED := TRUE;
	  cg$ind_true.INVOICE_TEMPLATE := TRUE;
   
	BEGIN                                                                                                                                                                                                   
		cg$table_columns('ID') := cg$columID;                                                                                                                                                                 
		cg$table_columns('IPR_ID') := cg$columIPR_ID;                                                                                                                                                         
		cg$table_columns('END_USERS_ID') := cg$columEND_USERS_ID;                                                                                                                                             
		cg$table_columns('USER_REFERENCE') := cg$columUSER_REFERENCE;                                                                                                                                         
		cg$table_columns('TYPE') := cg$columTYPE;                                                                                                                                                             
		cg$table_columns('VALID') := cg$columVALID;                                                                                                                                                           
		cg$table_columns('DATE_SUBSCRIBED') := cg$columDATE_SUBSCRIBED;                                                                                                                                       
		cg$table_columns('USER_COMMENT') := cg$columUSER_COMMENT;                                                                                                                                             
		cg$table_columns('UAT_ID') := cg$columUAT_ID;                                                                                                                                                         
		cg$table_columns('SIGNATURE_ID') := cg$columSIGNATURE_ID;                                                                                                                                             
		cg$table_columns('SIGNATURE') := cg$columSIGNATURE;                                                                                                                                                   
		cg$table_columns('EXTERNAL_CLIENT_ID') := cg$columEXTERNAL_CLIENT_ID;
		cg$table_columns('ACC_OWNER_ID') := cg$columACC_OWNER_ID;
		cg$table_columns('DATE_SBSCRPTREQ_FORWARDED') := cg$columDATE_SBSCRPTREQ_FORW;
		cg$table_columns('DATE_UNSBSCRPTREQ_FORWARDED') := cg$columDATE_UNSBSCRPTREQ_FORW;
		cg$table_columns('INVOICE_TEMPLATE') := cg$columINVOICE_TEMPLATE;
	END;

END cg$INVOICE_PROVIDER_CLIENTS;
/

