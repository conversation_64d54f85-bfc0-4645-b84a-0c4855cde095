
PROMPT Creating API Package Body for Table 'INVOICES'
--------------------------------------------------------------------------------
-- Name:        cg$INVOICES
-- Description: INVOICES table API package definitions
--------------------------------------------------------------------------------
create or replace PACKAGE BODY mcore.cg$INVOICES IS

/* op$isEqual(...) checks if two given records are equal or not! If they are equal
	TRUE is returned, else FALSE is returned. Works only with basic datatypes!
*/
FUNCTION op$isEqual (cg$rec_1 IN cg$row_type, cg$rec_2 IN cg$row_type, cg$ind IN cg$ind_type DEFAULT NULL, lastEvaluated OUT VARCHAR2)
RETURN BOOLEAN IS
BEGIN
	lastEvaluated := 'id';
	if cg$ind.id is null or cg$ind.id then
		if ((cg$rec_1.id is not null) or (cg$rec_2.id is not null)) then
			if cg$rec_1.id = cg$rec_2.id then null; else return false; end if;
		end if;
	end if;

	lastEvaluated := 'invoice_no';
	if cg$ind.invoice_no is null or cg$ind.invoice_no then
		if ((cg$rec_1.invoice_no is not null) or (cg$rec_2.invoice_no is not null)) then
			if cg$rec_1.invoice_no = cg$rec_2.invoice_no then null; else return false; end if;
		end if;
	end if;

	lastEvaluated := 'payment_type';
	if cg$ind.payment_type is null or cg$ind.payment_type then
		if ((cg$rec_1.payment_type is not null) or (cg$rec_2.payment_type is not null)) then
			if cg$rec_1.payment_type = cg$rec_2.payment_type then null; else return false; end if;
		end if;
	end if;

	lastEvaluated := 'status';
	if cg$ind.status is null or cg$ind.status then
		if ((cg$rec_1.status is not null) or (cg$rec_2.status is not null)) then
			if cg$rec_1.status = cg$rec_2.status then null; else return false; end if;
		end if;
	end if;

	lastEvaluated := 'description';
	if cg$ind.description is null or cg$ind.description then
		if ((cg$rec_1.description is not null) or (cg$rec_2.description is not null)) then
			if cg$rec_1.description = cg$rec_2.description then null; else return false; end if;
		end if;
	end if;

	lastEvaluated := 'tranval';
	if cg$ind.tranval is null or cg$ind.tranval then
		if ((cg$rec_1.tranval is not null) or (cg$rec_2.tranval is not null)) then
			if cg$rec_1.tranval = cg$rec_2.tranval then null; else return false; end if;
		end if;
	end if;

	lastEvaluated := 'tranval_currency_id';
	if cg$ind.tranval_currency_id is null or cg$ind.tranval_currency_id then
		if ((cg$rec_1.tranval_currency_id is not null) or (cg$rec_2.tranval_currency_id is not null)) then
			if cg$rec_1.tranval_currency_id = cg$rec_2.tranval_currency_id then null; else return false; end if;
		end if;
	end if;

	lastEvaluated := 'ipr_id';
	if cg$ind.ipr_id is null or cg$ind.ipr_id then
		if ((cg$rec_1.ipr_id is not null) or (cg$rec_2.ipr_id is not null)) then
			if cg$rec_1.ipr_id = cg$rec_2.ipr_id then null; else return false; end if;
		end if;
	end if;

	lastEvaluated := 'client_id';
	if cg$ind.client_id is null or cg$ind.client_id then
		if ((cg$rec_1.client_id is not null) or (cg$rec_2.client_id is not null)) then
			if cg$rec_1.client_id = cg$rec_2.client_id then null; else return false; end if;
		end if;
	end if;

	lastEvaluated := 'ibe_id';
	if cg$ind.ibe_id is null or cg$ind.ibe_id then
		if ((cg$rec_1.ibe_id is not null) or (cg$rec_2.ibe_id is not null)) then
			if cg$rec_1.ibe_id = cg$rec_2.ibe_id then null; else return false; end if;
		end if;
	end if;

	lastEvaluated := 'tranpay_id';
	if cg$ind.tranpay_id is null or cg$ind.tranpay_id then
		if ((cg$rec_1.tranpay_id is not null) or (cg$rec_2.tranpay_id is not null)) then
			if cg$rec_1.tranpay_id = cg$rec_2.tranpay_id then null; else return false; end if;
		end if;
	end if;

	lastEvaluated := 'date_of_processing';
	if cg$ind.date_of_processing is null or cg$ind.date_of_processing then
		if ((cg$rec_1.date_of_processing is not null) or (cg$rec_2.date_of_processing is not null)) then
			if cg$rec_1.date_of_processing = cg$rec_2.date_of_processing then null; else return false; end if;
		end if;
	end if;

	lastEvaluated := 'message';
	if cg$ind.message is null or cg$ind.message then
		if ((cg$rec_1.message is not null) or (cg$rec_2.message is not null)) then
			if dbms_lob.compare(cg$rec_1.message, cg$rec_2.message) = 0 then null; else return false; end if;
		end if;
	end if;

	lastEvaluated := 'billing_period';
	if cg$ind.billing_period is null or cg$ind.billing_period then
		if ((cg$rec_1.billing_period is not null) or (cg$rec_2.billing_period is not null)) then
			if cg$rec_1.billing_period = cg$rec_2.billing_period then null; else return false; end if;
		end if;
	end if;

	lastEvaluated := 'billing_date';
	if cg$ind.billing_date is null or cg$ind.billing_date then
		if ((cg$rec_1.billing_date is not null) or (cg$rec_2.billing_date is not null)) then
			if cg$rec_1.billing_date = cg$rec_2.billing_date then null; else return false; end if;
		end if;
	end if;

	lastEvaluated := 'payment_due_date';
	if cg$ind.payment_due_date is null or cg$ind.payment_due_date then
		if ((cg$rec_1.payment_due_date is not null) or (cg$rec_2.payment_due_date is not null)) then
			if cg$rec_1.payment_due_date = cg$rec_2.payment_due_date then null; else return false; end if;
		end if;
	end if;

	lastEvaluated := 'account_owner_id';
	if cg$ind.account_owner_id is null or cg$ind.account_owner_id then
		if ((cg$rec_1.account_owner_id is not null) or (cg$rec_2.account_owner_id is not null)) then
			if cg$rec_1.account_owner_id = cg$rec_2.account_owner_id then null; else return false; end if;
		end if;
	end if;

	lastEvaluated := 'display_on_overview';
	if cg$ind.display_on_overview is null or cg$ind.display_on_overview then
		if ((cg$rec_1.display_on_overview is not null) or (cg$rec_2.display_on_overview is not null)) then
			if cg$rec_1.display_on_overview = cg$rec_2.display_on_overview then null; else return false; end if;
		end if;
	end if;

	lastEvaluated := 'group_id';
	if cg$ind.group_id is null or cg$ind.group_id then
		if ((cg$rec_1.group_id is not null) or (cg$rec_2.group_id is not null)) then
			if cg$rec_1.group_id = cg$rec_2.group_id then null; else return false; end if;
		end if;
	end if;

	lastEvaluated := NULL;
	return true;
END op$isEqual;

PROCEDURE   validate_mandatory(cg$val_rec IN cg$row_type,
                               loc        IN VARCHAR2 DEFAULT '');
PROCEDURE   up_autogen_columns(cg$rec    IN OUT cg$row_type,
                               cg$ind    IN OUT cg$ind_type,
                               operation IN VARCHAR2 DEFAULT 'INS',
                               do_denorm IN BOOLEAN DEFAULT TRUE);
PROCEDURE   err_msg(msg  IN VARCHAR2,
                    type IN INTEGER,
                    loc  IN VARCHAR2 DEFAULT '');

--------------------------------------------------------------------------------
-- Name:        raise_uk_not_updateable
--
-- Description: Raise appropriate error when unique key updated
--
-- Parameters:  none
--------------------------------------------------------------------------------
PROCEDURE raise_uk_not_updateable(uk IN VARCHAR2) IS
BEGIN
    cg$errors.push(cg$errors.MsgGetText(cg$errors.API_UNIQUE_KEY_UPDATE, cg$errors.ERR_UK_UPDATE, uk),
                   'E',
                   'API',
                   cg$errors.API_UNIQUE_KEY_UPDATE,
                   'cg$INVOICES.raise_uk_not_updateable');
                   cg$errors.raise_failure;
END raise_uk_not_updateable;


--------------------------------------------------------------------------------
-- Name:        raise_fk_not_transferable
--
-- Description: Raise appropriate error when foreign key updated
--
-- Parameters:  none
--------------------------------------------------------------------------------
PROCEDURE raise_fk_not_transferable(fk IN VARCHAR2) IS
BEGIN
    cg$errors.push(cg$errors.MsgGetText(cg$errors.API_FOREIGN_KEY_TRANS, cg$errors.ERR_FK_TRANS, fk),
                   'E',
                   'API',
                   cg$errors.API_FOREIGN_KEY_TRANS,
                   'cg$INVOICES.raise_fk_not_transferable');
    cg$errors.raise_failure;
END raise_fk_not_transferable;


--------------------------------------------------------------------------------
-- Name:        up_autogen_columns
--
-- Description: Specific autogeneration of column values and conversion to
--              uppercase
--
-- Parameters:  cg$rec    Record of row to be manipulated
--              cg$ind    Indicators for row
--              operation Procedure where this procedure was called
--------------------------------------------------------------------------------
PROCEDURE up_autogen_columns(cg$rec IN OUT cg$row_type,
                             cg$ind IN OUT cg$ind_type,
                             operation IN VARCHAR2 DEFAULT 'INS',
                             do_denorm IN BOOLEAN DEFAULT TRUE) IS
BEGIN
  IF (operation = 'INS') THEN
    BEGIN
			IF (cg$ind.ID = FALSE
      OR  cg$rec.ID is NULL) THEN
				SELECT INVOICES_SEQ.nextval
				INTO   cg$rec.ID
				FROM   DUAL;
				cg$ind.ID := TRUE;
			END IF;
    EXCEPTION WHEN others THEN
      cg$errors.push(SQLERRM, 'E', 'ORA', SQLCODE,
                     'cg$INVOICES.up_autogen.ID.OTHERS');
      cg$errors.raise_failure;
    END;
    NULL;
  ELSE      -- (operation = 'UPD')
    NULL;
  END IF;   -- (operation = 'INS') ELSE (operation = 'UPD')

  -- Statements executed for both 'INS' and 'UPD'


EXCEPTION
  WHEN no_data_found THEN
    NULL;
  WHEN others THEN
    cg$errors.push( SQLERRM, 'E', 'ORA', SQLCODE,
                    'cg$INVOICES.up_autogen_columns');
    cg$errors.raise_failure;
END up_autogen_columns;


--------------------------------------------------------------------------------
-- Name:        validate_mandatory
--
-- Description: Checks all mandatory columns are not null and raises appropriate
--              error if not satisfied
--
-- Parameters:  cg$val_rec Record of row to be checked
--              loc        Place where this procedure was called for error
--                         trapping
--------------------------------------------------------------------------------
PROCEDURE validate_mandatory(cg$val_rec IN cg$row_type,
                             loc        IN VARCHAR2 DEFAULT '') IS
BEGIN
    IF (cg$val_rec.ID IS NULL) THEN
        cg$errors.push(cg$errors.MsgGetText(cg$errors.API_MAND_COLUMN_ISNULL, cg$errors.VAL_MAND, P10ID),
                       'E',
                       'API',
                       cg$errors.API_MAND_COLUMN_ISNULL,
                       loc);
    END IF;
    IF (cg$val_rec.INVOICE_NO IS NULL) THEN
        cg$errors.push(cg$errors.MsgGetText(cg$errors.API_MAND_COLUMN_ISNULL, cg$errors.VAL_MAND, P20INVOICE_NO),
                       'E',
                       'API',
                       cg$errors.API_MAND_COLUMN_ISNULL,
                       loc);
    END IF;
    IF (cg$val_rec.PAYMENT_TYPE IS NULL) THEN
        cg$errors.push(cg$errors.MsgGetText(cg$errors.API_MAND_COLUMN_ISNULL, cg$errors.VAL_MAND, P30PAYMENT_TYPE),
                       'E',
                       'API',
                       cg$errors.API_MAND_COLUMN_ISNULL,
                       loc);
    END IF;
    IF (cg$val_rec.STATUS IS NULL) THEN
        cg$errors.push(cg$errors.MsgGetText(cg$errors.API_MAND_COLUMN_ISNULL, cg$errors.VAL_MAND, P40STATUS),
                       'E',
                       'API',
                       cg$errors.API_MAND_COLUMN_ISNULL,
                       loc);
    END IF;
    IF (cg$val_rec.DESCRIPTION IS NULL) THEN
        cg$errors.push(cg$errors.MsgGetText(cg$errors.API_MAND_COLUMN_ISNULL, cg$errors.VAL_MAND, P50DESCRIPTION),
                       'E',
                       'API',
                       cg$errors.API_MAND_COLUMN_ISNULL,
                       loc);
    END IF;
    IF (cg$val_rec.TRANVAL IS NULL) THEN
        cg$errors.push(cg$errors.MsgGetText(cg$errors.API_MAND_COLUMN_ISNULL, cg$errors.VAL_MAND, P60TRANVAL),
                       'E',
                       'API',
                       cg$errors.API_MAND_COLUMN_ISNULL,
                       loc);
    END IF;
    IF (cg$val_rec.TRANVAL_CURRENCY_ID IS NULL) THEN
        cg$errors.push(cg$errors.MsgGetText(cg$errors.API_MAND_COLUMN_ISNULL, cg$errors.VAL_MAND, P70TRANVAL_CURRENCY_ID),
                       'E',
                       'API',
                       cg$errors.API_MAND_COLUMN_ISNULL,
                       loc);
    END IF;
    IF (cg$val_rec.IPR_ID IS NULL) THEN
        cg$errors.push(cg$errors.MsgGetText(cg$errors.API_MAND_COLUMN_ISNULL, cg$errors.VAL_MAND, P80IPR_ID),
                       'E',
                       'API',
                       cg$errors.API_MAND_COLUMN_ISNULL,
                       loc);
    END IF;
    IF (cg$val_rec.DISPLAY_ON_OVERVIEW IS NULL) THEN
        cg$errors.push(cg$errors.MsgGetText(cg$errors.API_MAND_COLUMN_ISNULL, cg$errors.VAL_MAND, P190DISPLAY_ON_OVERVIEW),
                       'E',
                       'API',
                       cg$errors.API_MAND_COLUMN_ISNULL,
                       loc);
    END IF;
    NULL;
END validate_mandatory;


--------------------------------------------------------------------------------
-- Name:        validate_foreign_keys
--
-- Description: Checks all mandatory columns are not null and raises appropriate
--              error if not satisfied
--
-- Parameters:  cg$rec Record of row to be checked
--------------------------------------------------------------------------------
PROCEDURE validate_foreign_keys_ins(cg$rec IN cg$row_type) IS
    fk_check INTEGER;
BEGIN
NULL;
END;

PROCEDURE validate_foreign_keys_upd( cg$rec IN cg$row_type,
                                     cg$old_rec IN cg$row_type,
                                     cg$ind IN cg$ind_type) IS
    fk_check INTEGER;
BEGIN
NULL;
END;

PROCEDURE validate_foreign_keys_del(cg$rec IN cg$row_type) IS
    fk_check INTEGER;
BEGIN
NULL;
END;


--------------------------------------------------------------------------------
-- Name:        slct
--
-- Description: Selects into the given parameter all the attributes for the row
--              given by the primary key
--
-- Parameters:  cg$sel_rec  Record of row to be selected into using its PK
--------------------------------------------------------------------------------
PROCEDURE slct(cg$sel_rec IN OUT cg$row_type) IS

BEGIN

    IF cg$sel_rec.the_rowid is null THEN
       SELECT    ID
       ,         INVOICE_NO
       ,         PAYMENT_TYPE
       ,         STATUS
       ,         DESCRIPTION
       ,         TRANVAL
       ,         TRANVAL_CURRENCY_ID
       ,         IPR_ID
       ,         CLIENT_ID
       ,         IBE_ID
       ,         TRANPAY_ID
       ,         DATE_OF_PROCESSING
       ,         MESSAGE
       ,         BILLING_PERIOD
       ,         BILLING_DATE
       ,         PAYMENT_DUE_DATE
       ,         ACCOUNT_OWNER_ID
       ,         DISPLAY_ON_OVERVIEW
       ,         GROUP_ID
       , rowid
       INTO      cg$sel_rec.ID
       ,         cg$sel_rec.INVOICE_NO
       ,         cg$sel_rec.PAYMENT_TYPE
       ,         cg$sel_rec.STATUS
       ,         cg$sel_rec.DESCRIPTION
       ,         cg$sel_rec.TRANVAL
       ,         cg$sel_rec.TRANVAL_CURRENCY_ID
       ,         cg$sel_rec.IPR_ID
       ,         cg$sel_rec.CLIENT_ID
       ,         cg$sel_rec.IBE_ID
       ,         cg$sel_rec.TRANPAY_ID
       ,         cg$sel_rec.DATE_OF_PROCESSING
       ,         cg$sel_rec.MESSAGE
       ,         cg$sel_rec.BILLING_PERIOD
       ,         cg$sel_rec.BILLING_DATE
       ,         cg$sel_rec.PAYMENT_DUE_DATE
       ,         cg$sel_rec.ACCOUNT_OWNER_ID
       ,         cg$sel_rec.DISPLAY_ON_OVERVIEW
       ,         cg$sel_rec.GROUP_ID
       ,cg$sel_rec.the_rowid
       FROM   INVOICES
       WHERE        ID = cg$sel_rec.ID;
    ELSE
       SELECT    ID
       ,         INVOICE_NO
       ,         PAYMENT_TYPE
       ,         STATUS
       ,         DESCRIPTION
       ,         TRANVAL
       ,         TRANVAL_CURRENCY_ID
       ,         IPR_ID
       ,         CLIENT_ID
       ,         IBE_ID
       ,         TRANPAY_ID
       ,         DATE_OF_PROCESSING
       ,         MESSAGE
       ,         BILLING_PERIOD
       ,         BILLING_DATE
       ,         PAYMENT_DUE_DATE
       ,         ACCOUNT_OWNER_ID
       ,         DISPLAY_ON_OVERVIEW
       ,         GROUP_ID
       , rowid
       INTO      cg$sel_rec.ID
       ,         cg$sel_rec.INVOICE_NO
       ,         cg$sel_rec.PAYMENT_TYPE
       ,         cg$sel_rec.STATUS
       ,         cg$sel_rec.DESCRIPTION
       ,         cg$sel_rec.TRANVAL
       ,         cg$sel_rec.TRANVAL_CURRENCY_ID
       ,         cg$sel_rec.IPR_ID
       ,         cg$sel_rec.CLIENT_ID
       ,         cg$sel_rec.IBE_ID
       ,         cg$sel_rec.TRANPAY_ID
       ,         cg$sel_rec.DATE_OF_PROCESSING
       ,         cg$sel_rec.MESSAGE
       ,         cg$sel_rec.BILLING_PERIOD
       ,         cg$sel_rec.BILLING_DATE
       ,         cg$sel_rec.PAYMENT_DUE_DATE
       ,         cg$sel_rec.ACCOUNT_OWNER_ID
       ,         cg$sel_rec.DISPLAY_ON_OVERVIEW
       ,         cg$sel_rec.GROUP_ID
       ,cg$sel_rec.the_rowid
       FROM   INVOICES
       WHERE  rowid = cg$sel_rec.the_rowid;
    END IF;

EXCEPTION WHEN OTHERS THEN
    cg$errors.push(SQLERRM,
                   'E',
                   'ORA',
                   SQLCODE,
                   'cg$INVOICES.slct.others');
    cg$errors.raise_failure;

END slct;


--------------------------------------------------------------------------------
-- Name:        cascade_update
--
-- Description: Updates all child tables affected by a change to INVOICES
--
-- Parameters:  cg$rec     Record of INVOICES current values
--              cg$old_rec Record of INVOICES previous values
--------------------------------------------------------------------------------
PROCEDURE cascade_update(cg$new_rec IN OUT cg$row_type,
                         cg$old_rec IN     cg$row_type) IS
BEGIN
  NULL;
END cascade_update;


--------------------------------------------------------------------------------
-- Name:        validate_domain_cascade_update
--
-- Description: Implement the Domain Key Constraint Cascade Updates Resticts rule
--              of each child table that references this tableINVOICES
--
-- Parameters:  cg$old_rec     Record of INVOICES current values
--------------------------------------------------------------------------------
PROCEDURE validate_domain_cascade_update( cg$old_rec IN cg$row_type ) IS
  dk_check INTEGER;
BEGIN
  NULL;
END validate_domain_cascade_update;


-----------------------------------------------------------------------------------------
-- Name:        domain_cascade_update
--
-- Description: Implement the Domain Key Constraint Cascade Updates rules of each
--              child table that references this table INVOICES
--
-- Parameters:  cg$new_rec  New values for INVOICES's domain key constraint columns
--              cg$new_ind  Indicates changed INVOICES's domain key constraint columns
--              cg$old_rec  Current values for INVOICES's domain key constraint columns
-----------------------------------------------------------------------------------------
PROCEDURE domain_cascade_update(cg$new_rec IN OUT cg$row_type,
                                cg$new_ind IN OUT cg$ind_type,
                                cg$old_rec IN     cg$row_type) IS
BEGIN
  NULL;
END domain_cascade_update;


--------------------------------------------------------------------------------
-- Name:        cascade_delete
--
-- Description: Delete all child tables affected by a delete to INVOICES
--
-- Parameters:  cg$rec     Record of INVOICES current values
--------------------------------------------------------------------------------
PROCEDURE cascade_delete(cg$old_rec IN OUT cg$row_type)
IS
BEGIN
  NULL;
END cascade_delete;

--------------------------------------------------------------------------------
-- Name:        domain_cascade_delete
--
-- Description: Implement the Domain Key Constraint Cascade Delete rules of each
--              child table that references this tableINVOICES
--
-- Parameters:  cg$old_rec     Record of INVOICES current values
--------------------------------------------------------------------------------
PROCEDURE domain_cascade_delete( cg$old_rec IN cg$row_type )
IS
BEGIN
  NULL;
END domain_cascade_delete;


--------------------------------------------------------------------------------
-- Name:        validate_domain_cascade_delete
--
-- Description: Implement the Domain Key Constraint Cascade Delete Restricts rule
--              of each child table that references this tableINVOICES
--
-- Parameters:  cg$old_rec     Record of INVOICES current values
--------------------------------------------------------------------------------
PROCEDURE validate_domain_cascade_delete(cg$old_rec IN cg$row_type)
IS
    dk_check INTEGER;
BEGIN
  NULL;
END validate_domain_cascade_delete;



--------------------------------------------------------------------------------
-- Name:        validate_arc
--
-- Description: Checks for adherence to arc relationship
--
-- Parameters:  cg$rec     Record of INVOICES current values
--------------------------------------------------------------------------------
PROCEDURE validate_arc(cg$rec IN OUT cg$row_type) IS
i NUMBER;
BEGIN
    NULL;
END validate_arc;


--------------------------------------------------------------------------------
-- Name:        validate_domain
--
-- Description: Checks against reference table for values lying in a domain
--
-- Parameters:  cg$rec     Record of INVOICES current values
--------------------------------------------------------------------------------
PROCEDURE validate_domain(cg$rec IN OUT cg$row_type,
                          cg$ind IN cg$ind_type DEFAULT cg$ind_true)
IS
  dummy NUMBER;
  found BOOLEAN;
  no_tabview EXCEPTION;
  PRAGMA EXCEPTION_INIT(no_tabview, -942);
BEGIN

   NULL;

EXCEPTION
    WHEN cg$errors.cg$error THEN
        cg$errors.raise_failure;
    WHEN no_tabview THEN
        cg$errors.push(cg$errors.MsgGetText(cg$errors.API_RV_TAB_NOT_FOUND,
                                            cg$errors.APIMSG_RV_TAB_NOT_FOUND,
                                            'CG_REF_CODES','INVOICES'),
                       'E',
                       'API',
                       cg$errors.API_RV_TAB_NOT_FOUND,
                       'cg$INVOICES.v_domain.no_reftable_found');
        cg$errors.raise_failure;
    WHEN OTHERS THEN
        cg$errors.push(SQLERRM,
                       'E',
                       'ORA',
                       SQLCODE,
                       'cg$INVOICES.v_domain.others');
        cg$errors.raise_failure;
END validate_domain;


--------------------------------------------------------------------------------
-- Name:        err_msg
--
-- Description: Pushes onto stack appropriate user defined error message
--              depending on the rule violated
--
-- Parameters:  msg     Oracle error message
--              type    Type of violation e.g. check_constraint: ERR_CHECK_CON
--              loc     Place where this procedure was called for error
--                      trapping
--------------------------------------------------------------------------------
PROCEDURE err_msg(msg   IN VARCHAR2,
                  type  IN INTEGER,
                  loc   IN VARCHAR2 DEFAULT '') IS
con_name VARCHAR2(240);
BEGIN
    con_name := cg$errors.parse_constraint(msg, type);
    IF (con_name = 'INE_PK') THEN
        cg$errors.push(nvl(INE_PK
                  ,cg$errors.MsgGetText(cg$errors.API_PK_CON_VIOLATED
					                 ,cg$errors.APIMSG_PK_VIOLAT
                                     ,'INE_PK'
                                     ,'INVOICES')),
                       'E',
                       'API',
                       cg$errors.API_PK_CON_VIOLATED,
                       loc);

    ELSIF (con_name = 'INE_TRANPAY_FK') THEN
        cg$errors.push(nvl(INE_TRANPAY_FK
                      ,cg$errors.MsgGetText(cg$errors.API_FK_CON_VIOLATED
					                 ,cg$errors.APIMSG_FK_VIOLAT
                                     ,'INE_TRANPAY_FK'
                                     ,'INVOICES')),
                       'E',
                       'API',
                       cg$errors.API_FK_CON_VIOLATED,
                       loc);
    ELSIF (con_name = 'INE_CLIENT_FK') THEN
        cg$errors.push(nvl(INE_CLIENT_FK
                      ,cg$errors.MsgGetText(cg$errors.API_FK_CON_VIOLATED
					                 ,cg$errors.APIMSG_FK_VIOLAT
                                     ,'INE_CLIENT_FK'
                                     ,'INVOICES')),
                       'E',
                       'API',
                       cg$errors.API_FK_CON_VIOLATED,
                       loc);
    ELSIF (con_name = 'INE_RTE_FK') THEN
        cg$errors.push(nvl(INE_RTE_FK
                      ,cg$errors.MsgGetText(cg$errors.API_FK_CON_VIOLATED
					                 ,cg$errors.APIMSG_FK_VIOLAT
                                     ,'INE_RTE_FK'
                                     ,'INVOICES')),
                       'E',
                       'API',
                       cg$errors.API_FK_CON_VIOLATED,
                       loc);
    ELSIF (con_name = 'INE_IPR_FK') THEN
        cg$errors.push(nvl(INE_IPR_FK
                      ,cg$errors.MsgGetText(cg$errors.API_FK_CON_VIOLATED
					                 ,cg$errors.APIMSG_FK_VIOLAT
                                     ,'INE_IPR_FK'
                                     ,'INVOICES')),
                       'E',
                       'API',
                       cg$errors.API_FK_CON_VIOLATED,
                       loc);
    ELSIF (con_name = 'INE_CUE_FK') THEN
        cg$errors.push(nvl(INE_CUE_FK
                      ,cg$errors.MsgGetText(cg$errors.API_FK_CON_VIOLATED
					                 ,cg$errors.APIMSG_FK_VIOLAT
                                     ,'INE_CUE_FK'
                                     ,'INVOICES')),
                       'E',
                       'API',
                       cg$errors.API_FK_CON_VIOLATED,
                       loc);
    ELSIF (con_name = 'INE_IBE_FK') THEN
        cg$errors.push(nvl(INE_IBE_FK
                      ,cg$errors.MsgGetText(cg$errors.API_FK_CON_VIOLATED
					                 ,cg$errors.APIMSG_FK_VIOLAT
                                     ,'INE_IBE_FK'
                                     ,'INVOICES')),
                       'E',
                       'API',
                       cg$errors.API_FK_CON_VIOLATED,
                       loc);
    ELSIF (con_name = 'INE_ACC_OWNER_FK') THEN
        cg$errors.push(nvl(INE_ACC_OWNER_FK
                      ,cg$errors.MsgGetText(cg$errors.API_FK_CON_VIOLATED
					                 ,cg$errors.APIMSG_FK_VIOLAT
                                     ,'INE_ACC_OWNER_FK'
                                     ,'INVOICES')),
                       'E',
                       'API',
                       cg$errors.API_FK_CON_VIOLATED,
                       loc);
    ELSE
        cg$errors.push(SQLERRM,
                       'E',
                       'ORA',
                       SQLCODE,
                       loc);
    END IF;
END err_msg;


--------------------------------------------------------------------------------
-- Name:        insert_jn
--
-- Description: Insert a record into the journal table for auditing purposes
--
-- Parameters:  cg$rec    Record of row to be journalled
--              operation Type of action to be journalled for this record
--------------------------------------------------------------------------------
PROCEDURE insert_jn(cg$rec    IN cg$row_type,
                    operation IN VARCHAR2 DEFAULT 'INS') IS
BEGIN
    INSERT INTO INVOICES_JN
        (JN_OPERATION
        ,JN_ORACLE_USER
        ,JN_DATETIME
        ,JN_NOTES
        ,JN_APPLN
        ,JN_SESSION
        ,ID
        ,INVOICE_NO
        ,PAYMENT_TYPE
        ,STATUS
        ,DESCRIPTION
        ,TRANVAL
        ,TRANVAL_CURRENCY_ID
        ,IPR_ID
        ,CLIENT_ID
        ,IBE_ID
        ,TRANPAY_ID
        ,DATE_OF_PROCESSING
        ,MESSAGE
        ,BILLING_PERIOD
        ,BILLING_DATE
        ,PAYMENT_DUE_DATE
        ,ACCOUNT_OWNER_ID
        ,DISPLAY_ON_OVERVIEW
        ,GROUP_ID)
    VALUES
        (operation
        ,NVL(SYS_CONTEXT('APISESSMGMT', 'APICALL_USERNAME'), USER)
        ,SYSDATE
        ,cg$rec.jn_notes
        ,'CG$INVOICES.'||operation
        ,userenv('sessionid')
        ,cg$rec.ID
        ,cg$rec.INVOICE_NO
        ,cg$rec.PAYMENT_TYPE
        ,cg$rec.STATUS
        ,cg$rec.DESCRIPTION
        ,cg$rec.TRANVAL
        ,cg$rec.TRANVAL_CURRENCY_ID
        ,cg$rec.IPR_ID
        ,cg$rec.CLIENT_ID
        ,cg$rec.IBE_ID
        ,cg$rec.TRANPAY_ID
        ,cg$rec.DATE_OF_PROCESSING
        ,cg$rec.MESSAGE
        ,cg$rec.BILLING_PERIOD
        ,cg$rec.BILLING_DATE
        ,cg$rec.PAYMENT_DUE_DATE
        ,cg$rec.ACCOUNT_OWNER_ID
        ,cg$rec.DISPLAY_ON_OVERVIEW
        ,cg$rec.GROUP_ID);
EXCEPTION
    WHEN OTHERS THEN
        cg$errors.push(SQLERRM,
                      'E',
                      'ORA',
                      SQLCODE,
                      'cg$INVOICES.insert_jn_'||operation||'.others');
        cg$errors.raise_failure;
END insert_jn;


--------------------------------------------------------------------------------
-- Name:        doLobs
--
-- Description: This function is updating lob columns
--
-- Parameters:  cg$rec  Record of row to be inserted
--              cg$ind  Record of columns specifically set
--------------------------------------------------------------------------------
PROCEDURE doLobs(cg$rec IN OUT cg$row_type,
                 cg$ind IN OUT cg$ind_type) IS
BEGIN
   IF(cg$rec.MESSAGE is not null) THEN
      DECLARE
         lobVar CLOB;
         lobSize INTEGER;
         existingLobSize INTEGER;
      BEGIN
         IF cg$rec.MESSAGE IS NOT NULL THEN
          lobSize := DBMS_LOB.GETLENGTH(cg$rec.MESSAGE);
         ELSE
          lobSize := 0;
         END IF;
         
          SELECT MESSAGE INTO lobVar
          FROM INVOICES
          WHERE ID = cg$rec.ID;
         
          IF lobSize < 1 THEN -- Novi prazan
            existingLobSize := DBMS_LOB.GETLENGTH(lobVar);
            IF existingLobSize > 0 THEN -- Stari bio, pa ga treba obrisati
              DBMS_LOB.ERASE(lobVar, existingLobSize);          
            END IF;
          ELSE
            DBMS_LOB.COPY(lobVar, cg$rec.MESSAGE, lobSize);
          END IF;
      END;
   END IF;
   NULL;
END doLobs;


--------------------------------------------------------------------------------
-- Name:        ins
--
-- Description: API insert procedure
--
-- Parameters:  cg$rec  Record of row to be inserted
--              cg$ind  Record of columns specifically set
--              do_ins  Whether we want the actual INSERT to occur
--------------------------------------------------------------------------------
PROCEDURE ins(cg$rec IN OUT cg$row_type,
              cg$ind IN OUT cg$ind_type,
              do_ins IN BOOLEAN DEFAULT TRUE) IS
cg$tmp_rec cg$row_type;

--  Constant default values

D40_STATUS CONSTANT INVOICES.STATUS%TYPE := 0;
D190_DISPLAY_ON_OVERVIEW CONSTANT INVOICES.DISPLAY_ON_OVERVIEW%TYPE := 1;
vEnabledPushNotif BOOLEAN;
vInvoiceObjectData obj$invoice;
vIprName invoice_providers.name%TYPE;
BEGIN
--  Application_logic Pre-Insert <<Start>>
--  Application_logic Pre-Insert << End >>

--  Defaulted

    IF NOT (cg$ind.STATUS) THEN cg$rec.STATUS := D40_STATUS; END IF;
    IF NOT (cg$ind.DISPLAY_ON_OVERVIEW) THEN cg$rec.DISPLAY_ON_OVERVIEW := D190_DISPLAY_ON_OVERVIEW; END IF;
--  Auto-generated and uppercased columns

    up_autogen_columns(cg$rec, cg$ind, 'INS', do_ins);

    called_from_package := TRUE;

    IF (do_ins) THEN
        validate_foreign_keys_ins(cg$rec);
        validate_arc(cg$rec);
        validate_domain(cg$rec);

        INSERT INTO INVOICES
            (ID
            ,INVOICE_NO
            ,PAYMENT_TYPE
            ,STATUS
            ,DESCRIPTION
            ,TRANVAL
            ,TRANVAL_CURRENCY_ID
            ,IPR_ID
            ,CLIENT_ID
            ,IBE_ID
            ,TRANPAY_ID
            ,DATE_OF_PROCESSING
            ,MESSAGE
            ,BILLING_PERIOD
            ,BILLING_DATE
            ,PAYMENT_DUE_DATE
            ,ACCOUNT_OWNER_ID
            ,DISPLAY_ON_OVERVIEW
            ,GROUP_ID)
        VALUES
            (cg$rec.ID
            ,cg$rec.INVOICE_NO
            ,cg$rec.PAYMENT_TYPE
            ,cg$rec.STATUS
            ,cg$rec.DESCRIPTION
            ,cg$rec.TRANVAL
            ,cg$rec.TRANVAL_CURRENCY_ID
            ,cg$rec.IPR_ID
            ,cg$rec.CLIENT_ID
            ,cg$rec.IBE_ID
            ,cg$rec.TRANPAY_ID
            ,cg$rec.DATE_OF_PROCESSING
            ,EMPTY_CLOB()
            ,cg$rec.BILLING_PERIOD
            ,cg$rec.BILLING_DATE
            ,cg$rec.PAYMENT_DUE_DATE
            ,cg$rec.ACCOUNT_OWNER_ID
            ,cg$rec.DISPLAY_ON_OVERVIEW
            ,cg$rec.GROUP_ID
);
        doLobs(cg$rec, cg$ind);
        slct(cg$rec);

        upd_oper_denorm2(cg$rec, cg$tmp_rec, cg$ind, 'INS');
    END IF;

    called_from_package := FALSE;


    insert_jn(cg$rec, 'INS');
	
	vEnabledPushNotif := sspkg.readBool('/Core/Main/InvoiceManagement/PushNotifications/enabled');

	IF vEnabledPushNotif THEN 
	
		BEGIN
			SELECT name INTO vIprName 
			FROM invoice_providers
			WHERE id = cg$rec.IPR_ID;
		EXCEPTION
			WHEN OTHERS THEN
				slog.error('/Core/Main/InvoiceManagement', 'CG$INVOICES',  sqlcode||':'||sqlerrm);
		END;
		
		vInvoiceObjectData := new obj$invoice(cg$rec.ID);
		
		vInvoiceObjectData.STATUS := cg$rec.STATUS;
		vInvoiceObjectData.PROVIDER_ID := cg$rec.IPR_ID;
		vInvoiceObjectData.PROVIDER_NAME := vIprName;
		vInvoiceObjectData.INVOICE_NO := cg$rec.INVOICE_NO;
		vInvoiceObjectData.TRANVAL := cg$rec.TRANVAL;
		vInvoiceObjectData.CURRENCY := cg$rec.TRANVAL_CURRENCY_ID;
		vInvoiceObjectData.DESCRIPTION := cg$rec.DESCRIPTION;
		vInvoiceObjectData.BILLING_PERIOD := cg$rec.BILLING_PERIOD;
		vInvoiceObjectData.BILLING_DATE := cg$rec.BILLING_DATE;
		vInvoiceObjectData.PAYMENT_DUE_DATE := cg$rec.PAYMENT_DUE_DATE;

		strmadmin.EnqueueUploadInvoiceNtfMsg(vInvoiceObjectData);

	END IF;

	-- Process auto payment if enabled
	BEGIN
		mcore.invoice_mgmt_pck.ProcessAutoPayment(pInvoiceId => cg$rec.ID);
	EXCEPTION
		WHEN OTHERS THEN
			slog.error('/Core/Main/InvoiceManagement', 'CG$INVOICES', 'Error processing auto payment: ' || SQLERRM);
	END;
--  Application logic Post-Insert <<Start>>
--  Application logic Post-Insert << End >>

EXCEPTION
    WHEN cg$errors.cg$error THEN
        called_from_package := FALSE;
        cg$errors.raise_failure;
    WHEN cg$errors.mandatory_missing THEN
        validate_mandatory(cg$rec, 'cg$INVOICES.ins.mandatory_missing');
        called_from_package := FALSE;
        cg$errors.raise_failure;
    WHEN cg$errors.check_violation THEN
        err_msg(SQLERRM, cg$errors.ERR_CHECK_CON, 'cg$INVOICES.ins.check_violation');
        called_from_package := FALSE;
        cg$errors.raise_failure;
    WHEN cg$errors.fk_violation THEN
        err_msg(SQLERRM, cg$errors.ERR_FOREIGN_KEY, 'cg$INVOICES.ins.fk_violation');
        called_from_package := FALSE;
        cg$errors.raise_failure;
    WHEN cg$errors.uk_violation THEN
        err_msg(SQLERRM, cg$errors.ERR_UNIQUE_KEY, 'cg$INVOICES.ins.uk_violation');
        called_from_package := FALSE;
        cg$errors.raise_failure;
    WHEN OTHERS THEN
        cg$errors.push(SQLERRM,
                       'E',
                       'ORA',
                       SQLCODE,
                       'cg$INVOICES.ins.others');
        called_from_package := FALSE;
        cg$errors.raise_failure;
END ins;


--------------------------------------------------------------------------------
-- Name:        upd
--
-- Description: API update procedure
--
-- Parameters:  cg$rec  Record of row to be updated
--              cg$ind  Record of columns specifically set
--              do_upd  Whether we want the actual UPDATE to occur
--------------------------------------------------------------------------------
PROCEDURE upd(cg$rec             IN OUT cg$row_type,
              cg$ind             IN OUT cg$ind_type,
              do_upd             IN BOOLEAN DEFAULT TRUE,
              cg$pk              IN cg$row_type DEFAULT NULL )
IS
  cg$upd_rec    cg$row_type;
  cg$old_rec    cg$row_type;
  RECORD_LOGGED BOOLEAN := FALSE;
  pUnit VARCHAR2(240) := 'cg$invoices.upd';
  do_upd_custom BOOLEAN := do_upd;
  lastEvaluated VARCHAR2(30);
BEGIN
--  Application_logic Pre-Update <<Start>>
--  Application_logic Pre-Update << End >>


    IF ( cg$pk.ID IS NULL ) THEN
      cg$upd_rec.ID := cg$rec.ID;
    ELSE
      cg$upd_rec.ID := cg$pk.ID;
    END IF;
    cg$old_rec.ID := cg$upd_rec.ID;

    IF ( cg$pk.the_rowid IS NULL ) THEN
      cg$upd_rec.the_rowid := cg$rec.the_rowid;
    ELSE
      cg$upd_rec.the_rowid := cg$pk.the_rowid;
    END IF;
    cg$old_rec.the_rowid := cg$upd_rec.the_rowid;

    IF ( do_upd_custom ) THEN

        slct(cg$upd_rec);

           --  Report error if attempt to update non transferable Foreign Key INE_IPR_FK
        IF (cg$ind.IPR_ID AND cg$rec.IPR_ID != cg$upd_rec.IPR_ID) THEN
            raise_fk_not_transferable('INE_IPR_FK');
        END IF;
        IF (cg$ind.IPR_ID AND cg$rec.IPR_ID IS NULL AND cg$upd_rec.IPR_ID IS NOT NULL) THEN
            raise_fk_not_transferable('INE_IPR_FK');
        END IF;
        IF (cg$ind.IPR_ID AND cg$rec.IPR_ID IS NOT NULL AND cg$upd_rec.IPR_ID IS NULL) THEN
            raise_fk_not_transferable('INE_IPR_FK');
        END IF;

        --  Report error if attempt to update non updateable Primary Key INE_PK
        IF (cg$ind.ID AND cg$rec.ID != cg$upd_rec.ID) THEN
            raise_uk_not_updateable('INE_PK');
        END IF;
        IF NOT (cg$ind.ID) THEN
            cg$rec.ID := cg$upd_rec.ID;
        END IF;
        IF NOT (cg$ind.INVOICE_NO) THEN
            cg$rec.INVOICE_NO := cg$upd_rec.INVOICE_NO;
        END IF;
        IF NOT (cg$ind.PAYMENT_TYPE) THEN
            cg$rec.PAYMENT_TYPE := cg$upd_rec.PAYMENT_TYPE;
        END IF;
        IF NOT (cg$ind.STATUS) THEN
            cg$rec.STATUS := cg$upd_rec.STATUS;
        END IF;
        IF NOT (cg$ind.DESCRIPTION) THEN
            cg$rec.DESCRIPTION := cg$upd_rec.DESCRIPTION;
        END IF;
        IF NOT (cg$ind.TRANVAL) THEN
            cg$rec.TRANVAL := cg$upd_rec.TRANVAL;
        END IF;
        IF NOT (cg$ind.TRANVAL_CURRENCY_ID) THEN
            cg$rec.TRANVAL_CURRENCY_ID := cg$upd_rec.TRANVAL_CURRENCY_ID;
        END IF;
        IF NOT (cg$ind.IPR_ID) THEN
            cg$rec.IPR_ID := cg$upd_rec.IPR_ID;
        END IF;
        IF NOT (cg$ind.CLIENT_ID) THEN
            cg$rec.CLIENT_ID := cg$upd_rec.CLIENT_ID;
        END IF;
        IF NOT (cg$ind.IBE_ID) THEN
            cg$rec.IBE_ID := cg$upd_rec.IBE_ID;
        END IF;
        IF NOT (cg$ind.TRANPAY_ID) THEN
            cg$rec.TRANPAY_ID := cg$upd_rec.TRANPAY_ID;
        END IF;
        IF NOT (cg$ind.DATE_OF_PROCESSING) THEN
            cg$rec.DATE_OF_PROCESSING := cg$upd_rec.DATE_OF_PROCESSING;
        END IF;
        IF NOT (cg$ind.MESSAGE) THEN
            cg$rec.MESSAGE := cg$upd_rec.MESSAGE;
        END IF;
        IF NOT (cg$ind.BILLING_PERIOD) THEN
            cg$rec.BILLING_PERIOD := cg$upd_rec.BILLING_PERIOD;
        END IF;
        IF NOT (cg$ind.BILLING_DATE) THEN
            cg$rec.BILLING_DATE := cg$upd_rec.BILLING_DATE;
        END IF;
        IF NOT (cg$ind.PAYMENT_DUE_DATE) THEN
            cg$rec.PAYMENT_DUE_DATE := cg$upd_rec.PAYMENT_DUE_DATE;
        END IF;
        IF NOT (cg$ind.ACCOUNT_OWNER_ID) THEN
            cg$rec.ACCOUNT_OWNER_ID := cg$upd_rec.ACCOUNT_OWNER_ID;
        END IF;
        IF NOT (cg$ind.DISPLAY_ON_OVERVIEW) THEN
            cg$rec.DISPLAY_ON_OVERVIEW := cg$upd_rec.DISPLAY_ON_OVERVIEW;
        END IF;
        IF NOT (cg$ind.GROUP_ID) THEN
            cg$rec.GROUP_ID := cg$upd_rec.GROUP_ID;
        END IF;
    ELSE
	     -- Perform checks if called from a trigger
	     -- Indicators are only set on changed values
	     null;
           --  Report error if attempt to update non transferable Foreign Key INE_IPR_FK
        IF ( cg$ind.IPR_ID ) THEN
          raise_fk_not_transferable('INE_IPR_FK');
        END IF;
        --  Report error if attempt to update non updateable Primary Key INE_PK
        IF ( cg$ind.ID ) THEN
          raise_uk_not_updateable('INE_PK');
        END IF;
    END IF;

    up_autogen_columns(cg$rec, cg$ind, 'UPD', do_upd_custom);  --  Auto-generated and uppercased columns
    IF op$isEqual (cg$rec_1 => cg$rec, cg$rec_2 => cg$upd_rec, cg$ind => cg$ind, lastEvaluated => lastEvaluated) THEN
       cg$common.writeCustomLog(cg$rec.ID || ':No changes. Skip!', pUnit, 'PROCESSING');
       do_upd_custom := FALSE;
    END IF;

--  Now do update if updateable columns exist
    IF (do_upd_custom) THEN
        DECLARE
            called_from BOOLEAN := called_from_package;
        BEGIN
          called_from_package := TRUE;

          slct(cg$old_rec);
          validate_foreign_keys_upd(cg$rec, cg$old_rec, cg$ind);
          validate_arc(cg$rec);
          validate_domain(cg$rec, cg$ind);
          validate_domain_cascade_update(cg$old_rec);

          IF cg$rec.the_rowid is null THEN
            UPDATE INVOICES
            SET
              INVOICE_NO = cg$rec.INVOICE_NO
              ,PAYMENT_TYPE = cg$rec.PAYMENT_TYPE
              ,STATUS = cg$rec.STATUS
              ,DESCRIPTION = cg$rec.DESCRIPTION
              ,TRANVAL = cg$rec.TRANVAL
              ,TRANVAL_CURRENCY_ID = cg$rec.TRANVAL_CURRENCY_ID
              ,IPR_ID = cg$rec.IPR_ID
              ,CLIENT_ID = cg$rec.CLIENT_ID
              ,IBE_ID = cg$rec.IBE_ID
              ,TRANPAY_ID = cg$rec.TRANPAY_ID
              ,DATE_OF_PROCESSING = cg$rec.DATE_OF_PROCESSING
              ,MESSAGE = cg$rec.MESSAGE
              ,BILLING_PERIOD = cg$rec.BILLING_PERIOD
              ,BILLING_DATE = cg$rec.BILLING_DATE
              ,PAYMENT_DUE_DATE = cg$rec.PAYMENT_DUE_DATE
              ,ACCOUNT_OWNER_ID = cg$rec.ACCOUNT_OWNER_ID
              ,DISPLAY_ON_OVERVIEW = cg$rec.DISPLAY_ON_OVERVIEW
              ,GROUP_ID = cg$rec.GROUP_ID
            WHERE  ID = cg$rec.ID;
            null;
          ELSE
            UPDATE INVOICES
            SET
              INVOICE_NO = cg$rec.INVOICE_NO
              ,PAYMENT_TYPE = cg$rec.PAYMENT_TYPE
              ,STATUS = cg$rec.STATUS
              ,DESCRIPTION = cg$rec.DESCRIPTION
              ,TRANVAL = cg$rec.TRANVAL
              ,TRANVAL_CURRENCY_ID = cg$rec.TRANVAL_CURRENCY_ID
              ,IPR_ID = cg$rec.IPR_ID
              ,CLIENT_ID = cg$rec.CLIENT_ID
              ,IBE_ID = cg$rec.IBE_ID
              ,TRANPAY_ID = cg$rec.TRANPAY_ID
              ,DATE_OF_PROCESSING = cg$rec.DATE_OF_PROCESSING
              ,MESSAGE = cg$rec.MESSAGE
              ,BILLING_PERIOD = cg$rec.BILLING_PERIOD
              ,BILLING_DATE = cg$rec.BILLING_DATE
              ,PAYMENT_DUE_DATE = cg$rec.PAYMENT_DUE_DATE
              ,ACCOUNT_OWNER_ID = cg$rec.ACCOUNT_OWNER_ID
              ,DISPLAY_ON_OVERVIEW = cg$rec.DISPLAY_ON_OVERVIEW
              ,GROUP_ID = cg$rec.GROUP_ID
            WHERE rowid = cg$rec.the_rowid;

            null;
          END IF;

          slct(cg$rec);

          upd_denorm2(cg$rec, cg$ind);
          upd_oper_denorm2(cg$rec, cg$old_rec, cg$ind, 'UPD');
          cascade_update(cg$rec, cg$old_rec);
          domain_cascade_update(cg$rec, cg$ind, cg$old_rec);
          called_from_package := called_from;
        END;
    END IF;


    insert_jn(cg$rec, 'UPD');

    IF NOT (do_upd_custom) THEN
        cg$table(idx).ID := cg$rec.ID;
        cg$tableind(idx).ID := cg$ind.ID;
        cg$table(idx).INVOICE_NO := cg$rec.INVOICE_NO;
        cg$tableind(idx).INVOICE_NO := cg$ind.INVOICE_NO;
        cg$table(idx).PAYMENT_TYPE := cg$rec.PAYMENT_TYPE;
        cg$tableind(idx).PAYMENT_TYPE := cg$ind.PAYMENT_TYPE;
        cg$table(idx).STATUS := cg$rec.STATUS;
        cg$tableind(idx).STATUS := cg$ind.STATUS;
        cg$table(idx).DESCRIPTION := cg$rec.DESCRIPTION;
        cg$tableind(idx).DESCRIPTION := cg$ind.DESCRIPTION;
        cg$table(idx).TRANVAL := cg$rec.TRANVAL;
        cg$tableind(idx).TRANVAL := cg$ind.TRANVAL;
        cg$table(idx).TRANVAL_CURRENCY_ID := cg$rec.TRANVAL_CURRENCY_ID;
        cg$tableind(idx).TRANVAL_CURRENCY_ID := cg$ind.TRANVAL_CURRENCY_ID;
        cg$table(idx).IPR_ID := cg$rec.IPR_ID;
        cg$tableind(idx).IPR_ID := cg$ind.IPR_ID;
        cg$table(idx).CLIENT_ID := cg$rec.CLIENT_ID;
        cg$tableind(idx).CLIENT_ID := cg$ind.CLIENT_ID;
        cg$table(idx).IBE_ID := cg$rec.IBE_ID;
        cg$tableind(idx).IBE_ID := cg$ind.IBE_ID;
        cg$table(idx).TRANPAY_ID := cg$rec.TRANPAY_ID;
        cg$tableind(idx).TRANPAY_ID := cg$ind.TRANPAY_ID;
        cg$table(idx).DATE_OF_PROCESSING := cg$rec.DATE_OF_PROCESSING;
        cg$tableind(idx).DATE_OF_PROCESSING := cg$ind.DATE_OF_PROCESSING;
        cg$table(idx).MESSAGE := cg$rec.MESSAGE;
        cg$tableind(idx).MESSAGE := cg$ind.MESSAGE;
        cg$table(idx).BILLING_PERIOD := cg$rec.BILLING_PERIOD;
        cg$tableind(idx).BILLING_PERIOD := cg$ind.BILLING_PERIOD;
        cg$table(idx).BILLING_DATE := cg$rec.BILLING_DATE;
        cg$tableind(idx).BILLING_DATE := cg$ind.BILLING_DATE;
        cg$table(idx).PAYMENT_DUE_DATE := cg$rec.PAYMENT_DUE_DATE;
        cg$tableind(idx).PAYMENT_DUE_DATE := cg$ind.PAYMENT_DUE_DATE;
        cg$table(idx).ACCOUNT_OWNER_ID := cg$rec.ACCOUNT_OWNER_ID;
        cg$tableind(idx).ACCOUNT_OWNER_ID := cg$ind.ACCOUNT_OWNER_ID;
        cg$table(idx).DISPLAY_ON_OVERVIEW := cg$rec.DISPLAY_ON_OVERVIEW;
        cg$tableind(idx).DISPLAY_ON_OVERVIEW := cg$ind.DISPLAY_ON_OVERVIEW;
        cg$table(idx).GROUP_ID := cg$rec.GROUP_ID;
        cg$tableind(idx).GROUP_ID := cg$ind.GROUP_ID;

        cg$table(idx).action_performed := 'UPD';
        idx := idx + 1;
    END IF;

--  Application_logic Post-Update <<Start>>
--  Application_logic Post-Update << End >>

EXCEPTION
    WHEN cg$errors.cg$error THEN
        called_from_package := FALSE;
        cg$errors.raise_failure;
    WHEN cg$errors.upd_mandatory_null THEN
        validate_mandatory(cg$rec, 'cg$INVOICES.upd.upd_mandatory_null');
        called_from_package := FALSE;
        cg$errors.raise_failure;
    WHEN cg$errors.check_violation THEN
        err_msg(SQLERRM, cg$errors.ERR_CHECK_CON, 'cg$INVOICES.upd.check_violation');
        called_from_package := FALSE;
        cg$errors.raise_failure;
    WHEN cg$errors.fk_violation THEN
        err_msg(SQLERRM, cg$errors.ERR_FOREIGN_KEY, 'cg$INVOICES.upd.fk_violation');
        called_from_package := FALSE;
        cg$errors.raise_failure;
    WHEN cg$errors.uk_violation THEN
        err_msg(SQLERRM, cg$errors.ERR_UNIQUE_KEY, 'cg$INVOICES.upd.uk_violation');
        called_from_package := FALSE;
        cg$errors.raise_failure;
    WHEN OTHERS THEN
        cg$errors.push(SQLERRM,
                       'E',
                       'ORA',
                       SQLCODE,
                       'cg$INVOICES.upd.others');
        called_from_package := FALSE;
        cg$errors.raise_failure;
END upd;


----------------------------------------------------------------------------------------
-- Name:        domain_cascade_upd
--
-- Description: Update the Domain Constraint Key columns of INVOICES when the
--              Cascade Update rule is Cascades and the domain table has been
--              updated. Called from <Domain Table pkg>.domain_cascade_update().
--
-- Parameters:  cg$rec      New values for INVOICES's domain key constraint columns
--              cg$ind      Indicates changed INVOICES's domain key constraint columns
--              cg$old_rec  Current values for INVOICES's domain key constraint columns
----------------------------------------------------------------------------------------
PROCEDURE   domain_cascade_upd( cg$rec     IN OUT cg$row_type,
                                cg$ind     IN OUT cg$ind_type,
                                cg$old_rec IN     cg$row_type )
IS
  called_from BOOLEAN := called_from_package;
BEGIN

  null;
END domain_cascade_upd;


--------------------------------------------------------------------------------
-- Name:        upd_denorm
--
-- Description: API procedure for simple denormalization
--
-- Parameters:  cg$rec  Record of row to be updated
--              cg$ind  Record of columns specifically set
--              do_upd  Whether we want the actual UPDATE to occur
--------------------------------------------------------------------------------
PROCEDURE upd_denorm2( cg$rec IN cg$row_type,
                       cg$ind IN cg$ind_type ) IS
BEGIN
  NULL;
END upd_denorm2;


--------------------------------------------------------------------------------
-- Name:        upd_oper_denorm
--
-- Description: API procedure for operation denormalization
--
-- Parameters:  cg$rec  Record of row to be updated
--              cg$ind  Record of columns specifically set
--              do_upd  Whether we want the actual UPDATE to occur
--------------------------------------------------------------------------------
PROCEDURE upd_oper_denorm2( cg$rec IN cg$row_type,
                            cg$old_rec IN cg$row_type,
                            cg$ind IN cg$ind_type,
                            operation IN VARCHAR2 DEFAULT 'UPD'
					           )
IS
BEGIN

















NULL;
END upd_oper_denorm2;

--------------------------------------------------------------------------------
-- Name:        del
--
-- Description: API delete procedure
--
-- Parameters:  cg$pk  Primary key record of row to be deleted
--------------------------------------------------------------------------------
PROCEDURE del(cg$pk IN cg$pk_type,
              do_del IN BOOLEAN DEFAULT TRUE) IS
cg$tmp_rec cg$row_type;
BEGIN
--  Application_logic Pre-Delete <<Start>>
--  Application_logic Pre-Delete << End >>
    cg$tmp_rec.ID := cg$pk.ID;
    cg$tmp_rec.jn_notes := cg$pk.jn_notes;

--  Delete the record

    called_from_package := TRUE;

    IF (do_del) THEN
        DECLARE
           cg$rec cg$row_type;
           cg$old_rec cg$row_type;
           cg$ind cg$ind_type;
        BEGIN
           cg$rec.ID := cg$pk.ID;
           slct(cg$rec);

           validate_foreign_keys_del(cg$rec);
           validate_domain_cascade_delete(cg$rec);

           IF cg$pk.the_rowid is null THEN
              DELETE INVOICES
              WHERE                    ID = cg$pk.ID;
           ELSE
              DELETE INVOICES
              WHERE  rowid = cg$pk.the_rowid;
           END IF;

           upd_oper_denorm2(cg$rec, cg$old_rec, cg$ind, 'DEL');
           cascade_delete(cg$rec);
           domain_cascade_delete(cg$rec);
        END;
    END IF;

    called_from_package := FALSE;

    insert_jn(cg$tmp_rec, 'DEL');

--  Application_logic Post-Delete <<Start>>
--  Application_logic Post-Delete << End >>

EXCEPTION
    WHEN cg$errors.cg$error THEN
        called_from_package := FALSE;
        cg$errors.raise_failure;
    WHEN cg$errors.delete_restrict THEN
        err_msg(SQLERRM, cg$errors.ERR_DELETE_RESTRICT, 'cg$INVOICES.del.delete_restrict');
        called_from_package := FALSE;
        cg$errors.raise_failure;
    WHEN no_data_found THEN
        cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_DEL, cg$errors.ROW_DEL),
                       'E',
                       'ORA',
                       SQLCODE,
                       'cg$INVOICES.del.no_data_found');
        called_from_package := FALSE;
        cg$errors.raise_failure;
    WHEN OTHERS THEN
        cg$errors.push(SQLERRM,
                       'E',
                       'ORA',
                       SQLCODE,
                       'cg$INVOICES.del.others');
        called_from_package := FALSE;
        cg$errors.raise_failure;
END del;


--------------------------------------------------------------------------------
-- Name:        lck
--
-- Description: API lock procedure
--
-- Parameters:  cg$old_rec  Calling apps view of record of row to be locked
--              cg$old_ind  Record of columns to raise error if modified
--              nowait_flag TRUE lock with NOWAIT, FALSE don't fail if busy
--------------------------------------------------------------------------------
PROCEDURE lck(cg$old_rec IN cg$row_type,
              cg$old_ind IN cg$ind_type,
              nowait_flag IN BOOLEAN DEFAULT TRUE) IS
cg$tmp_rec cg$row_type;
any_modified BOOLEAN := FALSE;

BEGIN
--  Application_logic Pre-Lock <<Start>>
--  Application_logic Pre-Lock << End >>

--  Do the row lock

    BEGIN
        IF (nowait_flag) THEN
            IF cg$old_rec.the_rowid is null THEN
               SELECT       ID
               ,            INVOICE_NO
               ,            PAYMENT_TYPE
               ,            STATUS
               ,            DESCRIPTION
               ,            TRANVAL
               ,            TRANVAL_CURRENCY_ID
               ,            IPR_ID
               ,            CLIENT_ID
               ,            IBE_ID
               ,            TRANPAY_ID
               ,            DATE_OF_PROCESSING
               ,            MESSAGE
               ,            BILLING_PERIOD
               ,            BILLING_DATE
               ,            PAYMENT_DUE_DATE
               ,            ACCOUNT_OWNER_ID
               ,            DISPLAY_ON_OVERVIEW
               ,            GROUP_ID
               INTO         cg$tmp_rec.ID
               ,            cg$tmp_rec.INVOICE_NO
               ,            cg$tmp_rec.PAYMENT_TYPE
               ,            cg$tmp_rec.STATUS
               ,            cg$tmp_rec.DESCRIPTION
               ,            cg$tmp_rec.TRANVAL
               ,            cg$tmp_rec.TRANVAL_CURRENCY_ID
               ,            cg$tmp_rec.IPR_ID
               ,            cg$tmp_rec.CLIENT_ID
               ,            cg$tmp_rec.IBE_ID
               ,            cg$tmp_rec.TRANPAY_ID
               ,            cg$tmp_rec.DATE_OF_PROCESSING
               ,            cg$tmp_rec.MESSAGE
               ,            cg$tmp_rec.BILLING_PERIOD
               ,            cg$tmp_rec.BILLING_DATE
               ,            cg$tmp_rec.PAYMENT_DUE_DATE
               ,            cg$tmp_rec.ACCOUNT_OWNER_ID
               ,            cg$tmp_rec.DISPLAY_ON_OVERVIEW
               ,            cg$tmp_rec.GROUP_ID
               FROM      INVOICES
               WHERE              ID = cg$old_rec.ID
               FOR UPDATE NOWAIT;
            ELSE
               SELECT       ID
               ,            INVOICE_NO
               ,            PAYMENT_TYPE
               ,            STATUS
               ,            DESCRIPTION
               ,            TRANVAL
               ,            TRANVAL_CURRENCY_ID
               ,            IPR_ID
               ,            CLIENT_ID
               ,            IBE_ID
               ,            TRANPAY_ID
               ,            DATE_OF_PROCESSING
               ,            MESSAGE
               ,            BILLING_PERIOD
               ,            BILLING_DATE
               ,            PAYMENT_DUE_DATE
               ,            ACCOUNT_OWNER_ID
               ,            DISPLAY_ON_OVERVIEW
               ,            GROUP_ID
               INTO         cg$tmp_rec.ID
               ,            cg$tmp_rec.INVOICE_NO
               ,            cg$tmp_rec.PAYMENT_TYPE
               ,            cg$tmp_rec.STATUS
               ,            cg$tmp_rec.DESCRIPTION
               ,            cg$tmp_rec.TRANVAL
               ,            cg$tmp_rec.TRANVAL_CURRENCY_ID
               ,            cg$tmp_rec.IPR_ID
               ,            cg$tmp_rec.CLIENT_ID
               ,            cg$tmp_rec.IBE_ID
               ,            cg$tmp_rec.TRANPAY_ID
               ,            cg$tmp_rec.DATE_OF_PROCESSING
               ,            cg$tmp_rec.MESSAGE
               ,            cg$tmp_rec.BILLING_PERIOD
               ,            cg$tmp_rec.BILLING_DATE
               ,            cg$tmp_rec.PAYMENT_DUE_DATE
               ,            cg$tmp_rec.ACCOUNT_OWNER_ID
               ,            cg$tmp_rec.DISPLAY_ON_OVERVIEW
               ,            cg$tmp_rec.GROUP_ID
               FROM      INVOICES
               WHERE rowid = cg$old_rec.the_rowid
               FOR UPDATE NOWAIT;
            END IF;
        ELSE
            IF cg$old_rec.the_rowid is null THEN
               SELECT       ID
               ,            INVOICE_NO
               ,            PAYMENT_TYPE
               ,            STATUS
               ,            DESCRIPTION
               ,            TRANVAL
               ,            TRANVAL_CURRENCY_ID
               ,            IPR_ID
               ,            CLIENT_ID
               ,            IBE_ID
               ,            TRANPAY_ID
               ,            DATE_OF_PROCESSING
               ,            MESSAGE
               ,            BILLING_PERIOD
               ,            BILLING_DATE
               ,            PAYMENT_DUE_DATE
               ,            ACCOUNT_OWNER_ID
               ,            DISPLAY_ON_OVERVIEW
               ,            GROUP_ID
               INTO         cg$tmp_rec.ID
               ,            cg$tmp_rec.INVOICE_NO
               ,            cg$tmp_rec.PAYMENT_TYPE
               ,            cg$tmp_rec.STATUS
               ,            cg$tmp_rec.DESCRIPTION
               ,            cg$tmp_rec.TRANVAL
               ,            cg$tmp_rec.TRANVAL_CURRENCY_ID
               ,            cg$tmp_rec.IPR_ID
               ,            cg$tmp_rec.CLIENT_ID
               ,            cg$tmp_rec.IBE_ID
               ,            cg$tmp_rec.TRANPAY_ID
               ,            cg$tmp_rec.DATE_OF_PROCESSING
               ,            cg$tmp_rec.MESSAGE
               ,            cg$tmp_rec.BILLING_PERIOD
               ,            cg$tmp_rec.BILLING_DATE
               ,            cg$tmp_rec.PAYMENT_DUE_DATE
               ,            cg$tmp_rec.ACCOUNT_OWNER_ID
               ,            cg$tmp_rec.DISPLAY_ON_OVERVIEW
               ,            cg$tmp_rec.GROUP_ID
               FROM      INVOICES
               WHERE              ID = cg$old_rec.ID
               FOR UPDATE;
            ELSE
               SELECT       ID
               ,            INVOICE_NO
               ,            PAYMENT_TYPE
               ,            STATUS
               ,            DESCRIPTION
               ,            TRANVAL
               ,            TRANVAL_CURRENCY_ID
               ,            IPR_ID
               ,            CLIENT_ID
               ,            IBE_ID
               ,            TRANPAY_ID
               ,            DATE_OF_PROCESSING
               ,            MESSAGE
               ,            BILLING_PERIOD
               ,            BILLING_DATE
               ,            PAYMENT_DUE_DATE
               ,            ACCOUNT_OWNER_ID
               ,            DISPLAY_ON_OVERVIEW
               ,            GROUP_ID
               INTO         cg$tmp_rec.ID
               ,            cg$tmp_rec.INVOICE_NO
               ,            cg$tmp_rec.PAYMENT_TYPE
               ,            cg$tmp_rec.STATUS
               ,            cg$tmp_rec.DESCRIPTION
               ,            cg$tmp_rec.TRANVAL
               ,            cg$tmp_rec.TRANVAL_CURRENCY_ID
               ,            cg$tmp_rec.IPR_ID
               ,            cg$tmp_rec.CLIENT_ID
               ,            cg$tmp_rec.IBE_ID
               ,            cg$tmp_rec.TRANPAY_ID
               ,            cg$tmp_rec.DATE_OF_PROCESSING
               ,            cg$tmp_rec.MESSAGE
               ,            cg$tmp_rec.BILLING_PERIOD
               ,            cg$tmp_rec.BILLING_DATE
               ,            cg$tmp_rec.PAYMENT_DUE_DATE
               ,            cg$tmp_rec.ACCOUNT_OWNER_ID
               ,            cg$tmp_rec.DISPLAY_ON_OVERVIEW
               ,            cg$tmp_rec.GROUP_ID
               FROM      INVOICES
               WHERE rowid = cg$old_rec.the_rowid
               FOR UPDATE;
            END IF;
        END IF;

    EXCEPTION
        WHEN cg$errors.cg$error THEN
            cg$errors.raise_failure;
        WHEN cg$errors.resource_busy THEN
            cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_LCK, cg$errors.ROW_LCK),
                           'E',
                           'ORA',
                           SQLCODE,
                           'cg$INVOICES.lck.resource_busy');
            cg$errors.raise_failure;
        WHEN no_data_found THEN
            cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_DEL, cg$errors.ROW_DEL),
                           'E',
                           'ORA',
                           SQLCODE,
                           'cg$INVOICES.lck.no_data_found');
            cg$errors.raise_failure;
        WHEN OTHERS THEN
            cg$errors.push(SQLERRM,
                           'E',
                           'ORA',
                           SQLCODE,
                           'cg$INVOICES.lck.others');
            cg$errors.raise_failure;
    END;

-- Optional Columns

    IF (cg$old_ind.CLIENT_ID) THEN
        IF (cg$tmp_rec.CLIENT_ID IS NOT NULL
        AND cg$old_rec.CLIENT_ID IS NOT NULL) THEN
            IF (cg$tmp_rec.CLIENT_ID NOT LIKE cg$old_rec.CLIENT_ID) THEN
                cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P90CLIENT_ID
                    ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICES.lck');
                any_modified := TRUE;
            END IF;
        ELSIF (cg$tmp_rec.CLIENT_ID IS NOT NULL
        OR cg$old_rec.CLIENT_ID IS NOT NULL) THEN
            cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P90CLIENT_ID
                ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICES.lck');
            any_modified := TRUE;
        END IF;
    END IF;
    IF (cg$old_ind.IBE_ID) THEN
        IF (cg$tmp_rec.IBE_ID IS NOT NULL
        AND cg$old_rec.IBE_ID IS NOT NULL) THEN
            IF (cg$tmp_rec.IBE_ID NOT LIKE cg$old_rec.IBE_ID) THEN
                cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P100IBE_ID
                    ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICES.lck');
                any_modified := TRUE;
            END IF;
        ELSIF (cg$tmp_rec.IBE_ID IS NOT NULL
        OR cg$old_rec.IBE_ID IS NOT NULL) THEN
            cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P100IBE_ID
                ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICES.lck');
            any_modified := TRUE;
        END IF;
    END IF;
    IF (cg$old_ind.TRANPAY_ID) THEN
        IF (cg$tmp_rec.TRANPAY_ID IS NOT NULL
        AND cg$old_rec.TRANPAY_ID IS NOT NULL) THEN
            IF (cg$tmp_rec.TRANPAY_ID NOT LIKE cg$old_rec.TRANPAY_ID) THEN
                cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P120TRANPAY_ID
                    ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICES.lck');
                any_modified := TRUE;
            END IF;
        ELSIF (cg$tmp_rec.TRANPAY_ID IS NOT NULL
        OR cg$old_rec.TRANPAY_ID IS NOT NULL) THEN
            cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P120TRANPAY_ID
                ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICES.lck');
            any_modified := TRUE;
        END IF;
    END IF;
    IF (cg$old_ind.DATE_OF_PROCESSING) THEN
        IF (cg$tmp_rec.DATE_OF_PROCESSING IS NOT NULL
        AND cg$old_rec.DATE_OF_PROCESSING IS NOT NULL) THEN
            IF (cg$tmp_rec.DATE_OF_PROCESSING NOT LIKE cg$old_rec.DATE_OF_PROCESSING) THEN
                cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P130DATE_OF_PROCESSING
                    ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICES.lck');
                any_modified := TRUE;
            END IF;
        ELSIF (cg$tmp_rec.DATE_OF_PROCESSING IS NOT NULL
        OR cg$old_rec.DATE_OF_PROCESSING IS NOT NULL) THEN
            cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P130DATE_OF_PROCESSING
                ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICES.lck');
            any_modified := TRUE;
        END IF;
    END IF;
    IF (cg$old_ind.BILLING_PERIOD) THEN
        IF (cg$tmp_rec.BILLING_PERIOD IS NOT NULL
        AND cg$old_rec.BILLING_PERIOD IS NOT NULL) THEN
            IF (cg$tmp_rec.BILLING_PERIOD NOT LIKE cg$old_rec.BILLING_PERIOD) THEN
                cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P150BILLING_PERIOD
                    ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICES.lck');
                any_modified := TRUE;
            END IF;
        ELSIF (cg$tmp_rec.BILLING_PERIOD IS NOT NULL
        OR cg$old_rec.BILLING_PERIOD IS NOT NULL) THEN
            cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P150BILLING_PERIOD
                ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICES.lck');
            any_modified := TRUE;
        END IF;
    END IF;
    IF (cg$old_ind.BILLING_DATE) THEN
        IF (cg$tmp_rec.BILLING_DATE IS NOT NULL
        AND cg$old_rec.BILLING_DATE IS NOT NULL) THEN
            IF (cg$tmp_rec.BILLING_DATE NOT LIKE cg$old_rec.BILLING_DATE) THEN
                cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P160BILLING_DATE
                    ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICES.lck');
                any_modified := TRUE;
            END IF;
        ELSIF (cg$tmp_rec.BILLING_DATE IS NOT NULL
        OR cg$old_rec.BILLING_DATE IS NOT NULL) THEN
            cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P160BILLING_DATE
                ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICES.lck');
            any_modified := TRUE;
        END IF;
    END IF;
    IF (cg$old_ind.PAYMENT_DUE_DATE) THEN
        IF (cg$tmp_rec.PAYMENT_DUE_DATE IS NOT NULL
        AND cg$old_rec.PAYMENT_DUE_DATE IS NOT NULL) THEN
            IF (cg$tmp_rec.PAYMENT_DUE_DATE NOT LIKE cg$old_rec.PAYMENT_DUE_DATE) THEN
                cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P170PAYMENT_DUE_DATE
                    ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICES.lck');
                any_modified := TRUE;
            END IF;
        ELSIF (cg$tmp_rec.PAYMENT_DUE_DATE IS NOT NULL
        OR cg$old_rec.PAYMENT_DUE_DATE IS NOT NULL) THEN
            cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P170PAYMENT_DUE_DATE
                ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICES.lck');
            any_modified := TRUE;
        END IF;
    END IF;
    IF (cg$old_ind.ACCOUNT_OWNER_ID) THEN
        IF (cg$tmp_rec.ACCOUNT_OWNER_ID IS NOT NULL
        AND cg$old_rec.ACCOUNT_OWNER_ID IS NOT NULL) THEN
            IF (cg$tmp_rec.ACCOUNT_OWNER_ID NOT LIKE cg$old_rec.ACCOUNT_OWNER_ID) THEN
                cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P180ACCOUNT_OWNER_ID
                    ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICES.lck');
                any_modified := TRUE;
            END IF;
        ELSIF (cg$tmp_rec.ACCOUNT_OWNER_ID IS NOT NULL
        OR cg$old_rec.ACCOUNT_OWNER_ID IS NOT NULL) THEN
            cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P180ACCOUNT_OWNER_ID
                ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICES.lck');
            any_modified := TRUE;
        END IF;
    END IF;
    IF (cg$old_ind.GROUP_ID) THEN
        IF (cg$tmp_rec.GROUP_ID IS NOT NULL
        AND cg$old_rec.GROUP_ID IS NOT NULL) THEN
            IF (cg$tmp_rec.GROUP_ID NOT LIKE cg$old_rec.GROUP_ID) THEN
                cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P200GROUP_ID
                    ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICES.lck');
                any_modified := TRUE;
            END IF;
        ELSIF (cg$tmp_rec.GROUP_ID IS NOT NULL
        OR cg$old_rec.GROUP_ID IS NOT NULL) THEN
            cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P200GROUP_ID
                ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICES.lck');
            any_modified := TRUE;
        END IF;
    END IF;

-- Mandatory Columns

    IF (cg$old_ind.ID) THEN
        IF (cg$tmp_rec.ID != cg$old_rec.ID) THEN
            cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P10ID
                ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICES.lck');
            any_modified := TRUE;
        END IF;
    END IF;
    IF (cg$old_ind.INVOICE_NO) THEN
        IF (cg$tmp_rec.INVOICE_NO != cg$old_rec.INVOICE_NO) THEN
            cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P20INVOICE_NO
                ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICES.lck');
            any_modified := TRUE;
        END IF;
    END IF;
    IF (cg$old_ind.PAYMENT_TYPE) THEN
        IF (cg$tmp_rec.PAYMENT_TYPE != cg$old_rec.PAYMENT_TYPE) THEN
            cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P30PAYMENT_TYPE
                ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICES.lck');
            any_modified := TRUE;
        END IF;
    END IF;
    IF (cg$old_ind.STATUS) THEN
        IF (cg$tmp_rec.STATUS != cg$old_rec.STATUS) THEN
            cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P40STATUS
                ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICES.lck');
            any_modified := TRUE;
        END IF;
    END IF;
    IF (cg$old_ind.DESCRIPTION) THEN
        IF (cg$tmp_rec.DESCRIPTION != cg$old_rec.DESCRIPTION) THEN
            cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P50DESCRIPTION
                ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICES.lck');
            any_modified := TRUE;
        END IF;
    END IF;
    IF (cg$old_ind.TRANVAL) THEN
        IF (cg$tmp_rec.TRANVAL != cg$old_rec.TRANVAL) THEN
            cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P60TRANVAL
                ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICES.lck');
            any_modified := TRUE;
        END IF;
    END IF;
    IF (cg$old_ind.TRANVAL_CURRENCY_ID) THEN
        IF (cg$tmp_rec.TRANVAL_CURRENCY_ID != cg$old_rec.TRANVAL_CURRENCY_ID) THEN
            cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P70TRANVAL_CURRENCY_ID
                ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICES.lck');
            any_modified := TRUE;
        END IF;
    END IF;
    IF (cg$old_ind.IPR_ID) THEN
        IF (cg$tmp_rec.IPR_ID != cg$old_rec.IPR_ID) THEN
            cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P80IPR_ID
                ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICES.lck');
            any_modified := TRUE;
        END IF;
    END IF;
    IF (cg$old_ind.DISPLAY_ON_OVERVIEW) THEN
        IF (cg$tmp_rec.DISPLAY_ON_OVERVIEW != cg$old_rec.DISPLAY_ON_OVERVIEW) THEN
            cg$errors.push(cg$errors.MsgGetText(cg$errors.API_ROW_MOD, cg$errors.ROW_MOD, P190DISPLAY_ON_OVERVIEW
                ),'E', 'API', CG$ERRORS.API_MODIFIED, 'cg$INVOICES.lck');
            any_modified := TRUE;
        END IF;
    END IF;

    IF (any_modified) THEN
        cg$errors.raise_failure;
    END IF;

--  Application_logic Post-Lock <<Start>>
--  Application_logic Post-Lock << End >>

END lck;

PROCEDURE val$length (
   pID IN NUMBER
  ,pINVOICE_NO IN VARCHAR2
  ,pPAYMENT_TYPE IN VARCHAR2
  ,pSTATUS IN NUMBER
  ,pDESCRIPTION IN VARCHAR2
  ,pTRANVAL IN NUMBER
  ,pTRANVAL_CURRENCY_ID IN VARCHAR2
  ,pIPR_ID IN NUMBER
  ,pCLIENT_ID IN NUMBER
  ,pIBE_ID IN NUMBER
  ,pTRANPAY_ID IN NUMBER
  ,pDATE_OF_PROCESSING IN DATE
  ,pMESSAGE IN CLOB
  ,pBILLING_PERIOD IN VARCHAR2
  ,pBILLING_DATE IN DATE
  ,pPAYMENT_DUE_DATE IN DATE
  ,pACCOUNT_OWNER_ID IN VARCHAR2
  ,pDISPLAY_ON_OVERVIEW IN NUMBER
  ,pGROUP_ID IN VARCHAR2
  ) IS
  vLoc VARCHAR2(60) := 'cg$invoices.val$length';
BEGIN
		cg$common.val$length(pcg$table_columns => cg$table_columns, pLoc => vLoc, pColumnName => cg$table_columns('ID').COLUMN_NAME, pValue => pID);
		cg$common.val$length(pcg$table_columns => cg$table_columns, pLoc => vLoc, pColumnName => cg$table_columns('INVOICE_NO').COLUMN_NAME, pValue => pINVOICE_NO);
		cg$common.val$length(pcg$table_columns => cg$table_columns, pLoc => vLoc, pColumnName => cg$table_columns('PAYMENT_TYPE').COLUMN_NAME, pValue => pPAYMENT_TYPE);
		cg$common.val$length(pcg$table_columns => cg$table_columns, pLoc => vLoc, pColumnName => cg$table_columns('STATUS').COLUMN_NAME, pValue => pSTATUS);
		cg$common.val$length(pcg$table_columns => cg$table_columns, pLoc => vLoc, pColumnName => cg$table_columns('DESCRIPTION').COLUMN_NAME, pValue => pDESCRIPTION);
		cg$common.val$length(pcg$table_columns => cg$table_columns, pLoc => vLoc, pColumnName => cg$table_columns('TRANVAL').COLUMN_NAME, pValue => pTRANVAL);
		cg$common.val$length(pcg$table_columns => cg$table_columns, pLoc => vLoc, pColumnName => cg$table_columns('TRANVAL_CURRENCY_ID').COLUMN_NAME, pValue => pTRANVAL_CURRENCY_ID);
		cg$common.val$length(pcg$table_columns => cg$table_columns, pLoc => vLoc, pColumnName => cg$table_columns('IPR_ID').COLUMN_NAME, pValue => pIPR_ID);
		cg$common.val$length(pcg$table_columns => cg$table_columns, pLoc => vLoc, pColumnName => cg$table_columns('CLIENT_ID').COLUMN_NAME, pValue => pCLIENT_ID);
		cg$common.val$length(pcg$table_columns => cg$table_columns, pLoc => vLoc, pColumnName => cg$table_columns('IBE_ID').COLUMN_NAME, pValue => pIBE_ID);
		cg$common.val$length(pcg$table_columns => cg$table_columns, pLoc => vLoc, pColumnName => cg$table_columns('TRANPAY_ID').COLUMN_NAME, pValue => pTRANPAY_ID);
		cg$common.val$length(pcg$table_columns => cg$table_columns, pLoc => vLoc, pColumnName => cg$table_columns('DATE_OF_PROCESSING').COLUMN_NAME, pValue => pDATE_OF_PROCESSING);
		cg$common.val$length(pcg$table_columns => cg$table_columns, pLoc => vLoc, pColumnName => cg$table_columns('MESSAGE').COLUMN_NAME, pValue => pMESSAGE);
		cg$common.val$length(pcg$table_columns => cg$table_columns, pLoc => vLoc, pColumnName => cg$table_columns('BILLING_PERIOD').COLUMN_NAME, pValue => pBILLING_PERIOD);
		cg$common.val$length(pcg$table_columns => cg$table_columns, pLoc => vLoc, pColumnName => cg$table_columns('BILLING_DATE').COLUMN_NAME, pValue => pBILLING_DATE);
		cg$common.val$length(pcg$table_columns => cg$table_columns, pLoc => vLoc, pColumnName => cg$table_columns('PAYMENT_DUE_DATE').COLUMN_NAME, pValue => pPAYMENT_DUE_DATE);
		cg$common.val$length(pcg$table_columns => cg$table_columns, pLoc => vLoc, pColumnName => cg$table_columns('ACCOUNT_OWNER_ID').COLUMN_NAME, pValue => pACCOUNT_OWNER_ID);
		cg$common.val$length(pcg$table_columns => cg$table_columns, pLoc => vLoc, pColumnName => cg$table_columns('DISPLAY_ON_OVERVIEW').COLUMN_NAME, pValue => pDISPLAY_ON_OVERVIEW);
		cg$common.val$length(pcg$table_columns => cg$table_columns, pLoc => vLoc, pColumnName => cg$table_columns('GROUP_ID').COLUMN_NAME, pValue => pGROUP_ID);
END val$length;

BEGIN
      cg$ind_true.ID := TRUE;
      cg$ind_true.INVOICE_NO := TRUE;
      cg$ind_true.PAYMENT_TYPE := TRUE;
      cg$ind_true.STATUS := TRUE;
      cg$ind_true.DESCRIPTION := TRUE;
      cg$ind_true.TRANVAL := TRUE;
      cg$ind_true.TRANVAL_CURRENCY_ID := TRUE;
      cg$ind_true.IPR_ID := TRUE;
      cg$ind_true.CLIENT_ID := TRUE;
      cg$ind_true.IBE_ID := TRUE;
      cg$ind_true.TRANPAY_ID := TRUE;
      cg$ind_true.DATE_OF_PROCESSING := TRUE;
      cg$ind_true.MESSAGE := TRUE;
      cg$ind_true.BILLING_PERIOD := TRUE;
      cg$ind_true.BILLING_DATE := TRUE;
      cg$ind_true.PAYMENT_DUE_DATE := TRUE;
      cg$ind_true.ACCOUNT_OWNER_ID := TRUE;
      cg$ind_true.DISPLAY_ON_OVERVIEW := TRUE;
      cg$ind_true.GROUP_ID := TRUE;

BEGIN
		cg$table_columns('ID') := cg$columID;
		cg$table_columns('INVOICE_NO') := cg$columINVOICE_NO;
		cg$table_columns('PAYMENT_TYPE') := cg$columPAYMENT_TYPE;
		cg$table_columns('STATUS') := cg$columSTATUS;
		cg$table_columns('DESCRIPTION') := cg$columDESCRIPTION;
		cg$table_columns('TRANVAL') := cg$columTRANVAL;
		cg$table_columns('TRANVAL_CURRENCY_ID') := cg$columTRANVAL_CURRENCY_ID;
		cg$table_columns('IPR_ID') := cg$columIPR_ID;
		cg$table_columns('CLIENT_ID') := cg$columCLIENT_ID;
		cg$table_columns('IBE_ID') := cg$columIBE_ID;
		cg$table_columns('TRANPAY_ID') := cg$columTRANPAY_ID;
		cg$table_columns('DATE_OF_PROCESSING') := cg$columDATE_OF_PROCESSING;
		cg$table_columns('MESSAGE') := cg$columMESSAGE;
		cg$table_columns('BILLING_PERIOD') := cg$columBILLING_PERIOD;
		cg$table_columns('BILLING_DATE') := cg$columBILLING_DATE;
		cg$table_columns('PAYMENT_DUE_DATE') := cg$columPAYMENT_DUE_DATE;
		cg$table_columns('ACCOUNT_OWNER_ID') := cg$columACCOUNT_OWNER_ID;
		cg$table_columns('DISPLAY_ON_OVERVIEW') := cg$columDISPLAY_ON_OVERVIEW;
		cg$table_columns('GROUP_ID') := cg$columGROUP_ID;
END;

END cg$INVOICES;
/

show errors
