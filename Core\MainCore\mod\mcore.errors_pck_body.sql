PROMPT Creating package body script for package ERRORS_PCK
CREATE OR REPLACE
PACKAGE BODY MCORE.ERRORS_PCK
IS

	cREF_CLASS CONSTANT VARCHAR2(17) := 'MCORE.ERRORS_PCK.';
    channelId constant varchar2(100) := sspkg.readvchar(pkgCtxId || '/MsgChannelId');
    fromAddr  constant varchar2(100) := sspkg.readvchar(pkgCtxId || '/FromAddr');
	cERR_TYPE_ATT CONSTANT mcore.attachments.service%TYPE := 'Error';	

    FUNCTION registerError
    (pDescription mcore.error_reports.description%TYPE,
     pDetails mcore.error_reports.details%TYPE,
	 pPh0 mcore.error_reports.ph0%TYPE := NULL,
     pPh1 mcore.error_reports.ph1%TYPE := NULL,
     pPh2 mcore.error_reports.ph2%TYPE := NULL,
     pPh3 mcore.error_reports.ph3%TYPE := NULL,
     pPh4 mcore.error_reports.ph4%TYPE := NULL,
     pPh5 mcore.error_reports.ph5%TYPE := NULL,
     pPh6 mcore.error_reports.ph6%TYPE := NULL,
     pPh7 mcore.error_reports.ph7%TYPE := NULL,
     pPh8 mcore.error_reports.ph8%TYPE := NULL,
     pPh9 mcore.error_reports.ph9%TYPE := NULL
    )
    RETURN mcore.error_reports.id%TYPE
    IS
        myunit CONSTANT VARCHAR2(13) := 'registerError';
        vUserId mcore.end_users.id%TYPE := mcauth.auth.getClientId;

        vErrorReportId mcore.error_reports.id%TYPE;
		vDescription VARCHAR2(4000 BYTE);
		vDetails VARCHAR2(4000 BYTE); 
    BEGIN
            slog.debug(pkgCtxId, myUnit);
			
			vDescription := SUBSTRB(pDescription, 1, 4000);
			vDetails := SUBSTRB(pDetails, 1, 4000);

            INSERT INTO mcore.error_reports (id, user_id, description, details, 
               valid, date_created, date_modified, status, ph0, ph1, ph2,
               ph3, ph4, ph5, ph6, ph7, ph8, ph9)
            VALUES (mcore.error_reports_id_seq.nextval, vUserId, vDescription, vDetails, 
               1, sysdate, null, 'NEW', 0, pPh1, pPh2, pPh3, pPh4, pPh5, pPh6, pPh7, pPh8, pPh9)
            RETURNING id INTO vErrorReportId;

            slog.debug(pkgCtxId, myUnit, 'Returning id: ' || vErrorReportId);
            RETURN vErrorReportId;
    END registerError;

    PROCEDURE addErrorAttachment(pErrorReportId mcore.attachments.err_report_id%TYPE,
        pContent BLOB,
        pMimeType mcore.attachments.mime_type%TYPE,
        pFileName mcore.attachments.filename%TYPE)
    IS
        myunit CONSTANT VARCHAR2(18) := 'addErrorAttachment';
		vPom PLS_INTEGER;
		vFileName VARCHAR2(1024);
		vFileHandle BFILE;
		vAttachmentId mcore.attachments.id%TYPE;
    BEGIN
        slog.debug(pkgCtxId, myUnit, 'Execute ' || myunit);
		   
		IF dbms_lob.getlength(pContent) = 0 THEN
			sspkg.raiseError(mcore.common_pck.cERR_FilesizeZero, null, pkgCtxId, myunit);
		END IF;
    
		vAttachmentId := mcore.ATTACHMENTS_ID_SEQ.nextval;
    
		vPom := instr(pFileName, '.', -1);
		IF vPom > 0 THEN
			vFileName := vAttachmentId || substr(pFileName, vPom);    
		ELSE
			vFileName := vAttachmentId;
		END IF;
    
		vFileHandle := util.WriteBLOBToFILE (pDestDirectory => mcore.common_pck.cDIR_ATTACHMENTS, pFileName => vFileName, sourceData => pContent);		

		INSERT INTO mcore.attachments(id, err_report_id, ext_id, service, content, mime_Type, user_id) 
		VALUES (vAttachmentId, pErrorReportId, pErrorReportId, cERR_TYPE_ATT, vFileHandle, pMimeType, mcauth.auth.getClientId);

    END addErrorAttachment;

    FUNCTION isErrorReportPosted(pErrorReportId mcore.attachments.err_report_id%TYPE)
    RETURN BOOLEAN IS
        vPom PLS_INTEGER;
        myunit CONSTANT VARCHAR2(19) := 'isErrorReportPosted';

    BEGIN
        slog.debug(pkgCtxId, myUnit, 'Execute ' || myunit);
        SELECT 1
        INTO vPom
        FROM mcore.error_reports
        WHERE id = pErrorReportId;
        RETURN TRUE;
    EXCEPTION
        WHEN no_data_found THEN
            RETURN FALSE;
    END isErrorReportPosted;

    FUNCTION getRegisteredErrors(pOffset PLS_INTEGER := 1, pArraySize PLS_INTEGER := 10)
    RETURN sys_refcursor
    IS
        myunit CONSTANT VARCHAR2(19) := 'getRegisteredErrors';
        vUserId mcore.end_users.id%TYPE;
        vUsedLanguage VARCHAR2(40);
        
        vOffset PLS_INTEGER;
        vArraySize PLS_INTEGER;

        rez sys_refcursor;

    BEGIN
            slog.debug(pkgCtxId, myUnit);
			
			vUserId := mcauth.auth.getClientId;
			vUsedLanguage := nvl(mcauth.auth.getLang, 'en');

            common_pck.CommonSecurityChecks;

            common_pck.SetOffsetArraySize(pOffset, pArraySize,
                                      vOffset, vArraySize);

            OPEN rez FOR
            SELECT  id, description, details, 
                    valid, date_created, date_modified,
                    status, 
                    mlang.trans(vUsedLanguage, '/Core/Main/Errors/status_' || status) status_translated,
                    admin_comment, ph0, ph1, ph2, ph3, ph4, ph5, ph6, ph7, ph8, ph9
              FROM (SELECT  id, description, details, 
                            valid, date_created, date_modified, status, admin_comment,
                            ph0, ph1, ph2, ph3, ph4, ph5, ph6, ph7, ph8, ph9,
                            ROW_NUMBER() OVER (ORDER BY ID DESC) rn
                    FROM mcore.error_reports
                    WHERE user_id = vUserId)
              WHERE rn BETWEEN vOffset AND (vOffset + vArraySize)
              ORDER BY rn ASC;

            RETURN rez;
    END getRegisteredErrors;
	
FUNCTION isGrantedToUSer(p_role varchar2, p_username varchar2)
RETURN PLS_INTEGER IS
BEGIN
	RETURN util.bool2int(mcadmin.isUserHasRole(p_role, p_username));
END isGrantedToUSer;	
	
PROCEDURE sendErrorNotification
  IS
    PRAGMA AUTONOMOUS_TRANSACTION;
	
    myunit CONSTANT VARCHAR2(21) := 'sendErrorNotification';
    res pls_integer;
	vBodyMsg VARCHAR2(32765 CHAR);
		
	CURSOR c IS
      SELECT er.rowid, er.description subject, er.details message_body, ad.email contact_email, er.id, er.user_id,
			 eu.first_name, eu.last_name, ad.email
      FROM  (mcore.error_reports er
	  JOIN mcore.end_users eu ON (eu.id = er.user_id))
	  CROSS JOIN 
	  (SELECT distinct a.email FROM mcadmin.administrator a JOIN mcadmin.administrator_roles ar ON (a.id = ar.administrator_id)
	  JOIN mcadmin.roles r ON (ar.role_id = r.id) WHERE a.email IS NOT NULL AND r.name IN ('ROLE_ISSUES_MANAGER', 'ROLE_SUPER') and a.enabled = 1) ad
      WHERE er.delivered = 0;

  BEGIN
	slog.debug(pkgCtxId, myUnit);
	
	IF sspkg.ReadBool(pkgCtxId || '/sendNotification') THEN
		slog.debug(pkgCtxId, myUnit, 'Send email notifications');
		FOR messages IN c LOOP
		
		  BEGIN
			slog.debug(pkgCtxId, myUnit, 'Try to send message with id:' || messages.id || ' from user:' || messages.user_id || ' to:' || messages.contact_email);
			-- #8757: Koristi substrb umjesto substr jer smijemo proslijediti maks. 4000 byte-a a ne 4000 znakova!
			vBodyMsg := substrb(
				mlang.trans('bs', '/Core/Main/Errors/errMessageHeader', messages.user_id, messages.last_name, messages.first_name)
				|| messages.subject || chr(10) || chr(10)
				|| messages.message_body, 1, 4000);

			res := msging.send(
				fromAddr=>fromAddr,
				toAddr=>messages.contact_email,
				subject=>'Elba / Prijava problema - (' || messages.user_id || ') ' ||messages.last_name || ' ' || messages.first_name,
				bodyMsg=>vBodyMsg,
				MC_ID=>channelId,
				toExtUser=>NULL);
			slog.debug(pkgCtxId, myUnit, 'Message sent');

			slog.debug(pkgCtxId, myUnit, 'Mark message as delivered ...');
			UPDATE error_reports er
			SET delivered = 1, date_modified = sysdate
			WHERE rowid = messages.rowid;

			COMMIT;
			slog.debug(pkgCtxId, myUnit, 'Mark message as delivered ... OK');
			
		  EXCEPTION
			WHEN SSPKG.SYSEXCEPTION THEN	
				slog.error(pkgCtxId, myUnit,'Sspkg error during send error notification for error report : ' || messages.id || ':' || NVL(sspkg.getErrorUserMessage, sspkg.getErrorMessage));
				ROLLBACK;
			WHEN OTHERS THEN
				slog.error(pkgCtxId, myunit, 'Oracle error during email notification sending encountered: ' || sqlcode || ':' || sqlerrm);
				ROLLBACK;
		  END;
		END LOOP;
	END IF;
  COMMIT;
  END sendErrorNotification;	


  
FUNCTION getErrorReportCount
RETURN PLS_INTEGER IS

    myunit CONSTANT VARCHAR2(19) := 'getErrorReportCount';
    vUserId mcore.end_users.id%TYPE := mcauth.auth.getClientId;
    vResult PLS_INTEGER := 0;
    
BEGIN
    slog.debug(pkgCtxId, myUnit, vUserId);

    SELECT COUNT(*)INTO vResult FROM mcore.error_reports er
    WHERE er.user_id = vUserId;
    
    RETURN vResult;
    
EXCEPTION
    WHEN OTHERS THEN
      RETURN 0;
    
END getErrorReportCount;



FUNCTION getErrorReport(vErrorReportId mcore.error_reports.id%TYPE)
RETURN sys_refcursor IS

    myunit CONSTANT VARCHAR2(14) := 'getErrorReport';
    vUserId mcore.end_users.id%TYPE := mcauth.auth.getClientId;
    rez sys_refcursor;
    vUsedLanguage VARCHAR2(40);

    
BEGIN
    slog.debug(pkgCtxId, myUnit, vUserId);
	
	vUsedLanguage := nvl(mcauth.auth.getLang, 'en');

      OPEN rez FOR
            SELECT  id, description, details,
                    valid, date_created, date_modified,
                    status,
                    mlang.trans(vUsedLanguage, '/Core/Main/Errors/status_' || status) status_translated,
                    admin_comment, ph0, ph1, ph2, ph3, ph4, ph5, ph6, ph7, ph8, ph9
              FROM (SELECT  id, description, details,
                            valid, date_created, date_modified, status, admin_comment,
                            ph0, ph1, ph2, ph3, ph4, ph5, ph6, ph7, ph8, ph9
                    FROM mcore.error_reports
                    WHERE user_id = vUserId
                    and id = vErrorReportId);
    
    RETURN rez;
    
     EXCEPTION
        WHEN no_data_found THEN
            sspkg.raiseError('Report "'||vErrorReportId||'" does NOT exist!', pkgCtxId, myunit);


    
END getErrorReport;
   


END;
/
show error

