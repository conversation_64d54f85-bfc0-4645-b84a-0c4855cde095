Prompt CREATE OR REPLACE PACKAGE mcore.invoice_mgmt_pck 

CREATE OR REPLACE
PACKAGE mcore.invoice_mgmt_pck IS

    pkgCtxId CONSTANT VARCHAR2(28) := '/Core/Main/InvoiceManagement';
	cIprAttribAccountId CONSTANT VARCHAR2(10) := 'ACCOUNT_ID';
	
	vWorkerThread# PLS_INTEGER;

	FUNCTION getIprAttribAccountId RETURN VARCHAR2;


	/* Subscribtion related */

	/* RegisterConsumer - pretplata klijenta na ra�une odre�enog izdavatelja
		Varijanta s dva parametra se koristi od strane administratorskog modula (ukoliko postoji).
		Ovim je omogu�ena registracija bilo kog klijenta pri bilo kom izdavatelju od strane administratora
	*/
	FUNCTION RegisterConsumer(pInvoiceProviderId invoice_provider_clients.ipr_id%TYPE,
							pEndUserId invoice_provider_clients.end_users_id%TYPE,
							pType invoice_provider_clients.type%TYPE,
							pUserReference invoice_provider_clients.user_reference%TYPE,
							pUserComment invoice_provider_clients.user_comment%TYPE,
							pValid invoice_provider_clients.valid%TYPE,
							pUserAgreementId invoice_provider_clients.uat_id%TYPE DEFAULT NULL,
							pResponse VARCHAR2 := NULL,
                            pOtp VARCHAR2 := NULL,
                            pSourceData VARCHAR2 := NULL,
                            pSignature VARCHAR2 := NULL,
							pInvoiceTemplate CLOB DEFAULT NULL)
	RETURN NUMBER;

	-- Overloaded version with auto payment parameters
	FUNCTION RegisterConsumer(pInvoiceProviderId invoice_provider_clients.ipr_id%TYPE,
							pEndUserId invoice_provider_clients.end_users_id%TYPE,
							pType invoice_provider_clients.type%TYPE,
							pUserReference invoice_provider_clients.user_reference%TYPE,
							pUserComment invoice_provider_clients.user_comment%TYPE,
							pValid invoice_provider_clients.valid%TYPE,
							pUserAgreementId invoice_provider_clients.uat_id%TYPE,
							pResponse VARCHAR2,
                            pOtp VARCHAR2,
                            pSourceData VARCHAR2,
                            pSignature VARCHAR2,
							pInvoiceTemplate CLOB,
							pAutoPaymentEnabled invoice_provider_clients.auto_payment_enabled%TYPE,
							pPaymentAccountId invoice_provider_clients.payment_account_id%TYPE)
	RETURN NUMBER;

	PROCEDURE UnregisterConsumer(pId IN PLS_INTEGER,
		pEndUserId invoice_provider_clients.end_users_id%TYPE,
		pType invoice_provider_clients.type%TYPE);

	FUNCTION RegisterConsumer(pInvoiceProviderName invoice_providers.name%TYPE,
							pEndUserId invoice_provider_clients.end_users_id%TYPE,
							pType invoice_provider_clients.type%TYPE,
							pUserReference invoice_provider_clients.user_reference%TYPE,
							pValid invoice_provider_clients.valid%TYPE,
							pUserAgreementId invoice_provider_clients.uat_id%TYPE DEFAULT NULL)
	RETURN NUMBER;

    PROCEDURE UnregisterConsumer(pInvoiceProviderName invoice_providers.name%TYPE,
		pEndUserId invoice_provider_clients.end_users_id%TYPE,
		pType invoice_provider_clients.type%TYPE);
		
	PROCEDURE RejectSubscription(pId IN PLS_INTEGER, pRejectionReason IN VARCHAR2);


	/* hasActiveSubscriptions - govori da li za datog user-a postoji aktivna pretplata
	*/
	FUNCTION hasActiveSubscriptions(pEndUserId invoice_provider_clients.end_users_id%TYPE)
	RETURN BOOLEAN;

	/* getActiveSubscribtions - vra�a listu aktivnih pretplata za datog klijenta tipa CONSUMER

	Rezultantni ref-cursor ima opis

	ipr_id			- Interni ID izdavatelja ra�una (odgovara vrijednosti kolone invoice_provider_clients.ipr_id -> invoice_providers.id za izdavatelja)
	ipr_name		- Naziv izdavatelja ra�una (odgovara vrijednosti kolone invoice_providers.name za izdavatelja)
	user_reference	- Referenca ra�una koju je klijent prethodno unio
	user_comment	- Opcionalni komentar uz pretplatu unesen od strane klijenta
	*/
	FUNCTION getActiveSubscriptions(pEndUserId invoice_provider_clients.end_users_id%TYPE) RETURN SYS_REFCURSOR;

	FUNCTION checkATMCardlessSubscription RETURN BOOLEAN;
	FUNCTION checkQRPaySubscription RETURN BOOLEAN;


	/* getProviders - vra�a listu izdavaoca ra�una

	Rezultantni ref-cursor ima opis
	ipr_id			- Interni ID izdavatelja ra�una (odgovara vrijednosti kolone invoice_provider_clients.ipr_id -> invoice_providers.id za izdavatelja)
	ipr_name		- Naziv izdavatelja ra�una (odgovara vrijednosti kolone invoice_providers.name za izdavatelja)
	ipr_description - Opis izdavatelja ra�una (dodatne informacije o istim)
	blob_data 		- Slika primjera ra�una u cilju lak�eg pronala�enja reference ra�una (BLOB)
	agreement_text	- Tekst ugovora o prihvatanju primanja ra�una putem aplikacije (CLOB)
	*/
	FUNCTION getProviders RETURN SYS_REFCURSOR;

	/* Invoice related */

	/* getInvoices - vra�a listu ra�una za klijenta

	Ulazni parametri
	pEndUserId - Interni ID izdavatelja ra�una (odgovara vrijednosti kolone invoice_provider_clients.ipr_id -> invoice_providers.id za izdavatelja)
	pAdminUserId - Interni ID Elba user-a  - administratora dodijeljenog izdavatelju ra�una

	Rezultantni ref-cursor ima opis
		** NIJE UPISANO **
	*/
    FUNCTION getInvoices(pEndUserId IN end_users.id%TYPE, pStatus IN VARCHAR2 DEFAULT '%')
    RETURN sys_refcursor;

	/** Vra�a broj ra�una za datog user-a i status.
	* @param pEndUserId () ID Elba user-a
	* @param pStatus () Status ra�una. Dozvoljene vrijednosti 0,1,2,3,4,5 ili %
	* @return () Broj ra�una za datog user-a u statusu pStatus
	* */
	FUNCTION getInvoicesCount(pEndUserId IN end_users.id%TYPE, pStatus IN VARCHAR2 DEFAULT '%')
	RETURN PLS_INTEGER;

	/** Vra�a pla�ene ra�una za datog user-a.
	* @param pEndUserId () ID Elba user-a
	* @return () SYS-REF cursor s ra�unima za datog user-a u statusu 2
	* */
    FUNCTION getProcessedInvoices(pEndUserId IN end_users.id%TYPE)
    RETURN sys_refcursor;

	/** Vra�a broj pla�enih ra�una za datog user-a.
	* @param pEndUserId () ID Elba user-a
	* @return () Broj ra�una za datog user-a u statusu 2
	* */
	FUNCTION getProcessedInvoicesCount(pEndUserId IN end_users.id%TYPE)
	RETURN PLS_INTEGER;

    /**
     * Vra�a ra�una za dati ID.
     * @param pInvoiceId () ID ra�una
	 * @return () Podatke o ra�unu
     */
    FUNCTION getInvoice(pInvoiceId IN mcore.invoices.id%TYPE) RETURN SYS_REFCURSOR;

    PROCEDURE processInvoice(pInvoiceID IN invoices.id%TYPE, pAction IN PLS_INTEGER, pTranpayId IN NUMBER DEFAULT NULL, pExtrefName IN tranpay_extref.extref_name%TYPE DEFAULT NULL);

	PROCEDURE changeInvoiceStatus(pTranpayId IN VARCHAR2, pStatus IN PLS_INTEGER);


	/* getManagedProviders - vra�a listu izdavaoca ra�una za koje je trenutno prijavljeni korisnik kao administrator prijavljen

	Ulazni parametri
	pEndUsersId 	- Interni ID Elba user-a koji se predstavlja kao administrator

	Rezultantni ref-cursor ima opis
	ipr_id			- Interni ID izdavatelja ra�una (odgovara vrijednosti kolone invoice_provider_clients.ipr_id -> invoice_providers.id za izdavatelja)
	ipr_name		- Naziv izdavatelja ra�una (odgovara vrijednosti kolone invoice_providers.name za izdavatelja)
	ipr_description - Opis izdavatelja ra�una (dodatne informacije o istim)
	blob_data 		- Slika primjera ra�una u cilju lak�eg pronala�enja reference ra�una (BLOB)
	mime_type		- Mime type slike (blob_data)
	agreement_text	- Tekst ugovora o prihvatanju primanja ra�una putem aplikacije (CLOB)
	*/
	FUNCTION getManagedProviders(pEndUserId invoice_provider_clients.end_users_id%TYPE) RETURN SYS_REFCURSOR;

	/* getClients - vra�a listu klijenata koji su pretpla�eni na primanje ra�una od strane odabranog izdavatelja ra�una
	*
	* @param pInvoiceProviderId ID izdavatelja ra�una
	* @param pAdminUserId ID administratora izdavatelja ra�una
	* @return SYS_REFCURSOR s sljede�im opisom
	* 	id				INTEGER
	*	user_reference	VARCHAR2()
	*	date_subscribed DATE
	*   end_users_id    INTEGER
	*/
	FUNCTION getClients(pInvoiceProviderId IN invoice_provider_clients.ipr_id%TYPE, pAdminUserId IN invoice_provider_clients.end_users_id%TYPE)
	RETURN SYS_REFCURSOR;

	/* getClientSubscription - vra�a REF cursor s podacima o pretplati i potpisanim uslovima kori�tenja
	*
	* @param pInvoiceProviderId ID izdavatelja ra�una
	* @param pAdminUserId ID administratora izdavatelja ra�una
	* @param pEndUserId ID Elba user-a
	* @return SYS_REFCURSOR s sljede�im opisom

	invoice_provider_client_id	- (INTEGER) ID pretplate
	user_id						- (INTEGER) ID Elba user-a
	first_name					- (VARCHAR2) Ime Elba user-a
	last_name					- (VARCHAR2) Prezime Elba user-a
	user_comment				- (VARCHAR2) Opcionalni komentar user-a uz pretplatu
	user_reference				- (VARCHAR2) Referenca korisnika
	date_subscribed				- (DATE) Datum pretplate
	agreement_signature			- (VARCHAR2) Hash string potpisa uslova kori�tenja
	agreement_text				- (CLOB) Text uslova kori�tenja
	valid_from					- (DATE) Datum po�etka va�enja uslova kori�tenja
	invoice_provder_id 			- (INTEGER) ID izdavatelja ra�una
	invoice_provder_name 		- (VARCHAR2) Naziv izdavatelja ra�una
	aea_extauth_id 				- (INTEGER) ID externe autentikacije
	aea_device_id 				- (VARCHAR2) Identifikator ure�aja
	application_id				- (VARCHAR2) Identifikator aplikacije (REF. na applications.id)
	signature_method 			- (VARCHAR2) Metoda potpisa
	action_signature			- (VARCHAR2) HASH kombinacije informacija koji predstavlja potpis uslova kori�tenja	*/
	FUNCTION getClientSubscription(
		pInvoiceProviderId IN invoice_provider_clients.ipr_id%TYPE,
		pAdminUserId IN invoice_provider_clients.end_users_id%TYPE,
		pEndUserId IN end_users.id%TYPE)
	RETURN SYS_REFCURSOR;


	-- Batch related methods
	FUNCTION CreateBatch(
		pInvoiceProviderId IN invoice_providers.id%TYPE,
		pName IN invoice_batches.name%TYPE,
		pValid IN invoice_batches.valid%TYPE)
	RETURN invoice_batches.id%TYPE;

	FUNCTION CreateBatch(
		pInvoiceProviderId IN invoice_providers.id%TYPE,
		pEndUserId invoice_provider_clients.end_users_id%TYPE,
		pName IN invoice_batches.name%TYPE,
		pValid IN invoice_batches.valid%TYPE)
	RETURN invoice_batches.id%TYPE;

  PROCEDURE ChangeBatchStatus(
		pInvoiceBatchId IN invoice_batches.id%TYPE,
		pValid IN invoice_batches.valid%TYPE);

	PROCEDURE ChangeBatchStatus(pEndUserId invoice_provider_clients.end_users_id%TYPE,
		pInvoiceBatchId IN invoice_batches.id%TYPE,
		pValid IN invoice_batches.valid%TYPE);

	PROCEDURE RemoveBatch(pInvoiceBatchId IN invoice_batches.id%TYPE);
	PROCEDURE RemoveBatch(pInvoiceBatchId IN invoice_batches.id%TYPE, pEndUserId invoice_provider_clients.end_users_id%TYPE);
	
	FUNCTION isExistingBatch(pInvoiceProviderId INTEGER, pBatchName IN VARCHAR2)
	RETURN PLS_INTEGER;

	PROCEDURE RemoveInvoice(pInvoiceId IN invoices.id%TYPE);

	FUNCTION getBatches(
		pInvoiceProviderId IN invoice_batches.ipr_id%TYPE,
		pEndUserId invoice_provider_clients.end_users_id%TYPE)
	RETURN sys_refcursor;

	FUNCTION UploadInvoices(
		pInvoiceProviderId IN invoice_providers.id%TYPE,
		pEndUserId invoice_provider_clients.end_users_id%TYPE,
		pBatchName IN invoice_batches.name%TYPE,
		pInvoiceList IN invoice_list,
		pInvoiceBatchId OUT invoice_batches.id%TYPE,
		pImportResult OUT invoice_import_result_list)
	RETURN BOOLEAN;

	FUNCTION getUploadedInvoices(
				pInvoiceBatchId IN invoice_batches.id%TYPE,
				pAdminUserId invoice_provider_clients.end_users_id%TYPE)
	RETURN sys_refcursor;

	/* setInvoiceProviderData - a�urira podatke o izdavateljima ra�una
	*
	* @param pId ID izdavatelja ra�una
	* @param pDescription Opis izdavatelja ra�una
	* @param pBlobData Logo izdavatelja ra�una
	* @param pMimeType Mime-type logo-a
	* @param pAgreementText Uslovi kori�tenja usluge
	*/
	PROCEDURE setInvoiceProviderData(
				pId IN invoice_providers.id%TYPE,
				pDescription IN invoice_providers.description%TYPE,
				pBlobData IN invoice_providers.blob_data%TYPE,
				pMimeType IN invoice_providers.mime_type%TYPE,
				pAgreementText IN invoice_providers.agreement_text%TYPE);

	-- Deprecated
	/* getUserAgreementText - vra�a uslove kori�tenja za dati ID izdavatelja ra�una
	*
	* @param pId ID izdavatelja ra�una
	* @return CLOB - Text uslova kori�tenja usluge
	*/
	--FUNCTION getUserAgreementText(pID IN invoice_providers.id%TYPE)
	--RETURN user_agreement.agreement_text%TYPE;

	PROCEDURE AssignAgreementTextToProvider(pInvoiceProviderID IN invoice_providers.id%TYPE, pUserAgreementText IN user_agreement.agreement_text%TYPE);

	FUNCTION getActiveUserAgreement(pInvoiceProviderId IN invoice_providers.id%TYPE)
	RETURN SYS_REFCURSOR;

	FUNCTION getUserAgreement(pInvoiceProviderId IN invoice_providers.id%TYPE)
	RETURN SYS_REFCURSOR;

	FUNCTION getActiveUserAgreement(pInvoiceProviderName IN invoice_providers.name%TYPE)
	RETURN SYS_REFCURSOR;

	FUNCTION CreateTranpayForInvoice(
		pInvoiceId IN invoices.id%TYPE,
		pInvoiceProviderName IN invoice_providers.name%TYPE,
		pInvoiceNo IN invoices.group_id%TYPE,
		pSenderBankAccountId IN tranpays.account_id%TYPE,
		pSenderName IN VARCHAR2,
		pTranval IN tranpays.tranval%TYPE,
		pTranpayDescription IN tranpays.description%TYPE,
		pReference IN VARCHAR2,
		pResponse VARCHAR2 := NULL,
		pOtp VARCHAR2 := NULL,
        pSourceData VARCHAR2 := NULL,
        pSignature VARCHAR2 := NULL)
	RETURN tranpays.id%TYPE;

	PROCEDURE SendSMSNotification;

	FUNCTION UploadInvoice_v2(
		pInvoiceBatchId IN invoices.ibe_id%TYPE,
		pInvoiceProviderId IN invoices.ipr_id%TYPE,
		pSubscriptionId IN invoice_provider_clients.id%TYPE,
		pUserReference IN invoice_provider_clients.user_reference%TYPE,
        pExternalClientId IN invoice_provider_clients.external_client_id%TYPE,
		pInvoiceNo IN invoices.invoice_no%TYPE,
		pPaymentType IN invoices.payment_type%TYPE,
		pDescription IN invoices.description%TYPE,
		pTranval IN invoices.tranval%TYPE,
		pCurrencyId IN invoices.tranval_currency_id%TYPE,
		pBeneficiaryName IN VARCHAR2,
		pBeneficiaryAccount IN VARCHAR2,
		pMessage IN CLOB,
		pBILLING_PERIOD IN VARCHAR2,
		pBILLING_DATE IN DATE,
		pPAYMENT_DUE_DATE IN DATE,
		pImportResult OUT invoice_import_result
	) RETURN BOOLEAN;
	
	FUNCTION UploadInvoice_v2(
		pInvoiceId IN OUT invoices.id%TYPE,
		pInvoiceBatchId IN invoices.ibe_id%TYPE,
		pInvoiceProviderId IN invoices.ipr_id%TYPE,
		pSubscriptionId IN invoice_provider_clients.id%TYPE,
		pUserReference IN invoice_provider_clients.user_reference%TYPE,
		pExternalClientId IN invoice_provider_clients.external_client_id%TYPE,
		pInvoiceNo IN invoices.invoice_no%TYPE,
		pPaymentType IN invoices.payment_type%TYPE,
		pDescription IN invoices.description%TYPE,
		pTranval IN invoices.tranval%TYPE,
		pCurrencyId IN invoices.tranval_currency_id%TYPE,
		pBeneficiaryName IN VARCHAR2,
		pBeneficiaryAccount IN VARCHAR2,
		pMessage IN CLOB,
		pBILLING_PERIOD IN VARCHAR2,
		pBILLING_DATE IN DATE,
		pPAYMENT_DUE_DATE IN DATE,
		pImportResult OUT invoice_import_result
	) RETURN BOOLEAN ;
	
	FUNCTION ProcessInvoiceFromInterface (pId invoice_import_iface_tbl.id%TYPE)
	RETURN invoice_import_result;

	PROCEDURE RgstrIfaceBtchForAsyncPrcssing (pBatchId invoice_import_iface_tbl.ibe_id%TYPE);
	PROCEDURE ProcessBatchFromInterface (pBatchId invoice_import_iface_tbl.ibe_id%TYPE, pSendNotification IN INTEGER DEFAULT 0);
	
	FUNCTION getSubscriptions(pInvoiceProviderId IN invoice_provider_clients.ipr_id%TYPE)
	RETURN SYS_REFCURSOR;

	FUNCTION getSubscriptionsCount(pInvoiceProviderId IN invoice_provider_clients.ipr_id%TYPE)
	RETURN PLS_INTEGER;

	PROCEDURE RemoveInvoiceFromInterface (pId invoice_import_iface_tbl.id%TYPE);
	PROCEDURE RemoveBatchFromInterface (pBatchId invoice_import_iface_tbl.ibe_id%TYPE);

	PROCEDURE NtfyPrvderAboutTrpyStsChange (pTranpay obj$tranpay);

	FUNCTION checkInvoiceAccess(pInvoiceId invoices.id%TYPE) 
   	RETURN BOOLEAN;
	
	PROCEDURE InvalidateSubscription(slct$ipc IN OUT cg$invoice_provider_clients.cg$row_type);
	PROCEDURE InvalidateSubscription(pSubscriptionId PLS_INTEGER);
	
	PROCEDURE checkUserReference(
		pInvoiceProviderId IN invoice_providers.id%TYPE,
		pUserReference IN VARCHAR2,
		pSessionLanguage IN VARCHAR2,
		pResultCode OUT INTEGER,
		pResultMessage OUT VARCHAR2);
		
	PROCEDURE checkUserReference(
		pInvoiceProviderId IN invoice_providers.id%TYPE,
		pUserReference IN VARCHAR2,
		pSessionLanguage IN VARCHAR2,
		pUserId IN INTEGER,
		pAccOwnerId IN VARCHAR2,
		pResultCode OUT INTEGER,
		pResultMessage OUT VARCHAR2);
		
	FUNCTION isAdmin(pInvoiceProviderId IN invoice_provider_clients.ipr_id%TYPE,
		pUsername IN VARCHAR2)
	RETURN INTEGER;
	
	FUNCTION getAdminUserId (pInvoiceProviderId IN invoice_provider_clients.ipr_id%TYPE, pUsername IN VARCHAR2)
	RETURN invoice_provider_clients.end_users_id%TYPE;
		
	PROCEDURE EnqueueMessage(pMessage IN obj$ipr_clients_aq_dt);
	PROCEDURE EnqueueSubscriptionMessage(cg$rec IN cg$invoice_provider_clients.cg$row_type);
	PROCEDURE EnqueueUnSubscriptionMessage(cg$rec IN cg$invoice_provider_clients.cg$row_type);
	
	FUNCTION getProviderAccountId (pInvoiceProviderId invoice_providers.id%TYPE)
	RETURN VARCHAR2;
	FUNCTION getInvoiceProviderDetail (pIPRAttribId IN VARCHAR2, pIprDetailsTable IN cg$ipr_details.cg$table_type) 
	RETURN VARCHAR2;
	FUNCTION getInvoiceProviderDetails (pInvoiceProviderId IN invoice_providers.id%TYPE) 
	RETURN cg$ipr_details.cg$table_type;
	FUNCTION getInvoiceProviderName(pInvoiceProviderId IN invoice_providers.id%TYPE)
	RETURN invoice_providers.name%TYPE;
	FUNCTION getInvoiceProviderData (pInvoiceProviderId IN invoice_providers.id%TYPE) 
	RETURN cg$invoice_providers.cg$row_type;
	
	PROCEDURE checkInvoiceAmount(pInvoiceId invoices.id%TYPE, pInvoiceAmount invoices.tranval%TYPE);

	PROCEDURE getInvoiceProvidersForDetail(pIprId IN invoice_providers.id%TYPE DEFAULT NULL,
										   pIprAttribId IN VARCHAR2 DEFAULT '%', 
										   pIprDetailValue IN VARCHAR2 DEFAULT '%',  
										   pAllInvoiceProviders OUT sys_refcursor,
									       pAllIprDetails OUT sys_refcursor);
										   
	PROCEDURE getInvoiceProvidersForService(pIprId IN NUMBER DEFAULT NULL,
											pAdditionalServiceType IN VARCHAR2 DEFAULT NULL,
											pAllInvoiceProviders OUT sys_refcursor,
											pAllIprDetails OUT sys_refcursor);
	
	FUNCTION getActiveSubscriptsForReqType(pReqTypeId IN VARCHAR2 DEFAULT NULL) RETURN SYS_REFCURSOR;	
	
	PROCEDURE UpdateConsumer(pIpcId invoice_provider_clients.id%TYPE,
							 pUserReference VARCHAR2,
							 pUserComment VARCHAR2,
		                     pInvoiceTemplate CLOB);

	-- Overloaded version with auto payment parameters
	PROCEDURE UpdateConsumer(pIpcId invoice_provider_clients.id%TYPE,
							 pUserReference VARCHAR2,
							 pUserComment VARCHAR2,
		                     pInvoiceTemplate CLOB,
		                     pAutoPaymentEnabled invoice_provider_clients.auto_payment_enabled%TYPE,
		                     pPaymentAccountId invoice_provider_clients.payment_account_id%TYPE);
							 
	FUNCTION getInvoiceProviderForReqType(pReqTypeId request_types.id%TYPE)
	RETURN sys_refcursor;

	PROCEDURE ProcessAutoPayment(pInvoiceId invoices.id%TYPE);

	PROCEDURE SendAutoPaymentNotification(pInvoiceId invoices.id%TYPE, pTranpayId tranpays.id%TYPE, pSuccess BOOLEAN, pErrorMessage VARCHAR2 DEFAULT NULL);

	PROCEDURE ProcessBatchAutoPayments(pBatchId invoice_batches.id%TYPE);

END invoice_mgmt_pck;
/

show errors

Prompt
Prompt GRANT EXECUTE ON mcore.invoice_mgmt_pck TO mflex
GRANT EXECUTE ON mcore.invoice_mgmt_pck TO mflex
/

Prompt
Prompt GRANT EXECUTE ON mcore.invoice_mgmt_pck TO mmobile
GRANT EXECUTE ON mcore.invoice_mgmt_pck TO mmobile
/

Prompt
Prompt GRANT EXECUTE ON mcore.invoice_mgmt_pck TO mc_super, mc_usermanager, mc_admin, mcadmin
GRANT EXECUTE ON mcore.invoice_mgmt_pck TO mc_super, mc_usermanager, mc_admin, mcadmin
/

Prompt
Prompt GRANT EXECUTE ON mcore.invoice_mgmt_pck TO mscheduler
GRANT EXECUTE ON mcore.invoice_mgmt_pck TO mscheduler
/

Prompt
Prompt GRANT EXECUTE ON mcore.invoice_mgmt_pck TO strmadmin
GRANT EXECUTE ON mcore.invoice_mgmt_pck TO strmadmin
/


Prompt
Prompt GRANT EXECUTE ON mcore.invoice_mgmt_pck TO mw_user
GRANT EXECUTE ON mcore.invoice_mgmt_pck TO mw_user
/