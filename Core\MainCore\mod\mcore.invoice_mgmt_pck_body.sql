set define off
Prompt CREATE OR REPLACE PACKAGE BODY mcore.invoice_mgmt_pck

CREATE OR REPLACE 
PACKAGE BODY mcore.invoice_mgmt_pck IS

    cERR_InvalidInvoiceStatus       CONSTANT VARCHAR2(100) := pkgCtxId || '/err/InvalidInvoiceStatus';
    cERR_InvalidInvoice             CONSTANT VARCHAR2(100) := pkgCtxId || '/err/InvalidInvoice';
    cERR_MissTranpayIdForInvoice    CONSTANT VARCHAR2(100) := pkgCtxId || '/err/MissingTranpayIdForInvoice';
	cERR_MissingAccOwnerInfo        CONSTANT VARCHAR2(100) := pkgCtxId || '/err/MissingAccountOwnerInfo';
	cERR_InvoiceParsingError        CONSTANT VARCHAR2(100) := pkgCtxId || '/err/invoiceParsingError';
	cERR_DuplicateAssignment        CONSTANT VARCHAR2(100) := pkgCtxId || '/err/DuplicateAssignment';
	cERR_InvalidUserReference       CONSTANT VARCHAR2(100) := pkgCtxId || '/err/InvalidUserReference';
	cERR_InvalidInvoiceProvider     CONSTANT VARCHAR2(100) := pkgCtxId || '/err/InvalidInvoiceProvider';
	cERR_MissingUserAgreement       CONSTANT VARCHAR2(100) := pkgCtxId || '/err/MissingUserAgreement';
	cERR_MissingProviderAccounts    CONSTANT VARCHAR2(100) := pkgCtxId || '/err/MissingProviderAccounts';
	cERR_InvalidAgreement			CONSTANT VARCHAR2(100) := pkgCtxId || '/err/InvalidUserAgreement';
	cERR_NoAdminPrivilege     		CONSTANT VARCHAR2(100) := pkgCtxId || '/err/NoAdminPrivilege';
	cERR_NoSubscribtion     		CONSTANT VARCHAR2(100) := pkgCtxId || '/err/NoSubscribtion';
	cERR_InvalidBatch     		    CONSTANT VARCHAR2(100) := pkgCtxId || '/err/InvalidBatch';
	cERR_InternalError     		    CONSTANT VARCHAR2(100) := pkgCtxId || '/err/InternalError';
	cERR_MultipleATMCardLessSubs    CONSTANT VARCHAR2(100) := pkgCtxId || '/err/MultipleATMCardLessSubscriptions';
	cERR_MultipleQRPaySubs    		CONSTANT VARCHAR2(100) := pkgCtxId || '/err/MultipleQRPaySubscriptions';
	cERR_InvoiceAmountNotAllowed   	CONSTANT VARCHAR2(100) := pkgCtxId || '/err/InvoiceAmountNotAllowed';
	cERR_InvalidIprClient  				CONSTANT VARCHAR2(100) := pkgCtxId || '/err/InvalidInvoiceProviderClient';
	cUserAgreementTextTypeId		CONSTANT VARCHAR2(16) := 'INVOICE_PROVIDER';
	cExtRefPostingScheme			CONSTANT VARCHAR2(14) := 'SEMA_KNJIZENJA';
	
	v_cg_result BOOLEAN;
	vMSG VARCHAR2(512);
	vERROR VARCHAR2(1);
	vMSG_TYPE VARCHAR2(3);
	vMSGID INTEGER;
	vLOC VARCHAR2(2000 BYTE);
	cbnficry_acc_rstrct_all CONSTANT VARCHAR2(3) := 'ALL';
	cbnficry_acc_rstrct_enlisted CONSTANT VARCHAR2(8) := 'ENLISTED';
	cbnficry_acc_rstrct_defaulted CONSTANT VARCHAR2(9) := 'DEFAULTED';
	
	cValidFalse	CONSTANT PLS_INTEGER := 0;
	cValidTrue	CONSTANT PLS_INTEGER := 1;
	
	cPaymentBelowNotAllowed NUMBER(1) := 0;
	cPaymentOverNotAllowed NUMBER(1) := 0;
		
	FUNCTION getIprAttribAccountId RETURN VARCHAR2 IS
	BEGIN
		RETURN cIprAttribAccountId;
	END getIprAttribAccountId;

	PROCEDURE CreateIprDetailsRecord(pIprId IN ipr_details.ipr_id%TYPE,
										pIabId IN ipr_details.iab_id%TYPE,
										pDataVchar IN ipr_details.data_vchar%TYPE,
										pDataBlob IN ipr_details.data_blob%TYPE)
    IS
		myunit CONSTANT VARCHAR2(30) := 'CreateIprDetailsRecord';

		CURSOR cIprDetails IS
			SELECT IPR_ID
			, IAB_ID
			, ID
			, DESCRIPTION
			, DATA_VCHAR
			, DATA_BLOB
			, rowid the_rowid
			, NULL JN_NOTES
		FROM   IPR_DETAILS
		WHERE IPR_ID = pIprId
		AND IAB_ID = pIabId;	-- Ne treba nam kolona ID jer ho�emo sve slogove za IPR_ID i IAB_ID

		TYPE tt$ipr_details IS TABLE OF cg$ipr_details.cg$row_type;
		t$ipr_details tt$ipr_details;

		cg$ipr_attribs_rec cg$ipr_attribs.cg$row_type;

		cg$ipr_details_rec cg$ipr_details.cg$row_type;
		cg$ind cg$ipr_details.cg$ind_type;

	BEGIN
		slog.debug(pkgCtxId, myunit, pIprId || ':' || pIabId || ':' || pDataVchar);
		IF (pIprId IS NULL OR pIabId IS NULL) THEN
			sspkg.raiseError(common_pck.cERR_MissingParameter, null, pkgCtxId, myunit);
		END IF;

		slog.debug(pkgCtxId, myunit, 'Retrieve atributes data');
		cg$ipr_attribs_rec.id := pIabId;
		cg$ipr_attribs.slct(cg$ipr_attribs_rec);

		slog.debug(pkgCtxId, myunit, 'Prepare record with new/updated data');
		cg$ipr_details_rec.ipr_id 		:= pIprId;
		cg$ipr_details_rec.iab_id 		:= pIabId;
		cg$ipr_details_rec.data_vchar 	:= pDataVchar;
		cg$ipr_details_rec.data_blob 	:= pDataBlob;

		slog.debug(pkgCtxId, myunit, 'Retrieve previous detail data');
		OPEN cIprDetails;
		FETCH cIprDetails BULK COLLECT INTO t$ipr_details;
		CLOSE cIprDetails;

		IF t$ipr_details.COUNT = 0 THEN
			slog.debug(pkgCtxId, myunit, 'Previous record not found');

			INSERT INTO ipr_details (ipr_id, iab_id, data_vchar, data_blob)
			VALUES (cg$ipr_details_rec.ipr_id, cg$ipr_details_rec.iab_id, cg$ipr_details_rec.data_vchar, cg$ipr_details_rec.data_blob)
			RETURNING ROWID, id INTO cg$ipr_details_rec.the_rowid, cg$ipr_details_rec.id;
			slog.debug(pkgCtxId, myunit, 'Created new record with ID ' || cg$ipr_details_rec.id);

		ELSE

			IF cg$ipr_attribs_rec.occurence <> 1 THEN
				-- Unsupported
				slog.error(pkgCtxId, myunit, cERR_InternalError, pIprId || ':' || pIabId || ':' || cg$ipr_attribs_rec.occurence);
				sspkg.raiseError(cERR_InternalError, null, pkgCtxId, myunit);
			END IF;

			slog.debug(pkgCtxId, myunit, 'Previous record found');

			FOR i IN t$ipr_details.FIRST..t$ipr_details.LAST LOOP

				slog.debug(pkgCtxId, myunit, 'Processing ' || i || ' record');
				-- Mark columns which should be compared

				-- Copy old ROWID for fast processing
				cg$ipr_details_rec.the_rowid := t$ipr_details(i).the_rowid;
				-- Mark columns which should not be updated
				cg$ind.ipr_id := FALSE;
				cg$ind.iab_id := FALSE;
				cg$ind.id := FALSE;
				cg$ind.data_vchar := TRUE;
				cg$ind.data_blob := TRUE;

				cg$ipr_details.upd(
					cg$rec => cg$ipr_details_rec,
					cg$ind => cg$ind,
					do_upd => TRUE,
					cg$pk => NULL);

			END LOOP;

		END IF;

	END CreateIprDetailsRecord;
	
 PROCEDURE EnqueueMessage(pMessage IN obj$ipr_clients_aq_dt)
  IS

	enqueue_options     	DBMS_AQ.enqueue_options_t;
	message_properties  	DBMS_AQ.message_properties_t;
	message_handle      	RAW(16);

	myunit 					CONSTANT VARCHAR2(30) := 'EnqueueMessage';
  BEGIN
	slog.debug(pkgCtxId, myunit);
	
	IF sspkg.ReadBool(pkgCtxId || '/sendNotificationMessage') THEN
		slog.debug(pkgCtxId, myunit, 'Send notification enabled');
		
		message_properties.correlation := pMessage.service_name;

		slog.debug(pkgCtxId, myunit, 'Message properties set');
		
		DBMS_AQ.ENQUEUE(
			queue_name => 'STRMADMIN.IPR_CLIENTS_AQ',
			enqueue_options => enqueue_options,
			message_properties => message_properties,
			payload => pMessage,
			msgid => message_handle);
			
	  slog.debug(pkgCtxId, myunit, 'Message sent to queue');
	ELSE
		slog.debug(pkgCtxId, myunit, 'Send notification not enabled');
	END IF;
  END EnqueueMessage;

  PROCEDURE EnqueueSubscriptionMessage(cg$rec IN cg$invoice_provider_clients.cg$row_type)
  IS
	 myunit 		CONSTANT VARCHAR2(30) := 'EnqueueSubscriptionMessage';
     message		obj$ipr_clients_aq_dt;
	 vServiceName 	invoice_providers.service_name%TYPE;
  BEGIN
	slog.debug(pkgCtxId, myunit, cg$rec.ipr_id);

	BEGIN
		SELECT service_name
		INTO vServiceName
		FROM mcore.invoice_providers
		WHERE id = cg$rec.ipr_id;
	EXCEPTION
		WHEN no_data_found THEN
			slog.error(pkgCtxId, myunit, cERR_InvalidInvoiceProvider, cg$rec.ipr_id);
			sspkg.raiseError(cERR_InvalidInvoiceProvider, null, pkgCtxId, myunit);
		WHEN OTHERS THEN
			slog.error(pkgCtxId, myunit, sqlerrm);
			sspkg.raiseError(cERR_InvalidInvoiceProvider, null, pkgCtxId, myunit);
	END;

	-- Ukoliko se u buducnosti pojavi neka vrsta nova razmjene koja podrazumijeva da je upisan servise name, ali ne i da se radi enqueue za istu, onda je ovdje potrebno istu ukljuciti u exclusion listu
	IF vServiceName IS NOT NULL AND vServiceName NOT IN (common_pck.cClickPayService_FileExchange) THEN
		slog.debug(pkgCtxId, myunit, vServiceName);
		
		message := new obj$ipr_clients_aq_dt(vServiceName, 'SUBSCRIBE');
		
		message.id						:= cg$rec.id;
		message.ipr_id					:= cg$rec.ipr_id;
		message.end_users_id       	    := cg$rec.end_users_id;
		message.user_reference		    := cg$rec.user_reference;
		message.type				    := cg$rec.type;
		message.valid				    := cg$rec.valid;
		message.date_subscribed		    := cg$rec.date_subscribed;
		message.user_comment		    := cg$rec.user_comment;
		message.uat_id				    := cg$rec.uat_id;
		message.signature_id		    := cg$rec.signature_id;
		message.signature			    := cg$rec.signature;
		message.external_client_id	    := cg$rec.external_client_id;
		message.acc_owner_id		    := cg$rec.acc_owner_id;

		slog.debug(pkgCtxId, myunit, 'Message prepared');

		EnqueueMessage(pMessage  => message);
		slog.debug(pkgCtxId, myunit, 'Message post to delivery');
    END IF;
  END EnqueueSubscriptionMessage;

  PROCEDURE EnqueueUnSubscriptionMessage(cg$rec IN cg$invoice_provider_clients.cg$row_type)
  IS

     myunit 		CONSTANT VARCHAR2(30) := 'EnqueueUnSubscriptionMessage';
	 message		obj$ipr_clients_aq_dt;
     vServiceName 	invoice_providers.service_name%TYPE;
  BEGIN
	slog.debug(pkgCtxId, myunit, cg$rec.ipr_id);

	BEGIN
		SELECT service_name
		INTO vServiceName
		FROM mcore.invoice_providers
		WHERE id = cg$rec.ipr_id;
	EXCEPTION
	    WHEN no_data_found THEN
			slog.error(pkgCtxId, myunit, cERR_InvalidInvoiceProvider, cg$rec.ipr_id);
			sspkg.raiseError(cERR_InvalidInvoiceProvider, null, pkgCtxId, myunit);
		WHEN OTHERS THEN
			slog.error(pkgCtxId, myunit, sqlerrm);
			sspkg.raiseError(cERR_InvalidInvoiceProvider, null, pkgCtxId, myunit);
	END;
	
	IF vServiceName IS NOT NULL AND vServiceName NOT IN (common_pck.cClickPayService_FileExchange) THEN
		slog.debug(pkgCtxId, myunit, vServiceName);
		
		message := new obj$ipr_clients_aq_dt(vServiceName, 'UNSUBSCRIBE');
		
		message.id						:= cg$rec.id;
		message.ipr_id					:= cg$rec.ipr_id;
		message.end_users_id       	    := cg$rec.end_users_id;
		message.user_reference		    := cg$rec.user_reference;
		message.type				    := cg$rec.type;
		message.valid				    := cg$rec.valid;
		message.date_subscribed		    := cg$rec.date_subscribed;
		message.user_comment		    := cg$rec.user_comment;
		message.uat_id				    := cg$rec.uat_id;
		message.signature_id		    := cg$rec.signature_id;
		message.signature			    := cg$rec.signature;
		message.external_client_id	    := cg$rec.external_client_id;
		message.acc_owner_id		    := cg$rec.acc_owner_id;

		slog.debug(pkgCtxId, myunit, 'Message prepared');

		EnqueueMessage(pMessage  => message);
		slog.debug(pkgCtxId, myunit, 'Message post to delivery');
	END IF;

  END EnqueueUnSubscriptionMessage;

	PROCEDURE CheckDupRefAssignment(pInvoiceProviderId invoice_provider_clients.ipr_id%TYPE, pUserReference invoice_provider_clients.user_reference%TYPE, pType invoice_provider_clients.type%TYPE, pEndUserId invoice_provider_clients.end_users_id%TYPE, pValid invoice_provider_clients.valid%TYPE)
  AS
  myunit CONSTANT VARCHAR2(30) := 'CheckDupRefAssignment';
		vEndUserId invoice_provider_clients.end_users_id%TYPE;
  BEGIN
						slog.debug(pkgCtxId, myUnit, 'Check if there is already a valid reference ...');

						SELECT end_users_id
						INTO vEndUserId
						FROM invoice_provider_clients
						WHERE   ipr_id 				= pInvoiceProviderId
							AND	  user_reference 	= pUserReference
							AND	  type 				= pType
							AND   valid 			= cValidTrue;

						slog.error(pkgCtxId, myunit, cERR_DuplicateAssignment, pInvoiceProviderId || ':' || pEndUserId || ':' || vEndUserId || ':' || pType || ':' || pUserReference || ':' || pValid);
						sspkg.raiseError(cERR_DuplicateAssignment, null, pkgCtxId, myunit);

  EXCEPTION
    WHEN no_data_found THEN
      slog.debug(pkgCtxId, myUnit, 'Check if there is already a valid reference ... NOT FOUND');
  END CheckDupRefAssignment;

	FUNCTION RegisterConsumerInt(pInvoiceProviderId invoice_provider_clients.ipr_id%TYPE,
							pEndUserId invoice_provider_clients.end_users_id%TYPE,
							pType invoice_provider_clients.type%TYPE,
							pUserReference invoice_provider_clients.user_reference%TYPE,
							pUserComment invoice_provider_clients.user_comment%TYPE,
							pValid invoice_provider_clients.valid%TYPE,
							pUserAgreementId invoice_provider_clients.uat_id%TYPE DEFAULT NULL,
							pSignatureId invoice_provider_clients.signature_id%TYPE DEFAULT NULL,
							pSignatureData VARCHAR2 DEFAULT NULL,
							pInvoiceTemplate CLOB DEFAULT NULL)
	RETURN NUMBER IS
		slct$ipc cg$invoice_provider_clients.cg$row_type;
		vIpcId invoice_provider_clients.id%TYPE;
		myunit CONSTANT VARCHAR2(30) := 'RegisterConsumerInt';
	BEGIN
		slog.debug(pkgCtxId, myunit, pInvoiceProviderId || ':' || pEndUserId || ':' || pType || ':' || pUserReference);

		slct$ipc.ipr_id := pInvoiceProviderId;
		slct$ipc.end_users_id := pEndUserId;
		slct$ipc.type := pType;
		slct$ipc.user_reference := pUserReference;
		slct$ipc.user_comment := pUserComment;
		slct$ipc.valid := pValid;
		slct$ipc.uat_id := pUserAgreementId;
		slct$ipc.signature_id := pSignatureId;
		slct$ipc.signature_data := pSignatureData;
		slct$ipc.invoice_template := pInvoiceTemplate;
		slog.debug(pkgCtxId, myUnit, 'Prepare new ipc data ... invoice_template: ' || slct$ipc.invoice_template);

		vIpcId := cg$invoice_provider_clients.ins(slct$ipc);

		slog.debug(pkgCtxId, myunit, 'Registered subscription with ID :' || vIpcId);

		RETURN vIpcId;

	EXCEPTION
		WHEN DUP_VAL_ON_INDEX THEN
			slog.error(pkgCtxId, myunit, cERR_AlreadySubscribed, pInvoiceProviderId || ':' || pEndUserId || ':' || pType || ':' || pUserReference);
			sspkg.raiseError(cERR_AlreadySubscribed, null, pkgCtxId, myunit);
		WHEN OTHERS THEN
			slog.error(pkgCtxId, myunit, sqlcode||':'||sqlerrm);
			RAISE;
	END RegisterConsumerInt;

	-- Overloaded version with auto payment parameters
	FUNCTION RegisterConsumerInt(pInvoiceProviderId invoice_provider_clients.ipr_id%TYPE,
							pEndUserId invoice_provider_clients.end_users_id%TYPE,
							pType invoice_provider_clients.type%TYPE,
							pUserReference invoice_provider_clients.user_reference%TYPE,
							pUserComment invoice_provider_clients.user_comment%TYPE,
							pValid invoice_provider_clients.valid%TYPE,
							pUserAgreementId invoice_provider_clients.uat_id%TYPE,
							pSignatureId invoice_provider_clients.signature_id%TYPE,
							pSignatureData VARCHAR2,
							pInvoiceTemplate CLOB,
							pAutoPaymentEnabled invoice_provider_clients.auto_payment_enabled%TYPE,
							pPaymentAccountId invoice_provider_clients.payment_account_id%TYPE)
	RETURN NUMBER IS
		slct$ipc cg$invoice_provider_clients.cg$row_type;
		vIpcId invoice_provider_clients.id%TYPE;
		myunit CONSTANT VARCHAR2(30) := 'RegisterConsumerInt';
	BEGIN
		slog.debug(pkgCtxId, myunit, pInvoiceProviderId || ':' || pEndUserId || ':' || pType || ':' || pUserReference);

		slct$ipc.ipr_id := pInvoiceProviderId;
		slct$ipc.end_users_id := pEndUserId;
		slct$ipc.type := pType;
		slct$ipc.user_reference := pUserReference;
		slct$ipc.user_comment := pUserComment;
		slct$ipc.valid := pValid;
		slct$ipc.uat_id := pUserAgreementId;
		slct$ipc.signature_id := pSignatureId;
		slct$ipc.signature_data := pSignatureData;
		slct$ipc.invoice_template := pInvoiceTemplate;
		slct$ipc.auto_payment_enabled := pAutoPaymentEnabled;
		slct$ipc.payment_account_id := pPaymentAccountId;

		slog.debug(pkgCtxId, myUnit, 'Prepare new ipc data ... invoice_template: ' || slct$ipc.invoice_template);
		slog.debug(pkgCtxId, myUnit, 'Prepare new ipc data ... auto_payment_enabled: ' || slct$ipc.auto_payment_enabled);
		slog.debug(pkgCtxId, myUnit, 'Prepare new ipc data ... payment_account_id: ' || slct$ipc.payment_account_id);

		vIpcId := cg$invoice_provider_clients.ins(slct$ipc);

		slog.debug(pkgCtxId, myunit, 'Registered subscription with ID :' || vIpcId);

		RETURN vIpcId;

	EXCEPTION
		WHEN DUP_VAL_ON_INDEX THEN
			slog.error(pkgCtxId, myunit, cERR_AlreadySubscribed, pInvoiceProviderId || ':' || pEndUserId || ':' || pType || ':' || pUserReference);
			sspkg.raiseError(cERR_AlreadySubscribed, null, pkgCtxId, myunit);
		WHEN OTHERS THEN
			slog.error(pkgCtxId, myunit, sqlcode||':'||sqlerrm);
			RAISE;
	END RegisterConsumerInt;


	FUNCTION RegisterConsumer(pInvoiceProviderId invoice_provider_clients.ipr_id%TYPE,
							pEndUserId invoice_provider_clients.end_users_id%TYPE,
							pType invoice_provider_clients.type%TYPE,
							pUserReference invoice_provider_clients.user_reference%TYPE,
							pUserComment invoice_provider_clients.user_comment%TYPE,
							pValid invoice_provider_clients.valid%TYPE,
							pUserAgreementId invoice_provider_clients.uat_id%TYPE DEFAULT NULL,
							pResponse VARCHAR2 := NULL,
							pOtp VARCHAR2 := NULL,
							pSourceData VARCHAR2 := NULL,
                            pSignature VARCHAR2 := NULL,
							pInvoiceTemplate CLOB DEFAULT NULL)
	RETURN NUMBER IS
		myunit CONSTANT VARCHAR2(30) := 'RegisterConsumer';
		
		vChallenge CONSTANT VARCHAR2(40) := NULL;
		vSignatureValid BOOLEAN := TRUE;
		vClientSignatureMethod VARCHAR2(4000);
		vClientOTPType VARCHAR2(4000);
		vErrorCode VARCHAR2(4000);
		vSignatureId invoice_provider_clients.signature_id%TYPE;
		
		vIpcId invoice_provider_clients.id%TYPE;
	BEGIN
		-- Provjera potpisa za uslove kori�tenja
		<<CheckSignature>>
		BEGIN
			slog.debug(pkgCtxId, myunit);
			
			mcauth.auth.setEvent (common_pck.cAUTHEVT_SIGN);
			vSignatureValid := 	authorization_pck.checkSignature(
									pChallenge => vChallenge,
									pResponse => pResponse,
									pOtp => pOtp,
									pSourceData => pSourceData,
									pSignature => pSignature,
									pClientSignatureMethod => vClientSignatureMethod,
									pClientOTPType => vClientOTPType,
									pErrorCode => vErrorCode);

			slog.debug(pkgCtxId, myunit, 'Validate signature ... finish');
			IF vSignatureValid THEN
				slog.debug(pkgCtxId, myUnit, 'Signature successfully verified. Insert data into signatures ... ');
				-- Signature is recorded and assigned to particular request
				vSignatureId := authorization_pck.insertSignature(
					pClientSignatureMethod => vClientSignatureMethod,
					pChallenge => vChallenge,
					pResponse => pResponse,
					pOtp => pOtp,
					pSourceData => pSourceData,
					pSignature => pSignature);
				slog.debug(pkgCtxId, myUnit, 'Create signature with ID :' || vSignatureId);
			ELSE
				sspkg.raiseError(vErrorCode, null, pkgCtxId, myunit);
			END IF;
		END CheckSignature;
		
			
		slog.debug(pkgCtxId, myUnit, 'Create signature hash');
		
		vIpcId := RegisterConsumerInt(pInvoiceProviderId => pInvoiceProviderId,
			pEndUserId => pEndUserId,
			pType => pType,
			pUserReference => pUserReference,
			pUserComment => pUserComment,
			pValid => pValid,
			pUserAgreementId => pUserAgreementId,
			pSignatureId => vSignatureId,
			pSignatureData => vClientSignatureMethod || vChallenge || pResponse || pOtp || pSourceData || pSignature,
			pInvoiceTemplate => pInvoiceTemplate);
			
		slog.debug(pkgCtxId, myunit, 'Reistered subscription with ID :' || vIpcId);
			
		RETURN vIpcId;
	END RegisterConsumer;

	-- Overloaded version with auto payment parameters
	FUNCTION RegisterConsumer(pInvoiceProviderId invoice_provider_clients.ipr_id%TYPE,
							pEndUserId invoice_provider_clients.end_users_id%TYPE,
							pType invoice_provider_clients.type%TYPE,
							pUserReference invoice_provider_clients.user_reference%TYPE,
							pUserComment invoice_provider_clients.user_comment%TYPE,
							pValid invoice_provider_clients.valid%TYPE,
							pUserAgreementId invoice_provider_clients.uat_id%TYPE,
							pResponse VARCHAR2,
                            pOtp VARCHAR2,
                            pSourceData VARCHAR2,
                            pSignature VARCHAR2,
							pInvoiceTemplate CLOB,
							pAutoPaymentEnabled invoice_provider_clients.auto_payment_enabled%TYPE,
							pPaymentAccountId invoice_provider_clients.payment_account_id%TYPE)
	RETURN NUMBER IS
		myunit CONSTANT VARCHAR2(30) := 'RegisterConsumer';
		vIpcId invoice_provider_clients.id%TYPE;
		vSignatureId signatures.id%TYPE;
		vClientSignatureMethod VARCHAR2(10);
		vChallenge VARCHAR2(4000);
	BEGIN
		slog.debug(pkgCtxId, myunit, pInvoiceProviderId || ':' || pEndUserId || ':' || pType || ':' || pUserReference || ':' || pValid);

		slog.debug(pkgCtxId, myUnit, 'Create signature hash');

		vIpcId := RegisterConsumerInt(pInvoiceProviderId => pInvoiceProviderId,
			pEndUserId => pEndUserId,
			pType => pType,
			pUserReference => pUserReference,
			pUserComment => pUserComment,
			pValid => pValid,
			pUserAgreementId => pUserAgreementId,
			pSignatureId => vSignatureId,
			pSignatureData => vClientSignatureMethod || vChallenge || pResponse || pOtp || pSourceData || pSignature,
			pInvoiceTemplate => pInvoiceTemplate,
			pAutoPaymentEnabled => pAutoPaymentEnabled,
			pPaymentAccountId => pPaymentAccountId);

		slog.debug(pkgCtxId, myunit, 'Registered subscription with ID :' || vIpcId);

		RETURN vIpcId;
	END RegisterConsumer;

	PROCEDURE UnregisterConsumer(pId IN PLS_INTEGER,
		pEndUserId invoice_provider_clients.end_users_id%TYPE,
		pType invoice_provider_clients.type%TYPE)
	IS
		myunit CONSTANT VARCHAR2(30) := 'UnregisterConsumer';
		upd$ipc cg$invoice_provider_clients.cg$row_type;
		ind$ipc cg$invoice_provider_clients.cg$ind_type;

	BEGIN
		slog.debug(pkgCtxId, myUnit, pId || ':' || pEndUserId || ':' || pType);
		
		upd$ipc.id := pId;
		cg$invoice_provider_clients.slct ( upd$ipc );
		
		upd$ipc.valid := cValidFalse;
		
		ind$ipc.valid := TRUE;
		cg$invoice_provider_clients.upd ( upd$ipc, ind$ipc );

		EnqueueUnSubscriptionMessage(cg$rec => upd$ipc);

	EXCEPTION
		WHEN no_data_found THEN
			slog.error(pkgCtxId, myunit, cERR_NoSubscribtion, pId || ':' || pEndUserId || ':' || pType);
			sspkg.raiseError(cERR_NoSubscribtion, null, pkgCtxId, myunit);

	END UnregisterConsumer;

	FUNCTION RegisterConsumer(pInvoiceProviderName invoice_providers.name%TYPE,
							pEndUserId invoice_provider_clients.end_users_id%TYPE,
							pType invoice_provider_clients.type%TYPE,
							pUserReference invoice_provider_clients.user_reference%TYPE,
							pValid invoice_provider_clients.valid%TYPE,
							pUserAgreementId invoice_provider_clients.uat_id%TYPE DEFAULT NULL)
	RETURN NUMBER IS
		myunit CONSTANT VARCHAR2(30) := 'RegisterConsumer2';
        vInvoiceProviderId invoice_providers.id%TYPE;
		
		vIpcId invoice_provider_clients.id%TYPE;

	BEGIN

		slog.debug(pkgCtxId, myunit, pInvoiceProviderName || ':' || pEndUserId || ':' || pType || ':' || pUserReference || ':' || pValid);

		BEGIN
			slog.debug(pkgCtxId, myunit, 'Retrieve provider');
			SELECT id
			INTO vInvoiceProviderId
			FROM invoice_providers
			WHERE name = pInvoiceProviderName
			  AND valid = cValidTrue;

			slog.debug(pkgCtxId, myunit, 'Retrieve provider :' || vInvoiceProviderId);
		EXCEPTION
				WHEN no_data_found THEN
					slog.error(pkgCtxId, myunit, cERR_InvalidInvoiceProvider, pInvoiceProviderName);
					sspkg.raiseError(cERR_InvalidInvoiceProvider, null, pkgCtxId, myunit);
		END;
		
		vIpcId := RegisterConsumerInt(pInvoiceProviderId => vInvoiceProviderId,
			pEndUserId => pEndUserId,
			pType => pType,
			pUserReference => pUserReference,
			pUserComment => NULL,
			pValid => pValid,
			pUserAgreementId => pUserAgreementId,
			pSignatureId => NULL,
			pSignatureData => NULL );
			
		slog.debug(pkgCtxId, myunit, 'Reistered subscription with ID :' || vIpcId);

		RETURN vIpcId;

	END RegisterConsumer;

    PROCEDURE UnregisterConsumer(pInvoiceProviderName invoice_providers.name%TYPE,
		pEndUserId invoice_provider_clients.end_users_id%TYPE,
		pType invoice_provider_clients.type%TYPE)
	IS
		myunit CONSTANT VARCHAR2(30) := 'UnregisterConsumer2';
		
		cursor c_ipc is 
			SELECT ipc.id as ipc_id, ip.id as ip_id
				FROM invoice_provider_clients ipc 
				JOIN invoice_providers ip
				ON ip.id = ipc.ipr_id
				WHERE ip.name = pInvoiceProviderName
				AND ipc.end_users_id = pEndUserId
				AND ipc.type = pType
				AND ip.valid = cValidTrue
				AND ipc.valid = cValidTrue;
		
	BEGIN
	
		BEGIN
			FOR item IN c_ipc LOOP 
				slog.debug(pkgCtxId, myunit, 'Retrieve provider :' || item.ip_id  || 'Retrieve invoice provider client id :' || item.ipc_id);
				UnregisterConsumer(pId => item.ipc_id,
								pEndUserId => pEndUserId,
								pType => pType);
			END LOOP;

			  
		EXCEPTION
				WHEN no_data_found THEN
					slog.error(pkgCtxId, myunit, cERR_InvalidInvoiceProvider, pInvoiceProviderName);
					sspkg.raiseError(cERR_InvalidInvoiceProvider, null, pkgCtxId, myunit);
		END;

		

		IF (pInvoiceProviderName = common_pck.cATMCardlessIPrName) THEN

		  BEGIN
			  UPDATE mcore.otpayment
			  SET valid_until = SYSDATE
			  WHERE end_users_id = pEndUserId;
			  COMMIT;
		  END;

		END IF;
		
	EXCEPTION
		WHEN no_data_found THEN
			slog.error(pkgCtxId, myunit, cERR_NoSubscribtion, pEndUserId || ':' || pType);
			sspkg.raiseError(cERR_NoSubscribtion, null, pkgCtxId, myunit);

	END UnregisterConsumer;
	
	PROCEDURE RejectSubscription(pId IN PLS_INTEGER, pRejectionReason IN VARCHAR2)
	IS
		myunit CONSTANT VARCHAR2(30) := 'RejectSubscription';
		upd$ipc cg$invoice_provider_clients.cg$row_type;
		ind$ipc cg$invoice_provider_clients.cg$ind_type;
		
		slct$ipr cg$invoice_providers.cg$row_type;

	BEGIN
		slog.debug(pkgCtxId, myUnit, pId);
		
		upd$ipc.id := pId;
		cg$invoice_provider_clients.slct ( upd$ipc );
		
		slct$ipr.id := upd$ipc.ipr_id;
		cg$invoice_providers.slct ( slct$ipr );
		
		upd$ipc.valid := cValidFalse;
		
		ind$ipc.valid := TRUE;
		cg$invoice_provider_clients.upd ( upd$ipc, ind$ipc );

		DECLARE
			vNewsId VARCHAR2(40);
			vDescription CONSTANT VARCHAR2(4000) := 'Notify client that a subscription request was rejected';
			vPriority CONSTANT VARCHAR2(20) := 'NORMAL';
			vTitleEn VARCHAR2(4000);
			vTitleBs VARCHAR2(4000);
			vAbstractEn VARCHAR2(4000);
			VAbstractBs VARCHAR2(4000);
			vAuthor CONSTANT VARCHAR2(40) := 'SYSTEM';

		BEGIN

			vNewsId := 'SUBSCRIPTION-' || upd$ipc.id || '-' || to_char(SYSDATE,'YYYYMMDDHH24miSS');

			vTitleEn := mlang.trans('en', '/Core/Main/InvoiceManagement/err/Rejected_eINVOICE_SUBSCRIPTION_Message_Title');
							vTitleBs := mlang.trans('bs', '/Core/Main/InvoiceManagement/err/Rejected_eINVOICE_SUBSCRIPTION_Message_Title');
							vAbstractEn := mlang.trans('en', '/Core/Main/InvoiceManagement/err/Rejected_eINVOICE_SUBSCRIPTION_Message_Abstract', slct$ipr.name, upd$ipc.user_reference, upd$ipc.user_comment, pRejectionReason);
							VAbstractBs := mlang.trans('bs', '/Core/Main/InvoiceManagement/err/Rejected_eINVOICE_SUBSCRIPTION_Message_Abstract', slct$ipr.name, upd$ipc.user_reference, upd$ipc.user_comment, pRejectionReason);

			mcore.advertisment_pck.CreateElbaNotification(
				pNewsId => vNewsId,
				pDescription => vDescription,
				pPriority => vPriority,
				pTitleEn => vTitleEn,
				pTitleBs => vTitleBs,
				pAbstractEn => vAbstractEn,
				pAbstractBs => vAbstractBs,
				pAuthor => vAuthor,
				pUserId => upd$ipc.end_users_id);
			slog.debug (pkgCtxId, myunit, 'Elba notification created ' || vNewsId);

		EXCEPTION
			WHEN OTHERS THEN
				slog.error(pkgCtxId, myunit, 'Error creating ELBA notification: ' || sqlerrm);
		END;

	EXCEPTION
		WHEN no_data_found THEN
			slog.error(pkgCtxId, myunit, cERR_NoSubscribtion, pId);
			sspkg.raiseError(cERR_NoSubscribtion, null, pkgCtxId, myunit);

	END RejectSubscription;

	FUNCTION hasActiveSubscriptions(pEndUserId invoice_provider_clients.end_users_id%TYPE)
	RETURN BOOLEAN IS
		vPom VARCHAR2(1);
		myunit CONSTANT VARCHAR2(30) := 'hasActiveSubscriptions';
	BEGIN
		slog.debug(pkgCtxId, myUnit, pEndUserId);
		SELECT NULL
		INTO vPom
		FROM DUAL
		WHERE EXISTS (
			SELECT NULL
			FROM invoice_provider_clients ipc JOIN invoice_providers ip ON (ipc.ipr_id = ip.id)
			WHERE ipc.end_users_id = pEndUserId
			AND ipc.type = common_pck.cINVPRVCLTYPECONSUMER
			AND ip.valid = cValidTrue
			AND ipc.valid = cValidTrue);
		slog.debug(pkgCtxId, myUnit, 'Has active subscribtions');
		RETURN TRUE;
	EXCEPTION
		WHEN no_data_found THEN
			slog.debug(pkgCtxId, myUnit, 'Has no active subscribtions');
			RETURN FALSE;
	END hasActiveSubscriptions;

	FUNCTION getActiveSubscriptions(pEndUserId invoice_provider_clients.end_users_id%TYPE)
	RETURN SYS_REFCURSOR IS
        myunit CONSTANT VARCHAR2(30) := 'getActiveSubscriptions';
        rez SYS_REFCURSOR;
	BEGIN
        slog.debug(pkgCtxId, myUnit, pEndUserId);

        OPEN rez FOR
			SELECT ipc.id id, ip.id ipr_id, ip.name ipr_name, ipc.user_reference, ipc.user_comment
			FROM invoice_provider_clients ipc JOIN invoice_providers ip ON (ipc.ipr_id = ip.id)
			WHERE ipc.end_users_id = pEndUserId
			AND ipc.type = common_pck.cINVPRVCLTYPECONSUMER
			AND ip.valid = cValidTrue
			AND ipc.valid = cValidTrue
			AND ip.name not like 'INTERNAL%'
            AND ipc.acc_owner_id like mcauth.auth.getAccountOwner
			ORDER by ipr_name ASC, ipc.user_reference;

		RETURN rez;
	END getActiveSubscriptions;

	FUNCTION checkATMCardlessSubscription
    RETURN BOOLEAN IS
        myunit CONSTANT VARCHAR2(30) := 'checkATMCardlessSubscription';
        vSubscriptionId invoice_provider_clients.id%TYPE;
    BEGIN

        slog.debug(pkgCtxId, myUnit);
        common_pck.CommonSecurityChecks;

        BEGIN

            SELECT ipc.id
            INTO vSubscriptionId
            FROM invoice_provider_clients ipc JOIN invoice_providers ip ON (ipc.ipr_id = ip.id)
            WHERE ipc.end_users_id = mcauth.auth.getClientId AND ipc.type = common_pck.cINVPRVCLTYPECONSUMER AND ip.valid = cValidTrue AND ipc.valid = cValidTrue AND ip.name = common_pck.cATMCardlessIPrName;

        EXCEPTION
        WHEN no_data_found THEN
            -- There is no active subscribtion for ATM CardLess
            RETURN FALSE;
        WHEN too_many_rows THEN
				slog.error(pkgCtxId, myUnit, cERR_MultipleATMCardLessSubs);
                sspkg.raiseError(cERR_MultipleATMCardLessSubs, null, pkgCtxId, myunit);
        END;

        RETURN TRUE;

    END checkATMCardlessSubscription;

	FUNCTION checkQRPaySubscription
    RETURN BOOLEAN IS
        myunit CONSTANT VARCHAR2(30) := 'checkQRPaySubscription';
        vSubscriptionId invoice_provider_clients.id%TYPE;
    BEGIN

        slog.debug(pkgCtxId, myUnit);
        common_pck.CommonSecurityChecks;

        BEGIN

            SELECT ipc.id
            INTO vSubscriptionId
            FROM invoice_provider_clients ipc JOIN invoice_providers ip ON (ipc.ipr_id = ip.id)
            WHERE ipc.end_users_id = mcauth.auth.getClientId AND ipc.type = common_pck.cINVPRVCLTYPECONSUMER AND ip.valid = cValidTrue AND ipc.valid = cValidTrue AND ip.name = common_pck.cQRPayIPrName;

        EXCEPTION
        WHEN no_data_found THEN
            -- There is no active subscribtion for QRPay
            RETURN FALSE;
        WHEN too_many_rows THEN
                slog.error(pkgCtxId, myUnit, cERR_MultipleQRPaySubs);
                sspkg.raiseError(cERR_MultipleQRPaySubs, null, pkgCtxId, myunit);
        END;

        RETURN TRUE;

    END checkQRPaySubscription;

	FUNCTION getProviders
	RETURN SYS_REFCURSOR IS
        myunit CONSTANT VARCHAR2(30) := 'getProviders';
        rez SYS_REFCURSOR;
	BEGIN
		slog.debug(pkgCtxId, myUnit);

		OPEN rez FOR
			SELECT ip.id ipr_id, ip.name ipr_name, ip.description ipr_description, ip.blob_data, ip.mime_type, ip.registration_message
			FROM invoice_providers ip
			WHERE ip.valid = cValidTrue
			  AND ip.name NOT LIKE 'INTERNAL%'	-- filter out internal invoice providers
			ORDER BY ip.name ASC;

		RETURN rez;
	END getProviders;

	-- Used by provider app
	FUNCTION getManagedProviders(pEndUserId invoice_provider_clients.end_users_id%TYPE)
	RETURN SYS_REFCURSOR IS
		myunit CONSTANT VARCHAR2(30) := 'getManagedProviders';
        rez SYS_REFCURSOR;
	BEGIN
		slog.debug(pkgCtxId, myUnit, pEndUserId);

		OPEN rez FOR
			SELECT ip.id ipr_id, ip.name ipr_name, ip.description ipr_description, ip.blob_data, ip.mime_type, ip.agreement_text
			FROM invoice_provider_clients ipc JOIN invoice_providers ip ON (ipc.ipr_id = ip.id)
			WHERE ipc.end_users_id = pEndUserId AND ipc.type = 'A' AND ip.valid = cValidTrue AND ipc.valid = cValidTrue
			ORDER by ipr_name ASC;

		RETURN rez;
	END getManagedProviders;

	FUNCTION isAdmin(pInvoiceProviderId IN invoice_provider_clients.ipr_id%TYPE, pAdminUserId IN invoice_provider_clients.end_users_id%TYPE)
	RETURN BOOLEAN IS
		vPom VARCHAR2(1);
		myunit CONSTANT VARCHAR2(30) := 'isAdmin';
	BEGIN
		slog.debug(pkgCtxId, myUnit, pInvoiceProviderId || ':' || pAdminUserId);
		
		SELECT NULL
		INTO vPom
		FROM DUAL
		WHERE EXISTS
		(SELECT NULL FROM invoice_provider_clients ipc JOIN invoice_providers ipr ON (ipc.ipr_id = ipr.id)
		WHERE ipc.ipr_id = pInvoiceProviderId
		AND ipc.end_users_id = pAdminUserId
		AND ipc.type = common_pck.cINVPRVCLTYPEADMIN
		AND ipr.valid = cValidTrue
		AND ipc.valid = cValidTrue);
		
		slog.debug(pkgCtxId, myUnit, 'Found record');

		RETURN TRUE;
	EXCEPTION
		WHEN no_data_found THEN
			slog.debug(pkgCtxId, myUnit, 'Record not found');
			RETURN FALSE;
	END isAdmin;
	
	FUNCTION isAdmin(pInvoiceProviderId IN invoice_provider_clients.ipr_id%TYPE, pUsername IN VARCHAR2)
	RETURN INTEGER IS
		vPom VARCHAR2(1);
		myunit CONSTANT VARCHAR2(30) := 'isAdmin2';
	BEGIN
		slog.debug(pkgCtxId, myUnit, pInvoiceProviderId || ':' || pUsername);
		
		SELECT NULL
		INTO vPom
		FROM DUAL
		WHERE EXISTS
		(SELECT NULL FROM invoice_provider_clients ipc 
			JOIN invoice_providers ipr ON (ipc.ipr_id = ipr.id)
			JOIN mcauth.client c ON (ipc.end_users_id = c.id)
		WHERE ipc.ipr_id = pInvoiceProviderId
		AND UPPER(c.username) = UPPER(pUsername)
		AND ipc.type = common_pck.cINVPRVCLTYPEADMIN
		AND c.enabled = cValidTrue
		AND ipr.valid = cValidTrue
		AND ipc.valid = cValidTrue);
		
		slog.debug(pkgCtxId, myUnit, 'Found record');

		RETURN cValidTrue;
	EXCEPTION
		WHEN no_data_found THEN
			slog.debug(pkgCtxId, myUnit, 'Record not found');
			RETURN cValidFalse;
	END isAdmin;
	
	FUNCTION getAdminUserId (pInvoiceProviderId IN invoice_provider_clients.ipr_id%TYPE, pUsername IN VARCHAR2)
	RETURN invoice_provider_clients.end_users_id%TYPE 
	IS		
		vAdminUserId invoice_provider_clients.end_users_id%TYPE;
		myunit CONSTANT VARCHAR2(30) := 'isAdmin2';
	BEGIN
		slog.debug(pkgCtxId, myUnit, pInvoiceProviderId || ':' || pUsername);
		
		SELECT ipc.end_users_id
		INTO vAdminUserId
		FROM invoice_provider_clients ipc 
			JOIN invoice_providers ipr ON (ipc.ipr_id = ipr.id)
			JOIN mcauth.client c ON (ipc.end_users_id = c.id)
		WHERE ipc.ipr_id = pInvoiceProviderId
		AND UPPER(c.username) = UPPER(pUsername)
		AND ipc.type = common_pck.cINVPRVCLTYPEADMIN
		AND c.enabled = cValidTrue
		AND ipr.valid = cValidTrue
		AND ipc.valid = cValidTrue
		AND rownum < 2;
		
		slog.debug(pkgCtxId, myUnit, 'Found record');
		
		RETURN vAdminUserId;
	EXCEPTION
		WHEN no_data_found THEN
			slog.debug(pkgCtxId, myUnit, 'Record not found');
			RETURN NULL;		
	END getAdminUserId;

	FUNCTION getClients(
		pInvoiceProviderId IN invoice_provider_clients.ipr_id%TYPE,
		pAdminUserId IN invoice_provider_clients.end_users_id%TYPE)
	RETURN SYS_REFCURSOR IS
        myunit CONSTANT VARCHAR2(30) := 'getClients';
        rez SYS_REFCURSOR;
	BEGIN
        slog.debug(pkgCtxId, myUnit, pInvoiceProviderId || ':' || pAdminUserId);

		IF NOT isAdmin(pInvoiceProviderId, pAdminUserId) THEN
			slog.error(pkgCtxId, myUnit, cERR_NoAdminPrivilege, pInvoiceProviderId || ':' || pAdminUserId);
			sspkg.raiseError(cERR_NoAdminPrivilege, null, pkgCtxId, myunit);
		END IF;

		OPEN rez FOR
			SELECT ipc.id, ipc.user_reference, ipc.date_subscribed, ipc.end_users_id, eu.contact_email user_email, ipc.uat_id user_agreement_id
			  FROM invoice_provider_clients ipc
				JOIN invoice_providers i ON (i.id = ipc.ipr_id)
				JOIN end_users eu ON (ipc.end_users_id = eu.id)
		     WHERE i.id = pInvoiceProviderId
			   AND ipc.type = common_pck.cINVPRVCLTYPECONSUMER
			   AND ipc.valid = cValidTrue
			   AND eu.valid = cValidTrue
			   AND ((i.must_accept_uat = cValidTrue AND ipc.uat_id IS NOT NULL) OR i.must_accept_uat = cValidFalse)
		  ORDER BY ipc.date_subscribed, ipc.end_users_id;

		RETURN rez;
	END getClients;

	-- Used by WSO2 SubscribtionDBService
	FUNCTION getSubscriptions(pInvoiceProviderId IN invoice_provider_clients.ipr_id%TYPE)
	RETURN SYS_REFCURSOR IS
		myunit CONSTANT VARCHAR2(30) := 'getSubscriptions';
        rez SYS_REFCURSOR;
	BEGIN
		slog.debug(pkgCtxId, myUnit, pInvoiceProviderId);

		OPEN rez FOR
			SELECT ipc.id, ipc.end_users_id, eu.first_name, eu.last_name, eu.contact_email, eu.contact_phone,
				eu.city, eu.zip_code, eu.address, ipc.user_reference, ipc.date_subscribed, ipc.user_comment,
				ipc.external_client_id, ipc.acc_owner_id, ipc.uat_id
		FROM mcore.invoice_provider_clients ipc
			JOIN mcore.invoice_providers i ON (ipc.ipr_id = i.id)
			JOIN mcore.end_users eu ON (eu.id = ipc.end_users_id)
		WHERE i.id = pInvoiceProviderId
		 AND i.valid = cValidTrue
		 AND ipc.valid = cValidTrue
		 AND ipc.type = common_pck.cINVPRVCLTYPECONSUMER
		 and eu.valid = cValidTrue
		 AND ((i.must_accept_uat = cValidTrue AND ipc.uat_id IS NOT NULL) OR i.must_accept_uat = cValidFalse)
		 ORDER BY last_name, first_name;

		 RETURN rez;
	END;

	-- Used by jpepbih genSubscription workflow to check if proces should start
	FUNCTION getSubscriptionsCount(pInvoiceProviderId IN invoice_provider_clients.ipr_id%TYPE)
	RETURN PLS_INTEGER IS
		myunit CONSTANT VARCHAR2(30) := 'getSubscriptionsCount';
        rez PLS_INTEGER;
	BEGIN
		slog.debug(pkgCtxId, myUnit, pInvoiceProviderId);

		SELECT COUNT(*) INTO rez
		FROM mcore.invoice_provider_clients ipc
			JOIN mcore.invoice_providers i ON (ipc.ipr_id = i.id)
			JOIN mcore.end_users eu ON (eu.id = ipc.end_users_id)
		WHERE i.id = pInvoiceProviderId
		 AND i.valid = cValidTrue
		 AND ipc.valid = cValidTrue
		 AND ipc.type = common_pck.cINVPRVCLTYPECONSUMER
		 and eu.valid = cValidTrue
		 AND ((i.must_accept_uat = cValidTrue AND ipc.uat_id IS NOT NULL) OR i.must_accept_uat = cValidFalse);

		slog.debug(pkgCtxId, myUnit, pInvoiceProviderId || ':' || rez);

		RETURN rez;
	END;

	FUNCTION getClientSubscription(
		pInvoiceProviderId IN invoice_provider_clients.ipr_id%TYPE,
		pAdminUserId IN invoice_provider_clients.end_users_id%TYPE,
		pEndUserId IN end_users.id%TYPE)
	RETURN SYS_REFCURSOR IS
        myunit CONSTANT VARCHAR2(30) := 'getClientSubscription';
        rez SYS_REFCURSOR;
	BEGIN
        slog.debug(pkgCtxId, myUnit, pInvoiceProviderId || ':' || pAdminUserId || ':' || pEndUserId);

		IF NOT isAdmin(pInvoiceProviderId, pAdminUserId) THEN
			slog.error(pkgCtxId, myUnit, cERR_NoAdminPrivilege, pInvoiceProviderId || ':' || pAdminUserId);
			sspkg.raiseError(cERR_NoAdminPrivilege, null, pkgCtxId, myunit);
		END IF;

		OPEN rez FOR
			SELECT
				ipc.id invoice_provider_client_id,
				eu.id user_id,
				eu.first_name,
				eu.last_name,
				ipc.user_comment,
				ipc.user_reference,
				ipc.date_subscribed,
				ipc.signature agreement_signature,
				uat.agreement_text,
				uat.valid_from,
				ipr.id invoice_provder_id,
				ipr.name invoice_provder_name,
				s.aea_extauth_id,
				s.aea_device_id,
				s.application_id,
				s.signature_method,
				s.signature action_signature
			  FROM invoice_provider_clients ipc
				JOIN user_agreement uat ON (ipc.uat_id = uat.id)
				JOIN invoice_providers ipr ON (ipc.ipr_id = ipr.id)
				JOIN end_users eu ON (ipc.end_users_id = eu.id)
				LEFT JOIN signatures s ON (ipc.signature_id = s.id)
		     WHERE ipc.ipr_id = pInvoiceProviderId
			   AND ipc.type = common_pck.cINVPRVCLTYPECONSUMER
			   AND ipc.end_users_id = pEndUserId
			   AND ipc.valid = cValidTrue
			   AND uat.valid = cValidTrue
			   AND ipr.valid = cValidTrue;
		RETURN rez;
	END getClientSubscription;

	FUNCTION getInvoicesCount(pEndUserId IN end_users.id%TYPE, pStatus IN VARCHAR2 DEFAULT '%')
	RETURN PLS_INTEGER IS
		myunit CONSTANT VARCHAR2(30) := 'getInvoicesCount';
		vInvoices# PLS_INTEGER;
		vStatus VARCHAR2(1 CHAR);
	BEGIN
		slog.debug(pkgCtxId, myUnit,  pEndUserId || ':' || pStatus || ':' || mcauth.auth.getAccountOwner);

        common_pck.CommonSecurityChecks;

		IF LENGTH(pStatus) > 1 THEN
			slog.error(pkgCtxId, myUnit, cERR_InvalidInvoiceStatus, pEndUserId || ':' || pStatus);
            sspkg.raiseError(cERR_InvalidInvoiceStatus, null, pkgCtxId, myunit);
		END IF;

		vStatus := NVL(pStatus, '%');

        IF vStatus NOT IN ('0','1','2','3','%') THEN
			slog.error(pkgCtxId, myUnit, cERR_InvalidInvoiceStatus, pEndUserId || ':' || pStatus);
            sspkg.raiseError(cERR_InvalidInvoiceStatus, null, pkgCtxId, myunit);
        END IF;

		slog.debug(pkgCtxId, myUnit, 'Get count');

		SELECT COUNT(invoice_id)
		INTO vInvoices#
		FROM
		(-- 1. Invoices related to user
		SELECT ine.id invoice_id
			FROM invoices ine
				JOIN invoice_provider_clients ipt ON (ipt.id = ine.client_id)
				JOIN invoice_providers ipr ON (ipr.id = ipt.ipr_id)
			WHERE
                		ipt.acc_owner_id LIKE mcauth.auth.getAccountOwner AND
				ipr.valid = cValidTrue AND
				ipt.valid = cValidTrue AND
				TO_CHAR(ine.status) LIKE vStatus AND
				ipr.name not like 'INTERNAL%' AND
				ipt.end_users_id = pEndUserId
			UNION
			-- 2. Invoices koji se ve�u uz vlasnika raeuna a ne Elba user-a
		SELECT ine.id invoice_id
			FROM invoices ine
				JOIN invoice_providers ipr ON (ipr.id = ine.ipr_id)
				JOIN invoice_provider_clients ipt ON (ipt.id = ine.client_id)
				JOIN bank_accounts ba ON (ba.account_owner_id = ine.account_owner_id)
				JOIN action_grants ag ON (ag.account_id = ba.ph4)
				JOIN actions ac ON (ag.application_id = ac.application_id and ac.id = ag.action_id)
				JOIN applications ap ON (ap.id = ac.application_id)
			WHERE
				ine.account_owner_id LIKE mcauth.auth.getAccountOwner AND -- Restrict on account owner for session
				ag.user_id = mcauth.auth.getClientId AND -- Restrict on Elba user
				TO_CHAR(ine.status) LIKE vStatus AND -- Restrict on invoice status
				ap.id = mcauth.auth.getSApp AND -- Restrict on session application
				-- Fixed filter
				ac.id = 'CREATE_TRANPAY' AND
				ine.tranval > 0 AND
				-- Additional restriction on tables introduced by joins
				ipr.valid = cValidTrue AND
				ipt.valid = cValidTrue AND
				ag.valid = cValidTrue AND
				ac.valid = cValidTrue AND
				ap.valid = cValidTrue AND
				ba.parent_id is null AND
				ba.status = 'A' AND 
				ipr.name not like 'INTERNAL%' AND
				((ac.req_type_sep = cValidTrue AND ag.req_type_id = ine.payment_type) OR ac.req_type_sep = cValidFalse));

			slog.debug(pkgCtxId, myUnit, 'Found ' || vInvoices# || ' records!');
		RETURN vInvoices#;

	END getInvoicesCount;

    FUNCTION getInvoices(pEndUserId IN end_users.id%TYPE, pStatus IN VARCHAR2 DEFAULT '%')
    RETURN sys_refcursor
    IS
        myunit CONSTANT VARCHAR2(30) := 'getInvoices';
        vStatus VARCHAR2(1 CHAR);
        rez sys_refcursor;

    BEGIN
        slog.debug(pkgCtxId, myUnit,  pEndUserId || ':' || pStatus || ':' || mcauth.auth.getAccountOwner);

        common_pck.CommonSecurityChecks;

		IF LENGTH(pStatus) > 1 THEN
			slog.error(pkgCtxId, myUnit, cERR_InvalidInvoiceStatus, pEndUserId || ':' || pStatus);
            sspkg.raiseError(cERR_InvalidInvoiceStatus, null, pkgCtxId, myunit);
		END IF;

		vStatus := NVL(pStatus, '%');

        IF vStatus NOT IN ('0','1','2','3','%') THEN
			slog.error(pkgCtxId, myUnit, cERR_InvalidInvoiceStatus, pEndUserId || ':' || pStatus);
            sspkg.raiseError(cERR_InvalidInvoiceStatus, null, pkgCtxId, myunit);
        END IF;

        OPEN rez FOR
		SELECT i.invoice_id, i.invoice_provider_id, i.invoice_provider_name, i.payment_over, i.payment_below, i.payment_type, i.reference, i.status, i.description, i.tranval, i.currency_id, i.tranpay_id, i.date_of_processing,
		i.billing_period, i.billing_date, i.payment_due_date, i.hitno, i.naziv_primaoca, i.racun_primaoca, i.naziv_posiljaoca, i.javni_prihodi, i.referenca_placanja, i.idporobv, i.vrsta_uplate, i.vrsta_prihoda, i.porperiod_od, i.porperiod_do, i.opcina, i.poziv_na_broj,
		i.budzetska_organizacija, i.fiksno,	i.iznos_kupovine, i.racun, i.racun_valuta, m.message, i.display_on_overview,  i.group_id,
		decode(i.group_id,
                common_pck.cINVGROUP_INVOICE, common_pck.cACCTLTYPE_TRAN,
                common_pck.cINVGROUP_LOANDEPT, common_pck.cACCTLTYPE_CRED,
                common_pck.cINVGROUP_SAVING, common_pck.cACCTLTYPE_SAVE) tl_type,
            mlang.trans(mcauth.auth.getLang, '/Core/Main/InvoicesGroupNames/'||i.group_id) group_id_translated, i.invoice_download_available, i.service_name, i.user_comment
		FROM
		(-- 1. Invoices related to user
		SELECT ine.id invoice_id, ipr.id invoice_provider_id, ipr.name invoice_provider_name, ipr.payment_over payment_over, ipr.payment_below payment_below, ine.payment_type payment_type, ine.invoice_no reference, ine.status, ine.description, ine.tranval, ine.tranval_currency_id currency_id,
		ine.tranpay_id, ine.date_of_processing, ine.billing_period, ine.billing_date, ine.payment_due_date,
		ine.display_on_overview, ine.group_id, ipr.provide_invoice_history invoice_download_available, ipr.service_name, ipt.user_comment,
				MAX (DECODE(idt.attrib_id, 'HITNO', idt.data_vchar, NULL))                    HITNO,
				MAX (DECODE(idt.attrib_id, common_pck.cTRANPAY_ATT_NAZIV_PRIMAOCA, idt.data_vchar, NULL))           NAZIV_PRIMAOCA,
				MAX (DECODE(idt.attrib_id, common_pck.cTRANPAY_ATT_RACUN_PRIMAOCA, idt.data_vchar, NULL))           RACUN_PRIMAOCA,
				MAX (DECODE(idt.attrib_id, common_pck.cTRANPAY_ATT_NAZIV_POSILJAOCA, idt.data_vchar, NULL))         NAZIV_POSILJAOCA,
				MAX (DECODE(idt.attrib_id, 'JAVNI_PRIHODI', idt.data_vchar, NULL))            JAVNI_PRIHODI,
				MAX (DECODE(idt.attrib_id, common_pck.cTRPAY_ATT_REFERENCA_PLACANJA, idt.data_vchar, NULL))       REFERENCA_PLACANJA,
				MAX (DECODE(idt.attrib_id, 'IDPOROBV', idt.data_vchar, NULL))                 IDPOROBV,
				MAX (DECODE(idt.attrib_id, 'VRSTA_UPLATE', idt.data_vchar, NULL))             VRSTA_UPLATE,
				MAX (DECODE(idt.attrib_id, common_pck.cTRANPAY_ATT_VRSTA_PRIHODA, idt.data_vchar, NULL))            VRSTA_PRIHODA,
				MAX (DECODE(idt.attrib_id, 'PORPERIOD_OD', idt.data_vchar, NULL))    PORPERIOD_OD,
				MAX (DECODE(idt.attrib_id, 'PORPERIOD_DO', idt.data_vchar, NULL))    PORPERIOD_DO,
				MAX (DECODE(idt.attrib_id, common_pck.cTRANPAY_ATT_OPCINA, idt.data_vchar, NULL))                   OPCINA,
				MAX (DECODE(idt.attrib_id, 'POZIV_NA_BROJ', idt.data_vchar, NULL))            POZIV_NA_BROJ,
				MAX (DECODE(idt.attrib_id, 'BUDZETSKA_ORGANIZACIJA', idt.data_vchar, NULL))   BUDZETSKA_ORGANIZACIJA,
				DECODE(ine.payment_type, common_pck.cRTI_TRANSFER, 'D', NULL)                                FIKSNO,
				TO_NUMBER(DECODE(ine.payment_type, common_pck.cRTI_TRANSFER, ine.tranval, NULL))                 IZNOS_KUPOVINE,
				DECODE(ine.payment_type, common_pck.cRTI_TRANSFER, MAX (DECODE(idt.attrib_id, common_pck.cTRANPAY_ATT_RACUN, idt.data_vchar, NULL)), NULL)  RACUN,
				DECODE(ine.payment_type, common_pck.cRTI_TRANSFER, ine.tranval_currency_id, NULL)                RACUN_VALUTA
			FROM invoices ine
				JOIN invoice_provider_clients ipt ON (ipt.id = ine.client_id)
				JOIN invoice_providers ipr ON (ipr.id = ipt.ipr_id)
				JOIN invoice_details idt ON (idt.ine_id = ine.id)
			WHERE
                		ipt.acc_owner_id LIKE mcauth.auth.getAccountOwner AND
				ipr.valid = cValidTrue AND
				ipt.valid = cValidTrue AND
				TO_CHAR(ine.status) LIKE vStatus AND
				ipr.name not like 'INTERNAL%' AND
				ipt.end_users_id = pEndUserId
			GROUP BY ine.id, ipr.id, ipr.name, ipr.payment_over, ipr.payment_below, ine.payment_type, ine.invoice_no, ine.status, ine.description, ine.tranval, ine.tranval_currency_id, ine.tranpay_id, ine.date_of_processing,
				ine.billing_period, ine.billing_date, ine.payment_due_date, ine.display_on_overview, ine.group_id, ipr.provide_invoice_history, ipr.service_name, ipt.user_comment
			UNION
			-- 2. Invoices koji se ve�u uz vlasnika raeuna a ne Elba user-a
		SELECT ine.id invoice_id, ipr.id invoice_provider_id, ipr.name invoice_provider_name, ipr.payment_over payment_over, ipr.payment_below payment_below, ine.payment_type payment_type, ine.invoice_no reference, ine.status, ine.description, ine.tranval, ine.tranval_currency_id currency_id,
		ine.tranpay_id, ine.date_of_processing, ine.billing_period, ine.billing_date, ine.payment_due_date,
		ine.display_on_overview, ine.group_id, ipr.provide_invoice_history invoice_download_available, ipr.service_name, ipt.user_comment,
				MAX (DECODE(idt.attrib_id, 'HITNO', idt.data_vchar, NULL))                    HITNO,
				MAX (DECODE(idt.attrib_id, common_pck.cTRANPAY_ATT_NAZIV_PRIMAOCA, idt.data_vchar, NULL))           NAZIV_PRIMAOCA,
				MAX (DECODE(idt.attrib_id, common_pck.cTRANPAY_ATT_RACUN_PRIMAOCA, idt.data_vchar, NULL))           RACUN_PRIMAOCA,
				MAX (DECODE(idt.attrib_id, common_pck.cTRANPAY_ATT_NAZIV_POSILJAOCA, idt.data_vchar, NULL))         NAZIV_POSILJAOCA,
				MAX (DECODE(idt.attrib_id, 'JAVNI_PRIHODI', idt.data_vchar, NULL))            JAVNI_PRIHODI,
				MAX (DECODE(idt.attrib_id, common_pck.cTRPAY_ATT_REFERENCA_PLACANJA, idt.data_vchar, NULL))       REFERENCA_PLACANJA,
				MAX (DECODE(idt.attrib_id, 'IDPOROBV', idt.data_vchar, NULL))                 IDPOROBV,
				MAX (DECODE(idt.attrib_id, 'VRSTA_UPLATE', idt.data_vchar, NULL))             VRSTA_UPLATE,
				MAX (DECODE(idt.attrib_id, common_pck.cTRANPAY_ATT_VRSTA_PRIHODA, idt.data_vchar, NULL))            VRSTA_PRIHODA,
				MAX (DECODE(idt.attrib_id, 'PORPERIOD_OD', idt.data_vchar, NULL))    PORPERIOD_OD,
				MAX (DECODE(idt.attrib_id, 'PORPERIOD_DO', idt.data_vchar, NULL))    PORPERIOD_DO,
				MAX (DECODE(idt.attrib_id, common_pck.cTRANPAY_ATT_OPCINA, idt.data_vchar, NULL))                   OPCINA,
				MAX (DECODE(idt.attrib_id, 'POZIV_NA_BROJ', idt.data_vchar, NULL))            POZIV_NA_BROJ,
				MAX (DECODE(idt.attrib_id, 'BUDZETSKA_ORGANIZACIJA', idt.data_vchar, NULL))   BUDZETSKA_ORGANIZACIJA,
				DECODE(ine.payment_type, common_pck.cRTI_TRANSFER, 'D', NULL)                    FIKSNO,
				TO_NUMBER(DECODE(ine.payment_type, common_pck.cRTI_TRANSFER, ine.tranval, NULL)) IZNOS_KUPOVINE,
				DECODE(ine.payment_type, common_pck.cRTI_TRANSFER, MAX (DECODE(idt.attrib_id, common_pck.cTRANPAY_ATT_RACUN, idt.data_vchar, NULL)), NULL)  RACUN,
				DECODE(ine.payment_type, common_pck.cRTI_TRANSFER, ine.tranval_currency_id, NULL)                RACUN_VALUTA
			FROM invoices ine
				JOIN invoice_provider_clients ipt ON (ipt.id = ine.client_id)
				JOIN invoice_providers ipr ON (ipr.id = ipt.ipr_id)
				JOIN invoice_details idt ON (idt.ine_id = ine.id)
				JOIN bank_accounts ba ON (ba.account_owner_id = ine.account_owner_id)
				JOIN action_grants ag ON (ag.account_id = ba.ph4)
				JOIN actions ac ON (ag.application_id = ac.application_id and ac.id = ag.action_id)
				JOIN applications ap ON (ap.id = ac.application_id)
			WHERE
				ine.account_owner_id LIKE mcauth.auth.getAccountOwner AND -- Restrict on account owner for session
				ag.user_id = mcauth.auth.getClientId AND -- Restrict on Elba user
				TO_CHAR(ine.status) LIKE vStatus AND -- Restrict on invoice status
				ap.id = mcauth.auth.getSApp AND -- Restrict on session application
				-- Fixed filter
				ac.id = 'CREATE_TRANPAY' AND
				ine.tranval > 0 AND
				-- Additional restriction on tables introduced by joins
				ipr.valid = cValidTrue AND
				ipt.valid = cValidTrue AND
				ag.valid = cValidTrue AND
				ac.valid = cValidTrue AND
				ap.valid = cValidTrue AND
				ba.parent_id is null AND
				ba.status = 'A' AND
				ipr.name not like 'INTERNAL%' AND
				((ac.req_type_sep = cValidTrue AND ag.req_type_id = ine.payment_type) OR ac.req_type_sep = cValidFalse)
			GROUP BY ine.id, ipr.id, ipr.name, ipr.payment_over, ipr.payment_below, ine.payment_type, ine.invoice_no, ine.status, ine.description, ine.tranval, ine.tranval_currency_id, ine.tranpay_id, ine.date_of_processing,
				ine.billing_period, ine.billing_date, ine.payment_due_date, ine.display_on_overview, ine.group_id, ipr.provide_invoice_history, ipr.service_name, ipt.user_comment
				) i JOIN invoices m ON (m.id = i.invoice_id)
			ORDER BY NVL(billing_date, payment_due_date) DESC;

        RETURN rez;

    END getInvoices;

	FUNCTION getProcessedInvoices(pEndUserId IN end_users.id%TYPE)
    RETURN sys_refcursor IS
		myunit CONSTANT VARCHAR2(30) := 'getProcessedInvoices';
        vStatus CONSTANT NUMBER(1) := 2;
        rez sys_refcursor;

    BEGIN
        slog.debug(pkgCtxId, myUnit,  pEndUserId || ':' || vStatus || ':' || mcauth.auth.getAccountOwner);

        common_pck.CommonSecurityChecks;

        OPEN rez FOR
		SELECT i.invoice_id, i.invoice_provider_id, i.invoice_provider_name, i.payment_over, i.payment_below, i.payment_type, i.reference, i.status, i.description, i.tranval, i.currency_id, i.tranpay_tranval, i.tranpay_tranval_currency_id, i.tranpay_id, i.date_of_processing,
		i.billing_period, i.billing_date, i.payment_due_date, i.hitno, i.naziv_primaoca, i.racun_primaoca, i.naziv_posiljaoca, i.javni_prihodi, i.referenca_placanja, i.idporobv, i.vrsta_uplate, i.vrsta_prihoda, i.porperiod_od, i.porperiod_do, i.opcina, i.poziv_na_broj,
		i.budzetska_organizacija, i.fiksno,	i.iznos_kupovine, i.racun, i.racun_valuta, m.message, i.display_on_overview,  i.group_id,
		decode(i.group_id,
                common_pck.cINVGROUP_INVOICE, common_pck.cACCTLTYPE_TRAN,
                common_pck.cINVGROUP_LOANDEPT, common_pck.cACCTLTYPE_CRED,
                common_pck.cINVGROUP_SAVING, common_pck.cACCTLTYPE_SAVE) tl_type,
            mlang.trans(mcauth.auth.getLang, '/Core/Main/InvoicesGroupNames/'||i.group_id) group_id_translated, i.invoice_download_available, i.service_name, i.user_comment
		FROM
		(-- 1. Invoices related to user
		SELECT ine.id invoice_id, ipr.id invoice_provider_id, ipr.name invoice_provider_name, ipr.payment_over payment_over, ipr.payment_below payment_below, ine.payment_type payment_type, ine.invoice_no reference, ine.status, ine.description, ine.tranval, ine.tranval_currency_id currency_id,
		t.tranval tranpay_tranval, t.tranval_currency_id tranpay_tranval_currency_id,
		ine.tranpay_id, ine.date_of_processing, ine.billing_period, ine.billing_date, ine.payment_due_date,
		ine.display_on_overview, ine.group_id, ipr.provide_invoice_history invoice_download_available, ipr.service_name, ipt.user_comment,
				MAX (DECODE(idt.attrib_id, 'HITNO', idt.data_vchar, NULL))                    HITNO,
				MAX (DECODE(idt.attrib_id, common_pck.cTRANPAY_ATT_NAZIV_PRIMAOCA, idt.data_vchar, NULL))           NAZIV_PRIMAOCA,
				MAX (DECODE(idt.attrib_id, common_pck.cTRANPAY_ATT_RACUN_PRIMAOCA, idt.data_vchar, NULL))           RACUN_PRIMAOCA,
				MAX (DECODE(idt.attrib_id, common_pck.cTRANPAY_ATT_NAZIV_POSILJAOCA, idt.data_vchar, NULL))         NAZIV_POSILJAOCA,
				MAX (DECODE(idt.attrib_id, 'JAVNI_PRIHODI', idt.data_vchar, NULL))            JAVNI_PRIHODI,
				MAX (DECODE(idt.attrib_id, common_pck.cTRPAY_ATT_REFERENCA_PLACANJA, idt.data_vchar, NULL))       REFERENCA_PLACANJA,
				MAX (DECODE(idt.attrib_id, 'IDPOROBV', idt.data_vchar, NULL))                 IDPOROBV,
				MAX (DECODE(idt.attrib_id, 'VRSTA_UPLATE', idt.data_vchar, NULL))             VRSTA_UPLATE,
				MAX (DECODE(idt.attrib_id, common_pck.cTRANPAY_ATT_VRSTA_PRIHODA, idt.data_vchar, NULL))            VRSTA_PRIHODA,
				MAX (DECODE(idt.attrib_id, 'PORPERIOD_OD', idt.data_vchar, NULL))    PORPERIOD_OD,
				MAX (DECODE(idt.attrib_id, 'PORPERIOD_DO', idt.data_vchar, NULL))    PORPERIOD_DO,
				MAX (DECODE(idt.attrib_id, common_pck.cTRANPAY_ATT_OPCINA, idt.data_vchar, NULL))                   OPCINA,
				MAX (DECODE(idt.attrib_id, 'POZIV_NA_BROJ', idt.data_vchar, NULL))            POZIV_NA_BROJ,
				MAX (DECODE(idt.attrib_id, 'BUDZETSKA_ORGANIZACIJA', idt.data_vchar, NULL))   BUDZETSKA_ORGANIZACIJA,
				DECODE(ine.payment_type, common_pck.cRTI_TRANSFER, 'D', NULL)                                FIKSNO,
				TO_NUMBER(DECODE(ine.payment_type, common_pck.cRTI_TRANSFER, ine.tranval, NULL))                 IZNOS_KUPOVINE,
				DECODE(ine.payment_type, common_pck.cRTI_TRANSFER, MAX (DECODE(idt.attrib_id, common_pck.cTRANPAY_ATT_RACUN, idt.data_vchar, NULL)), NULL)  RACUN,
				DECODE(ine.payment_type, common_pck.cRTI_TRANSFER, ine.tranval_currency_id, NULL)                RACUN_VALUTA
			FROM invoices ine
				JOIN invoice_provider_clients ipt ON (ipt.id = ine.client_id)
				JOIN invoice_providers ipr ON (ipr.id = ipt.ipr_id)
				JOIN invoice_details idt ON (idt.ine_id = ine.id)
				JOIN tranpays t ON (t.id = ine.tranpay_id)
			WHERE
				ipr.valid = cValidTrue AND
				ipt.valid = cValidTrue AND
				ine.status = vStatus AND
				ipt.end_users_id = pEndUserId AND
				ipr.name not like 'INTERNAL%'
			GROUP BY ine.id, ipr.id, ipr.name, ipr.payment_over, ipr.payment_below, ine.payment_type, ine.invoice_no, ine.status, ine.description, ine.tranval, ine.tranval_currency_id,
				t.tranval, t.tranval_currency_id,
				ine.tranpay_id, ine.date_of_processing,
				ine.billing_period, ine.billing_date, ine.payment_due_date, ine.display_on_overview, ine.group_id, ipr.provide_invoice_history, ipr.service_name, ipt.user_comment
			UNION
			-- 2. Invoices koji se ve�u uz vlasnika ra�una a ne Elba user-a
		SELECT ine.id invoice_id, ipr.id invoice_provider_id, ipr.name invoice_provider_name, ipr.payment_over payment_over, ipr.payment_below payment_below, ine.payment_type payment_type, ine.invoice_no reference, ine.status, ine.description, ine.tranval, ine.tranval_currency_id currency_id,
		t.tranval tranpay_tranval, t.tranval_currency_id tranpay_tranval_currency_id,
		ine.tranpay_id, ine.date_of_processing, ine.billing_period, ine.billing_date, ine.payment_due_date,
		ine.display_on_overview, ine.group_id, ipr.provide_invoice_history invoice_download_available, ipr.service_name, ipt.user_comment,
				MAX (DECODE(idt.attrib_id, 'HITNO', idt.data_vchar, NULL))                    HITNO,
				MAX (DECODE(idt.attrib_id, common_pck.cTRANPAY_ATT_NAZIV_PRIMAOCA, idt.data_vchar, NULL))           NAZIV_PRIMAOCA,
				MAX (DECODE(idt.attrib_id, common_pck.cTRANPAY_ATT_RACUN_PRIMAOCA, idt.data_vchar, NULL))           RACUN_PRIMAOCA,
				MAX (DECODE(idt.attrib_id, common_pck.cTRANPAY_ATT_NAZIV_POSILJAOCA, idt.data_vchar, NULL))         NAZIV_POSILJAOCA,
				MAX (DECODE(idt.attrib_id, 'JAVNI_PRIHODI', idt.data_vchar, NULL))            JAVNI_PRIHODI,
				MAX (DECODE(idt.attrib_id, common_pck.cTRPAY_ATT_REFERENCA_PLACANJA, idt.data_vchar, NULL))       REFERENCA_PLACANJA,
				MAX (DECODE(idt.attrib_id, 'IDPOROBV', idt.data_vchar, NULL))                 IDPOROBV,
				MAX (DECODE(idt.attrib_id, 'VRSTA_UPLATE', idt.data_vchar, NULL))             VRSTA_UPLATE,
				MAX (DECODE(idt.attrib_id, common_pck.cTRANPAY_ATT_VRSTA_PRIHODA, idt.data_vchar, NULL))            VRSTA_PRIHODA,
				MAX (DECODE(idt.attrib_id, 'PORPERIOD_OD', idt.data_vchar, NULL))    PORPERIOD_OD,
				MAX (DECODE(idt.attrib_id, 'PORPERIOD_DO', idt.data_vchar, NULL))    PORPERIOD_DO,
				MAX (DECODE(idt.attrib_id, common_pck.cTRANPAY_ATT_OPCINA, idt.data_vchar, NULL))                   OPCINA,
				MAX (DECODE(idt.attrib_id, 'POZIV_NA_BROJ', idt.data_vchar, NULL))            POZIV_NA_BROJ,
				MAX (DECODE(idt.attrib_id, 'BUDZETSKA_ORGANIZACIJA', idt.data_vchar, NULL))   BUDZETSKA_ORGANIZACIJA,
				DECODE(ine.payment_type, common_pck.cRTI_TRANSFER, 'D', NULL)                    FIKSNO,
				TO_NUMBER(DECODE(ine.payment_type, common_pck.cRTI_TRANSFER, ine.tranval, NULL)) IZNOS_KUPOVINE,
				DECODE(ine.payment_type, common_pck.cRTI_TRANSFER, MAX (DECODE(idt.attrib_id, common_pck.cTRANPAY_ATT_RACUN, idt.data_vchar, NULL)), NULL)  RACUN,
				DECODE(ine.payment_type, common_pck.cRTI_TRANSFER, ine.tranval_currency_id, NULL)                RACUN_VALUTA
			FROM invoices ine
				JOIN invoice_provider_clients ipt ON (ipt.id = ine.client_id)
				JOIN invoice_providers ipr ON (ipr.id = ipt.ipr_id)
				JOIN invoice_details idt ON (idt.ine_id = ine.id)
				JOIN tranpays t ON (t.id = ine.tranpay_id)
				JOIN bank_accounts ba ON (ba.account_owner_id = ine.account_owner_id)
				JOIN action_grants ag ON (ag.account_id = ba.ph4)
				JOIN actions ac ON (ag.application_id = ac.application_id and ac.id = ag.action_id)
				JOIN applications ap ON (ap.id = ac.application_id)
			WHERE
				ine.account_owner_id LIKE mcauth.auth.getAccountOwner AND -- Restrict on account owner for session
				ag.user_id = pEndUserId AND -- Restrict on Elba user
				ine.status = vStatus AND -- Restrict on invoice status
				ap.id = mcauth.auth.getSApp AND -- Restrict on session application
				-- Fixed filter
				ac.id = 'CREATE_TRANPAY' AND
				ine.tranval > 0 AND
				-- Additional restriction on tables introduced by joins
				ipr.valid = cValidTrue AND
				ag.valid = cValidTrue AND
				ac.valid = cValidTrue AND
				ap.valid = cValidTrue AND
				ba.parent_id is null AND
				ipr.name not like 'INTERNAL%' AND
				ba.status = 'A' AND
				((ac.req_type_sep = cValidTrue AND ag.req_type_id = ine.payment_type) OR ac.req_type_sep = cValidFalse)
			GROUP BY ine.id, ipr.id, ipr.name, ipr.payment_over, ipr.payment_below, ine.payment_type, ine.invoice_no, ine.status, ine.description, ine.tranval, ine.tranval_currency_id, t.tranval, t.tranval_currency_id, ine.tranpay_id, ine.date_of_processing,
				ine.billing_period, ine.billing_date, ine.payment_due_date, ine.display_on_overview, ine.group_id, ipr.provide_invoice_history, ipr.service_name, ipt.user_comment
				) i JOIN invoices m ON (m.id = i.invoice_id)
			ORDER BY NVL(billing_date, payment_due_date) DESC;

        RETURN rez;

	END getProcessedInvoices;

	FUNCTION getProcessedInvoicesCount(pEndUserId IN end_users.id%TYPE)
	RETURN PLS_INTEGER IS
		myunit CONSTANT VARCHAR2(30) := 'getProcessedInvoicesCount';
		vInvoices# PLS_INTEGER;
		vStatus CONSTANT NUMBER(1) := 2;
	BEGIN
		slog.debug(pkgCtxId, myUnit,  pEndUserId || ':'  || mcauth.auth.getAccountOwner);

        common_pck.CommonSecurityChecks;

		slog.debug(pkgCtxId, myUnit, 'Get count');

		SELECT COUNT(broj_racuna)
		INTO vInvoices#
		FROM
		(-- 1. Invoices related to user
		SELECT 1 broj_racuna
			FROM invoices ine
				JOIN invoice_provider_clients ipt ON (ipt.id = ine.client_id)
				JOIN invoice_providers ipr ON (ipr.id = ipt.ipr_id)
			WHERE
				ipr.valid = cValidTrue AND
				ipt.valid = cValidTrue AND
				ine.status = vStatus AND
				ipt.end_users_id = pEndUserId AND
				ipr.name not like 'INTERNAL%'
			UNION ALL
			-- 2. Invoices koji se ve�u uz vlasnika ra�una a ne Elba user-a
		SELECT 1 broj_racuna
			FROM invoices ine
				JOIN invoice_providers ipr ON (ipr.id = ine.ipr_id)
				JOIN bank_accounts ba ON (ba.account_owner_id = ine.account_owner_id)
				JOIN action_grants ag ON (ag.account_id = ba.ph4)
				JOIN actions ac ON (ag.application_id = ac.application_id and ac.id = ag.action_id)
				JOIN applications ap ON (ap.id = ac.application_id)
			WHERE
				ine.account_owner_id LIKE mcauth.auth.getAccountOwner AND -- Restrict on account owner for session
				ag.user_id = pEndUserId AND -- Restrict on Elba user
				ine.status = vStatus AND -- Restrict on invoice status
				ap.id = mcauth.auth.getSApp AND -- Restrict on session application
				-- Fixed filter
				ac.id = 'CREATE_TRANPAY' AND
				ine.tranval > 0 AND
				-- Additional restriction on tables introduced by joins
				ipr.valid = cValidTrue AND
				ag.valid = cValidTrue AND
				ac.valid = cValidTrue AND
				ap.valid = cValidTrue AND
				ba.parent_id is null AND
				ipr.name not like 'INTERNAL%' AND
				ba.status = 'A' AND 
				((ac.req_type_sep = cValidTrue AND ag.req_type_id = ine.payment_type) OR ac.req_type_sep = cValidFalse));

			slog.debug(pkgCtxId, myUnit, 'Found ' || vInvoices# || ' records!');
		RETURN vInvoices#;

	END getProcessedInvoicesCount;

    FUNCTION getInvoice(pInvoiceId IN mcore.invoices.id%TYPE) RETURN SYS_REFCURSOR
    IS
        myunit CONSTANT VARCHAR2(30) := 'getInvoice';
        hasAccess BOOLEAN := FALSE;
        rez sys_refcursor;

    BEGIN
        slog.debug(pkgCtxId, myUnit,  pInvoiceId);

        common_pck.CommonSecurityChecks;

        -- access right:
        -- 1. current user has access because this invoice is assigned to him or to his account owner
        -- 2. current user has access because this is unassigned invoice (i.e. QRPay bill)
        FOR c IN (SELECT 1
        			FROM invoices i
					LEFT JOIN invoice_provider_clients ipc ON (ipc.id = i.client_id)
	    			WHERE i.id = pInvoiceId
	    			AND (ipc.end_users_id IS NULL OR (ipc.end_users_id IS NOT NULL AND ipc.end_users_id = mcauth.auth.getSCID))
	   				AND (i.account_owner_id IS NULL OR (i.account_owner_id IS NOT NULL AND i.account_owner_id LIKE mcauth.auth.getAccountOwner)))
	   	LOOP
          hasAccess := TRUE;
	    END LOOP;

	    IF NOT hasAccess THEN
	      slog.error(pkgCtxId, myUnit, 'invoice: ' || pInvoiceId || ' does not exist or current user has no access to it.');
	      RETURN NULL;
	    END IF;

        OPEN rez FOR
		SELECT i.invoice_id, i.invoice_provider_id, i.invoice_provider_name, i.user_comment, i.payment_over, i.payment_below, i.payment_type, i.reference, i.status, i.description, i.tranval, i.currency_id, i.tranpay_id, i.date_of_processing,
		i.billing_period, i.billing_date, i.payment_due_date, i.hitno, i.naziv_primaoca, i.racun_primaoca, i.naziv_posiljaoca, i.javni_prihodi, i.referenca_placanja, i.idporobv, i.vrsta_uplate, i.vrsta_prihoda, i.porperiod_od, i.porperiod_do, i.opcina, i.poziv_na_broj,
		i.budzetska_organizacija, i.fiksno,	i.iznos_kupovine, i.racun, i.racun_valuta, m.message, i.display_on_overview,  i.group_id,
		decode(i.group_id,
                common_pck.cINVGROUP_INVOICE, common_pck.cACCTLTYPE_TRAN,
                common_pck.cINVGROUP_LOANDEPT, common_pck.cACCTLTYPE_CRED,
                common_pck.cINVGROUP_SAVING, common_pck.cACCTLTYPE_SAVE) tl_type,
            mlang.trans(mcauth.auth.getLang, '/Core/Main/InvoicesGroupNames/'||i.group_id) group_id_translated, i.invoice_download_available, i.service_name
		FROM
		(
		SELECT ine.id invoice_id, ipr.id invoice_provider_id, ipr.name invoice_provider_name, ipc.user_comment user_comment, ipr.payment_over payment_over, ipr.payment_below payment_below, ine.payment_type payment_type, ine.invoice_no reference, ine.status, ine.description, ine.tranval, ine.tranval_currency_id currency_id,
		ine.tranpay_id, ine.date_of_processing, ine.billing_period, ine.billing_date, ine.payment_due_date,
		ine.display_on_overview, ine.group_id, ipr.provide_invoice_history invoice_download_available, ipr.service_name,
				MAX (DECODE(idt.attrib_id, 'HITNO', idt.data_vchar, NULL))                    HITNO,
				MAX (DECODE(idt.attrib_id, common_pck.cTRANPAY_ATT_NAZIV_PRIMAOCA, idt.data_vchar, NULL))           NAZIV_PRIMAOCA,
				MAX (DECODE(idt.attrib_id, common_pck.cTRANPAY_ATT_RACUN_PRIMAOCA, idt.data_vchar, NULL))           RACUN_PRIMAOCA,
				MAX (DECODE(idt.attrib_id, common_pck.cTRANPAY_ATT_NAZIV_POSILJAOCA, idt.data_vchar, NULL))         NAZIV_POSILJAOCA,
				MAX (DECODE(idt.attrib_id, 'JAVNI_PRIHODI', idt.data_vchar, NULL))            JAVNI_PRIHODI,
				MAX (DECODE(idt.attrib_id, common_pck.cTRPAY_ATT_REFERENCA_PLACANJA, idt.data_vchar, NULL))       REFERENCA_PLACANJA,
				MAX (DECODE(idt.attrib_id, 'IDPOROBV', idt.data_vchar, NULL))                 IDPOROBV,
				MAX (DECODE(idt.attrib_id, 'VRSTA_UPLATE', idt.data_vchar, NULL))             VRSTA_UPLATE,
				MAX (DECODE(idt.attrib_id, common_pck.cTRANPAY_ATT_VRSTA_PRIHODA, idt.data_vchar, NULL))            VRSTA_PRIHODA,
				MAX (DECODE(idt.attrib_id, 'PORPERIOD_OD', idt.data_vchar, NULL))    PORPERIOD_OD,
				MAX (DECODE(idt.attrib_id, 'PORPERIOD_DO', idt.data_vchar, NULL))    PORPERIOD_DO,
				MAX (DECODE(idt.attrib_id, common_pck.cTRANPAY_ATT_OPCINA, idt.data_vchar, NULL))                   OPCINA,
				MAX (DECODE(idt.attrib_id, 'POZIV_NA_BROJ', idt.data_vchar, NULL))            POZIV_NA_BROJ,
				MAX (DECODE(idt.attrib_id, 'BUDZETSKA_ORGANIZACIJA', idt.data_vchar, NULL))   BUDZETSKA_ORGANIZACIJA,
				DECODE(ine.payment_type, common_pck.cRTI_TRANSFER, 'D', NULL)                                FIKSNO,
				TO_NUMBER(DECODE(ine.payment_type, common_pck.cRTI_TRANSFER, ine.tranval, NULL))                 IZNOS_KUPOVINE,
				DECODE(ine.payment_type, common_pck.cRTI_TRANSFER, MAX (DECODE(idt.attrib_id, common_pck.cTRANPAY_ATT_RACUN, idt.data_vchar, NULL)), NULL)  RACUN,
				DECODE(ine.payment_type, common_pck.cRTI_TRANSFER, ine.tranval_currency_id, NULL)                RACUN_VALUTA
			FROM invoices ine
				JOIN invoice_providers ipr ON (ipr.id = ine.ipr_id)
				JOIN invoice_details idt ON (idt.ine_id = ine.id)
				LEFT JOIN mcore.invoice_provider_clients ipc ON (ipc.id = ine.client_id)
			WHERE
				ipr.valid = cValidTrue AND
				ine.id = pInvoiceId
			GROUP BY ine.id, ipr.id, ipr.name, ipc.user_comment, ipr.payment_over, ipr.payment_below, ine.payment_type, ine.invoice_no, ine.status, ine.description, ine.tranval, ine.tranval_currency_id, ine.tranpay_id, ine.date_of_processing,
				ine.billing_period, ine.billing_date, ine.payment_due_date, ine.display_on_overview, ine.group_id, ipr.provide_invoice_history, ipr.service_name
		) i JOIN invoices m ON (m.id = i.invoice_id);

        RETURN rez;

    END getInvoice;

	PROCEDURE processInvoice(pInvoiceID IN invoices.id%TYPE, pAction IN PLS_INTEGER, pTranpayId IN NUMBER DEFAULT NULL, pExtrefName IN tranpay_extref.extref_name%TYPE DEFAULT NULL)
    IS
		myunit CONSTANT VARCHAR2(30) := 'processInvoice';
		vReference invoices.invoice_no%TYPE;
		-- Status 1 - Kreiranje naloga za ra�una
		-- Status 3 - Cancel ra�una
	BEGIN
		slog.debug(pkgCtxId, myUnit,  pInvoiceID || ':' || pAction || ':' || pTranpayId);

		IF pAction NOT IN (1,3) THEN
			slog.error(pkgCtxId, myUnit, cERR_InvalidInvoiceStatus, pInvoiceID || ':' || pAction || ':' || pTranpayId);
			sspkg.raiseError(cERR_InvalidInvoiceStatus, null, pkgCtxId, myunit);
		END IF;

		IF pAction = 1 AND pTranpayId IS NULL THEN
			slog.error(pkgCtxId, myUnit, cERR_MissTranpayIdForInvoice, pInvoiceID || ':' || pAction || ':' || pTranpayId);
			sspkg.raiseError(cERR_MissTranpayIdForInvoice, null, pkgCtxId, myunit);
		END IF;

		BEGIN

			UPDATE invoices
			SET status = pAction,
				tranpay_id = pTranpayId
			WHERE id = pInvoiceId AND status = 0
			RETURNING invoice_no INTO vReference;

			IF SQL%ROWCOUNT = 0 THEN
				slog.error(pkgCtxId, myUnit, cERR_InvalidInvoice, pInvoiceID ||':' || pAction || ':' || pTranpayId || ':' || vReference);
				sspkg.raiseError(cERR_InvalidInvoice, null, pkgCtxId, myunit);
			END IF;

			IF pTranpayId IS NOT NULL AND vReference IS NOT NULL THEN

				tranpays_pck.assignExtRefId2TRanpay(pTranpaysId => pTranpayId,
					pExtRef_Id => vReference,
					pExtRef_Name => NVL(pExtrefName, 'INVOICE'),
					pExtRef_Status => 'NEW');

			END IF;

		EXCEPTION
			WHEN no_data_found THEN
				slog.error(pkgCtxId, myUnit, cERR_InvalidInvoice, pInvoiceID || ':' || pAction || ':' || pTranpayId);
				sspkg.raiseError(cERR_InvalidInvoice, null, pkgCtxId, myunit);
		END;

    END processInvoice;

	PROCEDURE changeInvoiceStatus(pTranpayId IN VARCHAR2, pStatus IN PLS_INTEGER)
    IS
        myunit CONSTANT VARCHAR2(30) := 'changeInvoiceStatus';
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pTranpayId || ':' || pStatus);

        IF pStatus = 0 THEN

          -- Kada nalog biva odbijen, faktura se razdvaja od istog!
          UPDATE invoices
           SET status = pStatus, tranpay_id = null
          WHERE tranpay_id = pTranpayId;

        ELSIF pStatus = 2 THEN
		
			UPDATE invoices
            SET status = pStatus, date_of_processing = sysdate
            WHERE tranpay_id = pTranpayId;
			
		ELSE

          UPDATE invoices
           SET status = pStatus
          WHERE tranpay_id = pTranpayId;

        END IF;

    END changeInvoiceStatus;

	FUNCTION CreateBatch(
		pInvoiceProviderId IN invoice_providers.id%TYPE,
		pName IN invoice_batches.name%TYPE,
		pValid IN invoice_batches.valid%TYPE)
	RETURN invoice_batches.id%TYPE IS
		myunit CONSTANT VARCHAR2(30) := 'CreateBatch2';
		cg$rec     cg$invoice_batches.cg$row_type;
		cg$ind     cg$invoice_batches.cg$ind_type;

	BEGIN
		slog.debug(pkgCtxId, myUnit, pInvoiceProviderId || ':' || pName || ':' || pValid);

		BEGIN
			cg$invoice_batches.val$length(
					pID => NULL
					,pIPR_ID => pInvoiceProviderId
					,pNAME => pName
					,pBATCH_DATE => SYSDATE
					,pVALID => pValid
				);
			cg$rec.ipr_id := pInvoiceProviderId;
			cg$rec.name := pName;
			cg$rec.batch_date := SYSDATE;
			cg$rec.valid := pValid;
			cg$ind.ipr_id := TRUE;
			cg$ind.name := TRUE;
			cg$ind.batch_date := TRUE;
			cg$ind.valid := TRUE;
		END;

		BEGIN
			cg$invoice_batches.ins(cg$rec => cg$rec, cg$ind => cg$invoice_batches.cg$ind_true, do_ins => TRUE);
			slog.debug(pkgCtxId, myunit, 'INS completed');
			RETURN cg$rec.id;
		EXCEPTION
			WHEN cg$errors.cg$error THEN
				v_cg_result := cg$errors.pop(msg => vMSG
					,error => vERROR
					,msg_type => vMSG_TYPE
					,msgid => vMSGID
					,loc => vLOC);

				cg$errors.clear;

				slog.error(pkgCtxId, myunit, pInvoiceProviderId || ':' || pName || ':' || pValid || ':INS:' || vMSG);
				RAISE;
			WHEN OTHERS THEN
				slog.error(pkgCtxId, myunit, pInvoiceProviderId || ':' || pName || ':' || pValid || ':INS:' || SQLERRM);
				RAISE;
		END;

	END CreateBatch;

  FUNCTION CreateBatch(
		pInvoiceProviderId IN invoice_providers.id%TYPE,
		pEndUserId invoice_provider_clients.end_users_id%TYPE,
		pName IN invoice_batches.name%TYPE,
		pValid IN invoice_batches.valid%TYPE)
	RETURN invoice_batches.id%TYPE IS
    myunit CONSTANT VARCHAR2(30) := 'CreateBatch';
  BEGIN
    slog.debug(pkgCtxId, myUnit, pInvoiceProviderId || ':' || pEndUserId);
    common_pck.CommonSecurityChecks;

		IF NOT isAdmin(pInvoiceProviderId, pEndUserId) THEN
			slog.error(pkgCtxId, myUnit, cERR_NoAdminPrivilege, pInvoiceProviderId || ':' || pEndUserId);
			sspkg.raiseError(cERR_NoAdminPrivilege, null, pkgCtxId, myunit);
		END IF;

    RETURN CreateBatch(pInvoiceProviderId => pInvoiceProviderId, pName => pName, pValid => pValid);

  END CreateBatch;

	PROCEDURE ChangeBatchStatus(
		pInvoiceBatchId IN invoice_batches.id%TYPE,
		pValid IN invoice_batches.valid%TYPE)
	IS
		myunit CONSTANT VARCHAR2(30) := 'ChangeBatchStatus2';

	BEGIN
		slog.debug(pkgCtxId, myUnit, pInvoiceBatchId || ':' || pValid);

		UPDATE invoice_batches SET valid = pValid WHERE id = pInvoiceBatchId;

	END ChangeBatchStatus;

	PROCEDURE ChangeBatchStatus(
		pEndUserId invoice_provider_clients.end_users_id%TYPE,
		pInvoiceBatchId IN invoice_batches.id%TYPE,
		pValid IN invoice_batches.valid%TYPE)
	IS
		myunit CONSTANT VARCHAR2(30) := 'ChangeBatchStatus';
		vInvoiceProviderId invoice_providers.id%TYPE;
	BEGIN
		slog.debug(pkgCtxId, myUnit, pEndUserId || ':' || pInvoiceBatchId || ':' || pValid);
    common_pck.CommonSecurityChecks;

		BEGIN
			SELECT ipr_id
			INTO vInvoiceProviderId
			FROM invoice_batches
			WHERE id = pInvoiceBatchId;

		EXCEPTION
			WHEN no_data_found THEN
				slog.error(pkgCtxId, myUnit, cERR_InvalidBatch, pEndUserId || ':' || pInvoiceBatchId || ':' || pValid);
				sspkg.raiseError(cERR_InvalidBatch, null, pkgCtxId, myunit);
		END;

    IF NOT isAdmin(vInvoiceProviderId, pEndUserId) THEN
			slog.error(pkgCtxId, myUnit, cERR_NoAdminPrivilege, vInvoiceProviderId || ':' || pEndUserId || ':' || pInvoiceBatchId || ':' || pValid);
			sspkg.raiseError(cERR_NoAdminPrivilege, null, pkgCtxId, myunit);
		END IF;

    ChangeBatchStatus(
      pInvoiceBatchId => pInvoiceBatchId,
      pValid => pValid);

  END ChangeBatchStatus;

  PROCEDURE RemoveBatch(pInvoiceBatchId IN invoice_batches.id%TYPE)
	IS
		myunit CONSTANT VARCHAR2(30) := 'RemoveBatch2';
	BEGIN
    slog.debug(pkgCtxId, myUnit,  pInvoiceBatchId);

		DELETE FROM invoices
		WHERE ibe_id = pInvoiceBatchId
		AND status = 0;

		UPDATE invoice_batches
		   SET valid = cValidFalse
		 WHERE id = pInvoiceBatchId;

	END RemoveBatch;

	PROCEDURE RemoveBatch(pInvoiceBatchId IN invoice_batches.id%TYPE, pEndUserId invoice_provider_clients.end_users_id%TYPE)
	IS
		myunit CONSTANT VARCHAR2(30) := 'RemoveBatch';
		vInvoiceProviderId invoice_providers.id%TYPE;
	BEGIN
        slog.debug(pkgCtxId, myUnit,  pInvoiceBatchId);
        common_pck.CommonSecurityChecks;

		BEGIN

			SELECT ipr_id
			INTO vInvoiceProviderId
			FROM invoice_batches
			WHERE id = pInvoiceBatchId;

		EXCEPTION
			WHEN no_data_found THEN
				slog.error(pkgCtxId, myUnit, cERR_InvalidBatch, pInvoiceBatchId || ':' || pEndUserId);
				sspkg.raiseError(cERR_InvalidBatch, null, pkgCtxId, myunit);
		END;

		IF NOT isAdmin(vInvoiceProviderId, pEndUserId) THEN
			slog.error(pkgCtxId, myUnit, cERR_NoAdminPrivilege, vInvoiceProviderId || ':' || pInvoiceBatchId || ':' || pEndUserId);
			sspkg.raiseError(cERR_NoAdminPrivilege, null, pkgCtxId, myunit);
		END IF;

		RemoveBatch(pInvoiceBatchId => pInvoiceBatchId);

	END RemoveBatch;
	
	-- Provjera da li već postoji batch s datim nazivom za izdavatelja	
	-- Used by InvoicesImportWF
	FUNCTION isExistingBatch(pInvoiceProviderId INTEGER, pBatchName IN VARCHAR2)
	RETURN PLS_INTEGER IS
		vBatchAlreadyExists VARCHAR2(1);
		myunit CONSTANT VARCHAR2(30) := 'isExistingBatch';
	BEGIN
		slog.debug(pkgCtxId, myunit);

		SELECT NULL 
		INTO vBatchAlreadyExists 
		FROM DUAL WHERE EXISTS
		(SELECT NULL FROM mcore.invoice_batches WHERE ipr_id = pInvoiceProviderId AND name = pBatchName AND valid = cValidTrue);

		RETURN cValidTrue;
	EXCEPTION
		WHEN no_data_found THEN
			RETURN cValidFalse;
	END isExistingBatch;

	PROCEDURE RemoveInvoice(pInvoiceId IN invoices.id%TYPE)
	IS
		vRowId ROWID;
		myunit CONSTANT VARCHAR2(30) := 'RemoveInvoice';
	BEGIN

		SELECT ROWID
		INTO vRowID
		FROM invoices
		WHERE id = pInvoiceId
		and status = 0;

		DELETE FROM invoices WHERE ROWID = vRowId;

	EXCEPTION
	WHEN no_data_found THEN
	  slog.error(pkgCtxId, myUnit, cERR_InvalidInvoice, pInvoiceID);
			sspkg.raiseError(cERR_InvalidInvoice, null, pkgCtxId, myunit);
	END RemoveInvoice;

    FUNCTION getBatches(pInvoiceProviderId IN invoice_batches.ipr_id%TYPE)
	RETURN sys_refcursor IS
		myunit CONSTANT VARCHAR2(30) := 'getBatches2';
		rez sys_refcursor;
	BEGIN
		slog.debug(pkgCtxId, myUnit,  pInvoiceProviderId);

		OPEN rez FOR
			SELECT id, name, batch_date
			FROM invoice_batches
			WHERE ipr_id = pInvoiceProviderId
			  AND valid = cValidTrue
			ORDER BY batch_date DESC;
		RETURN rez;
	END getBatches;

	FUNCTION getBatches(pInvoiceProviderId IN invoice_batches.ipr_id%TYPE, pEndUserId invoice_provider_clients.end_users_id%TYPE)
	RETURN sys_refcursor IS
		myunit CONSTANT VARCHAR2(30) := 'getBatches';
	BEGIN
    slog.debug(pkgCtxId, myUnit,  pInvoiceProviderId);
    common_pck.CommonSecurityChecks;

		IF NOT isAdmin(pInvoiceProviderId, pEndUserId) THEN
			slog.error(pkgCtxId, myUnit, cERR_NoAdminPrivilege, pInvoiceProviderId || ':' || pEndUserId);
			sspkg.raiseError(cERR_NoAdminPrivilege, null, pkgCtxId, myunit);
		END IF;

    RETURN getBatches(pInvoiceProviderId => pInvoiceProviderId);

	END getBatches;

	FUNCTION UploadInvoice(
		pInvoiceBatchId IN invoices.ibe_id%TYPE,
		pInvoiceProviderId IN invoices.ipr_id%TYPE,
		pUserReference IN invoice_provider_clients.user_reference%TYPE,
		pInvoiceNo IN invoices.invoice_no%TYPE,
		pPaymentType IN invoices.payment_type%TYPE,
		pDescription IN invoices.description%TYPE,
		pTranval IN invoices.tranval%TYPE,
		pCurrencyId IN invoices.tranval_currency_id%TYPE,
		pBeneficiaryName IN VARCHAR2,
		pBeneficiaryAccount IN VARCHAR2,
		pMessage IN CLOB,
		pBILLING_PERIOD IN VARCHAR2,
		pBILLING_DATE IN DATE,
		pPAYMENT_DUE_DATE IN DATE,
		pImportResult OUT invoice_import_result
	) RETURN BOOLEAN IS
		myunit CONSTANT VARCHAR2(30) := 'UploadInvoice';
		vApiCallResult PLS_INTEGER;
		vInvoiceId invoices.id%TYPE := NULL;
		vDataVChar VARCHAR2(400);
		vInvoiceProviderClientId invoice_provider_clients.id%TYPE;
		vNumberOfInvoices PLS_INTEGER;

	BEGIN
		pImportResult := invoice_import_result(NULL, NULL, NULL, NULL);
		pImportResult.invoice_no := pInvoiceNo;
		pImportResult.user_reference := pUserReference;

		IF pUserReference = '-' THEN
			-- Raise INVALID USER REFERENCE
			slog.error(pkgCtxId, myunit, cERR_InvalidUserReference, pInvoiceProviderId || ':' || pUserReference);
			pImportResult.status := 'GRESKA';
			pImportResult.message := 'Referenca ne mo�e biti -';
			RETURN FALSE;
		END IF;

		-- Retrieve invoice_provider_clients.id based on provided UserReference
		BEGIN
			SELECT id
			INTO vInvoiceProviderClientId
			FROM invoice_provider_clients
			WHERE ipr_id = pInvoiceProviderId
				AND user_reference = pUserReference
				AND type = common_pck.cINVPRVCLTYPECONSUMER
				AND valid = cValidTrue;

		EXCEPTION
		WHEN no_data_found THEN
			-- There is no active subscribtion for given provider id and user reference
			-- Possible causes:
				-- User has canceled subscribtion after report was generated
				-- Provider uses non-existent user reference
			pImportResult.status := 'GRESKA';
			pImportResult.message := 'Ne postoji aktivna pretplata s referencom ' || pUserReference;
			RETURN FALSE;
		END;

		SELECT COUNT(*)
        	INTO vNumberOfInvoices
        	FROM invoices
        	WHERE invoice_no = pInvoiceNo
            	AND client_id = vInvoiceProviderClientId;

        	IF vNumberOfInvoices > 0 THEN

            		pImportResult.status := 'OK';
            		pImportResult.message := NULL;
            		RETURN TRUE;

        	END IF;

		vApiCallResult := ia$invoices.setInvoices (
			pID => vInvoiceId
			,pINVOICE_NO => pInvoiceNo
			,pPAYMENT_TYPE => pPaymentType
			,pstatus => 0
			,pDESCRIPTION => pDescription
			,pTRANVAL => pTranval
			,pTRANVAL_CURRENCY_ID => pCurrencyId
			,pIPR_ID => pInvoiceProviderId
			,pCLIENT_ID => vInvoiceProviderClientId
			,pIBE_ID => pInvoiceBatchId
			,pTRANPAY_ID => NULL
			,pDATE_OF_PROCESSING => NULL
			,pMESSAGE => pMessage
			,pBILLING_PERIOD => pBILLING_PERIOD
			,pBILLING_DATE => pBILLING_DATE
			,pPAYMENT_DUE_DATE => pPAYMENT_DUE_DATE
			,pACCOUNT_OWNER_ID => NULL
			,pDISPLAY_ON_OVERVIEW => cValidTrue
		    ,pGROUP_ID => NULL
			,pAction => 'WRITE'
			,pAutonomousTransaction => FALSE
			,pDoCommit => FALSE
			,pmsg => vMSG
			,perror => vERROR
			,pmsg_type => vMSG_TYPE
			,pmsgid => vMSGID
			,ploc => vLOC);

		IF vApiCallResult > 0 THEN
			pImportResult.status := 'GRESKA';
			pImportResult.message := SUBSTR('Interna gre�ka: ' || vInvoiceProviderClientId ||':'||pPaymentType || ':' || vMSG, 1, 1000);
			RETURN FALSE;

		END IF;

          FOR i IN (SELECT id FROM tranpay_attribs ta WHERE ta.req_type_id = common_pck.cRTI_UPP AND (ta.for_copy = cValidTrue OR ta.id = cExtRefPostingScheme)) LOOP

            CASE i.id
                WHEN 'RACUN_PRIMAOCA' THEN vDataVChar := pBeneficiaryAccount;
                WHEN 'REFERENCA_PLACANJA' THEN vDataVChar := NULL;
                WHEN 'HITNO' THEN vDataVChar := 'F';
                WHEN 'IDPOROBV' THEN vDataVChar := NULL;
                WHEN 'JAVNI_PRIHODI' THEN vDataVChar := 'F';
                WHEN 'NAZIV_POSILJAOCA' THEN vDataVChar := NULL;
                WHEN 'NAZIV_PRIMAOCA' THEN vDataVChar := pBeneficiaryName;
                WHEN 'VRSTA_PRIHODA' THEN vDataVChar := NULL;
                WHEN 'VRSTA_UPLATE' THEN vDataVChar := NULL;
                WHEN 'OPCINA' THEN vDataVChar := NULL;
                WHEN 'PORPERIOD_DO' THEN vDataVChar := NULL;
                WHEN 'PORPERIOD_OD' THEN vDataVChar := NULL;
                WHEN 'POZIV_NA_BROJ'THEN vDataVChar := NULL;
                WHEN 'BUDZETSKA_ORGANIZACIJA' THEN vDataVChar := NULL;
				ELSE CONTINUE;
            END CASE;

            vApiCallResult := ia$invoice_details.setInvoiceDetails (
				pINE_ID => vInvoiceId
				,pREQ_TYPE_ID => pPaymentType
				,pATTRIB_ID => i.id
				,pDESCRIPTION => NULL
				,pDATA_VCHAR => vDataVChar
				,pAction => 'WRITE'
				,pAutonomousTransaction => FALSE
				,pDoCommit => FALSE
				,pmsg => vMSG
				,perror => vERROR
				,pmsg_type => vMSG_TYPE
				,pmsgid => vMSGID
				,ploc => vLOC);

			  IF vApiCallResult > 0 THEN
				pImportResult.status := 'GRESKA';
				pImportResult.message := SUBSTR('Interna gre�ka: ' || i.id || ':' || vDataVChar || ':' || vMSG, 1, 1000);
				RETURN FALSE;
              END IF;
            END LOOP;

		pImportResult.status := 'OK';
		pImportResult.message := NULL;
		RETURN TRUE;
	END UploadInvoice;

	FUNCTION UploadInvoice_v2(
		pInvoiceId IN OUT invoices.id%TYPE,
		pInvoiceBatchId IN invoices.ibe_id%TYPE,
		pInvoiceProviderId IN invoices.ipr_id%TYPE,
		pSubscriptionId IN invoice_provider_clients.id%TYPE,
		pUserReference IN invoice_provider_clients.user_reference%TYPE,
		pExternalClientId IN invoice_provider_clients.external_client_id%TYPE,
		pInvoiceNo IN invoices.invoice_no%TYPE,
		pPaymentType IN invoices.payment_type%TYPE,
		pDescription IN invoices.description%TYPE,
		pTranval IN invoices.tranval%TYPE,
		pCurrencyId IN invoices.tranval_currency_id%TYPE,
		pBeneficiaryName IN VARCHAR2,
		pBeneficiaryAccount IN VARCHAR2,
		pMessage IN CLOB,
		pBILLING_PERIOD IN VARCHAR2,
		pBILLING_DATE IN DATE,
		pPAYMENT_DUE_DATE IN DATE,
		pImportResult OUT invoice_import_result
	) RETURN BOOLEAN IS
		myunit CONSTANT VARCHAR2(30) := 'UploadInvoice_v2';
		vApiCallResult PLS_INTEGER;
		vDataVChar VARCHAR2(400);
		vInvoiceProviderClientId invoice_provider_clients.id%TYPE;
		vAccountOwnerID mcore.account_owners.id%TYPE;

	BEGIN
	
		slog.debug(pkgCtxId, myUnit, pInvoiceId || ':' || pInvoiceBatchId || ':' || pInvoiceProviderId || ':' || pSubscriptionId || ':' || pUserReference || ':' || pExternalClientId || ':' || pInvoiceNo);
		pImportResult := invoice_import_result(NULL, NULL, NULL, NULL);
		pImportResult.invoice_no := pInvoiceNo;
		pImportResult.user_reference := pExternalClientId;

		IF pSubscriptionId IS NOT NULL THEN
			BEGIN
			  SELECT id, acc_owner_id
			  INTO vInvoiceProviderClientId, vAccountOwnerID
			  FROM invoice_provider_clients
			  WHERE id = pSubscriptionId
				AND type = common_pck.cINVPRVCLTYPECONSUMER
				AND valid = cValidTrue;

			EXCEPTION
			WHEN no_data_found THEN
			  -- There is no active subscribtion for given subscribtion id
			  -- Possible causes:
				-- User has canceled subscribtion after report was generated
				-- Invalid subscribtion id provided
			  pImportResult.status := 'GRESKA';
			  pImportResult.message := 'Ne postoji aktivna pretplata s ID-om ' || pSubscriptionId;
			  RETURN FALSE;
			END;

		ELSIF pUserReference IS NOT NULL THEN
			-- Retrieve invoice_provider_clients.id based on provided UserReference

			BEGIN
			  SELECT id, acc_owner_id
			  INTO vInvoiceProviderClientId, vAccountOwnerID
			  FROM invoice_provider_clients
			  WHERE ipr_id = pInvoiceProviderId
				AND user_reference = pUserReference
				AND type = common_pck.cINVPRVCLTYPECONSUMER
				AND valid = cValidTrue;

			EXCEPTION
			WHEN no_data_found THEN
			  -- There is no active subscribtion for given provider id and user reference
			  -- Possible causes:
				-- User has canceled subscribtion after report was generated
				-- Provider uses non-existent user reference
			  pImportResult.status := 'GRESKA';
			  pImportResult.message := 'Ne postoji aktivna pretplata s referencom ' || pUserReference;
			  RETURN FALSE;
			END;
		ELSIF pExternalClientId IS NOT NULL THEN
		-- Retrieve invoice_provider_clients.id based on provided ExternalClientId

			BEGIN
				SELECT id, acc_owner_id
				INTO vInvoiceProviderClientId, vAccountOwnerID
				FROM invoice_provider_clients
				WHERE ipr_id = pInvoiceProviderId
				AND external_client_id = pExternalClientId
				AND valid = cValidTrue;

			EXCEPTION
			WHEN no_data_found THEN
				-- There is no active subscribtion for given provider id and user reference
				-- Possible causes:
				-- User has canceled subscribtion after report was generated
				-- Provider uses non-existent user reference
				pImportResult.status := 'GRESKA';
				pImportResult.message := 'Ne postoji aktivna pretplata za korisnika s external registration ID-em' || pExternalClientId;
				RETURN FALSE;
			END;
		ELSE
			pImportResult.status := 'GRESKA';
			pImportResult.message := 'Nije proslije�en ni ID pretplate, ni UserReference ni externi registration ID!';
			RETURN FALSE;
		END IF;

		vApiCallResult := ia$invoices.setInvoices (
			pID => pInvoiceId
			,pINVOICE_NO => pInvoiceNo
			,pPAYMENT_TYPE => pPaymentType
			,pstatus => 0
			,pDESCRIPTION => pDescription
			,pTRANVAL => pTranval
			,pTRANVAL_CURRENCY_ID => pCurrencyId
			,pIPR_ID => pInvoiceProviderId
			,pCLIENT_ID => vInvoiceProviderClientId
			,pIBE_ID => pInvoiceBatchId
			,pTRANPAY_ID => NULL
			,pDATE_OF_PROCESSING => NULL
			,pMESSAGE => pMessage
			,pBILLING_PERIOD => pBILLING_PERIOD
			,pBILLING_DATE => pBILLING_DATE
			,pPAYMENT_DUE_DATE => pPAYMENT_DUE_DATE
			,pACCOUNT_OWNER_ID => vAccountOwnerID
			,pDISPLAY_ON_OVERVIEW => cValidTrue
		    ,pGROUP_ID => NULL
			,pAction => 'WRITE'
			,pAutonomousTransaction => FALSE
			,pDoCommit => FALSE
			,pmsg => vMSG
			,perror => vERROR
			,pmsg_type => vMSG_TYPE
			,pmsgid => vMSGID
			,ploc => vLOC);

		IF vApiCallResult > 0 THEN
			pImportResult.status := 'GRESKA';
			pImportResult.message := SUBSTR('Interna gre�ka: ' || vInvoiceProviderClientId ||':'||pPaymentType || ':' || vMSG, 1, 1000);
			RETURN FALSE;

		END IF;

		FOR i IN (SELECT id FROM tranpay_attribs ta WHERE ta.req_type_id = pPaymentType AND (ta.for_copy = cValidTrue OR ta.id = cExtRefPostingScheme)) LOOP

		CASE i.id
			--details for UPP
			WHEN 'RACUN_PRIMAOCA' THEN vDataVChar := pBeneficiaryAccount;
			WHEN 'REFERENCA_PLACANJA' THEN vDataVChar := NULL;
			WHEN 'HITNO' THEN vDataVChar := 'F';
			WHEN 'IDPOROBV' THEN vDataVChar := NULL;
			WHEN 'JAVNI_PRIHODI' THEN vDataVChar := 'F';
			WHEN 'NAZIV_POSILJAOCA' THEN vDataVChar := NULL;
			WHEN 'NAZIV_PRIMAOCA' THEN vDataVChar := pBeneficiaryName;
			WHEN 'VRSTA_PRIHODA' THEN vDataVChar := NULL;
			WHEN 'VRSTA_UPLATE' THEN vDataVChar := NULL;
			WHEN 'OPCINA' THEN vDataVChar := NULL;
			WHEN 'PORPERIOD_DO' THEN vDataVChar := NULL;
			WHEN 'PORPERIOD_OD' THEN vDataVChar := NULL;
			WHEN 'POZIV_NA_BROJ'THEN vDataVChar := NULL;
			WHEN 'BUDZETSKA_ORGANIZACIJA' THEN vDataVChar := NULL;
			-- details for transfer
			WHEN 'FIKSNO' THEN vDataVChar := 'I';
			WHEN 'RACUN' THEN vDataVChar := pBeneficiaryAccount;
			WHEN 'IZNOS_KUPOVINE' THEN vDataVChar := pTranval;
			WHEN 'ACCOUNT_VALUTA' THEN vDataVChar := pCurrencyId;
			WHEN 'RACUN_VALUTA' THEN vDataVChar := pCurrencyId;
					
			ELSE CONTINUE;
		END CASE;

		vApiCallResult := ia$invoice_details.setInvoiceDetails (
			pINE_ID => pInvoiceId
			,pREQ_TYPE_ID => pPaymentType
			,pATTRIB_ID => i.id
			,pDESCRIPTION => NULL
			,pDATA_VCHAR => vDataVChar
			,pAction => 'WRITE'
			,pAutonomousTransaction => FALSE
			,pDoCommit => FALSE
			,pmsg => vMSG
			,perror => vERROR
			,pmsg_type => vMSG_TYPE
			,pmsgid => vMSGID
			,ploc => vLOC);

		  IF vApiCallResult > 0 THEN
			pImportResult.status := 'GRESKA';
			pImportResult.message := SUBSTR('Interna gre�ka: ' || i.id || ':' || vDataVChar || ':' || vMSG, 1, 1000);
			RETURN FALSE;
		  END IF;
		END LOOP;

		pImportResult.status := 'OK';
		pImportResult.message := NULL;
		RETURN TRUE;
	END UploadInvoice_v2;

	FUNCTION UploadInvoice_v2(
		pInvoiceBatchId IN invoices.ibe_id%TYPE,
		pInvoiceProviderId IN invoices.ipr_id%TYPE,
		pSubscriptionId IN invoice_provider_clients.id%TYPE,
		pUserReference IN invoice_provider_clients.user_reference%TYPE,
		pExternalClientId IN invoice_provider_clients.external_client_id%TYPE,
		pInvoiceNo IN invoices.invoice_no%TYPE,
		pPaymentType IN invoices.payment_type%TYPE,
		pDescription IN invoices.description%TYPE,
		pTranval IN invoices.tranval%TYPE,
		pCurrencyId IN invoices.tranval_currency_id%TYPE,
		pBeneficiaryName IN VARCHAR2,
		pBeneficiaryAccount IN VARCHAR2,
		pMessage IN CLOB,
		pBILLING_PERIOD IN VARCHAR2,
		pBILLING_DATE IN DATE,
		pPAYMENT_DUE_DATE IN DATE,
		pImportResult OUT invoice_import_result
	) RETURN BOOLEAN IS
		vInvoiceId invoices.id%TYPE := NULL;

	BEGIN

		RETURN UploadInvoice_v2(
			pInvoiceId => vInvoiceId,
			pInvoiceBatchId => pInvoiceBatchId,
			pInvoiceProviderId=> pInvoiceProviderId,
			pSubscriptionId=> pSubscriptionId,
			pUserReference=>pUserReference,
			pExternalClientId => pExternalClientId,
			pInvoiceNo => pInvoiceNo,
			pPaymentType => pPaymentType,
			pDescription => pDescription,
			pTranval => pTranval,
			pCurrencyId => pCurrencyId,
			pBeneficiaryName => pBeneficiaryName,
			pBeneficiaryAccount => pBeneficiaryAccount,
			pMessage => pMessage,
			pBILLING_PERIOD => pBILLING_PERIOD,
			pBILLING_DATE => pBILLING_DATE,
			pPAYMENT_DUE_DATE => pPAYMENT_DUE_DATE,
			pImportResult => pImportResult);


	END UploadInvoice_v2;

	FUNCTION UploadInvoices(
		pInvoiceProviderId IN invoice_providers.id%TYPE,
		pEndUserId invoice_provider_clients.end_users_id%TYPE,
		pBatchName IN invoice_batches.name%TYPE,
		pInvoiceList IN invoice_list,
		pInvoiceBatchId OUT invoice_batches.id%TYPE,
		pImportResult OUT invoice_import_result_list)
	RETURN BOOLEAN IS
		myunit CONSTANT VARCHAR2(30) := 'UploadInvoices';
		vImportResult invoice_import_result := invoice_import_result(NULL, NULL, NULL, NULL);
		vPartialResult BOOLEAN := FALSE;
		vResult BOOLEAN := TRUE;

		TYPE tt$beneficiaryAccountList IS TABLE OF VARCHAR2(40) INDEX BY PLS_INTEGER;
		tblBeneficiaryAccountList tt$beneficiaryAccountList;

		vFoundMatchingAccount BOOLEAN := FALSE;

		vRacunPrimaoca VARCHAR2(40);
		vNazivPrimaoca VARCHAR2(400);

		slct$ipr cg$invoice_providers.cg$row_type;

	BEGIN
		slog.debug(pkgCtxId, myUnit, pInvoiceProviderId || ':' || pEndUserId || ':' || pBatchName || ':' || pInvoiceList.COUNT);
		pImportResult := invoice_import_result_list();
		pInvoiceBatchId := CreateBatch(pInvoiceProviderId => pInvoiceProviderId,
			pEndUserId => pEndUserId,
			pName => pBatchName,
			pValid => cValidFalse);
		slog.debug(pkgCtxId, myUnit,  'Created batch with ID ' || pInvoiceBatchId);

		slct$ipr.id := pInvoiceProviderId;
		cg$invoice_providers.slct ( slct$ipr );

		IF slct$ipr.bnficiary_account_restriction IN (cbnficry_acc_rstrct_enlisted, cbnficry_acc_rstrct_defaulted) THEN
			SELECT data_vchar
			BULK COLLECT INTO tblBeneficiaryAccountList
			FROM ipr_details ipdl
			WHERE ipdl.ipr_id = pInvoiceProviderId
			  AND ipdl.iab_id = invoice_mgmt_pck.cIprAttribAccountId
			ORDER BY id ASC;

			-- According MOS Doc ID 268122.1, SELECT..BULK COLLECT doesn't raise no_data_found when no rows are returned
			IF tblBeneficiaryAccountList.COUNT = 0 THEN
				slog.error(pkgCtxId, myunit, cERR_MissingProviderAccounts, pInvoiceProviderId);
				sspkg.raiseError(cERR_MissingProviderAccounts, null, pkgCtxId, myunit);
			END IF;
		END IF;

		FOR i IN pInvoiceList.FIRST..pInvoiceList.LAST LOOP
			slog.debug(pkgCtxId, myUnit, 'Process invoice ' || i || ':' || pInvoiceList(i).invoice_no);

			vRacunPrimaoca := pInvoiceList(i).tranpay_data.racun_primaoca;

			IF slct$ipr.bnficiary_account_restriction = cbnficry_acc_rstrct_enlisted THEN
				vFoundMatchingAccount := FALSE;

				FOR j IN tblBeneficiaryAccountList.FIRST..tblBeneficiaryAccountList.LAST LOOP
					IF vRacunPrimaoca = tblBeneficiaryAccountList(j) THEN
						slog.debug(pkgCtxId, myUnit, 'Account ' || vRacunPrimaoca || ' found on list of allowed beneficiary accounts');
						vFoundMatchingAccount := TRUE;
					END IF;
				END LOOP;

				IF NOT vFoundMatchingAccount THEN
					slog.debug(pkgCtxId, myUnit, 'Account ' || vRacunPrimaoca || ' NOT found on list of allowed beneficiary accounts');
					vResult := FALSE;
					pImportResult.extend;

					vImportResult := invoice_import_result(NULL, NULL, NULL, NULL);
					vImportResult.invoice_no := pInvoiceList(i).invoice_no;
					vImportResult.user_reference := pInvoiceList(i).user_reference;

					slog.error(pkgCtxId, myunit, cERR_MissingProviderAccounts, pInvoiceProviderId || ':' || pInvoiceList(i).user_reference || ':' || vRacunPrimaoca);
					vImportResult.status := 'GRESKA';
					vImportResult.message := 'Pogre�an broj ra�una primaoca ' || vRacunPrimaoca;

					pImportResult(pImportResult.LAST) := vImportResult;

					CONTINUE;
				END IF;
			ELSIF slct$ipr.bnficiary_account_restriction = cbnficry_acc_rstrct_defaulted THEN
				 vRacunPrimaoca := tblBeneficiaryAccountList(tblBeneficiaryAccountList.FIRST);
				 slog.debug(pkgCtxId, myUnit, 'Defaulting beneficiary account number for invoice ' || pInvoiceList(i).invoice_no);
			END IF;

			vNazivPrimaoca := pInvoiceList(i).tranpay_data.naziv_primaoca;
			IF vNazivPrimaoca IS NULL THEN
				vNazivPrimaoca := slct$ipr.name;
			END IF;

			slog.debug(pkgCtxId, myUnit, 'Import invoice');
			vPartialResult := UploadInvoice(pInvoiceBatchId => pInvoiceBatchId,
				pInvoiceProviderId => pInvoiceProviderId,
				pUserReference => pInvoiceList(i).user_reference,
				pInvoiceNo => pInvoiceList(i).invoice_no,
				pPaymentType => pInvoiceList(i).tranpay_data.req_type_id,
				pDescription => pInvoiceList(i).tranpay_data.description,
				pTranval => pInvoiceList(i).tranpay_data.tranval,
				pCurrencyId => pInvoiceList(i).tranpay_data.currency_id,
				pBeneficiaryName => vNazivPrimaoca,
				pBeneficiaryAccount => vRacunPrimaoca,
				pMessage => pInvoiceList(i).message,
				pBILLING_PERIOD => pInvoiceList(i).billing_period,
				pBILLING_DATE => pInvoiceList(i).billing_date,
				pPAYMENT_DUE_DATE => pInvoiceList(i).payment_due_date,
				pImportResult => vImportResult);

			IF NOT vPartialResult THEN
				-- Indicates that a partial upload has failed
				vResult := FALSE;
			END IF;

			pImportResult.extend;
			pImportResult(pImportResult.LAST) := vImportResult;

		END LOOP;

		RETURN vResult;
	END UploadInvoices;

	FUNCTION getUploadedInvoices(
		pInvoiceBatchId IN invoice_batches.id%TYPE,
		pAdminUserId invoice_provider_clients.end_users_id%TYPE)
	RETURN sys_refcursor IS
		myunit CONSTANT VARCHAR2(30) := 'getUploadedInvoices';
		rez sys_refcursor;
	BEGIN
        slog.debug(pkgCtxId, myUnit,  pInvoiceBatchId || ':' || pAdminUserId);
        common_pck.CommonSecurityChecks;

		OPEN rez FOR
		SELECT i.invoice_no, ipc.user_reference
		FROM invoices i
		JOIN invoice_provider_clients ipc ON (ipc.id = i.client_id)
		JOIN invoice_batches ibe ON (i.ibe_id = ibe.id)
		JOIN invoice_providers ipr ON (ipr.id = ibe.ipr_id)
		JOIN invoice_provider_clients ipa ON (ipa.ipr_id = ipr.id)
		WHERE ibe.id = pInvoiceBatchId
		AND ipa.end_users_id = pAdminUserId
		AND ipa.type = 'A'
		AND ipa.valid = cValidTrue
		AND ipr.valid = cValidTrue
		AND ipc.valid = cValidTrue
		AND ibe.valid = cValidTrue;

		RETURN rez;
	END getUploadedInvoices;

	PROCEDURE setInvoiceProviderData(
				pId IN invoice_providers.id%TYPE,
				pDescription IN invoice_providers.description%TYPE,
				pBlobData IN invoice_providers.blob_data%TYPE,
				pMimeType IN invoice_providers.mime_type%TYPE,
				pAgreementText IN invoice_providers.agreement_text%TYPE)
	IS
		myunit CONSTANT VARCHAR2(30) := 'setInvoiceProviderData';

		cg$rec cg$invoice_providers.cg$row_type;
		cg$ind cg$invoice_providers.cg$ind_type;

	BEGIN
		slog.debug(pkgCtxId, myunit, pId);

		IF pId IS NULL THEN
			sspkg.raiseError(common_pck.cERR_MissingParameter, null, pkgCtxId, myunit);
		END IF;

		cg$rec.id := pId;
		cg$rec.description := pDescription;
		cg$rec.blob_data := pBlobData;
		cg$rec.mime_type := pMimeType;
		cg$rec.agreement_text := pAgreementText;


		IF pDescription IS NOT NULL THEN
			cg$ind.description := TRUE;
		END IF;

		IF pBlobData IS NOT NULL AND DBMS_LOB.GETLENGTH(pBlobData) > 0 THEN
			IF pMimeType IS NULL THEN
				slog.error(pkgCtxId, myunit, pId || ': Blob data provided but missing mime type!');
				sspkg.raiseError(common_pck.cERR_MissingParameter, null, pkgCtxId, myunit);
			END IF;

			cg$ind.blob_data := TRUE;
		END IF;

		IF pMimeType IS NOT NULL THEN
			cg$ind.mime_type := TRUE;
		END IF;

		IF pAgreementText IS NOT NULL AND DBMS_LOB.GETLENGTH(pAgreementText) > 0 THEN
			cg$ind.agreement_text := TRUE;
		END IF;

		BEGIN
			cg$invoice_providers.upd(cg$rec, cg$ind, TRUE);
		EXCEPTION
            WHEN cg$errors.cg$error THEN
            -- Retrieve error message to determine error cause !
                v_cg_result := cg$errors.pop(msg => vMSG
                        ,error => vERROR
                        ,msg_type => vMSG_TYPE
                        ,msgid => vMSGID
                        ,loc => vLOC);
                -- If API ERROR with code 100 - no_data_found, then ...
                IF vERROR = 'E' AND vMSG_TYPE = 'ORA' AND vMSGID = 100 THEN
                    -- Clear errors and ...
                    cg$errors.clear;
					sspkg.raiseError(cERR_InvalidInvoiceProvider, null, pkgCtxId, myunit);
				ELSE
					slog.error(pkgCtxId, myunit, vERROR || ':' || vMSG_TYPE || ':' || vMSGID || ':' || vLOC || ':' || vMSG);
					sspkg.raiseError(cERR_InternalError, null, pkgCtxId, myunit);
				END IF;

			-- Process auto payments for the batch after all invoices are loaded
			BEGIN
				ProcessBatchAutoPayments(pBatchId => pBatchId);
			EXCEPTION
				WHEN OTHERS THEN
					slog.error(pkgCtxId, myunit, 'Error processing batch auto payments for batch: ' || pBatchId || ', Error: ' || SQLERRM);
			END;
		END;

	END setInvoiceProviderData;

	PROCEDURE AssignAgreementTextToProvider(pInvoiceProviderID IN invoice_providers.id%TYPE, pUserAgreementText IN user_agreement.agreement_text%TYPE)
	IS
		myunit CONSTANT VARCHAR2(30) := 'AssignAgreementTextToProvider';
		vUatID user_agreement.id%TYPE := NULL;
		cg$invoice_providers_rec cg$invoice_providers.cg$row_type;
		cg$invoice_providers_ind cg$invoice_providers.cg$ind_type;
	BEGIN
		slog.debug(pkgCtxId, myunit, pInvoiceProviderID);

		BEGIN
			SELECT uat_id
			INTO vUatID
			FROM invoice_providers
			WHERE id = pInvoiceProviderID;

		EXCEPTION
			WHEN no_data_found THEN
				slog.error(pkgCtxId, myunit, cERR_InvalidInvoiceProvider, pInvoiceProviderId);
				sspkg.raiseError(cERR_InvalidInvoiceProvider, null, pkgCtxId, myunit);
		END;

		IF (vUatID IS NULL) THEN
			vUatID := user_agreement_pck.CreateUserAgreement(
				pAgreementType => cUserAgreementTextTypeId,
				pAgreementText => pUserAgreementText);
		ELSE
			vUatID := user_agreement_pck.UpdateUserAgreementText(
				pOldUserAgreementId => vUatID,
				pAgreementText => pUserAgreementText);
		END IF;

		cg$invoice_providers_rec.id     := pInvoiceProviderID;
		cg$invoice_providers_rec.uat_id := vUatID;
		cg$invoice_providers_ind.uat_id := TRUE;

		cg$invoice_providers.upd(
			cg$rec => cg$invoice_providers_rec,
			cg$ind => cg$invoice_providers_ind,
			do_upd => TRUE,
			cg$pk => NULL);

	END AssignAgreementTextToProvider;

	FUNCTION getActiveUserAgreement(pInvoiceProviderId IN invoice_providers.id%TYPE)
	RETURN SYS_REFCURSOR IS
        myunit CONSTANT VARCHAR2(30) := 'getActiveUserAgreement';
        rez SYS_REFCURSOR;

		vUatID invoice_providers.uat_id%TYPE;					-- Aktuelni ID uslova kori�tenja
	BEGIN
        slog.debug(pkgCtxId, myUnit, pInvoiceProviderId);

		BEGIN
				SELECT uat_id
				INTO vUatID
				FROM invoice_providers
				WHERE id = pInvoiceProviderId
				  AND valid = cValidTrue;

		EXCEPTION
			WHEN no_data_found THEN
				slog.error(pkgCtxId, myunit, cERR_InvalidInvoiceProvider, pInvoiceProviderId);
				sspkg.raiseError(cERR_InvalidInvoiceProvider, null, pkgCtxId, myunit);
		END;

        OPEN rez FOR
			SELECT uat.id, uat.agreement_text, uat.valid_from
			  FROM user_agreement uat
			  WHERE uat.id = vUatID
			    AND uat.agreement_type = cUserAgreementTextTypeId
			    AND uat.valid = cValidTrue;
		RETURN rez;

	EXCEPTION
		WHEN no_data_found THEN
			slog.error(pkgCtxId, myunit, cERR_MissingUserAgreement, pInvoiceProviderId);
			sspkg.raiseError(cERR_MissingUserAgreement, null, pkgCtxId, myunit);
	END getActiveUserAgreement;

	FUNCTION getUserAgreement(pInvoiceProviderId IN invoice_providers.id%TYPE)
	RETURN SYS_REFCURSOR IS
        myunit CONSTANT VARCHAR2(30) := 'getUserAgreement';
        rez SYS_REFCURSOR;

		vUatID invoice_providers.uat_id%TYPE;					-- Aktuelni ID uslova kori�tenja
	BEGIN
        slog.debug(pkgCtxId, myUnit, pInvoiceProviderId);

		BEGIN
				SELECT uat_id
				INTO vUatID
				FROM invoice_providers
				WHERE id = pInvoiceProviderId;

		EXCEPTION
			WHEN no_data_found THEN
				slog.error(pkgCtxId, myunit, cERR_InvalidInvoiceProvider, pInvoiceProviderId);
				sspkg.raiseError(cERR_InvalidInvoiceProvider, null, pkgCtxId, myunit);
		END;

        OPEN rez FOR
			SELECT uat.id, uat.agreement_text
			  FROM user_agreement uat
			  WHERE uat.id = vUatID
			    AND uat.agreement_type = cUserAgreementTextTypeId;
		RETURN rez;

	EXCEPTION
		WHEN no_data_found THEN
			slog.error(pkgCtxId, myunit, cERR_MissingUserAgreement, pInvoiceProviderId);
			sspkg.raiseError(cERR_MissingUserAgreement, null, pkgCtxId, myunit);
	END getUserAgreement;

	FUNCTION getActiveUserAgreement(pInvoiceProviderName IN invoice_providers.name%TYPE)
    RETURN SYS_REFCURSOR IS
        myunit CONSTANT VARCHAR2(30) := 'getActiveUserAgreement1';
        rez SYS_REFCURSOR;

        vUatID invoice_providers.uat_id%TYPE;					-- Aktuelni ID uslova kori�tenja
    BEGIN
        slog.debug(pkgCtxId, myUnit, pInvoiceProviderName);
		
        BEGIN
                SELECT uat_id
                INTO vUatID
                FROM invoice_providers
                WHERE name = pInvoiceProviderName
                  AND valid = cValidTrue;

        EXCEPTION
            WHEN no_data_found THEN
                slog.error(pkgCtxId, myunit, cERR_InvalidInvoiceProvider, pInvoiceProviderName);
                sspkg.raiseError(cERR_InvalidInvoiceProvider, null, pkgCtxId, myunit);
        END;

        OPEN rez FOR
            SELECT uat.id, uat.agreement_text, uat.valid_from
              FROM user_agreement uat
              WHERE uat.id = vUatID
                AND uat.agreement_type = cUserAgreementTextTypeId
                AND uat.valid = cValidTrue;
        RETURN rez;

    EXCEPTION
        WHEN no_data_found THEN
            slog.error(pkgCtxId, myunit, cERR_MissingUserAgreement, pInvoiceProviderName);
            sspkg.raiseError(cERR_MissingUserAgreement, null, pkgCtxId, myunit);
    END getActiveUserAgreement;

	FUNCTION CreateTranpayForInvoice(
		pInvoiceId IN invoices.id%TYPE,
		pInvoiceProviderName IN invoice_providers.name%TYPE,
		pInvoiceNo IN invoices.group_id%TYPE,
		pSenderBankAccountId IN tranpays.account_id%TYPE,
		pSenderName IN VARCHAR2,
		pTranval IN tranpays.tranval%TYPE,
		pTranpayDescription IN tranpays.description%TYPE,
		pReference IN VARCHAR2,
		pResponse VARCHAR2 := NULL,
		pOtp VARCHAR2 := NULL,
        pSourceData VARCHAR2 := NULL,
        pSignature VARCHAR2 := NULL)
	RETURN tranpays.id%TYPE IS

		myunit CONSTANT VARCHAR2(30) := 'CreateTranpayForInvoice';

		vTranpayId tranpays.id%TYPE;
		vAccountCurrencyId bank_accounts.currency_id%TYPE;
		vRacunAlias bank_accounts.alias%TYPE;

		CURSOR cTranpayTemplate(pInvoiceId IN invoices.id%TYPE) IS
		select i.id, i.invoice_no, i.payment_type, i.description, i.tranval, i.tranval_currency_id
		from mcore.invoices i
		where i.id = pInvoiceId;

		CURSOR cTranpayTemplateDetails(pInvoiceId IN invoices.id%TYPE) IS
		select id.attrib_id, id.data_vchar
		from invoice_details id JOIN tranpay_attribs ta ON (id.req_type_id = ta.req_type_id AND id.attrib_id = ta.id)
		where id.ine_id = pInvoiceId
		  and (ta.for_copy = cValidTrue OR ta.id = cExtRefPostingScheme);	-- Kako bi se osiguralo da atributi koji nisu za kopiranja (npr. datum slanja notifikacije) ne budu kopirani u nalog

		vChallenge CONSTANT VARCHAR2(40) := NULL;
		vTranpaySigned# PLS_INTEGER := 0;

		vInvoiceId invoices.id%TYPE;
		vTranpayReqTypeId request_types.id%TYPE;
		vTranvalInDomCurrency NUMBER;

		vAccountAlias bank_accounts.alias%TYPE;
	BEGIN
		slog.debug(pkgCtxId, myUnit, 'Fetch debit account data');

		vAccountCurrencyId := accounts_pck.getCurrency(pAccountId => pSenderBankAccountId);
		slog.debug(pkgCtxId, myUnit, pSenderBankAccountId || ':' || vAccountCurrencyId);

		IF pInvoiceId IS NOT NULL THEN
			vInvoiceId := pInvoiceId;
		ELSE
			<<retrieve_invoice_id>>
			BEGIN
				SELECT i.id INTO vInvoiceId
				FROM mcore.invoices i JOIN mcore.invoice_providers ipr ON (i.ipr_id = ipr.id)
				WHERE ipr.name = pInvoiceProviderName
					AND i.invoice_no = pInvoiceNo;
			EXCEPTION
				WHEN too_many_rows THEN
					slog.error(pkgCtxId, myunit, cERR_InternalError, pInvoiceProviderName || ':' || pInvoiceNo);
					sspkg.raiseError(cERR_InternalError, null, pkgCtxId, myunit);
			END retrieve_invoice_id;
		END IF;
		slog.debug(pkgCtxId, myUnit, 'Use invoice with ID ' || vInvoiceId);

		<<InvoiceHeaderLoop>>
		FOR rec_header IN cTranpayTemplate(vInvoiceId) LOOP
			slog.debug(pkgCtxId, myUnit, 'Create new tranpay for invoice ...');

			<<BalanceCheck>>
			BEGIN
				tranpays_pck.balanceCheck(
					pTranpayId => NULL, 	-- Not mandatory
					pAccountId => pSenderBankAccountId,
					pRequestTypeId => rec_header.payment_type,
					pTranval => pTranval,
					pTranvalCurrency => vAccountCurrencyId);
			END BalanceCheck;

			<<GetTranpayReqTypeId>>
			BEGIN
				slog.debug(pkgCtxId, myUnit, 'Retrieve parent req type id for ' || rec_header.payment_type);
				vTranpayReqTypeId := NVL(request_types_pck.getParentRequestType(pReqTypeId => rec_header.payment_type), rec_header.payment_type);

				slog.debug(pkgCtxId, myUnit, 'Use ' || vTranpayReqTypeId);



			EXCEPTION
				WHEN no_data_found THEN
					slog.error(pkgCtxId, myUnit, 'Internal error handled by failover procedure - request type ' || rec_header.payment_type || ' not found in Request Types codebook!');
					vTranpayReqTypeId := rec_header.payment_type;
			END GetTranpayReqTypeId;

			vTranpayId := tranpays_pck.CreateNewTranPay(pRequestTypeId => vTranpayReqTypeId,
				pDescription => pTranpayDescription,
				pInternalDescription => NULL,
				pTranval => pTranval,
				pTranvalCurrencyId => rec_header.tranval_currency_id,
				pTranpayGroupId => NULL,
				pAccountId => pSenderBankAccountId,
				pChartOfAccountsId => NULL,
				pScheduleId => NULL,
				pTestRecord => cValidFalse,
				pExtRef => NVL(pReference, cValidTrue));

			-- 2. Populate required details
			slog.debug(pkgCtxId, myUnit, 'Populate required details for tranpay :' || vTranpayId);
			<<InvoiceDetailsLoop>>
			FOR rec_details IN cTranpayTemplateDetails(rec_header.id) LOOP
				slog.debug(pkgCtxId, myUnit, 'Insert detail ' || rec_details.attrib_id || ':' || rec_details.data_vchar);
				tranpays_pck.AppendTranPayDetail(pTranpayId => vTranpayId, pRequestTypeId => vTranpayReqTypeId, pAttribId => rec_details.attrib_id, pDescription => NULL, pDataVCHAR => rec_details.data_vchar, pDataBLOB => NULL);
			END LOOP InvoiceDetailsLoop;

			BEGIN
				SELECT ba_head.alias
				INTO vAccountAlias
				FROM bank_accounts ba_det JOIN bank_accounts ba_head ON (ba_head.id = ba_det.ph4)
				WHERE ba_det.id = pSenderBankAccountId;
			EXCEPTION
				WHEN no_data_found THEN
					slog.error(pkgCtxId, myunit, cERR_InternalError, pSenderBankAccountId);
					sspkg.raiseError(cERR_InternalError, null, pkgCtxId, myunit);
			END;

			IF tranpays_pck.isValidTranpayAttribute(vTranpayReqTypeId, common_pck.cTRANPAY_ATT_RAC_POSILJAOCA_AL) THEN
			tranpays_pck.AppendTranPayDetail(
				pTranpayId => vTranpayId,
				pRequestTypeId => vTranpayReqTypeId,
				pAttribId => common_pck.cTRANPAY_ATT_RAC_POSILJAOCA_AL,
				pDescription => NULL,
				pDataVCHAR => vAccountAlias,
				pDataBLOB => NULL);
			END IF;

			IF tranpays_pck.isValidTranpayAttribute(vTranpayReqTypeId, common_pck.cTRANPAY_ATT_NAZIV_POSILJAOCA) THEN
			slog.debug(pkgCtxId, myUnit, 'Insert detail ' || 'NAZIV_POSILJAOCA' || ':' || pSenderName);
			tranpays_pck.AppendTranPayDetail(
				pTranpayId => vTranpayId,
				pRequestTypeId => vTranpayReqTypeId,
				pAttribId => common_pck.cTRANPAY_ATT_NAZIV_POSILJAOCA,
				pDescription => NULL,
				pDataVCHAR => pSenderName,
				pDataBLOB => NULL);
			END IF;
			
			IF tranpays_pck.isValidTranpayAttribute(vTranpayReqTypeId, common_pck.cTRANPAY_ATT_ACCOUNT_ALIAS) THEN
				slog.debug(pkgCtxId, myUnit, 'Insert detail ' || 'ACCOUNT_ALIAS' || ':' || vAccountAlias);
				tranpays_pck.AppendTranPayDetail(
					pTranpayId => vTranpayId,
					pRequestTypeId => vTranpayReqTypeId,
					pAttribId => common_pck.cTRANPAY_ATT_ACCOUNT_ALIAS,
					pDescription => NULL,
					pDataVCHAR => vAccountAlias,
					pDataBLOB => NULL);
			END IF;
			
			
			IF tranpays_pck.isValidTranpayAttribute(vTranpayReqTypeId, common_pck.cTRANPAY_ATT_RACUN_ALIAS) THEN
				BEGIN
					SELECT ba_head.alias	
					INTO vRacunAlias
					FROM invoice_details i
                    JOIN bank_accounts ba on (ba.id = i.data_vchar and i.attrib_id = common_pck.cTRANPAY_ATT_RACUN)
                    JOIN bank_accounts ba_head on (ba_head.id = ba.ph4)
					WHERE i.ine_id = pInvoiceId;
				EXCEPTION
					WHEN no_data_found THEN
					slog.error(pkgCtxId, myunit, cERR_InternalError, pInvoiceId);
					sspkg.raiseError(cERR_InternalError, null, pkgCtxId, myunit);
					WHEN OTHERS THEN
					slog.error(pkgCtxId, myUnit, pInvoiceId ||':'|| sqlcode||':'||sqlerrm);
					sspkg.raiseError(cERR_InternalError, null, pkgCtxId, myunit);
				END;
			
				slog.debug(pkgCtxId, myUnit, 'Insert detail ' || 'RACUN_ALIAS' || ':' || vRacunAlias);
				tranpays_pck.AppendTranPayDetail(
					pTranpayId => vTranpayId,
					pRequestTypeId => vTranpayReqTypeId,
					pAttribId => common_pck.cTRANPAY_ATT_RACUN_ALIAS,
					pDescription => NULL,
					pDataVCHAR => vRacunAlias,
					pDataBLOB => NULL);
			END IF;
			
			IF tranpays_pck.isValidTranpayAttribute(vTranpayReqTypeId, common_pck.cTRANPAY_ATT_RACUN_POKRICA) THEN
				slog.debug(pkgCtxId, myUnit, 'Insert detail ' || 'RACUN_POKRICA' || ':' || pSenderBankAccountId);
				tranpays_pck.AppendTranPayDetail(
					pTranpayId => vTranpayId,
					pRequestTypeId => vTranpayReqTypeId,
					pAttribId => common_pck.cTRANPAY_ATT_RACUN_POKRICA,
					pDescription => NULL,
					pDataVCHAR => pSenderBankAccountId,
					pDataBLOB => NULL);
			END IF;
			
			IF tranpays_pck.isValidTranpayAttribute(vTranpayReqTypeId, common_pck.cTRANPAY_ATT_RAC_POKRICA_AL) THEN
				slog.debug(pkgCtxId, myUnit, 'Insert detail ' || 'RACUN_POKRICA_ALIAS' || ':' || vAccountAlias);
				tranpays_pck.AppendTranPayDetail(
					pTranpayId => vTranpayId,
					pRequestTypeId => vTranpayReqTypeId,
					pAttribId => common_pck.cTRANPAY_ATT_RAC_POKRICA_AL,
					pDescription => NULL,
					pDataVCHAR => vAccountAlias,
					pDataBLOB => NULL);
			END IF;
			
			
			IF tranpays_pck.isValidTranpayAttribute(vTranpayReqTypeId, common_pck.cTRANPAY_ATT_VALUTA_POKRICA_AL) THEN
				slog.debug(pkgCtxId, myUnit, 'Insert detail ' || 'VALUTA_POKRICA' || ':' || vAccountCurrencyId);
				tranpays_pck.AppendTranPayDetail(
					pTranpayId => vTranpayId,
					pRequestTypeId => vTranpayReqTypeId,
					pAttribId => common_pck.cTRANPAY_ATT_VALUTA_POKRICA_AL,
					pDescription => NULL,
					pDataVCHAR => vAccountCurrencyId,
					pDataBLOB => NULL);
			END IF;

			-- Parent request type is getting registered by scheduler as the tranpay is executed
			-- Register "helper" request type only if it is realy helper
			IF rec_header.payment_type <> vTranpayReqTypeId THEN
				slog.debug(pkgCtxId, myUnit, 'Convert tranval in domestic currency');
				vTranvalInDomCurrency := conversion_pck.ConvertInDomCurrency(pTranval, vAccountCurrencyId);

				slog.debug(pkgCtxId, myUnit, 'Register tranpay in daily tranpay (limit) log ...');

				daily_tranpays_pck.dta$registerTranpay(pAccountId => pSenderBankAccountId,
					pUserId => mcauth.auth.getSCID,
					pRequestTypeId => rec_header.payment_type,
					pTranpayId => vTranpayId,
					pTranval => vTranvalInDomCurrency,
					pApplicationId => mcauth.auth.getSapp);

			END IF;

		END LOOP InvoiceHeaderLoop;

		slog.debug(pkgCtxId, myUnit, 'Mark tranpay ' || vTranpayId || ' as done');
		tranpays_pck.MarkAsDone(pTranpayId => vTranpayId);

		slog.debug(pkgCtxId, myUnit, 'Mark tranpay ' || vTranpayId || ' for sign');
		tranpays_pck.MarkForSigning(
			pTranpayId => vTranpayId,
			pCheckRequestType => FALSE,
			pAppIdentifier => mcauth.auth.getSApp);

		slog.debug(pkgCtxId, myunit, 'Sign tranpay ...');
		vTranpaySigned# := tranpays_pck.SignTranpay( pChallenge => vChallenge,
			pResponse => pResponse,
			pOtp => pOtp,
			pSourceData => pSourceData,
			pSignature => pSignature);

		slog.debug(pkgCtxId, myunit, 'Sign tranpay ... finish');

		slog.debug(pkgCtxId, myUnit, 'Return ID ' || vTranpayId);
		RETURN vTranpayId;
	END CreateTranpayForInvoice;

	PROCEDURE SendSMSNotification
	IS
		cAttribId CONSTANT VARCHAR2(20) := 'NOTIFICATION_SENT_AT';

		CURSOR cRacuni IS
			SELECT ipr.id Provider_ID, ipr.name Provider_Name, i.id ine_id, i.ibe_id, i.payment_type, ipc.id ipc_id, c.ph1 as jmbg, c.id client_id, c.name Client_Name, c.email, c.gsm, NVL(i.account_owner_id, ipc.acc_owner_id) account_owner_id
			FROM mcore.invoices i
			JOIN mcore.invoice_providers ipr ON (ipr.id = i.ipr_id)
			JOIN mcore.invoice_provider_clients ipc ON (ipc.id = i.client_id)
			JOIN mcauth.client c ON (c.id = ipc.end_users_id)
			WHERE ipr.name NOT LIKE 'INTERNAL%'
			AND i.status = 0
			AND ipr.valid = cValidTrue
			AND ipc.valid = cValidTrue
			AND c.enabled = cValidTrue
			AND NOT EXISTS (SELECT NULL FROM mcore.invoice_details id WHERE id.ine_id = i.id AND id.attrib_id = cAttribId)
            		ORDER BY client_id ASC;

		vMessage VARCHAR2(400);
		myunit CONSTANT VARCHAR2(30) := 'SendSMSNotification';
		vApiCallResult PLS_INTEGER;

		vLastClientId end_users.id%TYPE;

		vSMSNotificationChannelId VARCHAR2(100);
		vSMSNotificationFromAddr VARCHAR2(100);
		vSMSNotificationToAddr VARCHAR2(100);

		vErrorNotificationFromAddr  VARCHAR2(100);
		vErrorNotificationToAddr  VARCHAR2(100);
		vErrorNotificationChannelId  VARCHAR2(100);
		vClientGSM mcauth.client.gsm%TYPE;
		vGSMNotRequired BOOLEAN;

	BEGIN
		IF sspkg.ReadBool(pkgCtxId || '/sendSMSNotification') THEN
			slog.debug(pkgCtxId, myUnit, 'SMS Notification enabled');
			vLastClientId := NULL;

			vSMSNotificationChannelId := sspkg.readvchar('/Core/Main/InvoiceManagement/MsgChannelId');
            		vSMSNotificationFromAddr := sspkg.readvchar('/Core/Main/InvoiceManagement/FromAddr');
            		vSMSNotificationToAddr := sspkg.readvchar('/Core/Main/InvoiceManagement/ToAddr');

           		vErrorNotificationFromAddr  := sspkg.readvchar('/Core/Main/InvoiceManagement/ErrorNotificationFromAddr');
            		vErrorNotificationToAddr  := sspkg.readvchar('/Core/Main/InvoiceManagement/ErrorNotificationToAddr');
            		vErrorNotificationChannelId   := sspkg.readvchar('/Core/Main/InvoiceManagement/ErrorNotificationMsgChannelId');
			vGSMNotRequired := sspkg.readBool('/Messaging/Channel/' || vSMSNotificationChannelId || '/ClientGSMNotRequired');

			FOR i IN cRacuni LOOP

				BEGIN
				slog.debug(pkgCtxId, myUnit, 'Send SMS Notification for ' || i.Provider_ID || ':' || i.Provider_Name || ':' || i.client_id || ':' || i.Client_Name || ':' || i.gsm || ':' || i.jmbg || ':' || i.account_owner_id);

				vMessage := SUBSTR(mlang.trans('bs', pkgCtxId || '/SMSNotificationText', i.Provider_Name), 1, 400);
				slog.debug(pkgCtxId, myUnit, 'Message text :' || vMessage);

				IF vLastClientId IS NULL OR vLastClientId <> i.client_id THEN
		
					DECLARE
						vSndNtfMsgRslt integer;
						vSubject VARCHAR2(80);
					BEGIN
						IF vSMSNotificationChannelId = common_pck.cISPSMSChannel THEN
							vSubject := i.account_owner_id;
							vSMSNotificationToAddr := i.jmbg;
						ELSE
							
                                
                               				BEGIN
                                    				vClientGSM := mcauth.auth.getGsmForCID(i.client_id);
                                			EXCEPTION
                                   				WHEN OTHERS THEN
                                        				slog.error(pkgCtxId, myUnit, 'Error retrieving client GSM number! Error : ' || sqlerrm);
                                			END;
                                			slog.debug(pkgCtxId, myUnit, 'ClientGSM:'|| vClientGSM);

                                			IF NOT vGSMNotRequired AND vClientGSM IS NULL THEN
                                    				slog.warn(pkgCtxId, myUnit, 'Client "'||i.client_id||'" has no gsm phone configured.');
                                			END IF;

							vSubject := vClientGSM;
                            			END IF;
                
                        	IF vGSMNotRequired OR ((NOT vGSMNotRequired) AND vClientGSM IS NOT NULL) THEN
							
							   IF vSMSNotificationToAddr IS NOT NULL AND vSubject IS NOT NULL THEN
									vSndNtfMsgRslt := msging.send(fromAddr=>vSMSNotificationFromAddr, toAddr=>vSMSNotificationToAddr, subject=> vSubject, bodyMsg=> vMessage, MC_ID=>vSMSNotificationChannelId, sendAfter => SYSDATE, toExtUser=> i.client_id);
										slog.debug(pkgCtxId, myUnit, 'SMS Notification sent');
								END IF;
				END IF;
                        
			   END;

			   END IF;

			

			vApiCallResult := ia$invoice_details.setInvoiceDetails (
					pINE_ID => i.ine_id
					,pREQ_TYPE_ID => i.payment_type
					,pATTRIB_ID => cAttribId
					,pDESCRIPTION => NULL
					,pDATA_VCHAR => TO_CHAR(SYSDATE, common_pck.cDATETIME_MASK)
					,pAction => 'WRITE'
					,pAutonomousTransaction => FALSE
					,pDoCommit => FALSE
					,pmsg => vMSG
					,perror => vERROR
					,pmsg_type => vMSG_TYPE
					,pmsgid => vMSGID
					,ploc => vLOC);

				IF vApiCallResult > 0 THEN
					slog.error(pkgCtxId, myunit, cERR_InternalError, 'SMSNotification ' || i.Provider_ID|| ':' || i.Provider_Name|| ':' || i.ine_id|| ':' || i.ibe_id|| ':' || i.payment_type|| ':' || i.ipc_id|| ':' || i.client_id|| ':' || i.Client_Name|| ':' || i.email|| ':' || i.gsm);
					slog.error(pkgCtxId, myunit, cERR_InternalError, 'SMSNotification ' || vMSG || ':' || vERROR || ':' || vMSG_TYPE ||  ':' || vMSGID || ':' || vLOC);
					BEGIN
						mcore.send_mail(
							fromAddr => vErrorNotificationFromAddr,
							toAddr => vErrorNotificationToAddr,
							subject => 'Gre�ka prilikom a�uriranja datuma slanja SMS notifikacije u okviru usluge za placanje racuna kroz el./mob bankarstvo',
							bodyMsg => i.Provider_ID|| ':' || i.Provider_Name|| ':' || i.ine_id|| ':' || i.ibe_id|| ':' || i.payment_type|| ':' || i.ipc_id|| ':' || i.client_id|| ':' || i.Client_Name|| ':' || i.email|| ':' || i.gsm || common_pck.cNewLineTerminator
								|| vMSG || ':' || vERROR || ':' || vMSG_TYPE ||  ':' || vMSGID || ':' || vLOC,
							MC_ID => vErrorNotificationChannelId);
						slog.debug(pkgCtxId, myUnit, 'Error notification sent');

					EXCEPTION
						WHEN OTHERS THEN
							slog.error(pkgCtxId, myUnit, 'Unable to send email notification for failed action!');
							slog.error(pkgCtxId, myUnit, SQLERRM);
					END;
				END IF;

			EXCEPTION
				WHEN OTHERS THEN
					slog.error(pkgCtxId, myunit, cERR_InternalError, 'SMSNotification ' || i.Provider_ID|| ':' || i.Provider_Name|| ':' || i.ine_id|| ':' || i.ibe_id|| ':' || i.payment_type|| ':' || i.ipc_id|| ':' || i.client_id|| ':' || i.Client_Name|| ':' || i.email|| ':' || i.gsm || ':' || SQLERRM);
					slog.error(pkgCtxId, myunit, cERR_InternalError, 'SMSNotification ' || SQLERRM);

					BEGIN
						mcore.send_mail(
							fromAddr => vErrorNotificationFromAddr,
							toAddr => vErrorNotificationToAddr,
							subject => 'Gre�ka prilikom slanja SMS notifikacije u okviru usluge za placanje racuna kroz el./mob bankarstvo',
							bodyMsg => i.Provider_ID|| ':' || i.Provider_Name|| ':' || i.ine_id|| ':' || i.ibe_id|| ':' || i.payment_type|| ':' || i.ipc_id|| ':' || i.client_id|| ':' || i.Client_Name|| ':' || i.email|| ':' || i.gsm || common_pck.cNewLineTerminator
								|| SQLERRM,
							MC_ID => vErrorNotificationChannelId);

							slog.debug(pkgCtxId, myUnit, 'Error notification sent');
					EXCEPTION
						WHEN OTHERS THEN
							slog.error(pkgCtxId, myUnit, 'Unable to send email notification for failed action!');
							slog.error(pkgCtxId, myUnit, SQLERRM);
					END;
			END;
			vLastClientId := i.client_id;
			END LOOP;
		ELSE
			slog.error(pkgCtxId, myUnit, 'SMS notification for invoices disabled!');
		END IF;
	END SendSMSNotification;
	
	PROCEDURE MarkInterfaceRecordAsProcessed (
		pUpdRec$iil IN OUT cg$invoice_import_iface_tbl.cg$row_type,
		pInvoiceId IN invoices.id%TYPE,
		pImportResult IN OUT invoice_import_result)
	IS
		upd_ind$iil cg$invoice_import_iface_tbl.cg$ind_type;
	BEGIN
		upd_ind$iil.date_processed := TRUE;
		pUpdRec$iil.date_processed := SYSDATE;

		upd_ind$iil.status := TRUE;
		pUpdRec$iil.status := 1;	-- processed
		
		upd_ind$iil.ine_id := TRUE;
		pUpdRec$iil.ine_id := pInvoiceId;
		
		upd_ind$iil.error_message := TRUE;
		pUpdRec$iil.error_message := NULL;

		cg$invoice_import_iface_tbl.upd ( pUpdRec$iil, upd_ind$iil, TRUE );

		pImportResult.status := 'OK';
		pImportResult.message := NULL;

	END;
	
	PROCEDURE MarkInterfaceRecordAsRejected (
		pUpdRec$iil IN OUT cg$invoice_import_iface_tbl.cg$row_type,
		pErrorMessage IN VARCHAR2,
		pImportResult IN OUT invoice_import_result)
	IS
		upd_ind$iil cg$invoice_import_iface_tbl.cg$ind_type;
	BEGIN
		upd_ind$iil.date_processed := TRUE;
		pUpdRec$iil.date_processed := SYSDATE;

		upd_ind$iil.status := TRUE;
		pUpdRec$iil.status := 2;	-- gre�ka
		
		upd_ind$iil.error_message := TRUE;
		pUpdRec$iil.error_message := pErrorMessage;

		cg$invoice_import_iface_tbl.upd ( pUpdRec$iil, upd_ind$iil, TRUE );

		pImportResult.status := 'GRESKA';
		pImportResult.message := pErrorMessage;

	END;

	FUNCTION ProcessInvoiceFromInterface (pId invoice_import_iface_tbl.id%TYPE)
	RETURN invoice_import_result
	IS
		slct_rec$iil cg$invoice_import_iface_tbl.cg$row_type;
		upd_rec$iil cg$invoice_import_iface_tbl.cg$row_type;

		slct$ipr cg$invoice_providers.cg$row_type;

		vUploadInvoiceStatus BOOLEAN;
		vInvoiceId invoices.id%TYPE;	-- generated by internal procedures
		vImportResult invoice_import_result := invoice_import_result (null, null, null, null);

		vFoundMatchingAccount BOOLEAN := FALSE;

		TYPE tt$beneficiaryAccountList IS TABLE OF VARCHAR2(40) INDEX BY PLS_INTEGER;
		tblBeneficiaryAccountList tt$beneficiaryAccountList;

		vTranval NUMBER;
		vBillingDate DATE;
		vPaymentDueDate DATE;

		vSubscriptionId invoice_provider_clients.id%TYPE;
		vNumberOfInvoices PLS_INTEGER := 0;

		vDescription invoices.description%TYPE;

		myunit CONSTANT VARCHAR2(30) := 'ProcessInvoiceFromInterface';
	BEGIN
		slog.debug(pkgCtxId, myUnit, pId);

		IF pId IS NULL THEN
			slog.error(pkgCtxId, myUnit, 'Invalid iface ID provided NULL');
			vImportResult.status := 'GRESKA';
			vImportResult.message := 'Interna greska: nije proslije�en ID iz interface-a';

			RETURN vImportResult;
		END IF;

		slct_rec$iil.id := pId;

		BEGIN
			cg$invoice_import_iface_tbl.slct ( slct_rec$iil );
		EXCEPTION
			WHEN NO_DATA_FOUND THEN
				slog.error(pkgCtxId, myUnit, 'Invalid iface ID provided ' || pId);
				vImportResult.status := 'GRESKA';
				vImportResult.message := 'Interna gre�ka: ne va�e�i ID iz interface-a proslije�en ' || pId;

				RETURN vImportResult;
		END;

		vImportResult.invoice_no := slct_rec$iil.invoice_no;
		vImportResult.user_reference := slct_rec$iil.user_reference;

		IF slct_rec$iil.status = 1 THEN
			slog.debug(pkgCtxId, myUnit, pId || ': Invoice already imported!');
			vImportResult.status := 'OK';
			vImportResult.message := NULL;

			RETURN vImportResult;
		END IF;

		BEGIN
			-- Amount mora biti s tackom!
			-- Ako izdavatelj �alje vrijednost gdje se koristi zarez kao decimalni (zarez) onda se to mora rije�iti prije upisa sloga u interface-nu tabelu!
			vTranval := to_number(slct_rec$iil.amount,'999999999999999D00','NLS_NUMERIC_CHARACTERS=.,');
			slog.debug(pkgCtxId, myUnit, pId || ': Tranval ' || common_pck.formatMoney(vTranval));
		EXCEPTION
			WHEN OTHERS THEN
				slog.error(pkgCtxId, myUnit, pId || ':' || slct_rec$iil.amount || ': Unable to convert amount! ' || sqlerrm);
				MarkInterfaceRecordAsRejected ( pUpdRec$iil => upd_rec$iil, pErrorMessage => 'Vrijednost koja predstavlja iznos nije va�e�a!', pImportResult => vImportResult);
				RETURN vImportResult;
		END;

		upd_rec$iil := slct_rec$iil;

		IF vTranval <= 0 THEN
			MarkInterfaceRecordAsRejected ( pUpdRec$iil => upd_rec$iil, pErrorMessage => 'Ra�un presko�en - iznos <= 0', pImportResult => vImportResult);
			RETURN vImportResult;
		END IF;

		BEGIN
			vBillingDate := TO_DATE(slct_rec$iil.billing_date,'YYYY-MM-DD');
			slog.debug(pkgCtxId, myUnit, pId || ': BillingDate ' || slct_rec$iil.billing_date ||':' || common_pck.formatDate(vBillingDate) );
		EXCEPTION
			WHEN OTHERS THEN
				slog.error(pkgCtxId, myUnit, pId || ':' || slct_rec$iil.billing_date || ': Unable to convert billing_date! ' || sqlerrm);
				MarkInterfaceRecordAsRejected ( pUpdRec$iil => upd_rec$iil, pErrorMessage => 'Vrijednost koja predstavlja datum izdavanja ra�una nije ispravna', pImportResult => vImportResult);
				RETURN vImportResult;
		END;

		BEGIN
			vPaymentDueDate := TO_DATE(slct_rec$iil.payment_due_date,'YYYY-MM-DD');
			slog.debug(pkgCtxId, myUnit, pId || ': BillingDate ' || slct_rec$iil.payment_due_date || ':' || common_pck.formatDate(vPaymentDueDate) );
		EXCEPTION
			WHEN OTHERS THEN
				slog.error(pkgCtxId, myUnit, pId || ':' || slct_rec$iil.payment_due_date || ': Unable to convert payment_due_date! ' || sqlerrm);
				MarkInterfaceRecordAsRejected ( pUpdRec$iil => upd_rec$iil, pErrorMessage => 'Vrijednost koja predstavlja datum dospije�a nije ispravna', pImportResult => vImportResult);
				RETURN vImportResult;
		END;
		
		BEGIN
			slct$ipr.id := slct_rec$iil.ipr_id;
			cg$invoice_providers.slct ( slct$ipr );
		EXCEPTION
			WHEN OTHERS THEN
				slog.error(pkgCtxId, myUnit, pId || ':' || slct_rec$iil.ipr_id || ': proslije�eni identifikator izdavatelja nije va�e�i!');
				MarkInterfaceRecordAsRejected ( pUpdRec$iil => upd_rec$iil, pErrorMessage => 'Proslije�eni identifikator izdavatelja nije va�e�i!', pImportResult => vImportResult);
				RETURN vImportResult;
		END;

		IF slct$ipr.bnficiary_account_restriction IN (cbnficry_acc_rstrct_enlisted, cbnficry_acc_rstrct_defaulted) THEN
		
			SELECT data_vchar
			BULK COLLECT INTO tblBeneficiaryAccountList
			FROM ipr_details ipdl
			WHERE ipdl.ipr_id = slct$ipr.id
			  AND ipdl.iab_id = invoice_mgmt_pck.cIprAttribAccountId
			ORDER BY id ASC;

			-- According MOS Doc ID 268122.1, SELECT..BULK COLLECT doesn't raise no_data_found when no rows are returned
			IF tblBeneficiaryAccountList.COUNT = 0 THEN
				
				slog.error(pkgCtxId, myUnit, pId || ':' || slct_rec$iil.ipr_id || ': nije upisan broj ra�una primaoca za izdavatelja');
				MarkInterfaceRecordAsRejected ( pUpdRec$iil => upd_rec$iil, pErrorMessage => 'Gre�ka u postavkama izdavatelja: nedostaje broj ra�una na koji je dozvoljena uplata!', pImportResult => vImportResult);
				RETURN vImportResult;
									
			END IF;
		END IF;

		IF slct_rec$iil.subscription_id IS NOT NULL THEN
			BEGIN
				vSubscriptionId := slct_rec$iil.subscription_id;
			EXCEPTION
				WHEN OTHERS THEN
					slog.error(pkgCtxId, myUnit, pId || ':' || slct_rec$iil.subscription_id || ': Unable to convert subscription ID! ' || sqlerrm);
					MarkInterfaceRecordAsRejected ( pUpdRec$iil => upd_rec$iil, pErrorMessage => 'Vrijednost koja predstavlja ID pretplate nije ispravna', pImportResult => vImportResult);
					RETURN vImportResult;
			END;
		ELSIF slct_rec$iil.user_reference IS NOT NULL THEN
			-- Retrieve invoice_provider_clients.id based on provided UserReference

			BEGIN
			  SELECT id
			  INTO vSubscriptionId
			  FROM invoice_provider_clients
			  WHERE ipr_id = slct_rec$iil.ipr_id
				AND user_reference = slct_rec$iil.user_reference
				AND type = common_pck.cINVPRVCLTYPECONSUMER
				AND valid = cValidTrue;

			EXCEPTION
			-- TODO: Ovdje ce se pojaviti problem ukoliko vi�e korisnika ima pretplatu s istom referencom
			-- Pretohdni upit �e baciti too_many_rows expcetion
			-- Potencijalno rije�enje: Uraditi bulk-collect svih subscription id-eva u pl/sql tabelu, te uraditi loop insert nad tom tabelom svakom korisniku
			-- Dodatno, kada jedan od ra�una bude pla�en, sve ostale poni�titi!
			WHEN no_data_found THEN
			  -- There is no active subscribtion for given provider id and user reference
				slog.error(pkgCtxId, myUnit, pId || ':' || slct_rec$iil.user_reference || ': Ne postoji aktivna pretplata za datu referencu');
				MarkInterfaceRecordAsRejected ( pUpdRec$iil => upd_rec$iil, pErrorMessage => 'Ne postoji aktivna pretplata za datu referencu', pImportResult => vImportResult);
				RETURN vImportResult;
			END;
		ELSIF slct_rec$iil.external_client_id IS NOT NULL THEN
		-- Retrieve invoice_provider_clients.id based on provided ExternalClientId

			BEGIN
				SELECT id
				INTO vSubscriptionId
				FROM invoice_provider_clients
				WHERE ipr_id = slct_rec$iil.ipr_id
				AND external_client_id = slct_rec$iil.external_client_id
				AND valid = cValidTrue;

			EXCEPTION
			WHEN no_data_found THEN
				-- There is no active subscribtion for given provider id and user reference
				-- Possible causes:
				-- User has canceled subscribtion after report was generated
				-- Provider uses non-existent user reference
				slog.error(pkgCtxId, myUnit, pId || ':' || slct_rec$iil.external_client_id || ': Ne postoji aktivna pretplata za dati vanjski identifikator');
				MarkInterfaceRecordAsRejected ( pUpdRec$iil => upd_rec$iil, pErrorMessage => 'Ne postoji aktivna pretplata za dati vanjski identifikator', pImportResult => vImportResult);
				RETURN vImportResult;
					
			END;
		ELSE
			slog.error(pkgCtxId, myUnit, pId || ': Nije proslije�en ni ID pretplate, ni UserReference ni externi registration ID!');
			MarkInterfaceRecordAsRejected ( pUpdRec$iil => upd_rec$iil, pErrorMessage => 'Nije proslije�en ni ID pretplate, ni referenca ni vanjski registration ID!', pImportResult => vImportResult);
			RETURN vImportResult;			
		END IF;

		SELECT COUNT(*)
		INTO vNumberOfInvoices
		FROM invoices
		WHERE invoice_no = slct_rec$iil.invoice_no
		  AND client_id = vSubscriptionId;

		IF vNumberOfInvoices > 0 THEN
		
			slog.error(pkgCtxId, myUnit, pId || ': ve� postoji od ranije uvezen ra�un s istim brojem ra�una!');
			MarkInterfaceRecordAsRejected ( pUpdRec$iil => upd_rec$iil, pErrorMessage => 'Ra�un presko�en - ve� postoji od ranije uvezen ra�un s istim brojem ra�una!', pImportResult => vImportResult);
			RETURN vImportResult;

		END IF;

		IF slct$ipr.bnficiary_account_restriction = cbnficry_acc_rstrct_enlisted THEN
			vFoundMatchingAccount := FALSE;

			FOR j IN tblBeneficiaryAccountList.FIRST..tblBeneficiaryAccountList.LAST LOOP
				IF slct_rec$iil.beneficiary_account = tblBeneficiaryAccountList(j) THEN
					slog.debug(pkgCtxId, myUnit, 'Account ' || slct_rec$iil.beneficiary_account || ' found on list of allowed beneficiary accounts');
					vFoundMatchingAccount := TRUE;
				END IF;
			END LOOP;

			IF NOT vFoundMatchingAccount THEN
				slog.error(pkgCtxId, myUnit, 'Account ' || slct_rec$iil.beneficiary_account || ' NOT found on list of allowed beneficiary accounts');
				
				MarkInterfaceRecordAsRejected ( pUpdRec$iil => upd_rec$iil, pErrorMessage => 'Ra�un primaoca nije na listi ra�una primaoca na koje je dozvoljena uplata!', pImportResult => vImportResult);
				RETURN vImportResult;
			END IF;
		ELSIF slct$ipr.bnficiary_account_restriction = cbnficry_acc_rstrct_defaulted OR slct_rec$iil.beneficiary_account IS NULL THEN
			 slct_rec$iil.beneficiary_account := tblBeneficiaryAccountList(tblBeneficiaryAccountList.FIRST);
			 slog.debug(pkgCtxId, myUnit, 'Defaulting beneficiary account number for invoice ' || slct_rec$iil.invoice_no);
		END IF;

		IF slct_rec$iil.beneficiary_name IS NULL THEN
			slct_rec$iil.beneficiary_name := slct$ipr.name;
		END IF;

		DECLARE
			vDescriptionPrefix VARCHAR2(40);
		BEGIN
		
		BEGIN				
				select data_vchar into vDescriptionPrefix from ipr_details where iab_id = 'DESCRIPTION_PREFIX' and ipr_id = slct_rec$iil.ipr_id and id = 1;
		EXCEPTION
			WHEN no_data_found THEN
				vDescriptionPrefix := NULL;
		END;
			
		IF vDescriptionPrefix IS NOT NULL THEN
			IF slct_rec$iil.ph0 IS NULL THEN
				vDescription := vDescriptionPrefix || slct_rec$iil.description;
			ELSE
				vDescription := slct_rec$iil.ph0 || vDescriptionPrefix || slct_rec$iil.description;
			END IF;
		ELSE
			vDescription := slct_rec$iil.description;
		END IF;

		END;
			
		vUploadInvoiceStatus := invoice_mgmt_pck.UploadInvoice_v2(
			pInvoiceId => vInvoiceId,
			pInvoiceBatchId => slct_rec$iil.ibe_id,
			pInvoiceProviderId => slct_rec$iil.ipr_id,
			pSubscriptionId => vSubscriptionId,
			pUserReference => slct_rec$iil.user_reference,
			pExternalClientId => slct_rec$iil.external_client_id,
			pInvoiceNo => slct_rec$iil.invoice_no,
			pPaymentType => slct_rec$iil.payment_type,
			pDescription => vDescription,
			pTranval => vTranval,
			pCurrencyId => slct_rec$iil.currency,
			pBeneficiaryName => slct_rec$iil.beneficiary_name,
			pBeneficiaryAccount => slct_rec$iil.beneficiary_account,
			pMessage => slct_rec$iil.message,
			pBILLING_PERIOD => slct_rec$iil.billing_period,
			pBILLING_DATE => vBillingDate,
			pPAYMENT_DUE_DATE => vPaymentDueDate,
			pImportResult => vImportResult);

		IF vUploadInvoiceStatus THEN
			DECLARE
				extRefPostingSchemeInstruction VARCHAR2(4000);
				vExtRefPostingScheme VARCHAR2(20);
				vApiCallResult PLS_INTEGER;
			BEGIN
				extRefPostingSchemeInstruction := sspkg.readVchar(pkgCtxId || '/ExtRefPostingScheme/' || slct_rec$iil.ipr_id || '/ExtRefpostingSchemeInstruction');
			
				IF extRefPostingSchemeInstruction IS NOT NULL THEN
			
					slog.debug(pkgCtxId, myUnit, 'Execute extRefPostingSchemeInstruction for invoice_import_iface_tbl.id:' || slct_rec$iil.id);
				
					execute immediate extRefPostingSchemeInstruction using slct_rec$iil.id, out vExtRefPostingScheme;
				
					vApiCallResult := ia$invoice_details.setInvoiceDetails (
					pINE_ID => vInvoiceId
					,pREQ_TYPE_ID => slct_rec$iil.payment_type
					,pATTRIB_ID => cExtRefPostingScheme
					,pDESCRIPTION => NULL
					,pDATA_VCHAR => vExtRefPostingScheme
					,pAction => 'WRITE'
					,pAutonomousTransaction => FALSE
					,pDoCommit => FALSE
					,pmsg => vMSG
					,perror => vERROR
					,pmsg_type => vMSG_TYPE
					,pmsgid => vMSGID
					,ploc => vLOC);

					IF vApiCallResult > 0 THEN
						vImportResult.status := 'GRESKA';
						vImportResult.message := SUBSTR('Interna greska: ' || cExtRefPostingScheme || ':' || vExtRefPostingScheme || ':' || vMSG, 1, 1000);
						slog.error(pkgCtxId, myUnit, 'Greska prilikom dodavanja detalja' || cExtRefPostingScheme || ':' || vExtRefPostingScheme || 'za:' || slct_rec$iil.id);
					END IF;
							
				END IF;
			EXCEPTION
				WHEN OTHERS THEN
						slog.error(pkgCtxId, myUnit, 'Greska prilikom dohvatanja vrijednosti' || cExtRefPostingScheme || 'za: '|| slct_rec$iil.id ||':'|| SQLCODE ||':'|| SQLERRM);
			END;
			slog.debug(pkgCtxId, myUnit, pId || ': Racun obradjen');
			MarkInterfaceRecordAsProcessed ( pUpdRec$iil => upd_rec$iil, pInvoiceId => vInvoiceId, pImportResult => vImportResult);
		ELSE
			slog.error(pkgCtxId, myUnit, pId || ': Greska prilikom ucitavanja : ' || vImportResult.message);
			MarkInterfaceRecordAsRejected ( pUpdRec$iil => upd_rec$iil, pErrorMessage => vImportResult.message, pImportResult => vImportResult);
		END IF;

		RETURN vImportResult;
	EXCEPTION
		WHEN sspkg.sysexception THEN
			BEGIN
				slog.error(pkgCtxId, myUnit, pId || ': Greska prilikom ucitavanja : ' || SUBSTR(sspkg.getErrorMessage || ':' || sspkg.getErrorUserMessage, 1, 4000));
				MarkInterfaceRecordAsRejected ( pUpdRec$iil => upd_rec$iil, pErrorMessage => sspkg.getErrorUserMessage, pImportResult => vImportResult);
			EXCEPTION
				WHEN OTHERS THEN
					slog.error(pkgCtxId, myUnit, pId || ': Oracle error prilikom ucitavanja : ' || SQLERRM);
					-- Handle errors in case update operation is failing!
					MarkInterfaceRecordAsRejected ( pUpdRec$iil => upd_rec$iil, pErrorMessage => 'Oracle error', pImportResult => vImportResult);
			END;
			
			RETURN vImportResult;
		WHEN OTHERS THEN

			BEGIN
				MarkInterfaceRecordAsRejected ( pUpdRec$iil => upd_rec$iil, pErrorMessage => 'Oracle error', pImportResult => vImportResult);
				RETURN vImportResult;
			EXCEPTION
				WHEN OTHERS THEN
					RAISE;
			END;			
	END ProcessInvoiceFromInterface;
	
	PROCEDURE RgstrIfaceBtchForAsyncPrcssing (pBatchId invoice_import_iface_tbl.ibe_id%TYPE)
	IS
		myunit CONSTANT VARCHAR2(30) := 'RgstrIfaceBtchForAsyncPrcssing';
		
		enqueue_options     DBMS_AQ.enqueue_options_t;
		message_properties  DBMS_AQ.message_properties_t;
		message_handle      RAW(16);
				
  BEGIN
	slog.debug(pkgCtxId, myunit);
	
	message_properties.sender_id := sys.aq$_agent('INVOICE_IFACE_BATCH_PROCESSOR', null, null);
	
	DBMS_AQ.ENQUEUE(
		 queue_name => 'STRMADMIN.ASYNCPROCNOTIFICATION_AQ',
		 enqueue_options => enqueue_options,
		 message_properties => message_properties,
		 payload => ANYDATA.ConvertObject(new evt$process_invoiceiface_batch(pBatchId, NULL, SYSDATE)),
		 msgid => message_handle);

  EXCEPTION
	WHEN OTHERS THEN
		slog.error(pkgCtxId, myunit, pBatchId || ':' || sqlcode||':'||sqlerrm);
		RAISE;	
	
  END RgstrIfaceBtchForAsyncPrcssing;

	PROCEDURE ProcessBatchFromInterface (pBatchId invoice_import_iface_tbl.ibe_id%TYPE, pSendNotification IN INTEGER DEFAULT 0)
	IS
		CURSOR c IS
			SELECT id
			FROM invoice_import_iface_tbl
			WHERE ibe_id = pBatchId AND status IN (0, 2);
					

		vImportResult invoice_import_result;

		vProcessError BOOLEAN := FALSE;
		
		cSendNotification		CONSTANT PLS_INTEGER := 1;
		
		slct$ibe cg$invoice_batches.cg$row_type;
		slct$ipr cg$invoice_providers.cg$row_type;

		myunit CONSTANT VARCHAR2(30) := 'ProcessBatchFromInterface';
		
		
	BEGIN
		slog.debug(pkgCtxId, myunit, pBatchId || ':' || pSendNotification);
		
		FOR rec IN c LOOP
			slog.debug(pkgCtxId, myunit, rec.id);
			
			BEGIN
				vImportResult := ProcessInvoiceFromInterface (pId => rec.id);
				
				slog.debug(pkgCtxId, myunit, vImportResult.status);

				IF vImportResult.status = 'GRESKA' THEN
					vProcessError := TRUE;
				END IF;
			EXCEPTION
				WHEN OTHERS THEN
					vProcessError := TRUE;
			END;

		END LOOP;
				
		IF pSendNotification = cSendNotification THEN
		
			slog.debug(pkgCtxId, myunit, 'Send notification');
		
			slct$ibe.id := pBatchId;
			cg$invoice_batches.slct ( slct$ibe );
			
			slog.debug(pkgCtxId, myunit, 'IBE');
			
			slct$ipr.id := slct$ibe.ipr_id;
			cg$invoice_providers.slct ( slct$ipr );
			
			slog.debug(pkgCtxId, myunit, 'IPR');
			
		
			DECLARE
				channelId VARCHAR2(100);
				v_from_address  VARCHAR2(100);
				v_to_address  VARCHAR2(100);
				vIfaceStatusOverview CLOB;
				vIfaceStatusOverviewBlob BLOB;
				vInvoiceBatchDetails CLOB;
				vInvoiceBatchDetailsBlob BLOB;
				
				res pls_integer;
				
				CURSOR cIFaceStatusOverview IS
					select status, 
						decode(status, 0, 'Nije obra�en', 1, 'Obra�en', 2, 'Gre�ka u obradi') status_description, 
						error_message, 
						count(id) broj_racuna
					from invoice_import_iface_tbl 
					where ibe_id = pBatchId 
					group by status, error_message 
					order by status asc;
					
				CURSOR cInvoiceBatchDetails IS
					select id, date_imported, date_processed, status, invoice_no, amount, subscription_id, external_client_id, user_reference, 
						billing_date, payment_due_date, beneficiary_account, customer_name, error_message, ine_id 
					from invoice_import_iface_tbl 
					where ibe_id = pBatchId 
					order by id asc;
				
			BEGIN	

				channelId := sspkg.readvchar('/Core/Main/TranPays/Notifications/MsgChannelId');
				v_from_address := sspkg.readvchar('/Core/Main/TranPays/Notifications/FromAddr');
				v_to_address := sspkg.readvchar(pkgCtxId || '/workflowNotificationReceiver');
			
				dbms_lob.createtemporary(vIfaceStatusOverview, false, dbms_lob.call);
				dbms_lob.createtemporary(vIfaceStatusOverviewBlob, false, dbms_lob.call);
				dbms_lob.createtemporary(vInvoiceBatchDetails, false, dbms_lob.call);
				dbms_lob.createtemporary(vInvoiceBatchDetailsBlob, false, dbms_lob.call);
				
				vIfaceStatusOverview := vIfaceStatusOverview || 'Status;Opis statusa;Broj ra�una;Opis gre�ke' || common_pck.cNewLineTerminator;
				vInvoiceBatchDetails := vInvoiceBatchDetails || 'ID uvoza;Datum uvoza;Datum obrade;Status;Broj ra�una;Iznos;ID pretplate;Vanjski identifikator pretplate;Referenca;Datum ra�una;Datum dospije�a;Ra�un primaoca;Naziv korisnika;Gre�ka;ID uvezenog ra�una' || common_pck.cNewLineTerminator;
				
				FOR rec IN cIFaceStatusOverview LOOP					
					vIfaceStatusOverview := vIfaceStatusOverview || rec.status || ';' || rec.status_description || ';' || rec.broj_racuna || ';' || rec.error_message || common_pck.cNewLineTerminator;					
				END LOOP;
				slog.debug(pkgCtxId, myunit, 'vIfaceStatusOverview');
				
				FOR rec IN cInvoiceBatchDetails LOOP					
					DBMS_LOB.APPEND(vInvoiceBatchDetails , TO_CLOB( rec.id || ';' || common_pck.formatDate(rec.date_imported) || ';' || common_pck.formatDate(rec.date_processed) || ';' || rec.status || ';' || rec.invoice_no || ';' || common_pck.formatMoney (rec.amount) || ';' || rec.subscription_id || ';' || rec.external_client_id || ';' || rec.user_reference || ';' || rec.billing_date || ';' || rec.payment_due_date || ';' || rec.beneficiary_account || ';' || rec.customer_name || ';' ||rec.error_message || ';' || rec.ine_id || common_pck.cNewLineTerminator));
				END LOOP;
				slog.debug(pkgCtxId, myunit, 'vInvoiceBatchDetails');
				
				DECLARE
					dest_offset 	INTEGER;
					src_offset 		INTEGER;
					lang_context	INTEGER;
					warning			INTEGER;
				BEGIN
					dest_offset := 1;
					src_offset := 1;
					lang_context := DBMS_LOB.DEFAULT_LANG_CTX;
					
					DBMS_LOB.CONVERTTOBLOB(
					  dest_lob       => vIfaceStatusOverviewBlob,
					  src_clob       => vIfaceStatusOverview,
					  amount         => DBMS_LOB.LOBMAXSIZE,
					  dest_offset    => dest_offset,
					  src_offset     => src_offset, 
					  blob_csid      => dbms_lob.DEFAULT_CSID,
					  lang_context   => lang_context,
					  warning        => warning);
					slog.debug(pkgCtxId, myunit, 'vIfaceStatusOverviewBlob');
					  
					IF warning = DBMS_LOB.WARN_INCONVERTIBLE_CHAR THEN
						slog.error(pkgCtxId, myUnit, pBatchId || ': Warning trying to prepare email attachment (CLOB->BLOB) : WARN_INCONVERTIBLE_CHAR');
					END IF;
				EXCEPTION
					WHEN OTHERS THEN
						slog.error(pkgCtxId, myUnit, pBatchId || ': Oracle error trying to prepare email attachment vIfaceStatusOverview (CLOB->BLOB) : ' || SQLERRM);
				END;
				
				DECLARE
					dest_offset 	INTEGER;
					src_offset 		INTEGER;
					lang_context	INTEGER;
					warning			INTEGER;
				BEGIN
					dest_offset := 1;
					src_offset := 1;
					lang_context := DBMS_LOB.DEFAULT_LANG_CTX;
					
					DBMS_LOB.CONVERTTOBLOB(
					  dest_lob       => vInvoiceBatchDetailsBlob,
					  src_clob       => vInvoiceBatchDetails,
					  amount         => DBMS_LOB.LOBMAXSIZE,
					  dest_offset    => dest_offset,
					  src_offset     => src_offset, 
					  blob_csid      => dbms_lob.DEFAULT_CSID,
					  lang_context   => lang_context,
					  warning        => warning); 
					slog.debug(pkgCtxId, myunit, 'vInvoiceBatchDetailsBlob');
					
					IF warning = DBMS_LOB.WARN_INCONVERTIBLE_CHAR THEN
						slog.error(pkgCtxId, myUnit, pBatchId || ': Warning trying to prepare email attachment vInvoiceBatchDetails (CLOB->BLOB) : WARN_INCONVERTIBLE_CHAR');
					END IF;

				EXCEPTION
					WHEN OTHERS THEN
						slog.error(pkgCtxId, myUnit, pBatchId || ': Oracle error trying to prepare email attachment (CLOB->BLOB) : ' || SQLERRM);				  
				END;
				
				res := msging.send(
					fromAddr => v_from_address,
					toAddr => v_to_address,
					subject => 'Obavijest o izvr�enom u�itavanju ra�una izdavatelja (' || slct$ipr.id || ') ' || slct$ipr.name || ' paket (' || slct$ibe.id || ') ' || slct$ibe.name,
					bodyMsg => 'Po�tovani,' || common_pck.cNewLineTerminator || common_pck.cNewLineTerminator || 'U prilogu dostavljamo pregled statusa u�itavanja ra�una izdavatelja.',
					att0 => vIfaceStatusOverviewBlob, attFName0 => slct$ibe.name || '_STATUS_OVERVIEW.CSV',
					att1 => vInvoiceBatchDetailsBlob, attFName1 => slct$ibe.name || '_DETAILS.CSV',
					MC_ID => channelId,
					sendAfter => SYSDATE);
				slog.debug(pkgCtxId, myunit, 'msging.send');

			EXCEPTION
				WHEN OTHERS THEN
					slog.error(pkgCtxId, myUnit, 'Unable to send email notification for failed action!');
					slog.error(pkgCtxId, myUnit, SQLERRM);
			END;
			
		END IF;
	END;

	PROCEDURE RemoveInvoiceFromInterface (pId invoice_import_iface_tbl.id%TYPE)
	IS
		del$iil cg$invoice_import_iface_tbl.cg$pk_type;
	BEGIN
		del$iil.id := pId;
		cg$invoice_import_iface_tbl.del ( del$iil );
	END;

	PROCEDURE RemoveBatchFromInterface (pBatchId invoice_import_iface_tbl.ibe_id%TYPE)
	IS
	BEGIN
		DELETE FROM invoices WHERE ibe_id = pBatchId AND status = 0;
		DELETE FROM invoice_import_iface_tbl WHERE ibe_id = pBatchId;
	END;

	PROCEDURE NtfyPrvderAboutTrpyStsChange (pTranpay obj$tranpay) IS
		myunit CONSTANT VARCHAR2(30) := 'NtfyPrvderAboutTrpyStsChange';
		vNotificationAPI VARCHAR2(4000);
		vIprId ipr_details.ipr_id%TYPE;
		vIprDetailsTable cg$ipr_details.cg$table_type;
		vExchangeFormat VARCHAR2(10);
	BEGIN
		slog.debug(pkgCtxId, myUnit, pTranpay.id || ':' || pTranpay.extref_name);

		IF pTranpay.extref_name IS NOT NULL THEN
		
			BEGIN
				SELECT ipr_id INTO vIprId
				FROM ipr_details
				WHERE iab_id = common_pck.cExtrefName 
				AND data_vchar = pTranpay.extref_name
				AND id = 1;
			EXCEPTION 
				WHEN no_data_found THEN
					slog.debug(pkgCtxId, myunit, 'Did not found additional details for ' || pTranpay.extref_name);
					RETURN;
			END;
			
			vIprDetailsTable := invoice_mgmt_pck.getInvoiceProviderDetails(vIprId);
			
			vExchangeFormat := invoice_mgmt_pck.getInvoiceProviderDetail(common_pck.cExchangeFormat, vIprDetailsTable);
			
			vNotificationAPI := sspkg.ReadVchar('/Core/Main/TranPays/Notifications/ExternalProcessors/'|| vExchangeFormat ||'/WS_CLIENT_POST_STATE');
			
			EXECUTE IMMEDIATE vNotificationAPI USING pTranpay, vIprId;
			
		ELSE
			slog.debug(pkgCtxId, myUnit, 'Ignore!');
		END IF;
	END;


    FUNCTION checkInvoiceAccess(pInvoiceId invoices.id%TYPE)
    RETURN BOOLEAN
    IS

        myunit CONSTANT VARCHAR2(30) := 'checkInvoiceAccess';
        vPom PLS_INTEGER;

    BEGIN

        slog.debug(pkgCtxId, myUnit, pInvoiceId);


        SELECT NULL INTO vPom
        FROM DUAL
        WHERE EXISTS(SELECT NULL FROM invoices
                WHERE invoices.id = pInvoiceId
                AND account_owner_id = mcauth.auth.getPrimaryAccountOwner);
        RETURN TRUE;

    EXCEPTION
            WHEN no_data_found THEN
            RETURN FALSE;

    END checkInvoiceAccess;

    PROCEDURE InvalidateSubscription(slct$ipc IN OUT cg$invoice_provider_clients.cg$row_type) IS
		ind$ipc cg$invoice_provider_clients.cg$ind_type;

		myunit CONSTANT VARCHAR2(30) := 'InvalidateSubscription';
	BEGIN
		slog.debug(pkgCtxId, myUnit, slct$ipc .id);

		cg$invoice_provider_clients.slct ( slct$ipc );

		IF slct$ipc.valid = cValidTrue THEN
			ind$ipc.valid := TRUE;
			slct$ipc.valid := cValidFalse;
			cg$invoice_provider_clients.upd ( slct$ipc, ind$ipc,  true );
		END IF;

	END;


	PROCEDURE InvalidateSubscription(pSubscriptionId PLS_INTEGER) IS
		slct$ipc cg$invoice_provider_clients.cg$row_type;

		myunit CONSTANT VARCHAR2(30) := 'InvalidateSubscription';
	BEGIN
		slog.debug(pkgCtxId, myUnit, pSubscriptionId);

		slct$ipc.id := pSubscriptionId;
		InvalidateSubscription(slct$ipc  => slct$ipc);

	END;
	
	PROCEDURE checkUserReference(
		pInvoiceProviderId IN invoice_providers.id%TYPE,
		pUserReference IN VARCHAR2,
		pSessionLanguage IN VARCHAR2,
		pResultCode OUT INTEGER,
		pResultMessage OUT VARCHAR2)
	IS
	BEGIN
		checkUserReference(
			pInvoiceProviderId,
			pUserReference,
			pSessionLanguage,
			NULL,
			NULL,
			pResultCode,
			pResultMessage);
	END;
	

	PROCEDURE checkUserReference(
		pInvoiceProviderId IN invoice_providers.id%TYPE,
		pUserReference IN VARCHAR2,
		pSessionLanguage IN VARCHAR2,
		pUserId IN INTEGER,
		pAccOwnerId IN VARCHAR2,
		pResultCode OUT INTEGER,
		pResultMessage OUT VARCHAR2)
	IS
		myunit CONSTANT VARCHAR2(30) := 'checkUserReference';
		vCheckEnabled BOOLEAN;
		vCheckPluginCode VARCHAR2(32000);
		
		vPluginInput invoicesbscrptncheck_inputype;
		
	BEGIN
		slog.debug(pkgCtxId, myUnit, pInvoiceProviderId || ':' || pUserReference || ':' || pSessionLanguage || ':' || pUserId || ':' || pAccOwnerId );

		vCheckEnabled := sspkg.ReadBool(pkgCtxId || '/UserReferenceVerification/' || pInvoiceProviderId || '/checkUserReferenceIndicator');

		IF vCheckEnabled THEN
			slog.debug(pkgCtxId, myUnit, 'Check enabled!');

			vCheckPluginCode := sspkg.ReadVchar(pkgCtxId || '/UserReferenceVerification/' || pInvoiceProviderId || '/checkUserReferenceAPI');

			IF vCheckPluginCode IS NOT NULL THEN
                slog.debug(pkgCtxId, myUnit, 'Check plugin found!');
				DECLARE
					vResultCode INTEGER;
					vErrorCode VARCHAR2(100);
				BEGIN
					-- Plugin performs logical checks (may differ on provider level) using provided reference, user id and account owner id
					-- Result is 0 - error condition and 1 - can pass
					-- Plugin can return own error codes which take preference over default or global error message
					-- If provided, error code (ssetup variable name relative to pkgCtxId || '/UserReferenceVerification/' || pInvoiceProviderId/ ) is translated and returned to user
					
					vPluginInput := new invoicesbscrptncheck_inputype (pInvoiceProviderId, pUserId, pAccOwnerId, pUserReference);
					execute immediate vCheckPluginCode using vPluginInput, out vResultCode, out vErrorCode;
                    slog.debug(pkgCtxId, myUnit, 'Check resulted with : ' || vResultCode);

					IF vResultCode = cValidFalse THEN
						pResultCode := cValidFalse;
						
						IF vErrorCode IS NOT NULL THEN
							slog.debug(pkgCtxId, myUnit, 'Plugin returned error code ' || vErrorCode);
							pResultMessage := mlang.trans(NVL(pSessionLanguage, 'en'), pkgCtxId || '/UserReferenceVerification/' || pInvoiceProviderId || '/' || vErrorCode);
						ELSE

							IF sspkg.ReadVchar(pkgCtxId || '/UserReferenceVerification/' || pInvoiceProviderId || '/invalidReference') IS NOT NULL then
								-- Ako postoji poruka na nivou izdavatelja, onda uzmi nju
								slog.debug(pkgCtxId, myUnit, 'Found response message (errorMessage) on provider level');
								pResultMessage := mlang.trans(NVL(pSessionLanguage, 'en'), pkgCtxId || '/UserReferenceVerification/' || pInvoiceProviderId || '/invalidReference');
							ELSE
								-- Ina�e vrati generi�ku poruku
								slog.debug(pkgCtxId, myUnit, 'Response message on provider level not found');
								pResultMessage := mlang.trans(NVL(pSessionLanguage, 'en'), pkgCtxId || '/UserReferenceVerification/invalidReference');
							END IF;

							-- Dodatno, da bi sprije�ili da klijent dobije praznu poruku u bilo kom slu�aju, provjerimo jo� jednom
							IF pResultMessage IS NULL THEN
								slog.error(pkgCtxId, myUnit, 'No error message defined for provider ' || pInvoiceProviderId);
								IF NVL(pSessionLanguage, 'en') = 'en' THEN
									pResultMessage := 'Invalid user reference provided!';
								ELSE
									pResultMessage := 'Unesena referenca nije va�e�a!';
								END IF;
							END IF;
						END IF;
                    ELSE
                        pResultCode := cValidTrue;
                        pResultMessage := NULL;
					END IF;
				EXCEPTION
					WHEN OTHERS THEN

					slog.error(pkgCtxId, myUnit, 'Exception during user reference check! ' || sqlerrm);

					pResultCode := cValidFalse;
					IF NVL(pSessionLanguage, 'en') = 'en' THEN
						pResultMessage := 'Internal error encountered! Please contact support!';
					ELSE
						pResultMessage := 'Dogodila se interna gre�ka kod provjere reference! Molimo poku�ajte poslije ili kontaktirajte podr�ku!';
					END IF;
				END;
			ELSE
				slog.debug(pkgCtxId, myUnit, 'Check plugin API not defined or not exists!');

				pResultCode := cValidTrue;
				pResultMessage := NULL;
			END IF;
		ELSE
			slog.debug(pkgCtxId, myUnit, 'Check not enabled or configuration not found!');
			pResultCode := cValidTrue;
			pResultMessage := NULL;
		END IF;

	END checkUserReference;
		
	
	-- dohvata podatke o izdavatelju ra�?una za dati ID izdavatelja
	-- implicitno provjeri da li izdavatelj postoji i baci exception /Core/Main/InvoicesInterfaceMgmt/err/invalidProviderId ukoli ne postoji
	-- baca /Core/Main/InvoicesInterfaceMgmt/err/missingInvoiceProviderId ukoliko nije postavljen parametar InvoiceProviderId u posmatranom kontekstu
	FUNCTION getInvoiceProviderData (pInvoiceProviderId IN invoice_providers.id%TYPE) 
	RETURN cg$invoice_providers.cg$row_type 
	IS
		slct$ipr cg$invoice_providers.cg$row_type;
	
		myunit CONSTANT VARCHAR2(30) := 'getInvoiceProviderData';
	BEGIN
		
		-- Check if given provider ID is valid
		BEGIN
			slct$ipr.id := pInvoiceProviderId;
			cg$invoice_providers.slct ( slct$ipr );
			
		EXCEPTION
			WHEN cg$errors.cg$error THEN
				v_cg_result := cg$errors.pop(msg => vMSG
					,error => vERROR
					,msg_type => vMSG_TYPE
					,msgid => vMSGID
					,loc => vLOC);

				cg$errors.clear;
				
				slog.error(pkgCtxId, myunit, 'Invalid invoice provider ID provided!');
				sspkg.raiseError(pkgCtxId ||'/Common/err/invalidProviderId', null, pkgCtxId, myunit);
								
			WHEN no_data_found THEN	-- Check if realy neccesary!
				slog.error(pkgCtxId, myunit, 'Invalid invoice provider ID provided!');
				sspkg.raiseError(pkgCtxId ||'/Common/err/invalidProviderId', null, pkgCtxId, myunit);
		END;
		
		RETURN slct$ipr;
	END getInvoiceProviderData;
	
	FUNCTION getInvoiceProviderName(pInvoiceProviderId IN invoice_providers.id%TYPE)
	RETURN invoice_providers.name%TYPE IS
		myunit CONSTANT VARCHAR2(30) := 'getInvoiceProviderName';
		
		slct$ipr cg$invoice_providers.cg$row_type;
	BEGIN
		slog.debug(pkgCtxId, myUnit, pInvoiceProviderId);
		
		slct$ipr := getInvoiceProviderData ( pInvoiceProviderId );
		
		RETURN slct$ipr.name;
	EXCEPTION
		WHEN OTHERS THEN
			RETURN NULL;
	END getInvoiceProviderName;
	
	-- dohvata detalje izdavatelja ra�?una
	FUNCTION getInvoiceProviderDetails (pInvoiceProviderId IN invoice_providers.id%TYPE) 
	RETURN cg$ipr_details.cg$table_type 
	IS
		cg$table cg$ipr_details.cg$table_type;
	
		myunit CONSTANT VARCHAR2(30) := 'getInvoiceProviderDetails';
	BEGIN
		slog.debug(pkgCtxId, myUnit, pInvoiceProviderId);
		
		SELECT IPR_ID, IAB_ID, ID, NULL DESCRIPTION, DATA_VCHAR, NULL DATA_BLOB, NULL action_performed
		BULK COLLECT INTO cg$table
		FROM ipr_details
		WHERE ipr_id = pInvoiceProviderId;
		
		RETURN cg$table;
	END getInvoiceProviderDetails;
	
	FUNCTION getInvoiceProviderDetail (pIPRAttribId IN VARCHAR2, pIprDetailsTable IN cg$ipr_details.cg$table_type) 
	RETURN VARCHAR2
	IS
		myunit CONSTANT VARCHAR2(30) := 'getInvoiceProviderDetail';
	BEGIN
		slog.debug(pkgCtxId, myUnit, pIPRAttribId);
	
		FOR i IN pIprDetailsTable.FIRST..pIprDetailsTable.LAST LOOP
			IF pIprDetailsTable(i).iab_id = pIPRAttribId THEN
				RETURN pIprDetailsTable(i).data_vchar;
			END IF;			
		END LOOP;
		
		RETURN NULL;
		
	END getInvoiceProviderDetail;
	
	-- dohvata broj raeuna pridru�en izdavatelju
	-- baca /Core/Main/InvoicesInterfaceMgmt/Common/err/missingProviderAccountId ukoliko raeun nije definisan
	FUNCTION getProviderAccountId (pInvoiceProviderId invoice_providers.id%TYPE)
	RETURN VARCHAR2 IS
		vIPRAccountId ipr_details.data_vchar%TYPE;
		myunit CONSTANT VARCHAR2(30) := 'getProviderAccountId';
	BEGIN
		SELECT data_vchar 
		INTO vIPRAccountId
        FROM
		(SELECT data_vchar
		FROM ipr_details WHERE ipr_id=pInvoiceProviderId AND IAB_ID='ACCOUNT_ID'
        ORDER BY 1 ASC)
        WHERE ROWNUM <= 1;
		
		RETURN vIPRAccountId;
	EXCEPTION
		WHEN no_data_found THEN
			slog.error(pkgCtxId, myunit, 'Account for provider not defined!');
			sspkg.raiseError(pkgCtxId ||'/Common/err/missingProviderAccountId', null, pkgCtxId, myunit);
	END getProviderAccountId;
	
	PROCEDURE checkInvoiceAmount(pInvoiceId invoices.id%TYPE, pInvoiceAmount invoices.tranval%TYPE)
	IS
		myunit CONSTANT VARCHAR2(30) := 'checkInvoiceAmount';
		vInvoiceTranval invoices.tranval%TYPE;
		vInvoiceProviderId invoices.ipr_id%TYPE;
		vPaymentBelow invoice_providers.payment_below%TYPE;
		vPaymentOver invoice_providers.payment_over%TYPE;
	BEGIN
	
		BEGIN
			SELECT ipr_id, tranval INTO vInvoiceProviderId, vInvoiceTranval
			FROM mcore.invoices WHERE id = pInvoiceId;
		EXCEPTION
			WHEN no_data_found THEN
				slog.error(pkgCtxId, myUnit, cERR_InvalidInvoice, pInvoiceId);	
		END;
		
		BEGIN
			SELECT payment_below, payment_over INTO vPaymentBelow, vPaymentOver
			FROM mcore.invoice_providers WHERE id = vInvoiceProviderId;
		EXCEPTION
			WHEN no_data_found THEN
				slog.error(pkgCtxId, myUnit, cERR_InvalidInvoiceProvider, vInvoiceProviderId);	
		END;
		
		IF (vPaymentBelow = cPaymentBelowNotAllowed AND pInvoiceAmount < vInvoiceTranval) OR (vPaymentOver = cPaymentOverNotAllowed AND pInvoiceAmount > vInvoiceTranval) THEN
			slog.error(pkgCtxId, myUnit, cERR_InvoiceAmountNotAllowed, 'InvoiceId:'|| pInvoiceId || 'Tranval:' || vInvoiceTranval || 'pInvoiceAmount:' || pInvoiceAmount);
		   	sspkg.raiseError(cERR_InvoiceAmountNotAllowed, null, pkgCtxId, myunit);
		END IF;
		
	END checkInvoiceAmount;
	
	PROCEDURE getInvoiceProvidersForDetail(pIprId IN invoice_providers.id%TYPE DEFAULT NULL,
										   pIprAttribId IN VARCHAR2 DEFAULT '%', 
										   pIprDetailValue IN VARCHAR2 DEFAULT '%',  
										   pAllInvoiceProviders OUT sys_refcursor,
									       pAllIprDetails OUT sys_refcursor)
	IS 
		myUnit CONSTANT VARCHAR2(30) := 'getInvoiceProvidersForDetail';
		rez1 sys_refcursor;
		rez2 sys_refcursor;
	BEGIN 
		slog.debug(pkgCtxId, myUnit, pIprId ||':'|| pIprAttribId ||':'|| pIprDetailValue);
		
		IF pIprId IS NULL THEN 
			OPEN rez1 FOR 
				SELECT DISTINCT ipr.id, ipr.name, ipr.valid, ipr.description, ipr.service_name, ipr.payment_over, ipr.payment_below
				FROM invoice_providers ipr 
				JOIN ipr_details iprdt ON (ipr.id = iprdt.ipr_id) 
				WHERE iprdt.iab_id LIKE pIprAttribId
				AND iprdt.data_vchar LIKE pIprDetailValue;		
			OPEN rez2 FOR 
				SELECT DISTINCT iprdt.ipr_id, iprdt.iab_id, iprdt.description, iprdt.data_vchar
				FROM ipr_details iprdt
				JOIN invoice_providers ipr on (ipr.id = iprdt.ipr_id)
				JOIN ipr_details iprdt2 ON (ipr.id = iprdt2.ipr_id) 
				WHERE iprdt2.iab_id LIKE pIprAttribId
				AND iprdt2.data_vchar LIKE pIprDetailValue;
		ELSE 
			OPEN rez1 FOR 
				SELECT DISTINCT ipr.id, ipr.name, ipr.valid, ipr.description, ipr.service_name, ipr.payment_over, ipr.payment_below
				FROM invoice_providers ipr 
				JOIN ipr_details iprdt ON (ipr.id = iprdt.ipr_id) 
				WHERE iprdt.iab_id LIKE pIprAttribId
				AND iprdt.data_vchar LIKE pIprDetailValue
				AND ipr.id = pIprId;	
			OPEN rez2 FOR 
				SELECT DISTINCT iprdt.ipr_id, iprdt.iab_id, iprdt.description, iprdt.data_vchar
				FROM ipr_details iprdt
				JOIN invoice_providers ipr on (ipr.id = iprdt.ipr_id)
				JOIN ipr_details iprdt2 ON (ipr.id = iprdt2.ipr_id) 
				WHERE iprdt2.iab_id LIKE pIprAttribId
				AND iprdt2.data_vchar LIKE pIprDetailValue
				AND ipr.id = pIprId;
		END IF;
		
		pAllInvoiceProviders := rez1;
		pAllIprDetails := rez2;
		
	END;
	
	PROCEDURE getInvoiceProvidersForService(pIprId IN NUMBER DEFAULT NULL,
											pAdditionalServiceType IN VARCHAR2 DEFAULT NULL,
											pAllInvoiceProviders OUT sys_refcursor,
											pAllIprDetails OUT sys_refcursor)
		IS
		myunit CONSTANT VARCHAR2(30) := 'getInvoiceProvidersForService';
		rez1 SYS_REFCURSOR;
		rez2 SYS_REFCURSOR;
	BEGIN
        slog.debug(pkgCtxId, myUnit);
		
		common_pck.CommonSecurityChecks;
		
		IF pIprId IS NULL THEN 

			OPEN rez1 FOR
				SELECT ip.id, ip.name, ipc.id provider_client_id, ipc.end_users_id, ipc.user_reference, ipc.date_subscribed, ipc.signature_id, ipc.acc_owner_id, ipc.user_comment, ipc.invoice_template
				FROM invoice_providers ip
				LEFT JOIN invoice_provider_clients ipc ON (ipc.ipr_id = ip.id AND ipc.end_users_id = mcauth.auth.getClientId AND ipc.acc_owner_id like mcauth.auth.getAccountOwner AND ipc.valid = 1 OR ipc.end_users_id is null)
				WHERE ip.valid = 1 
				AND EXISTS (SELECT NULL FROM ipr_details idt WHERE idt.ipr_id = ip.id AND idt.iab_id = common_pck.cIPR_ATT_ADD_TRANPAY_SERVICE) 
				AND EXISTS (SELECT NULL FROM mcore.ipr_details idt WHERE idt.ipr_id = ip.id AND idt.data_vchar LIKE NVL(pAdditionalServiceType, '%'));
			
			OPEN rez2 FOR
				SELECT idt.ipr_id, idt.iab_id, idt.data_vchar
				FROM ipr_details idt
				JOIN invoice_providers ip ON (ip.id = idt.ipr_id)
				LEFT JOIN invoice_provider_clients ipc on (ipc.ipr_id = ip.id AND ipc.end_users_id = mcauth.auth.getClientId AND ipc.acc_owner_id like mcauth.auth.getAccountOwner AND ipc.valid = 1 OR ipc.end_users_id is null)
				WHERE ip.valid = 1 
				AND EXISTS (SELECT NULL FROM ipr_details idt2 WHERE idt2.ipr_id = ip.id AND idt2.iab_id = common_pck.cIPR_ATT_ADD_TRANPAY_SERVICE)
				AND EXISTS (SELECT NULL FROM mcore.ipr_details idt WHERE idt.ipr_id = ip.id AND idt.data_vchar LIKE NVL(pAdditionalServiceType, '%'));
		ELSE 
			OPEN rez1 FOR
				SELECT ip.id, ip.name, ipc.id provider_client_id, ipc.end_users_id, ipc.user_reference, ipc.date_subscribed, ipc.signature_id, ipc.acc_owner_id, ipc.user_comment, ipc.invoice_template
				FROM invoice_providers ip
				LEFT JOIN invoice_provider_clients ipc ON (ipc.ipr_id = ip.id AND ipc.end_users_id = mcauth.auth.getClientId AND ipc.acc_owner_id like mcauth.auth.getAccountOwner AND ipc.valid = 1 OR ipc.end_users_id is null)
				WHERE ip.valid = 1 
			    AND ip.id = pIprId;
			
			OPEN rez2 FOR
				SELECT idt.ipr_id, idt.iab_id, idt.data_vchar
				FROM ipr_details idt
				JOIN invoice_providers ip ON (ip.id = idt.ipr_id)
				LEFT JOIN invoice_provider_clients ipc on (ipc.ipr_id = ip.id AND ipc.end_users_id = mcauth.auth.getClientId AND ipc.acc_owner_id like mcauth.auth.getAccountOwner AND ipc.valid = 1 OR ipc.end_users_id is null)
				WHERE ip.valid = 1 
				AND ip.id = pIprId;
		
		END IF;
		
		pAllInvoiceProviders := rez1;
		pAllIprDetails := rez2;
		
	END getInvoiceProvidersForService;
											
	
	FUNCTION getActiveSubscriptsForReqType(pReqTypeId IN VARCHAR2 DEFAULT NULL)
	RETURN SYS_REFCURSOR
	IS 
		myunit CONSTANT VARCHAR2(30) := 'getActiveSubscriptsForReqType';
		rez SYS_REFCURSOR;
	BEGIN
        slog.debug(pkgCtxId, myUnit);
		
		common_pck.CommonSecurityChecks;

        OPEN rez FOR
			SELECT ipc.id id, ip.id ipr_id, ip.name ipr_name, ipc.user_reference, ipc.user_comment, ipc.date_subscribed, ipc.invoice_template
			FROM invoice_provider_clients ipc JOIN invoice_providers ip ON (ipc.ipr_id = ip.id)
			WHERE ipc.end_users_id = mcauth.auth.getClientId
			AND ip.valid = cValidTrue
			AND ipc.valid = cValidTrue
            AND ipc.acc_owner_id like mcauth.auth.getAccountOwner
			AND EXISTS (SELECT NULL FROM mcore.ipr_details idt WHERE idt.ipr_id = ip.id AND idt.iab_id = common_pck.cIPR_ATT_ADD_TRANPAY_SERVICE AND idt.data_vchar LIKE NVL(pReqTypeId, '%'))
			ORDER by ipr_name ASC;
		RETURN rez;
	END getActiveSubscriptsForReqType;
		
	PROCEDURE UpdateConsumer(pIpcId invoice_provider_clients.id%TYPE,
							 pUserReference VARCHAR2,
							 pUserComment VARCHAR2,
		                     pInvoiceTemplate CLOB)
	IS
		slct$ipc cg$invoice_provider_clients.cg$row_type;
		ind$ipc cg$invoice_provider_clients.cg$ind_type;
		vPom INTEGER;
		myunit CONSTANT VARCHAR2(30) := 'UpdateConsumer';

	BEGIN

		slog.debug(pkgCtxId, myunit, pIpcId);
		
		BEGIN
			SELECT id INTO slct$ipc.id
			FROM invoice_provider_clients 
			WHERE id = pIpcId;
		EXCEPTION
			WHEN no_data_found THEN
				slog.error(pkgCtxId, myunit, cERR_InvalidIprClient, pIpcId);
				sspkg.raiseError(cERR_InvalidIprClient, null, pkgCtxId, myunit);
		END;
			
		IF pUserReference IS NOT NULL THEN
			slct$ipc.user_reference := SUBSTR(pUserReference, 1, 400);
			ind$ipc.user_reference := TRUE;
			slog.debug(pkgCtxId, myUnit, 'Update existing ipc data ... user_reference: ' || slct$ipc.user_reference);
		END IF;
		
		IF pUserComment IS NOT NULL THEN
			slct$ipc.user_comment := SUBSTR(pUserComment, 1, 400);
			ind$ipc.user_comment := TRUE;		
			slog.debug(pkgCtxId, myUnit, 'Prepare new ipc data ... user_comment: ');
		END IF;
		
		IF pInvoiceTemplate IS NOT NULL THEN
			slct$ipc.invoice_template := pInvoiceTemplate;
			ind$ipc.invoice_template := TRUE;
			slog.debug(pkgCtxId, myUnit, 'Prepare new ipc data ... invoice_template: ' || slct$ipc.invoice_template);
		END IF;
									
		slog.debug(pkgCtxId, myUnit, 'Update existing ipc data ... try UPD');
		cg$invoice_provider_clients.upd(slct$ipc, ind$ipc, TRUE);
		slog.debug(pkgCtxId, myUnit, 'Update existing ipc data ... UPD complete');
				
		EXCEPTION
				-- Fetched also when record does not exists -- should try insert !
			WHEN cg$errors.cg$error THEN
					-- Retrieve error message to determine error cause !
					v_cg_result := cg$errors.pop(msg => vMSG
					,error => vERROR
					,msg_type => vMSG_TYPE
					,msgid => vMSGID
					,loc => vLOC);

					-- Else an unexpected error has occured, so tell it to the caller!
					cg$errors.clear;
					slog.error(pkgCtxId, myunit, pIpcId || ':' || ':UPD:' || vMSG);
					sspkg.raiseError(cERR_InternalError, null, pkgCtxId, myunit);
			WHEN OTHERS THEN
				slog.error(pkgCtxId, myunit, pIpcId || ':UPD:' || SQLERRM);
				sspkg.raiseError(pkgCtxId, myunit);

	END UpdateConsumer;

	-- Overloaded version with auto payment parameters
	PROCEDURE UpdateConsumer(pIpcId invoice_provider_clients.id%TYPE,
							 pUserReference VARCHAR2,
							 pUserComment VARCHAR2,
		                     pInvoiceTemplate CLOB,
		                     pAutoPaymentEnabled invoice_provider_clients.auto_payment_enabled%TYPE,
		                     pPaymentAccountId invoice_provider_clients.payment_account_id%TYPE)
	IS
		slct$ipc cg$invoice_provider_clients.cg$row_type;
		ind$ipc cg$invoice_provider_clients.cg$ind_type;
		vPom INTEGER;
		myunit CONSTANT VARCHAR2(30) := 'UpdateConsumer';
	BEGIN
		slog.debug(pkgCtxId, myunit, pIpcId);

		slct$ipc.id := pIpcId;
		slct$ipc.user_reference := pUserReference;
		slct$ipc.user_comment := pUserComment;

		ind$ipc.user_reference := TRUE;
		ind$ipc.user_comment := TRUE;

		IF pInvoiceTemplate IS NOT NULL THEN
			slct$ipc.invoice_template := pInvoiceTemplate;
			ind$ipc.invoice_template := TRUE;
			slog.debug(pkgCtxId, myUnit, 'Prepare new ipc data ... invoice_template: ' || slct$ipc.invoice_template);
		END IF;

		IF pAutoPaymentEnabled IS NOT NULL THEN
			slct$ipc.auto_payment_enabled := pAutoPaymentEnabled;
			ind$ipc.auto_payment_enabled := TRUE;
			slog.debug(pkgCtxId, myUnit, 'Prepare new ipc data ... auto_payment_enabled: ' || slct$ipc.auto_payment_enabled);
		END IF;

		IF pPaymentAccountId IS NOT NULL THEN
			slct$ipc.payment_account_id := pPaymentAccountId;
			ind$ipc.payment_account_id := TRUE;
			slog.debug(pkgCtxId, myUnit, 'Prepare new ipc data ... payment_account_id: ' || slct$ipc.payment_account_id);
		END IF;

		vPom := cg$invoice_provider_clients.upd(slct$ipc, ind$ipc);

		slog.debug(pkgCtxId, myunit, 'Updated subscription with ID :' || pIpcId);

	EXCEPTION
		WHEN OTHERS THEN
			slog.error(pkgCtxId, myunit, pIpcId || ':UPD:' || SQLERRM);
			sspkg.raiseError(pkgCtxId, myunit);
	END UpdateConsumer;

	FUNCTION getInvoiceProviderForReqType(pReqTypeId request_types.id%TYPE)
	RETURN sys_refcursor
	IS 
		rez sys_refcursor;
		myunit CONSTANT VARCHAR2(30) := 'getInvoiceProviderForReqType';
	BEGIN 
	
		slog.debug(pkgCtxId, myUnit);
		common_pck.CommonSecurityChecks;
		
		OPEN rez FOR
				SELECT ip.id, ip.name, ipc.id provider_client_id, ipc.end_users_id, ipc.user_reference, ipc.date_subscribed, ipc.signature_id, ipc.acc_owner_id, ipc.user_comment, 
				JSON_VALUE(ipc.invoice_template, '$.amount') AS amount,
				JSON_VALUE(ipc.invoice_template, '$.currencyId') AS currency_id,
				JSON_VALUE(ipc.invoice_template, '$.orderType') AS order_type,
				JSON_VALUE(ipc.invoice_template, '$.beneficiaryAlias') AS beneficiary_alias,
				JSON_VALUE(ipc.invoice_template, '$.beneficiaryAccountId') AS beneficiary_account_id
				FROM invoice_providers ip
				JOIN invoice_provider_clients ipc ON (ipc.ipr_id = ip.id AND ipc.end_users_id = mcauth.auth.getClientId AND ipc.acc_owner_id like mcauth.auth.getAccountOwner AND ipc.valid = 1)
				WHERE ip.valid = 1 
				AND ipc.user_reference LIKE '%' || pReqTypeId;
	
		RETURN rez;
	END;

	PROCEDURE ProcessAutoPayment(pInvoiceId invoices.id%TYPE)
	IS
		myunit CONSTANT VARCHAR2(30) := 'ProcessAutoPayment';

		CURSOR cInvoiceData IS
			SELECT i.id, i.ipr_id, i.client_id, i.invoice_no, i.description,
			       i.tranval, i.tranval_currency_id, i.status,
			       ipc.auto_payment_enabled, ipc.payment_account_id, ipc.end_users_id,
			       ipr.name as provider_name
			FROM invoices i
			JOIN invoice_provider_clients ipc ON i.client_id = ipc.id
			JOIN invoice_providers ipr ON i.ipr_id = ipr.id
			WHERE i.id = pInvoiceId
			  AND ipc.auto_payment_enabled = 1
			  AND ipc.payment_account_id IS NOT NULL
			  AND i.status = 0; -- Only process new invoices

		vInvoiceData cInvoiceData%ROWTYPE;
		vTranpayId tranpays.id%TYPE;
		vAutoPaymentEnabled BOOLEAN := FALSE;

	BEGIN
		slog.debug(pkgCtxId, myunit, 'Processing auto payment for invoice: ' || pInvoiceId);

		-- Check if auto payment is globally enabled
		vAutoPaymentEnabled := sspkg.readBool('/Core/Main/InvoiceManagement/AutoPayment/enabled');

		IF NOT vAutoPaymentEnabled THEN
			slog.debug(pkgCtxId, myunit, 'Auto payment globally disabled');
			RETURN;
		END IF;

		OPEN cInvoiceData;
		FETCH cInvoiceData INTO vInvoiceData;

		IF cInvoiceData%FOUND THEN
			slog.debug(pkgCtxId, myunit, 'Auto payment enabled for invoice: ' || pInvoiceId || ', account: ' || vInvoiceData.payment_account_id);

			BEGIN
				-- Create tranpay for automatic payment
				vTranpayId := CreateTranpayForInvoice(
					pInvoiceId => vInvoiceData.id,
					pInvoiceProviderName => vInvoiceData.provider_name,
					pInvoiceNo => vInvoiceData.invoice_no,
					pSenderBankAccountId => vInvoiceData.payment_account_id,
					pSenderName => 'AUTO PAYMENT',
					pTranval => vInvoiceData.tranval,
					pTranpayDescription => 'Automatsko plaćanje računa: ' || vInvoiceData.description,
					pReference => 'AUTO_PAY_' || vInvoiceData.id
				);

				slog.debug(pkgCtxId, myunit, 'Created tranpay for auto payment: ' || vTranpayId);

				-- Process the invoice with the created tranpay
				processInvoice(
					pInvoiceID => vInvoiceData.id,
					pAction => 1, -- Create payment order
					pTranpayId => vTranpayId
				);

				slog.debug(pkgCtxId, myunit, 'Auto payment processed successfully for invoice: ' || pInvoiceId);

			-- Send success notification
			SendAutoPaymentNotification(
				pInvoiceId => pInvoiceId,
				pTranpayId => vTranpayId,
				pSuccess => TRUE
			);

			EXCEPTION
				WHEN OTHERS THEN
					slog.error(pkgCtxId, myunit, 'Error processing auto payment for invoice: ' || pInvoiceId || ', Error: ' || SQLERRM);

					-- Send error notification
					SendAutoPaymentNotification(
						pInvoiceId => pInvoiceId,
						pTranpayId => vTranpayId,
						pSuccess => FALSE,
						pErrorMessage => SQLERRM
					);

					-- Don't raise error, just log it so normal invoice processing continues
			END;
		ELSE
			slog.debug(pkgCtxId, myunit, 'Auto payment not enabled or configured for invoice: ' || pInvoiceId);
		END IF;

		CLOSE cInvoiceData;

	EXCEPTION
		WHEN OTHERS THEN
			slog.error(pkgCtxId, myunit, 'Unexpected error in auto payment processing for invoice: ' || pInvoiceId || ', Error: ' || SQLERRM);
			IF cInvoiceData%ISOPEN THEN
				CLOSE cInvoiceData;
			END IF;
	END ProcessAutoPayment;

	PROCEDURE SendAutoPaymentNotification(pInvoiceId invoices.id%TYPE, pTranpayId tranpays.id%TYPE, pSuccess BOOLEAN, pErrorMessage VARCHAR2 DEFAULT NULL)
	IS
		myunit CONSTANT VARCHAR2(30) := 'SendAutoPaymentNotification';

		CURSOR cNotificationData IS
			SELECT i.id as invoice_id, i.invoice_no, i.description, i.tranval, i.tranval_currency_id,
			       ipc.end_users_id, ipr.name as provider_name,
			       t.id as tranpay_id, t.status as tranpay_status, t.status_message
			FROM invoices i
			JOIN invoice_provider_clients ipc ON i.client_id = ipc.id
			JOIN invoice_providers ipr ON i.ipr_id = ipr.id
			LEFT JOIN tranpays t ON t.id = pTranpayId
			WHERE i.id = pInvoiceId;

		vNotificationData cNotificationData%ROWTYPE;
		vSubject VARCHAR2(1000);
		vBody VARCHAR2(4000);
		vFromAddress VARCHAR2(100);
		vToAddress VARCHAR2(100);

	BEGIN
		slog.debug(pkgCtxId, myunit, 'Sending auto payment notification for invoice: ' || pInvoiceId || ', success: ' || CASE WHEN pSuccess THEN 'Y' ELSE 'N' END);

		OPEN cNotificationData;
		FETCH cNotificationData INTO vNotificationData;

		IF cNotificationData%FOUND THEN
			-- Get user email address
			BEGIN
				SELECT email INTO vToAddress
				FROM end_users
				WHERE id = vNotificationData.end_users_id;
			EXCEPTION
				WHEN NO_DATA_FOUND THEN
					slog.warn(pkgCtxId, myunit, 'No email found for user: ' || vNotificationData.end_users_id);
					CLOSE cNotificationData;
					RETURN;
			END;

			-- Get from address from configuration
			vFromAddress := sspkg.readVChar('/Core/Main/InvoiceManagement/AutoPayment/fromAddress');
			IF vFromAddress IS NULL THEN
				vFromAddress := '<EMAIL>';
			END IF;

			IF pSuccess THEN
				-- Success notification
				vSubject := 'Automatsko plaćanje računa - Uspješno izvršeno';
				vBody := 'Poštovani,' || CHR(10) || CHR(10) ||
				        'Obavještavamo Vas da je automatsko plaćanje računa uspješno izvršeno:' || CHR(10) || CHR(10) ||
				        'Izdavatelj: ' || vNotificationData.provider_name || CHR(10) ||
				        'Broj računa: ' || vNotificationData.invoice_no || CHR(10) ||
				        'Opis: ' || vNotificationData.description || CHR(10) ||
				        'Iznos: ' || vNotificationData.tranval || ' ' || vNotificationData.tranval_currency_id || CHR(10) ||
				        'ID naloga: ' || vNotificationData.tranpay_id || CHR(10) || CHR(10) ||
				        'Hvala što koristite Elba usluge.' || CHR(10) || CHR(10) ||
				        'Elba tim';
			ELSE
				-- Error notification
				vSubject := 'Automatsko plaćanje računa - Greška';
				vBody := 'Poštovani,' || CHR(10) || CHR(10) ||
				        'Obavještavamo Vas da automatsko plaćanje računa nije uspješno izvršeno:' || CHR(10) || CHR(10) ||
				        'Izdavatelj: ' || vNotificationData.provider_name || CHR(10) ||
				        'Broj računa: ' || vNotificationData.invoice_no || CHR(10) ||
				        'Opis: ' || vNotificationData.description || CHR(10) ||
				        'Iznos: ' || vNotificationData.tranval || ' ' || vNotificationData.tranval_currency_id || CHR(10) ||
				        'Razlog greške: ' || NVL(pErrorMessage, 'Nepoznata greška') || CHR(10) || CHR(10) ||
				        'Molimo Vas da ručno platite račun kroz Elba aplikaciju.' || CHR(10) || CHR(10) ||
				        'Hvala što koristite Elba usluge.' || CHR(10) || CHR(10) ||
				        'Elba tim';
			END IF;

			-- Send email notification
			BEGIN
				mcore.send_mail(
					fromAddr => vFromAddress,
					toAddr => vToAddress,
					subject => vSubject,
					bodyMsg => vBody,
					MC_ID => NULL
				);

				slog.debug(pkgCtxId, myunit, 'Auto payment notification sent to: ' || vToAddress);

			EXCEPTION
				WHEN OTHERS THEN
					slog.error(pkgCtxId, myunit, 'Error sending auto payment notification: ' || SQLERRM);
			END;
		END IF;

		CLOSE cNotificationData;

	EXCEPTION
		WHEN OTHERS THEN
			slog.error(pkgCtxId, myunit, 'Unexpected error sending auto payment notification: ' || SQLERRM);
			IF cNotificationData%ISOPEN THEN
				CLOSE cNotificationData;
			END IF;
	END SendAutoPaymentNotification;

	PROCEDURE ProcessBatchAutoPayments(pBatchId invoice_batches.id%TYPE)
	IS
		myunit CONSTANT VARCHAR2(30) := 'ProcessBatchAutoPayments';

		CURSOR cBatchInvoices IS
			SELECT i.id
			FROM invoices i
			JOIN invoice_provider_clients ipc ON i.client_id = ipc.id
			WHERE i.ibe_id = pBatchId
			  AND i.status = 0 -- Only new invoices
			  AND ipc.auto_payment_enabled = 1
			  AND ipc.payment_account_id IS NOT NULL;

		vProcessedCount PLS_INTEGER := 0;
		vErrorCount PLS_INTEGER := 0;

	BEGIN
		slog.debug(pkgCtxId, myunit, 'Processing auto payments for batch: ' || pBatchId);

		-- Check if auto payment is globally enabled
		IF NOT sspkg.readBool('/Core/Main/InvoiceManagement/AutoPayment/enabled') THEN
			slog.debug(pkgCtxId, myunit, 'Auto payment globally disabled');
			RETURN;
		END IF;

		-- Process each invoice in the batch that has auto payment enabled
		FOR rec IN cBatchInvoices LOOP
			BEGIN
				ProcessAutoPayment(pInvoiceId => rec.id);
				vProcessedCount := vProcessedCount + 1;

			EXCEPTION
				WHEN OTHERS THEN
					vErrorCount := vErrorCount + 1;
					slog.error(pkgCtxId, myunit, 'Error processing auto payment for invoice: ' || rec.id || ', Error: ' || SQLERRM);
			END;
		END LOOP;

		slog.debug(pkgCtxId, myunit, 'Batch auto payment processing completed. Processed: ' || vProcessedCount || ', Errors: ' || vErrorCount);

	EXCEPTION
		WHEN OTHERS THEN
			slog.error(pkgCtxId, myunit, 'Unexpected error in batch auto payment processing: ' || SQLERRM);
	END ProcessBatchAutoPayments;

END invoice_mgmt_pck;
/

show errors