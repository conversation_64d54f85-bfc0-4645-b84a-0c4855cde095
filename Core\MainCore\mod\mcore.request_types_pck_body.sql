set define off
Prompt CREATE OR REPLACE PACKAGE BODY mcore.request_types_pck

CREATE OR REPLACE	
PACKAGE BODY mcore.request_types_pck IS

    FUNCTION getParentRequestType(pReqTypeId request_types.id%TYPE)
	RETURN request_types.parent_req_type_id%TYPE IS
		
		myunit CONSTANT VARCHAR2(20) := 'getParentRequestType';
		vTranpayReqTypeId request_types.id%TYPE;
	BEGIN
		slog.debug(pkgCtxId, myUnit, 'Retrieve parent req type id for ' || pReqTypeId);
		SELECT parent_req_type_id
		INTO vTranpayReqTypeId
		FROM mcore.request_types 
		WHERE id = pReqTypeId;
				
		slog.debug(pkgCtxId, myUnit, 'Use ' || vTranpayReqTypeId);
		RETURN vTranpayReqTypeId;
	EXCEPTION
		WHEN no_data_found THEN
			slog.error(pkgCtxId, myUnit, 'No request type with ID ' || pReqTypeId || ' found!');
			RAISE;
	END getParentRequestType;
	
	FUNCTION getActiveUserAgreements(pReqTypeId request_types.id%TYPE) 
    RETURN sys_refcursor IS
		myunit CONSTANT VARCHAR2(30) := 'getActiveUserAgreements';
		rez sys_refcursor;
	BEGIN
		slog.debug(pkgCtxId, myunit, pReqTypeId);

		OPEN rez FOR
			SELECT uat.id, uat.agreement_type, uat.agreement_text, uat.agreement_text_hash, uat.valid_from, uat.valid_until, uat.valid, uat.previous_id
			FROM user_agreement uat JOIN
			req_type_uat rtu ON uat.ID = rtu.uat_id
			WHERE rtu.req_type_id = pReqTypeId and rtu.valid = 1 and rtu.valid_from < sysdate and (rtu.valid_to is null or rtu.valid_to > sysdate) and uat.valid = 1
			ORDER BY uat.id ASC;
        RETURN rez;
	END;

END request_types_pck;
/
show error

