PROMPT Creating package body script for package REQUESTS_PCK
CREATE OR REPLACE
PACKAGE BODY MCORE.REQUESTS_PCK
IS
/*
/Core/Main/Requests/err/NoContactEmail
/Core/Main/Requests/err/NoContactPhone
/Core/Main/Requests/err/MessageAlreadyAssigned
/Core/Main/Requests/err/sendError
/Core/Main/Requests/err/NoValidRequestType
/Core/Main/Requests/err/NoExistentRequest
/Core/Main/Requests/err/InvalidRequestStatus
*/

    -- Sistem constants
    cREF_CLASS CONSTANT VARCHAR2(19) := 'MCORE.REQUESTS_PCK.';

    cERR_NoContactEmail         CONSTANT VARCHAR2(38) := pkgCtxId || '/err/NoContactEmail';
    cERR_NoContactPhone         CONSTANT VARCHAR2(38) := pkgCtxId || '/err/NoContactPhone';
    cERR_MessageAlreadyAssigned CONSTANT VARCHAR2(46) := pkgCtxId || '/err/MessageAlreadyAssigned';
    cERR_sendError              CONSTANT VARCHAR2(33) := pkgCtxId || '/err/sendError';
    cERR_NoValidRequestType     CONSTANT VARCHAR2(42) := pkgCtxId || '/err/NoValidRequestType';
	cERR_NoExistentRequestType  CONSTANT VARCHAR2(46) := pkgCtxId || '/err/NoExistentRequestType';
    cERR_NoExistentRequest      CONSTANT VARCHAR2(41) := pkgCtxId || '/err/NoExistentRequest';
    cERR_InvalidRequestStatus   CONSTANT VARCHAR2(44) := pkgCtxId || '/err/InvalidRequestStatus';
	cERR_InvalidAccount   		CONSTANT VARCHAR2(38) := pkgCtxId || '/err/InvalidAccount';
	cERR_ReqDocValidationFailure CONSTANT VARCHAR2(51) := pkgCtxId || '/err/ReqTypeDocValidationFailure';
	cERR_InternalError          CONSTANT VARCHAR2(37) := pkgCtxId || '/err/InternalError';
	cERR_UnassignedAccOwner     CONSTANT VARCHAR2(42) := pkgCtxId || '/err/UnassignedAccOwner';
	cERR_NoLicenseForMobileApp CONSTANT VARCHAR2(50) := pkgCtxId || '/err/NoLicenseForMobileApp';
	
	cVisaPlatinumCardType CONSTANT VARCHAR2(3) := 'V9P';
	cIntesaHealthInsuranceVP CONSTANT VARCHAR2(37) := 'INTESA_HEALTH_INSURANCE_VISA_PLATINUM';

    FUNCTION sendClientMessage(pMessageSubject mcore.client_messages.subject%TYPE,
                               pMessageBody mcore.client_messages.message_body%TYPE,
                               pAnswerChannel mcore.client_messages.answer_channel%TYPE DEFAULT NULL,
                               pAnswerTo mcore.client_messages.answer_to%TYPE DEFAULT NULL)
    RETURN client_messages.id%TYPE
    IS
        myunit CONSTANT VARCHAR2(17) := 'sendClientMessage';
        vClientMessageId client_messages.id%TYPE;

        vAnswerChannel client_messages.answer_channel%TYPE;
        vAnswerTo      client_messages.answer_to%TYPE;

    BEGIN
        slog.debug(pkgCtxId, myUnit, 'Execute for :' || pMessageSubject || ':' || pMessageBody || ':' || pAnswerChannel || ':' || pAnswerTo);
        common_pck.CommonSecurityChecks;

        vAnswerChannel := pAnswerChannel;
        IF vAnswerChannel IS NULL THEN
          -- If no answer channel was specIFied, use email as default channel
          vAnswerChannel := 1;
        END IF;

        vAnswerTo := pAnswerTo;
        IF vAnswerTo IS NULL THEN
          -- IF no address(or phone) was specIFied, use user default's
			BEGIN
				SELECT decode(vAnswerChannel, 1, contact_email, contact_phone)
				  INTO vAnswerTo
				  FROM mcore.end_users
				WHERE id = mcauth.auth.getClientId;

				IF vAnswerTo IS NULL THEN
					IF vAnswerChannel = 1 THEN
						sspkg.raiseError(cERR_NoContactEmail, null, pkgCtxId, myunit); -- No default found! EXIT!
					ELSE
						sspkg.raiseError(cERR_NoContactPhone, null, pkgCtxId, myunit); -- No default found! EXIT!
					END IF;
				END IF;
			EXCEPTION
			WHEN no_data_found THEN
				sspkg.raiseError(mcore.common_pck.cERR_UnknownEndUser, null, pkgCtxId, myunit); -- no end user (should not happen!)
			END;
        END IF;

        INSERT INTO client_messages(subject, message_body, answer_channel, answer_to)
        VALUES (pMessageSubject, pMessageBody, vAnswerChannel, vAnswerTo)
        RETURN id INTO vClientMessageId;

        RETURN vClientMessageId;
    END sendClientMessage;

    PROCEDURE assignRecipientToClientMessage(pClientMessageId client_messages.id%TYPE, pContactId contacts.id%TYPE)
    IS
        myunit CONSTANT VARCHAR2(30) := 'assingRecipientToClientMessage';
    BEGIN
        slog.debug(pkgCtxId, myUnit, pClientMessageId || ':' || pContactId);
        common_pck.CommonSecurityChecks;

        INSERT INTO msg_recipients(cmsg_id, con_id, delivered)
        VALUES(pClientMessageId, pContactId, 0);

    EXCEPTION
        WHEN dup_val_on_index THEN
			slog.error(pkgCtxId, myUnit, cERR_MessageAlreadyAssigned, pClientMessageId || ':' || pContactId);
            sspkg.raiseError(cERR_MessageAlreadyAssigned, null, pkgCtxId, myunit);
    END assignRecipientToClientMessage;

    FUNCTION isMessageDelivered(pClientMessageId msg_recipients.cmsg_id%TYPE,
                                pContactId msg_recipients.con_id%TYPE)
    RETURN BOOLEAN IS
        myunit CONSTANT VARCHAR2(18) := 'isMessageDelivered';
        vDelivered msg_recipients.delivered%TYPE;
    BEGIN
        slog.debug(pkgCtxId, myUnit, pClientMessageId || ':' || pContactId);
        common_pck.CommonSecurityChecks;

        SELECT delivered
          INTO vDelivered
          FROM msg_recipients
         WHERE cmsg_id = pClientMessageId
           AND con_id = pContactId;

        IF vDelivered = 1 THEN
            RETURN TRUE;
        END IF;
        RETURN FALSE;
    EXCEPTION
        WHEN no_data_found THEN
            RETURN FALSE;
    END isMessageDelivered;

	PROCEDURE sendMessages
	IS
		pragma autonomous_transaction;
		myunit CONSTANT VARCHAR2(12) := 'sendMessages';
        cMaxNumberOfAttachments CONSTANT pls_integer := 10;
		res pls_integer;

		channelId varchar2(100);
		fromAddr varchar2(100);

		CURSOR c IS
			SELECT cm.subject, cm.message_body, cm.answer_channel, cm.answer_to, co.contact_email,
					mr.cmsg_id, mr.con_id, cm.user_id, eu.first_name, eu.last_name, cm.id
			FROM     client_messages cm
				JOIN msg_recipients mr ON (cm.id = mr.cmsg_id)
				JOIN contacts co ON (co.id = mr.con_id)
				JOIN end_users eu ON (eu.id = cm.user_id)
			WHERE mr.delivered = 0
				AND co.valid = 1;
        
        CURSOR cAttachments(pExtId mcore.attachments.ext_id%TYPE) IS
            SELECT rowid row_id, description, content, filename, date_created 
              FROM mcore.attachments
             WHERE ext_id = pExtId
		       AND delivered = 0;

        TYPE emailAttachmentsTT IS TABLE OF cAttachments%ROWTYPE INDEX BY PLS_INTEGER;

		emailAttachmentsTable emailAttachmentsTT;
	BEGIN
		slog.debug(pkgCtxId, myUnit);

		channelId := sspkg.readvchar('/Core/Main/Requests/MsgChannelId');
		fromAddr := sspkg.readvchar('/Core/Main/Requests/FromAddr');

		FOR messages IN c LOOP
			DECLARE
				vBodyMsg VARCHAR2(4000 CHAR);
			BEGIN
				slog.debug(pkgCtxId, myUnit, 'Try to send message cmsg_id=>' || messages.cmsg_id || ', con_id=>' || messages.con_id || ' ...');

				IF messages.answer_channel = 1 THEN
					vBodyMsg := SUBSTR('Poruka poslata od korisnika : ' || messages.last_name || ' ' || messages.first_name || '.' || chr(10) ||
						'Korisnik želi da bude kontaktiran putem e-maila: ' ||
						messages.answer_to || '.' || chr(10) || chr(10), 1, 4000);
				ELSE
					vBodyMsg := SUBSTR('Poruka poslata od korisnika : ' || messages.last_name || ' ' || messages.first_name || '.' || chr(10) ||
						'Korisnik želi da bude kontaktiran putem broja telefona: ' ||
						messages.answer_to || '.' || chr(10) || chr(10), 1, 4000);
				end IF;
                
                OPEN cAttachments(messages.id);
                LOOP
                    FETCH cAttachments BULK COLLECT INTO emailAttachmentsTable LIMIT cMaxNumberOfAttachments;
                    EXIT WHEN emailAttachmentsTable.COUNT = 0;

                    res := msging.send(
                                fromAddr=>fromAddr,
                                toAddr=>messages.contact_email,
                                subject=>messages.subject,
                                bodyMsg=>SUBSTR(vBodyMsg || messages.message_body, 1, 4000),
                                att0=>case WHEN emailAttachmentsTable.COUNT > 0 THEN mcore.util.readBlobFromBfile(emailAttachmentsTable(1).content) ELSE NULL END,
                                att1=>case WHEN emailAttachmentsTable.COUNT > 1 THEN mcore.util.readBlobFromBfile(emailAttachmentsTable(2).content) ELSE NULL END,
                                att2=>case WHEN emailAttachmentsTable.COUNT > 2 THEN mcore.util.readBlobFromBfile(emailAttachmentsTable(3).content) ELSE NULL END,
                                att3=>case WHEN emailAttachmentsTable.COUNT > 3 THEN mcore.util.readBlobFromBfile(emailAttachmentsTable(4).content) ELSE NULL END,
                                att4=>case WHEN emailAttachmentsTable.COUNT > 4 THEN mcore.util.readBlobFromBfile(emailAttachmentsTable(5).content) ELSE NULL END,
                                att5=>case WHEN emailAttachmentsTable.COUNT > 5 THEN mcore.util.readBlobFromBfile(emailAttachmentsTable(6).content) ELSE NULL END,
                                att6=>case WHEN emailAttachmentsTable.COUNT > 6 THEN mcore.util.readBlobFromBfile(emailAttachmentsTable(7).content) ELSE NULL END,
                                att7=>case WHEN emailAttachmentsTable.COUNT > 7 THEN mcore.util.readBlobFromBfile(emailAttachmentsTable(8).content) ELSE NULL END,
                                att8=>case WHEN emailAttachmentsTable.COUNT > 8 THEN mcore.util.readBlobFromBfile(emailAttachmentsTable(9).content) ELSE NULL END,
                                att9=>case WHEN emailAttachmentsTable.COUNT > 9 THEN mcore.util.readBlobFromBfile(emailAttachmentsTable(10).content) ELSE NULL END,
                                attFName0=>case WHEN emailAttachmentsTable.COUNT > 0 THEN emailAttachmentsTable(1).filename ELSE NULL END,
                                attFName1=>case WHEN emailAttachmentsTable.COUNT > 1 THEN emailAttachmentsTable(2).filename ELSE NULL END,
                                attFName2=>case WHEN emailAttachmentsTable.COUNT > 2 THEN emailAttachmentsTable(3).filename ELSE NULL END,
                                attFName3=>case WHEN emailAttachmentsTable.COUNT > 3 THEN emailAttachmentsTable(4).filename ELSE NULL END,
                                attFName4=>case WHEN emailAttachmentsTable.COUNT > 4 THEN emailAttachmentsTable(5).filename ELSE NULL END,
                                attFName5=>case WHEN emailAttachmentsTable.COUNT > 5 THEN emailAttachmentsTable(6).filename ELSE NULL END,
                                attFName6=>case WHEN emailAttachmentsTable.COUNT > 6 THEN emailAttachmentsTable(7).filename ELSE NULL END,
                                attFName7=>case WHEN emailAttachmentsTable.COUNT > 7 THEN emailAttachmentsTable(8).filename ELSE NULL END,
                                attFName8=>case WHEN emailAttachmentsTable.COUNT > 8 THEN emailAttachmentsTable(9).filename ELSE NULL END,
                                attFName9=>case WHEN emailAttachmentsTable.COUNT > 9 THEN emailAttachmentsTable(10).filename ELSE NULL END,
                                MC_ID=>channelId,
                                toExtUser=>NULL);
                    slog.debug(pkgCtxId, myUnit, 'Try to send message cmsg_id=>' || messages.cmsg_id || ', con_id=>' || messages.con_id || ' ... OK!');
    
                    UPDATE mcore.msg_recipients mr
                    SET delivered = 1
                    WHERE 	mr.cmsg_id = messages.cmsg_id and
                            mr.con_id = messages.con_id;
                    slog.debug(pkgCtxId, myUnit, 'Mark message message cmsg_id=>' || messages.cmsg_id || ', con_id=>' || messages.con_id || ' as delivered ... OK!');
    
                    
                    FORALL i IN emailAttachmentsTable.FIRST..emailAttachmentsTable.LAST
                      UPDATE attachments er
                      SET delivered = 1
                      WHERE rowid = emailAttachmentsTable(i).row_id;
                    slog.debug(pkgCtxId, myUnit, 'Mark attachments as delivered ... OK!');
                      
                    COMMIT;
                    slog.debug(pkgCtxId, myUnit, 'Mark message as delivered ... OK');
                END LOOP;
                CLOSE cAttachments;
			EXCEPTION
				WHEN SSPKG.SYSEXCEPTION THEN
					ROLLBACK;
					RAISE;
				WHEN OTHERS THEN
					slog.error(pkgCtxId, myUnit, common_pck.cERR_SendNotificationError, sqlcode || ':' || sqlerrm);
					ROLLBACK;
					sspkg.raiseError(common_pck.cERR_SendNotificationError, null, pkgCtxId, myunit);
			END;
		END LOOP;

		COMMIT;
	END sendMessages;

    FUNCTION requestRequireSignature
    RETURN BOOLEAN IS
        myunit CONSTANT VARCHAR2(23) := 'requestRequireSignature';
    BEGIN
        slog.debug(pkgCtxId, myUnit);
        common_pck.CommonSecurityChecks;

        RETURN sspkg.ReadBool(pkgCtxId || '/requireSignature');
    END requestRequireSignature;

    FUNCTION p_requestRequireSignature
    RETURN PLS_INTEGER IS
        myunit CONSTANT VARCHAR2(25) := 'p_requestRequireSignature';
    BEGIN
        slog.debug(pkgCtxId, myUnit);
        common_pck.CommonSecurityChecks;

        IF requestRequireSignature THEN
            RETURN 1;
        END IF;
        RETURN 0;
    END p_requestRequireSignature;

    FUNCTION getAllRequestsForPeriodCount(pRequestPeriodFrom DATE,
                                     pRequestPeriodTo DATE := SYSDATE,
                                     pStatus mcore.requests.status%TYPE)
    RETURN PLS_INTEGER IS
        myunit CONSTANT VARCHAR2(28) := 'getAllRequestsForPeriodCount';
        vDatumOd DATE;
        vDatumDo DATE;

        vCount PLS_INTEGER;

    BEGIN
        slog.debug(pkgCtxId, myUnit, pRequestPeriodFrom || ':' || pRequestPeriodTo || ':' || pStatus);
        common_pck.CommonSecurityChecks;

        vDatumOd := NVL(pRequestPeriodFrom, mcore.common_pck.cDATE_PAST);
        vDatumDo := NVL(pRequestPeriodTo + 1, mcore.common_pck.cDATE_FUTURE);

        SELECT COUNT(*)
          INTO vCount
          FROM mcore.vw$user_requests
        WHERE date_created between vDatumOd AND vDatumDo
          AND status = pStatus
		  AND (JSON_VALUE(data, '$.sections[*].fields[*].items[0]?(@.id=="requestIsTemplate").value') IS NULL
		  OR JSON_VALUE(data, '$.sections[*].fields[*].items[0]?(@.id=="requestIsTemplate").value') = '0');

        RETURN vCount;
    END getAllRequestsForPeriodCount;

    FUNCTION getAllRequestsForPeriod(pRequestPeriodFrom DATE,
                                     pRequestPeriodTo DATE := SYSDATE,
                                     pStatus mcore.requests.status%TYPE,
									 pBasicType mcore.request_types.basic_type%TYPE DEFAULT NULL,
                                     pOffset PLS_INTEGER := 1,
                                     pArraySize PLS_INTEGER := 10)
    RETURN sys_refcursor IS
        myunit CONSTANT VARCHAR2(23) := 'getAllRequestsForPeriod';
        vOffset PLS_INTEGER;
        vArraySize PLS_INTEGER;

        vDatumOd DATE;
        vDatumDo DATE;
        rez sys_refcursor;

    BEGIN
        slog.debug(pkgCtxId, myunit, pRequestPeriodFrom || ':' || pRequestPeriodTo || ':' || pStatus || ':' || pOffset || ':' || pArraySize);
        common_pck.CommonSecurityChecks;

        vDatumOd := NVL(pRequestPeriodFrom, mcore.common_pck.cDATE_PAST);
        vDatumDo := NVL(pRequestPeriodTo + 1, mcore.common_pck.cDATE_FUTURE);

        vArraySize := pArraySize - 1;
        IF vArraySize < 0 THEN
			slog.error(pkgCtxId, myUnit, cERR_sendError, pRequestPeriodFrom || ':' || pRequestPeriodTo || ':' || pStatus || ':' || pOffset || ':' || pArraySize);
            sspkg.raiseError(mcore.common_pck.cERR_InvalidPagingArraySize, null, pkgCtxId, myunit);
        END IF;

        vOffset := pOffset;
        IF vOffset < 0 THEN
           vOffset := 0;
        END IF;

        IF pBasicType IS NOT NULL THEN
          
          OPEN rez FOR
			SELECT request_id, application_id, user_id, status,
					   status_decoded, status_message, status_code, description,
					   date_created,
					   date_signed, date_processed, blob_mimetype,
					   test_record, req_type_id, req_type_description,
					   req_type_basic_type, guarantee_status, data FROM(
				SELECT request_id, application_id, user_id, status,
					   mcore.common_pck.decodeStatus(status) status_decoded,
					   status_message, status_code, description,
					   date_created,
					   date_signed, date_processed, blob_mimetype,
					   test_record, req_type_id, req_type_description,
					   req_type_basic_type, guarantee_status, data,
					   ROW_NUMBER() OVER (ORDER BY request_id DESC) rn
				  FROM mcore.vw$user_requests
				   WHERE req_type_basic_type = pBasicType
				   AND date_created between vDatumOd AND vDatumDo
					 AND status = pStatus
					 AND (
						  JSON_VALUE(data, '$.sections[*].fields[*].items[0]?(@.id=="requestIsTemplate").value') IS NULL
						  OR JSON_VALUE(data, '$.sections[*].fields[*].items[0]?(@.id=="requestIsTemplate").value') = '0'
						  OR (
							  JSON_VALUE(data, '$.sections[*].fields[*].items[0]?(@.id=="requestIsTemplate").value') = '1'
							  AND SYSDATE BETWEEN valid_from AND valid_to
							)
					    )
					)
			  WHERE rn BETWEEN vOffset AND (vOffset + vArraySize)
			  ORDER BY rn ASC;
        
        ELSE

			OPEN rez FOR
				SELECT request_id, application_id, user_id, status,
						   status_decoded, status_message, status_code, description,
						   date_created,
						   date_signed, date_processed, blob_mimetype,
						   test_record, req_type_id, req_type_description,
						   req_type_basic_type, guarantee_status, data FROM(
					SELECT request_id, application_id, user_id, status,
						   mcore.common_pck.decodeStatus(status) status_decoded,
						   status_message, status_code, description,
						   date_created,
						   date_signed, date_processed, blob_mimetype,
						   test_record, req_type_id, req_type_description,
						   req_type_basic_type, guarantee_status, data,
						   ROW_NUMBER() OVER (ORDER BY request_id DESC) rn
					FROM mcore.vw$user_requests
					WHERE date_created BETWEEN vDatumOd AND vDatumDo
					  AND status = pStatus
					  AND (
						  JSON_VALUE(data, '$.sections[*].fields[*].items[0]?(@.id=="requestIsTemplate").value') IS NULL
						  OR JSON_VALUE(data, '$.sections[*].fields[*].items[0]?(@.id=="requestIsTemplate").value') = '0'
						  OR (
							  JSON_VALUE(data, '$.sections[*].fields[*].items[0]?(@.id=="requestIsTemplate").value') = '1'
							  AND SYSDATE BETWEEN valid_from AND valid_to
						  )
					  )
					) 
				WHERE rn BETWEEN vOffset AND (vOffset + vArraySize)
				ORDER BY rn ASC;
          
         END IF;

        RETURN rez;
    END getAllRequestsForPeriod;

    FUNCTION getAllRequestsTypes(pRequestType VARCHAR2 := '%')
    RETURN sys_refcursor IS
        myunit CONSTANT VARCHAR2(19) := 'getAllRequestsTypes';
        vRequestType request_types.id%TYPE;
        vEndUserType VARCHAR2(1) := '%';
        rez sys_refcursor;
    BEGIN
        slog.debug(pkgCtxId, myunit, pRequestType);

        vRequestType := NVL(pRequestType, '%');

      BEGIN
        SELECT ao.ph0
        INTO vEndUserType
        FROM mcore.vw$user_acc_owners uao
            JOIN mcore.account_owners ao ON (uao.account_owner_id = ao.id)
        where rownum < 2;
      EXCEPTION
      WHEN no_data_found THEN
        vEndUserType := '%';
      WHEN too_many_rows THEN
		slog.error(pkgCtxId, myUnit, cERR_NoValidRequestType, pRequestType);
        sspkg.raiseError(cERR_NoValidRequestType, null, pkgCtxId, myunit);
      END;

        OPEN rez FOR
        SELECT id, description, basic_type, valid, doc_path, req_type_struct, visible, request_type_group_id, sort_order
          FROM request_types
         WHERE id LIKE vRequestType
		   AND NVL(application_id, NVL(mcauth.auth.getApplicationId,'%')) = NVL(mcauth.auth.getApplicationId,'%')
		   AND NVL(ao_type, vEndUserType) LIKE vEndUserType
           AND valid = 1
           AND CASE WHEN mcauth.auth.getClientId IS NULL THEN 1
               ELSE 0
               END = is_public 
          ORDER BY id ASC;

        RETURN rez;
    END getAllRequestsTypes;
	
	FUNCTION getAllRequestTypesForBasicType(pBasicType request_types.basic_type%TYPE)
    RETURN sys_refcursor IS
        myunit CONSTANT VARCHAR2(30) := 'getAllRequestTypesForBasicType';
        vEndUserType VARCHAR2(1) := '%';
        rez sys_refcursor;
    BEGIN
        slog.debug(pkgCtxId, myunit, pBasicType);

      BEGIN
        SELECT ao.ph0
        INTO vEndUserType
        FROM mcore.vw$user_acc_owners uao
            JOIN mcore.account_owners ao ON (uao.account_owner_id = ao.id)
        where rownum < 2;
      EXCEPTION
      WHEN no_data_found THEN
        vEndUserType := '%';
      WHEN too_many_rows THEN
		slog.error(pkgCtxId, myUnit, cERR_NoValidRequestType);
        sspkg.raiseError(cERR_NoValidRequestType, null, pkgCtxId, myunit);
      END;

        OPEN rez FOR
        SELECT id, description, basic_type, valid, doc_path, req_type_struct, visible, request_type_group_id, sort_order
          FROM request_types
         WHERE NVL(application_id, NVL(mcauth.auth.getApplicationId,'%')) = NVL(mcauth.auth.getApplicationId,'%')
			AND NVL(ao_type, vEndUserType) LIKE vEndUserType
			AND NVL(basic_type, pBasicType) LIKE pBasicType
			AND valid = 1
            AND CASE WHEN mcauth.auth.getClientId IS NULL THEN 1
                ELSE 0
                END = is_public            
          ORDER BY id ASC;

        RETURN rez;
    END getAllRequestTypesForBasicType;


    FUNCTION getRequestBasicType(pId mcore.request_types.id%TYPE)
    RETURN mcore.request_types.basic_type%TYPE IS
        myunit CONSTANT VARCHAR2(19) := 'getRequestBasicType';
        vBasicType mcore.request_types.basic_type%TYPE;
    BEGIN
        slog.debug(pkgCtxId, myunit, pId);
		IF pId IS NULL THEN
			sspkg.raiseError(cERR_NoExistentRequest, null, pkgCtxId, myunit);
		END IF;

        SELECT basic_type
          INTO vBasicType
          FROM request_types
         WHERE id = pId;

        RETURN vBasicType;
    EXCEPTION
      WHEN no_data_found THEN
        RETURN NULL;
    END getRequestBasicType;

	PROCEDURE getRequestContentData(pRequestId IN requests.id%TYPE, pContent OUT NOCOPY CLOB)
    IS
        myunit CONSTANT VARCHAR2(21) := 'getRequestContentData';
    BEGIN
        slog.debug(pkgCtxId, myunit, pRequestId);
		IF pRequestId IS NULL THEN
			sspkg.raiseError(cERR_NoExistentRequest, null, pkgCtxId, myunit);
		END IF;

        common_pck.CommonSecurityChecks;

        SELECT xmlserialize(document req.content as clob indent)        
        INTO pContent
        FROM vw$user_requests req
        WHERE req.request_id = pRequestId;
         
    EXCEPTION
      WHEN no_data_found THEN
		slog.error(pkgCtxId, myUnit, cERR_NoExistentRequest, pRequestId);
        sspkg.raiseError(cERR_NoExistentRequest, null, pkgCtxId, myunit);
    END getRequestContentData;
	
	PROCEDURE getRequestDataColumnData(pRequestId IN requests.id%TYPE, pData OUT NOCOPY CLOB)
    IS
        myunit CONSTANT VARCHAR2(24) := 'getRequestDataColumnData';
    BEGIN
        	slog.debug(pkgCtxId, myunit, pRequestId);
		IF pRequestId IS NULL THEN
			sspkg.raiseError(cERR_NoExistentRequest, null, pkgCtxId, myunit);
		END IF;

        	common_pck.CommonSecurityChecks;

        SELECT data
        INTO pData
        FROM vw$user_requests req
        WHERE req.request_id = pRequestId;

    EXCEPTION
      WHEN no_data_found THEN
		slog.error(pkgCtxId, myUnit, cERR_NoExistentRequest, pRequestId);
        sspkg.raiseError(cERR_NoExistentRequest, null, pkgCtxId, myunit);
    END getRequestDataColumnData;

    FUNCTION isValidRequestType(pReqType request_types.id%TYPE)
    RETURN BOOLEAN IS
        vValid request_types.valid%TYPE;
        myunit CONSTANT VARCHAR2(18) := 'isValidRequestType';
    BEGIN
        slog.debug(pkgCtxId, myunit, pReqType);
		IF pReqType IS NULL THEN
			sspkg.raiseError(cERR_NoExistentRequestType, null, pkgCtxId, myunit);
		END IF;

        SELECT valid
          INTO vValid
          FROM request_types
         WHERE id = pReqType;

        IF vValid = 0 THEN
            RETURN FALSE;
        END IF;

        RETURN TRUE;
    EXCEPTION
      WHEN no_data_found THEN
        RETURN FALSE;
    END isValidRequestType;

    FUNCTION isValidRequestStatus(pStatus requests.status%TYPE)
    RETURN BOOLEAN IS
        myunit CONSTANT VARCHAR2(20) := 'isValidRequestStatus';
    BEGIN
        slog.debug(pkgCtxId, myunit, pStatus);

        IF pStatus IN (mcore.common_pck.cTRPSTS_UW,mcore.common_pck.cTRPSTS_UC,mcore.common_pck.cTRPSTS_UD,mcore.common_pck.cTRPSTS_US,mcore.common_pck.cTRPSTS_BP,mcore.common_pck.cTRPSTS_BA,mcore.common_pck.cTRPSTS_BR) THEN
            RETURN TRUE;
        END IF;
        RETURN FALSE;
    END isValidRequestStatus;

    FUNCTION getRequestCurrentStatus(pRequestId requests.id%TYPE)
    RETURN requests.status%TYPE IS
        vRequestStatus requests.status%TYPE;
        myunit CONSTANT VARCHAR2(23) := 'getRequestCurrentStatus';
    BEGIN
        slog.debug(pkgCtxId, myunit, pRequestId);
		IF pRequestId IS NULL THEN
			sspkg.raiseError(cERR_NoExistentRequest, null, pkgCtxId, myunit);
		END IF;
        common_pck.CommonSecurityChecks;

        SELECT status
        INTO vRequestStatus
        FROM VW$USER_REQUESTS
        WHERE REQUEST_ID = pRequestId;

        RETURN vRequestStatus;

    EXCEPTION
        WHEN no_data_found THEN
			slog.error(pkgCtxId, myUnit, cERR_NoExistentRequest, pRequestId);
            sspkg.raiseError(cERR_NoExistentRequest, null, pkgCtxId, myunit);
    END getRequestCurrentStatus;

    FUNCTION isValidStatusTransition(pBasicType VARCHAR2,
                        pPreviousStatus VARCHAR2,
                        pNewStatus VARCHAR2)
    RETURN BOOLEAN
    IS
      myunit CONSTANT VARCHAR2(23) := 'isValidStatusTransition';
    BEGIN
        slog.debug(pkgCtxId, myunit, pBasicType || ':' || pPreviousStatus || ':' || pNewStatus);

        IF pPreviousStatus = mcore.common_pck.cTRPSTS_UD THEN
            RETURN pNewStatus IN (mcore.common_pck.cTRPSTS_US, mcore.common_pck.cTRPSTS_UC, mcore.common_pck.cTRPSTS_BP);
        ELSIF pPreviousStatus = mcore.common_pck.cTRPSTS_US THEN
            RETURN pNewStatus IN (mcore.common_pck.cTRPSTS_UC, mcore.common_pck.cTRPSTS_BP, mcore.common_pck.cTRPSTS_BR);	
        ELSIF pPreviousStatus = mcore.common_pck.cTRPSTS_BP THEN
            RETURN pNewStatus IN (mcore.common_pck.cTRPSTS_BP, mcore.common_pck.cTRPSTS_BA, mcore.common_pck.cTRPSTS_BR);
        END IF;

        IF pBasicType IN (mcore.common_pck.cBRT_ORD,mcore.common_pck.cBRT_REQ,mcore.common_pck.cBRT_TRAN) THEN
           IF pPreviousStatus IS NULL THEN
                RETURN pNewStatus IN (mcore.common_pck.cTRPSTS_UW, mcore.common_pck.cTRPSTS_UD);				
            ELSIF pPreviousStatus = mcore.common_pck.cTRPSTS_UW THEN
                RETURN pNewStatus IN (mcore.common_pck.cTRPSTS_UW, mcore.common_pck.cTRPSTS_UD, mcore.common_pck.cTRPSTS_UC);		
            END IF;
        ELSIF pBasicType = mcore.common_pck.cBRT_WUT THEN
            IF pPreviousStatus IS NULL THEN
                RETURN pNewStatus IN (mcore.common_pck.cTRPSTS_UW, mcore.common_pck.cTRPSTS_VB);
            ELSIF pPreviousStatus = mcore.common_pck.cTRPSTS_UW THEN
                RETURN pNewStatus IN (mcore.common_pck.cTRPSTS_UW, mcore.common_pck.cTRPSTS_VB, mcore.common_pck.cTRPSTS_UC);
            ELSIF pPreviousStatus = mcore.common_pck.cTRPSTS_VB THEN
                RETURN pNewStatus IN (mcore.common_pck.cTRPSTS_UW, mcore.common_pck.cTRPSTS_UD);
            END IF;
        END IF;
        RETURN FALSE;
    END isValidStatusTransition;

    FUNCTION isValidStatusTransition(pPreviousStatus VARCHAR2, pNewStatus VARCHAR2)
    RETURN BOOLEAN IS
    BEGIN
        RETURN isValidStatusTransition(pBasicType => mcore.common_pck.cBRT_ORD,
                                       pPreviousStatus => pPreviousStatus,
                                       pNewStatus => pNewStatus);
    END isValidStatusTransition;

    -- Data manipulation procedures
    FUNCTION CreateNewRequest(
            pReqType_id requests.req_type_id%TYPE,
            pDescription requests.description%TYPE,
            pBlobData requests.blob_data%TYPE,
            pBlobMimetype requests.blob_mimetype%TYPE,
            pTestRecord requests.test_record%TYPE,
            pContent CLOB DEFAULT NULL,
			pData CLOB DEFAULT NULL)
    RETURN requests.id%TYPE
    IS
        vRequestId requests.id%TYPE;
        vContent SYS.XMLTYPE;
        myunit CONSTANT VARCHAR2(16) := 'CreateNewRequest';
		vAccOwner account_owners.id%TYPE := NULL;

		PROCEDURE writeActionLog(
          pLogMessage IN VARCHAR2,
          pRefObject IN VARCHAR2 DEFAULT NULL
        ) IS
        BEGIN
          authorization_pck.writeActionLog(
              pActions => actions_list(mcore.common_pck.cACT_CreateRequest),
              pLogMessage => pReqType_id || ':' ||  pDescription || ':' || pBlobMimetype || ':' || pTestRecord || ':-' || pLogMessage,
              pRefClass => cREF_CLASS|| myunit,
              pRefObject => pRefObject);
        END writeActionLog;

    BEGIN
        slog.debug(pkgCtxId, myunit, pReqType_id || ':' || pDescription || ':' || pBlobMimetype || ':' || pTestRecord);
        common_pck.CommonSecurityChecks;

        IF isValidRequestType(pReqType_id) THEN
        
            IF pContent IS NOT NULL THEN
            
                vContent := XMLTYPE.createXML(pContent); 
                
                IF sspkg.ReadBool(pkgCtxId || '/validateRequestAgainstSchema') THEN
                    IF vContent.isSchemaValid(                           
                            schurl => pReqType_id || '.xsd',
                            elem => NULL) <> 1
                    THEN
                        slog.error(pkgCtxId, myUnit, cERR_ReqDocValidationFailure, pReqType_id || ':' || pDescription || ':' || pBlobMimetype || ':' || pTestRecord);
                        sspkg.raiseError(cERR_ReqDocValidationFailure, null, pkgCtxId, myunit);
                    END IF;
                END IF;
            END IF;
			
			BEGIN
				vAccOwner := mcauth.auth.getPrimaryAccountOwner;
			EXCEPTION
				WHEN OTHERS THEN
					slog.error(pkgCtxId, myUnit, SQLERRM);
			END;

            INSERT INTO requests(id, req_type_id, application_id, user_id, status, description, blob_data, blob_mimetype, test_record, date_created, ph1, content, data)
            VALUES(requests_id_seq.NEXTVAL, pReqType_id, mcauth.auth.getApplicationId, mcauth.auth.getClientId, mcore.common_pck.cTRPSTS_UW, pDescription, pBlobData, pBlobMimetype, pTestRecord, SYSDATE, vAccOwner, vContent, pData)
            RETURNING id INTO vRequestId;

			writeActionLog(
				pLogMessage => 'FINISH',
				pRefObject => vRequestId);

            RETURN vRequestId;
        ELSE
			slog.error(pkgCtxId, myUnit, cERR_NoValidRequestType, pReqType_id || ':' || pDescription || ':' || pBlobMimetype || ':' || pTestRecord);
            sspkg.raiseError(cERR_NoValidRequestType, null, pkgCtxId, myunit);
        END IF;

    EXCEPTION
      WHEN sspkg.sysException THEN
        writeActionLog(
          pLogMessage => sspkg.getErrorMessage,
          pRefObject => NULL);
        RAISE;
      WHEN OTHERS THEN
		slog.error(pkgCtxId, myUnit, pReqType_id || ':' || pDescription ||':' || pBlobMimetype || ':' || pTestRecord || ':' || sqlcode || ':' || sqlerrm);
        writeActionLog(
          pLogMessage => sqlerrm,
          pRefObject => NULL);
        RAISE;
    END CreateNewRequest;

    PROCEDURE UpdateExistingRequest(
            pRequestId requests.id%TYPE,
            pDescription requests.description%TYPE,
            pBlobData requests.blob_data%TYPE,
            pBlobMimetype requests.blob_mimetype%TYPE,
            pTestRecord requests.test_record%TYPE,
            pContent CLOB DEFAULT NULL,
			pData CLOB DEFAULT NULL)
    IS
        myunit CONSTANT VARCHAR2(21) := 'UpdateExistingRequest';
        vRequestCurrentStatus requests.status%TYPE;
        vContent SYS.XMLTYPE; 

		PROCEDURE writeActionLog(
          pLogMessage IN VARCHAR2,
          pRefObject IN VARCHAR2 DEFAULT NULL
        ) IS
        BEGIN
          authorization_pck.writeActionLog(
              pActions => actions_list(mcore.common_pck.cACT_CreateRequest),
              pLogMessage => pRequestId || ':' ||  pDescription || ':' || pBlobMimetype || ':' || pTestRecord || ':-' || pLogMessage,
              pRefClass => cREF_CLASS|| myunit,
              pRefObject => pRefObject);
        END writeActionLog;

    BEGIN
        slog.debug(pkgCtxId, myunit, pRequestId || ':' || pDescription || ':' || pBlobMimetype || ':' || pTestRecord);
		IF pRequestId IS NULL THEN
			sspkg.raiseError(cERR_NoExistentRequest, null, pkgCtxId, myunit);
		END IF;
        common_pck.CommonSecurityChecks;

        vRequestCurrentStatus := getRequestCurrentStatus(pRequestId);
        IF NOT isValidStatusTransition(vRequestCurrentStatus, mcore.common_pck.cTRPSTS_UW) THEN
			slog.error(pkgCtxId, myUnit, cERR_InvalidRequestStatus, pRequestId || ':' || pDescription || ':' || pBlobMimetype || ':' || pTestRecord);
            sspkg.raiseError(cERR_InvalidRequestStatus, null, pkgCtxId, myunit);
        END IF;
        
        vContent := XMLTYPE.createXML(pContent); 

        UPDATE requests
                SET description      = pDescription,
                    blob_data        = pBlobData,
                    blob_mimetype    = pBlobMimetype,
                    test_record      = pTestRecord,
                    date_modified    = sysdate,
                    application_id   = mcauth.auth.getApplicationId,
                    user_id          = mcauth.auth.getClientId,
                    content          = vContent,
					data 			 = pData
        WHERE id = pRequestId;

		writeActionLog(
			pLogMessage => 'FINISH',
			pRefObject => NULL);

    EXCEPTION
      WHEN sspkg.sysException THEN
        writeActionLog(
          pLogMessage => sspkg.getErrorMessage,
          pRefObject => NULL);
        RAISE;
      WHEN OTHERS THEN
		slog.error(pkgCtxId, myUnit, pRequestId || ':' || pDescription ||':' || pBlobMimetype || ':' || pTestRecord || ':' || sqlcode || ':' || sqlerrm);
        writeActionLog(
          pLogMessage => sqlerrm,
          pRefObject => NULL);
        RAISE;

    END UpdateExistingRequest;


    PROCEDURE CancelRequest(pRequestId requests.id%TYPE)
    IS
        myunit CONSTANT VARCHAR2(13) := 'CancelRequest';
        vRequestCurrentStatus requests.status%TYPE;
    BEGIN
        slog.debug(pkgCtxId, myunit, pRequestId);
		IF pRequestId IS NULL THEN
			sspkg.raiseError(cERR_NoExistentRequest, null, pkgCtxId, myunit);
		END IF;
        common_pck.CommonSecurityChecks;

        vRequestCurrentStatus := getRequestCurrentStatus(pRequestId);
        IF NOT isValidStatusTransition(vRequestCurrentStatus, mcore.common_pck.cTRPSTS_UC) THEN
			slog.error(pkgCtxId, myUnit, cERR_InvalidRequestStatus, pRequestId);
            sspkg.raiseError(cERR_InvalidRequestStatus, null, pkgCtxId, myunit);
        END IF;

        UPDATE requests
           SET status = mcore.common_pck.cTRPSTS_UC,
               date_modified = sysdate,
               application_id = mcauth.auth.getApplicationId,
               user_id = mcauth.auth.getClientId
         WHERE id = pRequestId;

    END CancelRequest;

    PROCEDURE MarkRequestAsDone(pRequestId requests.id%TYPE)
    IS
        myunit CONSTANT VARCHAR2(17) := 'MarkRequestAsDone';
        vRequestCurrentStatus requests.status%TYPE;

		PROCEDURE writeActionLog(
          pLogMessage IN VARCHAR2,
          pRefObject IN VARCHAR2 DEFAULT NULL
        ) IS
        BEGIN
          authorization_pck.writeActionLog(
              pActions => actions_list(mcore.common_pck.cACT_CreateRequest),
              pLogMessage => pRequestId || ':-' || pLogMessage,
              pRefClass => cREF_CLASS|| myunit,
              pRefObject => pRefObject);
        END writeActionLog;
    BEGIN
        slog.debug(pkgCtxId, myunit, pRequestId);
		IF pRequestId IS NULL THEN
			sspkg.raiseError(cERR_NoExistentRequest, null, pkgCtxId, myunit);
		END IF;
        common_pck.CommonSecurityChecks;

        vRequestCurrentStatus := getRequestCurrentStatus(pRequestId);
        IF NOT isValidStatusTransition(vRequestCurrentStatus, mcore.common_pck.cTRPSTS_UD) THEN
			slog.error(pkgCtxId, myUnit, cERR_InvalidRequestStatus, pRequestId || ':' || vRequestCurrentStatus || ':' || mcore.common_pck.cTRPSTS_UD);
            sspkg.raiseError(cERR_InvalidRequestStatus, null, pkgCtxId, myunit);
        END IF;

        UPDATE requests
           SET status = mcore.common_pck.cTRPSTS_UD,
               date_modified = sysdate,
               application_id = mcauth.auth.getApplicationId,
               user_id = mcauth.auth.getClientId
         WHERE id = pRequestId;

		writeActionLog(
			pLogMessage => 'FINISH',
			pRefObject => pRequestId);
    EXCEPTION
      WHEN sspkg.sysException THEN
        writeActionLog(
          pLogMessage => sspkg.getErrorMessage,
          pRefObject => NULL);
        RAISE;
      WHEN OTHERS THEN
		slog.error(pkgCtxId, myUnit, pRequestId || ':' || sqlcode || ':' || sqlerrm);
        writeActionLog(
          pLogMessage => sqlerrm,
          pRefObject => pRequestId);
        RAISE;
    END MarkRequestAsDone;

    PROCEDURE MarkRequestAsProcessed(pRequestId IN mcore.requests.id%TYPE, pMarkAsAccepted IN BOOLEAN := FALSE)
    IS
        myunit CONSTANT VARCHAR2(22) := 'MarkRequestAsProcessed';
        vRequestCurrentStatus requests.status%TYPE;
        vNow CONSTANT DATE := SYSDATE;
    BEGIN
        slog.debug(pkgCtxId, myunit, pRequestId || ':' || util.bool2char(pMarkAsAccepted));
        
        IF pRequestId IS NULL THEN
          sspkg.raiseError(cERR_NoExistentRequest, null, pkgCtxId, myunit);
        END IF;
        common_pck.CommonSecurityChecks;

        vRequestCurrentStatus := getRequestCurrentStatus(pRequestId);
        IF NOT isValidStatusTransition(vRequestCurrentStatus, mcore.common_pck.cTRPSTS_BP) THEN
            slog.error(pkgCtxId, myUnit, cERR_InvalidRequestStatus, pRequestId ||':' || util.bool2char(pMarkAsAccepted) || ':' || vRequestCurrentStatus || ':' || mcore.common_pck.cTRPSTS_BP);
            sspkg.raiseError(cERR_InvalidRequestStatus, null, pkgCtxId, myunit);
        END IF;

        slog.debug(pkgCtxId, myunit, util.bool2char(requestRequireSignature) || ':' || vRequestCurrentStatus);
        IF requestRequireSignature AND vRequestCurrentStatus <> mcore.common_pck.cTRPSTS_US THEN          
           slog.debug(pkgCtxId, myunit, 'Request require signature and request not signed!');
           RETURN;
        END IF;

        slog.debug(pkgCtxId, myunit, 'Mark request as BP');
        UPDATE requests
           SET status = mcore.common_pck.cTRPSTS_BP,
			   status_message = null,
			   status_code = null,
               date_modified = vNow,
               application_id = mcauth.auth.getApplicationId,
               user_id = mcauth.auth.getClientId
         WHERE id = pRequestId;

		-- Kao dodatni korak !
		IF pMarkAsAccepted THEN
			IF NOT isValidStatusTransition(mcore.common_pck.cTRPSTS_BP, mcore.common_pck.cTRPSTS_BA) THEN
				slog.error(pkgCtxId, myUnit, cERR_InvalidRequestStatus, pRequestId ||':' || util.bool2char(pMarkAsAccepted) || ':' || mcore.common_pck.cTRPSTS_BP || ':' || mcore.common_pck.cTRPSTS_BA);
				sspkg.raiseError(cERR_InvalidRequestStatus, null, pkgCtxId, myunit);
			END IF;

      		slog.debug(pkgCtxId, myunit, 'Mark request as BA');
			UPDATE 	requests
			SET 	status = mcore.common_pck.cTRPSTS_BA,
					date_processed = vNow
			WHERE 	id = pRequestId;
		END IF;
    END MarkRequestAsProcessed;	
    
    PROCEDURE MarkRequestAsAccepted(pRequestId IN mcore.requests.id%TYPE)
    IS
        myunit CONSTANT VARCHAR2(17) := 'MarkReqAsAccepted';
        vNow CONSTANT DATE := SYSDATE;
		vRequestCurrentStatus requests.status%TYPE;
    BEGIN
    
        slog.debug(pkgCtxId, myunit, pRequestId);
        IF pRequestId IS NULL THEN
          sspkg.raiseError(cERR_NoExistentRequest, null, pkgCtxId, myunit);
        END IF;		
        common_pck.CommonSecurityChecks;
		
		vRequestCurrentStatus := getRequestCurrentStatus(pRequestId);
        IF NOT isValidStatusTransition(vRequestCurrentStatus, mcore.common_pck.cTRPSTS_BA) THEN
            slog.error(pkgCtxId, myUnit, cERR_InvalidRequestStatus, pRequestId ||':' || vRequestCurrentStatus || ':' || mcore.common_pck.cTRPSTS_BA);
            sspkg.raiseError(cERR_InvalidRequestStatus, null, pkgCtxId, myunit);
        END IF;
                
        UPDATE 	requests
        SET 	status = mcore.common_pck.cTRPSTS_BA,
                date_processed = vNow
        WHERE 	id = pRequestId;
            
    END MarkRequestAsAccepted;
    
    PROCEDURE MarkRequestAsRejected(pRequestId IN mcore.requests.id%TYPE)
    IS
        myunit CONSTANT VARCHAR2(17) := 'MarkReqAsRejected';
        vNow CONSTANT DATE := SYSDATE;
		vRequestCurrentStatus requests.status%TYPE;
    BEGIN
    
        slog.debug(pkgCtxId, myunit, pRequestId);        
        IF pRequestId IS NULL THEN
          sspkg.raiseError(cERR_NoExistentRequest, null, pkgCtxId, myunit);
        END IF;
        common_pck.CommonSecurityChecks;
		
		vRequestCurrentStatus := getRequestCurrentStatus(pRequestId);
        IF NOT isValidStatusTransition(vRequestCurrentStatus, mcore.common_pck.cTRPSTS_BR) THEN
            slog.error(pkgCtxId, myUnit, cERR_InvalidRequestStatus, pRequestId ||':' || vRequestCurrentStatus || ':' || mcore.common_pck.cTRPSTS_BR);
            sspkg.raiseError(cERR_InvalidRequestStatus, null, pkgCtxId, myunit);
        END IF;		
        
        UPDATE 	requests
        SET 	status = mcore.common_pck.cTRPSTS_BR,
                date_processed = vNow
        WHERE 	id = pRequestId;
            
    END MarkRequestAsRejected;

    FUNCTION getSumOfSignPercentages(pRequestId requests.id%TYPE)
    RETURN NUMBER IS
       vSumOfSignPercentages NUMBER := 0;
       myunit CONSTANT VARCHAR2(23) := 'getSumOfSignPercentages';
    BEGIN
        slog.debug(pkgCtxId, myunit, pRequestId);
		IF pRequestId IS NULL THEN
			sspkg.raiseError(cERR_NoExistentRequest, null, pkgCtxId, myunit);
		END IF;

        common_pck.CommonSecurityChecks;

        SELECT SUM(pct_signed)
          INTO vSumOfSignPercentages
          FROM signers s
         WHERE s.request_id = pRequestId;

        RETURN NVL(vSumOfSignPercentages,0);
    EXCEPTION
        WHEN no_data_found THEN
            RETURN 0;
    END getSumOfSignPercentages;

    PROCEDURE getRequestBlobData(pRequestId IN requests.id%TYPE,
                                pBlobData OUT requests.blob_data%TYPE,
                                pMimeType OUT requests.blob_mimetype%TYPE)
    IS
        myunit CONSTANT VARCHAR2(18) := 'getRequestBlobData';
    BEGIN
        slog.debug(pkgCtxId, myunit, pRequestId);
		IF pRequestId IS NULL THEN
			sspkg.raiseError(cERR_NoExistentRequest, null, pkgCtxId, myunit);
		END IF;

        common_pck.CommonSecurityChecks;

        SELECT blob_data, blob_mimetype
          INTO pBlobData, pMimeType
          FROM VW$USER_REQUESTS
         WHERE request_id = pRequestId;
    EXCEPTION
      WHEN no_data_found THEN
		slog.error(pkgCtxId, myUnit, cERR_NoExistentRequest, pRequestId);
        sspkg.raiseError(cERR_NoExistentRequest, null, pkgCtxId, myunit);
    END getRequestBlobData;

    PROCEDURE MarkRequestAsSigned(pRequestId mcore.requests.id%TYPE)
    IS
        myunit CONSTANT VARCHAR2(19) := 'MarkRequestAsSigned';

		PROCEDURE writeActionLog(
          pLogMessage IN VARCHAR2,
          pRefObject IN VARCHAR2 DEFAULT NULL
        ) IS
        BEGIN
          authorization_pck.writeActionLog(
              pActions => actions_list(mcore.common_pck.cACT_SignRequest),
              pLogMessage => pRequestId || ':-' || pLogMessage,
              pRefClass => cREF_CLASS|| myunit,
              pRefObject => pRefObject);
        END writeActionLog;

    BEGIN
        slog.debug(pkgCtxId, myunit, pRequestId);
		IF pRequestId IS NULL THEN
			sspkg.raiseError(cERR_NoExistentRequest, null, pkgCtxId, myunit);
		END IF;

        common_pck.CommonSecurityChecks;

        UPDATE mcore.requests
        SET status = mcore.common_pck.cTRPSTS_BP,
            date_signed = sysdate,
            date_modified = sysdate,
            application_id = mcauth.auth.getApplicationId,
            user_id = mcauth.auth.getClientId
        WHERE id = pRequestId;

		writeActionLog(
			pLogMessage => 'FINISH',
			pRefObject => pRequestId);

    EXCEPTION
      WHEN sspkg.sysException THEN
        writeActionLog(
          pLogMessage => sspkg.getErrorMessage,
          pRefObject => pRequestId);
        RAISE;
      WHEN OTHERS THEN
		slog.error(pkgCtxId, myUnit, pRequestId || ':' || sqlcode || ':' || sqlerrm);
        writeActionLog(
          pLogMessage => sqlerrm,
          pRefObject => pRequestId);
        RAISE;

    END MarkRequestAsSigned;

    PROCEDURE MarkForSigning(pRequestId mcore.requests.id%TYPE,
                             pAccountId mcore.bank_accounts.id%TYPE)
    IS
        myunit CONSTANT VARCHAR2(14) := 'MarkForSigning';
        vRequestCurrentStatus mcore.requests.status%TYPE;

        PROCEDURE writeActionLog(
          pLogMessage IN VARCHAR2,
          pRefObject IN VARCHAR2 DEFAULT NULL
        ) IS
        BEGIN
          authorization_pck.writeActionLog(
              pActions => actions_list(mcore.common_pck.cACT_SignRequest),
              pLogMessage => pRequestId || ':' || pAccountId || ':-' || pLogMessage,
              pRefClass => cREF_CLASS|| myunit,
              pRefObject => pRefObject);
        END writeActionLog;

    BEGIN
        slog.debug(pkgCtxId, myunit, pRequestId || ':' || pAccountId);
		IF pRequestId IS NULL THEN
			sspkg.raiseError(cERR_NoExistentRequest, null, pkgCtxId, myunit);
		END IF;
		IF pAccountId IS NULL THEN			
			sspkg.raiseError(cERR_InvalidAccount, null, pkgCtxId, myunit);
		END IF;

        common_pck.CommonSecurityChecks;

        vRequestCurrentStatus := getRequestCurrentStatus(pRequestId);
        -- Check IF status transition is an allowed one
        IF NOT isValidStatusTransition(mcore.common_pck.cBRT_REQ, vRequestCurrentStatus, mcore.common_pck.cTRPSTS_US) THEN
			slog.error(pkgCtxId, myUnit, cERR_InvalidRequestStatus, pRequestId ||':' || pAccountId || ':' || mcore.common_pck.cBRT_REQ || ':' || vRequestCurrentStatus || ':' || mcore.common_pck.cTRPSTS_US);
            sspkg.raiseError(cERR_InvalidRequestStatus, null, pkgCtxId, myunit);
        END IF;

        INSERT INTO mcore.tmp$signing_candidates(request_id, account_id)
        VALUES(pRequestId, pAccountId);

        writeActionLog(
          pLogMessage => 'FINISH',
          pRefObject => pRequestId || ':' || pAccountId);

    EXCEPTION
        WHEN sspkg.sysException THEN
            writeActionLog(
                pLogMessage => sspkg.getErrorMessage,
                pRefObject => pRequestId || ':' || pAccountId);
            RAISE;
        WHEN OTHERS THEN
			slog.error(pkgCtxId, myUnit, pRequestId || ':' || pAccountId || ':' || sqlcode || ':' || sqlerrm);
            writeActionLog(
                pLogMessage => sqlerrm,
                pRefObject => pRequestId || ':' || pAccountId);
            RAISE;
    END MarkForSigning;
	
	FUNCTION getNextReqIDVal RETURN NUMBER
	IS
		 myunit CONSTANT VARCHAR2(30) := 'getNextReqIDVal';
	BEGIN
		slog.debug(pkgCtxId, myunit);
		RETURN requests_id_seq.nextval;
	EXCEPTION
		WHEN OTHERS THEN	
		   slog.error(pkgCtxId, myUnit, sqlcode || ':' || sqlerrm);
	END;
	
	PROCEDURE addUatForRequest(pRequestId requests.id%TYPE, pUatId user_agreement.id%TYPE)
	IS
		 myunit CONSTANT VARCHAR2(30) := 'addUatForRequest';
	BEGIN
	
		slog.debug(pkgCtxId, myunit, pRequestId || ':' || pUatId);
		common_pck.CommonSecurityChecks;
		
		INSERT INTO req_req_type_uat(id, req_type_uat_id, req_id, date_accepted) SELECT req_req_type_uat_seq.nextval, rtu.id, pRequestId, sysdate
		FROM req_type_uat rtu JOIN requests r ON rtu.req_type_id = r.req_type_id 
		WHERE rtu.uat_id = pUatId and r.id = pRequestId;
			
		slog.debug(pkgCtxId, myUnit, 'Values in req_req_type_uat inserted!');
	
	EXCEPTION
		WHEN no_data_found THEN
           slog.error(pkgCtxId, myUnit, common_pck.cERR_InvalidParameterValue, pRequestId || ':' || pUatId);
		   sspkg.raiseError(cERR_InternalError, null, pkgCtxId, myunit);
		WHEN OTHERS THEN
			slog.error(pkgCtxId, myUnit, 'Error: ', pRequestId || ':' || pUatId || ':' || sqlcode || ':' || sqlerrm);
			sspkg.raiseError(cERR_InternalError, null, pkgCtxId, myunit);
	END;
	
	FUNCTION getAllRequestTypeGroups(pRequestType VARCHAR2 := '%')
    RETURN sys_refcursor IS
        myunit CONSTANT VARCHAR2(30) := 'getAllRequestTypeGroups';
        vRequestType request_types.id%TYPE;
        vAccOwnerType account_owners.ph0%TYPE;
        rez sys_refcursor;
    BEGIN
        slog.debug(pkgCtxId, myunit, pRequestType);

        vRequestType := NVL(pRequestType, '%');

      BEGIN
        SELECT ao.ph0
        INTO vAccOwnerType
        FROM mcore.vw$user_acc_owners uao
            JOIN mcore.account_owners ao ON (uao.account_owner_id = ao.id)
        where rownum < 2;
      EXCEPTION
      WHEN no_data_found THEN
        vAccOwnerType := '%';
      END;

      OPEN rez FOR
        SELECT rtg.id, rtg.name, rtg.sort_order, rtg.parent_req_type_group_id, rtg.valid, rtg.ao_type, rtg.application_id, rtg.basic_type, rtg.visible, rtg.req_type_group_struct
        FROM request_type_groups rtg
        WHERE rtg.basic_type LIKE vRequestType
		AND NVL(rtg.application_id, NVL(mcauth.auth.getApplicationId,'%')) = NVL(mcauth.auth.getApplicationId,'%')
		AND NVL(rtg.ao_type, vAccOwnerType) LIKE vAccOwnerType
        AND rtg.valid = 1
        ORDER BY rtg.id ASC;

        RETURN rez;
    END getAllRequestTypeGroups;
		
	FUNCTION getRequest(pRequestId requests.id%TYPE)
    RETURN sys_refcursor IS
        myunit CONSTANT VARCHAR2(30) := 'getRequest';
        rez sys_refcursor;
    BEGIN
        slog.debug(pkgCtxId, myunit, pRequestId);
        common_pck.CommonSecurityChecks;      
        
        OPEN rez FOR
				SELECT request_id, application_id, user_id, status,
					   mcore.common_pck.decodeStatus(status) status_decoded,
					   status_message, status_code, description,
					   date_created,
					   date_signed, date_processed, blob_mimetype,
					   test_record, req_type_id, req_type_description,
					   req_type_basic_type, guarantee_status, data, req_type_struct
				  FROM vw$user_requests
				  WHERE request_id = pRequestId;
				  
        RETURN rez;
    END getRequest;
	
	PROCEDURE CreateReqForVisaPlatinumCard
	IS
		myunit CONSTANT VARCHAR2(30) := 'CreateReqForVisaPlatinumCard';
		vSumInsured VARCHAR2(20);
		vValidUntil DATE;
		vPolicyNumber VARCHAR2(100);
		vJsonTemplate CLOB;
		vName bank_accounts.card_owner%TYPE;
		vRequestId requests.id%TYPE;
		vAccOwnerId bank_accounts.account_owner_id%TYPE;
		vUserId end_users.id%TYPE;
		vRequestJson requests.data%TYPE;
		vValidTo requests.valid_to%TYPE;
		cNotTestRecord CONSTANT NUMBER := 0;
		vPom PLS_INTEGER;
				
		CURSOR cards IS 
			SELECT ad.acc_id, ad2.data_vchar request_id, ba.account_owner_id, ba.card_owner
			FROM account_details ad
			JOIN bank_accounts ba 
			ON (ba.id = ad.acc_id)
			LEFT JOIN account_details ad2 ON (ad.acc_id = ad2.acc_id AND ad2.acc_att_id = common_pck.cACC_ATT_ACTN_SHOW_REQ_DETAILS)
			WHERE ad.acc_att_id = common_pck.cACC_ATT_CARD_PRODUCT_NAME 
			AND ad.data_vchar = cVisaPlatinumCardType
			AND ba.status IN ('A', 'B');
					
	BEGIN 
	
		slog.debug(pkgCtxId, myunit);
		
		vSumInsured := sspkg.readVchar('/Core/Main/Requests/Insurance/IntesaHealthInsuranceVisaPlatinum/sumInsured');
		vValidUntil := sspkg.readDate('/Core/Main/Requests/Insurance/IntesaHealthInsuranceVisaPlatinum/validUntil');
		vPolicyNumber := sspkg.readVchar('/Core/Main/Requests/Insurance/IntesaHealthInsuranceVisaPlatinum/policyNumber');
		vJsonTemplate := sspkg.readVchar('/Core/Main/Requests/Insurance/IntesaHealthInsuranceVisaPlatinum/jsonTemplate');
	
		slog.debug(pkgCtxId, myunit, 'Insured sum:' || vSumInsured ||':'|| 'Valid until:'|| vValidUntil ||':'|| 'Policy number:'|| vPolicyNumber);
		
		FOR card IN cards LOOP 
		
			slog.debug(pkgCtxId, myunit, 'Card owner:' || card.card_owner ||':'|| 'Account owner:'|| card.account_owner_id ||':'|| 'Card acc_id:'||card.acc_id);
			
			BEGIN
				
				BEGIN
					SELECT id INTO vUserId 
					FROM end_users 
					WHERE ph1 = card.account_owner_id
					AND valid = 1;
				EXCEPTION 
					WHEN no_data_found THEN 
						slog.error(pkgCtxId, myUnit, card.account_owner_id || ':' || cERR_UnassignedAccOwner);
						CONTINUE;
					WHEN OTHERS THEN 
						slog.error(pkgCtxId, myUnit, 'Error: ', card.account_owner_id || ':' || sqlcode || ':' || sqlerrm);
						CONTINUE;
				END;
				
				BEGIN 
					SELECT NULL INTO vPom FROM DUAL
					WHERE EXISTS (
						SELECT NULL
						FROM licensemgrrt.lic_used
						WHERE end_users_id = vUserId
						AND modul_id in (common_pck.cMODUL_MOBILE, common_pck.cMODUL_MOBILE_LEGAL));
				EXCEPTION 
					WHEN no_data_found THEN
						slog.error(pkgCtxId, myUnit, cERR_NoLicenseForMobileApp, 'UserId:' || vUserId);
						CONTINUE;
					WHEN OTHERS THEN 
						slog.error(pkgCtxId, myUnit, 'Error: ', vUserId || ':' || sqlcode || ':' || sqlerrm);
						CONTINUE;
				END;

				vRequestJson := REPLACE(REPLACE(REPLACE(REPLACE(vJsonTemplate, '$policyHolder$', card.card_owner), '$policyNo$', vPolicyNumber), '$sumInsured$', vSumInsured), '$validUntil$', to_char(vValidUntil, 'DD.MM.YYYY.'));
				
				IF card.request_id IS NOT NULL THEN 
					BEGIN
						SELECT valid_to INTO vValidTo
						FROM requests 
						WHERE id = card.request_id;
					EXCEPTION 
						WHEN no_data_found THEN 
						slog.error(pkgCtxId, myUnit, card.request_id || ':' || cERR_NoExistentRequest);
						CONTINUE;
					WHEN OTHERS THEN 
						slog.error(pkgCtxId, myUnit, 'Error: ', card.request_id || ':' || sqlcode || ':' || sqlerrm);
						CONTINUE;
					END;
					IF vValidTo < vValidUntil THEN 
					
						INSERT INTO requests(id, req_type_id, application_id, user_id, status, test_record, date_processed, date_modified, date_created, ph1, data, valid_from, valid_to)
						VALUES(requests_id_seq.NEXTVAL, cIntesaHealthInsuranceVP, common_pck.cAPP_MOBILE, vUserId, common_pck.cTRPSTS_BA, cNotTestRecord, SYSDATE, SYSDATE, SYSDATE, card.account_owner_id, vRequestJson, trunc(sysdate), vValidUntil)
						RETURNING id INTO vRequestId;
					
						UPDATE account_details 
						SET data_vchar = vRequestId
						WHERE acc_id = card.acc_id 
						AND acc_att_id = common_pck.cACC_ATT_ACTN_SHOW_REQ_DETAILS;
					END IF;
				ELSE 
					INSERT INTO requests(id, req_type_id, application_id, user_id, status, test_record, date_processed, date_modified, date_created, ph1, data, valid_from, valid_to)
					VALUES(requests_id_seq.NEXTVAL, cIntesaHealthInsuranceVP, common_pck.cAPP_MOBILE, vUserId, common_pck.cTRPSTS_BA, cNotTestRecord, SYSDATE, SYSDATE, SYSDATE, card.account_owner_id, vRequestJson, trunc(sysdate), vValidUntil)
					RETURNING id INTO vRequestId;
					
					INSERT INTO account_details (acc_id, acc_att_id, data_vchar)
					VALUES (card.acc_id, common_pck.cACC_ATT_ACTN_SHOW_REQ_DETAILS, vRequestId);
				END IF;
				COMMIT;
			EXCEPTION
				WHEN OTHERS THEN
					slog.error(pkgCtxId, myUnit, 'Error: ', card.acc_id || ':' || sqlcode || ':' || sqlerrm);
					ROLLBACK;
			END;
		END LOOP;	

	END;

	FUNCTION getRequest(pReqTypeId request_types.id%TYPE, pRequestId requests.id%TYPE DEFAULT NULL)
	RETURN sys_refcursor
	IS 
		myunit CONSTANT VARCHAR2(30) := 'getRequest';
		vParseJSONApi VARCHAR2(4000);
		vInsuranceData sys_refcursor;
		vData CLOB;
		vDatum DATE := TRUNC(SYSDATE);
		vEmptyDataRefcursor sys_refcursor;
		vPrimaryAccountOwnerId end_users.ph1%TYPE := mcauth.auth.getPrimaryAccountOwner;
		vClientId mcauth.client.id%TYPE := mcauth.auth.getClientId;
	BEGIN  
		
		slog.debug(pkgCtxId, myunit, pReqTypeId || ':' || pRequestId || ':' || vClientId ||  ':' || vPrimaryAccountOwnerId);
		
        common_pck.CommonSecurityChecks;
		
		vParseJSONApi := sspkg.ReadVchar('/Core/Main/Requests/Insurance/IntesaAccidentInsurance/ParseJSONApi');
		slog.debug(pkgCtxId, myunit, 'vParseJSONApi:' || vParseJSONApi);
				
		IF vParseJSONApi IS NULL THEN
			sspkg.raiseError('/Core/Main/Requests/Insurance/IntesaAccidentInsurance/err/missingParseJSONApi', NULL, pkgCtxId, myunit);
		END IF;
		
		IF pRequestId IS NOT NULL THEN 
		   
			BEGIN
				SELECT data INTO vData
				FROM requests
				WHERE id = pRequestId
				AND user_id = vClientId
				AND ph1 = vPrimaryAccountOwnerId
				AND valid_from <= vDatum 
				AND valid_to >= vDatum;
			EXCEPTION 
				WHEN no_data_found THEN 
					slog.error(pkgCtxId, myUnit, cERR_NoExistentRequest, pRequestId);
					sspkg.raiseError(pkgCtxId, myUnit, cERR_NoExistentRequest);
				WHEN OTHERS THEN 
					slog.error(pkgCtxId, myUnit, 'Request id' || ':' ||  pRequestId || ' ' || 'Error:' || sqlcode || ':' || sqlerrm);
					sspkg.raiseError(pkgCtxId, myUnit, cERR_InternalError);	
			END;
		ELSE 
			BEGIN 
				SELECT r.data INTO vData 
				FROM (
				SELECT data, valid_from, valid_to 
				FROM requests 
				WHERE req_type_id = pReqTypeId
				AND user_id = vClientId
				AND ph1 = vPrimaryAccountOwnerId
				ORDER BY id DESC
				FETCH FIRST 1 ROW ONLY) r
				WHERE r.valid_from <= vDatum 
				AND r.valid_to >= vDatum;
			EXCEPTION 
				WHEN no_data_found THEN 
					OPEN vEmptyDataRefcursor FOR
					SELECT NULL AS policyNumber,
					NULL AS insuranceStartDate,
					NULL AS insuranceEndDate,
					NULL AS yearlyPremiumPaidUntil,
					NULL AS yearlyPremiumPaymentDueDate,
					NULL AS premiumAmount
					FROM DUAL 
					WHERE ROWNUM < 1;
					RETURN vEmptyDataRefcursor;
				WHEN OTHERS THEN 
					slog.error(pkgCtxId, myUnit, pReqTypeId || ':' || vClientId || ':' || vPrimaryAccountOwnerId || ':' || sqlcode || ':' || sqlerrm);
					sspkg.raiseError(pkgCtxId, myUnit, cERR_InternalError);
			END;
		END IF; 
		
		execute immediate vParseJSONApi using vData, out vInsuranceData;
		
		RETURN vInsuranceData;
	END;

END;
/
show error

