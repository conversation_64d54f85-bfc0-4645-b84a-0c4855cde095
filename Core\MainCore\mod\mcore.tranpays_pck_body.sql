PROMPT Creating package body script for package TRANPAYS_PCK
CREATE OR REPLACE
PACKAGE BODY mcore.tranpays_pck
IS

    -- Sistem constants
    cREF_CLASS CONSTANT VARCHAR2(19) := 'MCORE.TRANPAYS_PCK.';

	MSG VARCHAR2(512);
    ERROR VARCHAR2(1);
    MSG_TYPE VARCHAR2(3);
    MSGID INTEGER;
    LOC VARCHAR2(2000 BYTE);


/*
/Core/Main/TranPays/err/invalidMunicipalityCode
  19

*/
    cERR_InvalidStatus          CONSTANT VARCHAR2(37) := pkgCtxId || '/err/InvalidStatus';
    cERR_NoRequestType          CONSTANT VARCHAR2(37) := pkgCtxId || '/err/NoRequestType';
    cERR_InvalidTranpay         CONSTANT VARCHAR2(38) := pkgCtxId || '/err/InvalidTranpay';
    cERR_NoValidRequestType     CONSTANT VARCHAR2(42) := pkgCtxId || '/err/NoValidRequestType';
    cERR_InvalidTranpayValue    CONSTANT VARCHAR2(43) := pkgCtxId || '/err/InvalidTranpayValue';
    cERR_InvalidTranpayGroup    CONSTANT VARCHAR2(43) := pkgCtxId || '/err/InvalidTranpayGroup';
    cERR_InvalidTranpayCurrency CONSTANT VARCHAR2(46) := pkgCtxId || '/err/InvalidTranpayCurrency';
    cERR_InvalidCOA             CONSTANT VARCHAR2(34) := pkgCtxId || '/err/InvalidCOA';
    cERR_InvalidSchedule        CONSTANT VARCHAR2(39) := pkgCtxId || '/err/InvalidSchedule';
    cERR_InvalidBalance         CONSTANT VARCHAR2(38) := pkgCtxId || '/err/InvalidBalance';

    cERR_MaxTranpayValueExceeded    CONSTANT VARCHAR2(60) := pkgCtxId || '/err/MaximumTranpayValueExceeded';
    cERR_SchedulePeriodExceeded     CONSTANT VARCHAR2(60) := pkgCtxId || '/err/SchedulePeriodExceeded';
    cERR_invalidBIC                 CONSTANT VARCHAR2(60) := pkgCtxId || '/err/invalidBIC';
    cERR_RequiredAttributeMissing   CONSTANT VARCHAR2(60) := pkgCtxId || '/err/RequiredAttributeMissing';
    cERR_InvTrnsfDestAccount        CONSTANT VARCHAR2(60) := pkgCtxId || '/err/InvalidTransferDestinationAccount';
    cERR_InvalidTranpayAttribute    CONSTANT VARCHAR2(60) := pkgCtxId || '/err/InvalidTranpayAttribute';
    cERR_MissingTranpayDescription  CONSTANT VARCHAR2(60) := pkgCtxId || '/err/NoTranpayDescription';


    cERR_NoAccount                  CONSTANT VARCHAR2(33) := pkgCtxId || '/err/NoAccount';
	cERR_InvalidAccount				CONSTANT VARCHAR2(38) := pkgCtxId || '/err/InvalidAccount';
    cERR_InvalidTranpayTemplate     CONSTANT VARCHAR2(46) := pkgCtxId || '/err/InvalidTranpayTemplate';
    cERR_InvTranpayTemplatePackage  CONSTANT VARCHAR2(53) := pkgCtxId || '/err/invalidTranpayTemplatePackage';
    cERR_VariousTrpTmplReqTypes     CONSTANT VARCHAR2(58) := pkgCtxId || '/err/VariousTranpayTemplateRequestTypes';
    cERR_NoTrpTmplForGivenPackage   CONSTANT VARCHAR2(56) := pkgCtxId || '/err/NoTranpayTemplateForGivenPackage';

    cERR_InvalidMunicipalityCode    CONSTANT VARCHAR2(47) := pkgCtxId || '/err/invalidMunicipalityCode';
    cERR_InvalidRevenueType         CONSTANT VARCHAR2(42) := pkgCtxId || '/err/invalidRevenueType';

    cERR_NoCreatePrivilege          CONSTANT VARCHAR2(41) := pkgCtxId || '/err/NoCreatePrivilege';
    cERR_CannotFinishTranpay        CONSTANT VARCHAR2(43) := pkgCtxId || '/err/CannotFinishTranpay';
    cERR_TmplWithGivenNameExists    CONSTANT VARCHAR2(51) := pkgCtxId || '/err/TemplateWithGivenNameExists';
    cERR_DupValOnTempPackageName    CONSTANT VARCHAR2(52) := pkgCtxId || '/err/DuplicateTemplatePackageName';

    cERR_BoundTemplatePackage       CONSTANT VARCHAR2(44) := pkgCtxId || '/err/BoundTemplatePackage';

	cERR_InvalidRefCode				CONSTANT VARCHAR2(38) := pkgCtxId || '/err/InvalidRefCode';
	cERR_NoSignPrivilege    		CONSTANT VARCHAR2(39) := pkgCtxId || '/err/NoSignPrivilege';
	cERR_invalidReqType     		CONSTANT VARCHAR2(38) := pkgCtxId || '/err/invalidReqType';
	cERR_noRequiredAuthorization    CONSTANT VARCHAR2(47) := pkgCtxId || '/err/noRequiredAuthorization';
	cERR_unableToRetrieveTranpay    CONSTANT VARCHAR2(47) := pkgCtxId || '/err/unableToRetrieveTranpay';
	cERR_internalError              CONSTANT VARCHAR2(37) := pkgCtxId || '/err/internalError';
	cERR_CoreDataValidationError	CONSTANT VARCHAR2(47) := pkgCtxId || '/err/CoreDataValidationError';
    cERR_SendAndRecAccEquality	    CONSTANT VARCHAR2(45) := pkgCtxId || '/err/SendAndRecAccEquality';
	cERR_unblCrtSchdlWithAddTrnpys	CONSTANT VARCHAR2(55) := pkgCtxId || '/err/unblToCreateSchdleWthAddTrnpys';
	cERR_InvalidIprClient  			CONSTANT VARCHAR2(100) := '/Core/Main/InvoiceManagement/err/InvalidInvoiceProviderClient';
	cERR_InvalidGranteePermissionName CONSTANT VARCHAR2(100) := '/Core/Main/Grantees/err/invalidGranteePermissionName';
	cERR_UniqueTranpayGroupTypeError CONSTANT VARCHAR2(80) := '/Core/Main/Grantees/err/uniqueTranpayGroupTypeError';
	
	cTRANPAY_TYPE_ATT CONSTANT mcore.attachments.service%TYPE := 'Tranpay';

    vDisableInsertTriggers BOOLEAN := FALSE;

	vEnforceBICCode         BOOLEAN;
    vEnforceRevenueType     BOOLEAN;
    vEnforceMuncipalityCode BOOLEAN;

    TYPE tranpay_attrib_rec IS RECORD(
        description     tranpay_attribs.description%TYPE,
        basic_datatype  tranpay_attribs.basic_datatype%TYPE,
        maxsize         tranpay_attribs.maxsize%TYPE,
        value_required  tranpay_attribs.value_required%TYPE,
        blob_mimetype   tranpay_attribs.blob_mimetype%TYPE,
        regexp          tranpay_attribs.regexp%TYPE);

    -- FORWARD DECLARATIONS
	FUNCTION isValidGCBValue(pTableName IN VARCHAR2,
                                 pValue IN VARCHAR2)
    RETURN PLS_INTEGER;

    PROCEDURE validateRevenueType( pRequestTypeId  IN tranpays.req_type_id%TYPE,
                                  pAttribId       IN tranpay_details.attrib_id%TYPE,
                                  pDataVCHAR      IN tranpay_details.data_vchar%TYPE);

    PROCEDURE validateMunicipalityCode(pRequestTypeId  IN tranpays.req_type_id%TYPE,
                                      pAttribId       IN tranpay_details.attrib_id%TYPE,
                                      pDataVCHAR      IN tranpay_details.data_vchar%TYPE);

    -- Internal function. DO not expose to public
    PROCEDURE AppendBackgroundDetails(pTranpayId IN tranpays.id%TYPE,
                                       pReqTypeId IN tranpays.req_type_id%TYPE,
                                       pAccountId IN tranpays.account_id%TYPE);

	-- Internal procedure. DO NOT EXPOSE TO PUBLIC. Use AppendTranpayTemplateDetail instead
    PROCEDURE AppendTranpayTemplateDetailInt(pTranpayTemplateId tranpay_template_details.tranpay_template_id%TYPE,
                                        pAttributeReqTypeId tranpay_template_details.tranpay_attrib_req_type_id%TYPE,
                                        pAttributeId tranpay_template_details.tranpay_attrib_id%TYPE,
                                        pTranpayTemplateDetDesc tranpay_template_details.description%TYPE,
                                        pTranpayTemplateDetDataVChar tranpay_template_details.data_vchar%TYPE,
                                        pTranpayTemplateDetDataBlob tranpay_template_details.data_blob%TYPE);

    -- Internal procedure. DO NOT EXPOSE TO PUBLIC. Use AppendTranPayDetail instead
    PROCEDURE AppendTranPayDetailInt(pTranpayId        tranpays.id%TYPE,
                                  pRequestTypeId    tranpays.req_type_id%TYPE,
                                  pAttribId         tranpay_details.attrib_id%TYPE,
                                  pDescription      tranpay_details.description%TYPE,
                                  pDataVCHAR        tranpay_details.data_vchar%TYPE,
                                  pDataBLOB         tranpay_details.data_blob%TYPE);

	-- Internal procedure. DO NOT EXPOSE TO PUBLIC. Use CheckExistingTranpayData instead
	PROCEDURE CheckExistingTranpayDataINT(
		pTranpayId tranpays.id%TYPE,
		pRequestTypeId tranpays.req_type_id%TYPE,
		pTranval tranpays.tranval%TYPE,
		pTranvalCurrencyId tranpays.tranval_currency_id%TYPE,
		pTranpayGroupId tranpays.tranpay_group_id%TYPE,
		pChartOfAccountsId tranpays.chart_of_accounts_id%TYPE,
		pScheduleId tranpays.schedule_id%TYPE);

	PROCEDURE setTranpayDetailVCharData(pTranpayId tranpay_details.tranpay_id%TYPE, pReqTypeId IN tranpay_details.req_type_id%TYPE, pAttribId IN tranpay_details.attrib_id%TYPE, pDataVChar IN tranpay_details.data_vchar%TYPE);

	FUNCTION getTranpayValueInDomCur(pTranpayId IN tranpays.id%TYPE)
    RETURN NUMBER;

	-- PROCEDURE RegisterSplunkNotification(pTranpayId IN tranpays.id%TYPE, pAccountId IN bank_accounts.id%TYPE, pTranpayAmount IN NUMBER, pTranpayCurrency IN VARCHAR2, pActionId IN VARCHAR2);
	PROCEDURE RegisterSplunkNotification(pTranpayId IN tranpays.id%TYPE, pActionId IN VARCHAR2);

    PROCEDURE disableInsertTriggers
    IS
    BEGIN
        vDisableInsertTriggers := TRUE;
    END disableInsertTriggers;

    PROCEDURE enableInsertTriggers
    IS
    BEGIN
        vDisableInsertTriggers := FALSE;
    END enableInsertTriggers;

    FUNCTION isInsertTriggerDisabled
    RETURN BOOLEAN
    IS
    BEGIN
        RETURN vDisableInsertTriggers;
    END isInsertTriggerDisabled;
	
	FUNCTION hasAdditionalTranpays(pTranpayId tranpays.id%TYPE) 
	RETURN BOOLEAN
	IS 
		myunit CONSTANT VARCHAR2(30) := 'hasAdditionalTranpays';
		vAdditionalTranpay VARCHAR2(1);
		cHasAdditionalTranpays CONSTANT VARCHAR2(1) := 'T';
	BEGIN 
		slog.debug(pkgCtxId, myUnit);
	--potrebno provjeriti da li će se detalj upisivati samo ako postoji, tj. da li će uvijek biti T
		SELECT null INTO vAdditionalTranpay FROM DUAL WHERE EXISTS (SELECT NULL FROM tranpay_details where attrib_id = common_pck.cTRANPAY_ATT_ADDITIONAL_TRNPY AND tranpay_id = pTranpayId AND data_vchar = cHasAdditionalTranpays);
		RETURN TRUE;	
	EXCEPTION 
		WHEN no_data_found THEN 
			RETURN FALSE;
	END;

    PROCEDURE CheckStatusTransition(pTranpayId tranpays.id%TYPE, pNewStatus VARCHAR2)
    IS
        myunit CONSTANT VARCHAR2(21) := 'CheckStatusTransition';

        vRequestType tranpays.req_type_id%TYPE;
        vBasicType request_types.basic_type%TYPE;
        vTranpayCurrentStatus tranpays.status%TYPE;

    BEGIN
        slog.debug(pkgCtxId, myUnit, pTranpayId || ':' || pNewStatus);
        common_pck.CommonSecurityChecks;

        getTranpayCurrentStatus(pTranpayId => pTranpayId,
                                pRequestType    => vRequestType,
                                pTranpayStatus  => vTranpayCurrentStatus);

        slog.debug(pkgCtxId, myUnit, 'Current status for  ' || pTranpayId || ':' || vRequestType || ':' || vTranpayCurrentStatus);

        vBasicType := requests_pck.getRequestBasicType(vRequestType);
        slog.debug(pkgCtxId, myUnit, 'Basic type for ' || pTranpayId || ':' || vBasicType);

        IF NOT requests_pck.isValidStatusTransition(vBasicType, vTranpayCurrentStatus, pNewStatus) THEN
            slog.error(pkgCtxId, myUnit, cERR_InvalidStatus, pTranpayId || ':' || pNewStatus || ':' || vBasicType || ':' || vTranpayCurrentStatus);
            sspkg.raiseError(cERR_InvalidStatus, null, pkgCtxId, myunit);
        END IF;

        slog.debug(pkgCtxId, myUnit, 'Transition for ' || pTranpayId || ' valid');
    END CheckStatusTransition;

    PROCEDURE CheckMandatoryTranpayData(pRequestTypeId tranpays.req_type_id%TYPE)
    IS
        myunit CONSTANT VARCHAR2(25) := 'CheckMandatoryTranpayData';
    BEGIN
        slog.debug(pkgCtxId, myUnit, pRequestTypeId);

        -- Handle situation where request type was not specified
        IF pRequestTypeId IS NULL THEN
            sspkg.raiseError(cERR_NoRequestType, null, pkgCtxId, myunit);
        END IF;

        -- Handle situation where invalid request type was specified
        IF NOT requests_pck.isValidRequestType(pRequestTypeId) THEN
            slog.error(pkgCtxId, myUnit, cERR_NoValidRequestType, pRequestTypeId);
            sspkg.raiseError(cERR_NoValidRequestType, null, pkgCtxId, myunit);
        END IF;

    END CheckMandatoryTranpayData;

    PROCEDURE CheckOptionalTranpayData(pTranval tranpays.tranval%TYPE,
        pTranvalCurrencyId tranpays.tranval_currency_id%TYPE,
        pTranpayGroupId tranpays.tranpay_group_id%TYPE,
        pChartOfAccountsId tranpays.chart_of_accounts_id%TYPE,
        pScheduleId tranpays.schedule_id%TYPE)
    IS
        myunit CONSTANT VARCHAR2(24) := 'CheckOptionalTranpayData';

    BEGIN
        slog.debug(pkgCtxId, myUnit, pTranval || ':' || pTranvalCurrencyId || ':' || pTranpayGroupId || ':' || pChartOfAccountsId || ':' || pScheduleId);

        -- Tranval has to be > 0
        IF pTranval IS NOT NULL AND pTranval <= 0 THEN
            slog.error(pkgCtxId, myUnit, cERR_InvalidTranpayValue, pTranval);
            sspkg.raiseError(cERR_InvalidTranpayValue, null, pkgCtxId, myunit);
        END IF;

        -- Tranval max length IS NUMBER(14,4) => 10 digits + 4 decimals
        IF pTranval IS NOT NULL AND pTranval >= *********** THEN
            slog.error(pkgCtxId, myUnit, cERR_MaxTranpayValueExceeded, pTranval);
            sspkg.raiseError(cERR_MaxTranpayValueExceeded, null, pkgCtxId, myunit);
        END IF;

        -- Tranval currency has to be valid
        IF pTranvalCurrencyId IS NOT NULL AND NOT conversion_pck.isValidCurrency(pTranvalCurrencyId) THEN
            slog.error(pkgCtxId, myUnit, cERR_InvalidTranpayCurrency, pTranvalCurrencyId);
            sspkg.raiseError(cERR_InvalidTranpayCurrency, null, pkgCtxId, myunit);
        END IF;

        -- If tranpay group is specified, it has to be valid
        IF pTranpayGroupId IS NOT NULL AND NOT isValidTranpayGroup(pTranpayGroupId) THEN
            slog.error(pkgCtxId, myUnit, cERR_InvalidTranpayGroup, pTranpayGroupId);
            sspkg.raiseError(cERR_InvalidTranpayGroup, null, pkgCtxId, myunit);
        END IF;

        -- If charts of accounts is specified, it has to be valid
        IF pChartOfAccountsId IS NOT NULL AND NOT isValidCOA(pChartOfAccountsId) THEN
            slog.error(pkgCtxId, myUnit, cERR_InvalidCOA, pChartOfAccountsId);
            sspkg.raiseError(cERR_InvalidCOA, null, pkgCtxId, myunit);
        END IF;

        -- If schedule is specified, it has to be valid
        IF pScheduleId IS NOT NULL AND NOT isValidSchedule(pScheduleId) THEN
            slog.error(pkgCtxId, myUnit, cERR_InvalidSchedule, pScheduleId);
            sspkg.raiseError(cERR_InvalidSchedule, null, pkgCtxId, myunit);
        END IF;

    END CheckOptionalTranpayData;

    PROCEDURE CheckExistingTranpayData(pTranpayId tranpays.id%TYPE)
    IS
        myunit CONSTANT VARCHAR2(24) := 'CheckExistingTranpayData';

        vRequestTypeId tranpays.req_type_id%TYPE;
        vTranval tranpays.tranval%TYPE;
        vTranvalCurrencyId tranpays.tranval_currency_id%TYPE;
        vTranpayGroupId tranpays.tranpay_group_id%TYPE;
        vChartOfAccountsId tranpays.chart_of_accounts_id%TYPE;
        vScheduleId tranpays.schedule_id%TYPE;

    BEGIN
        slog.debug(pkgCtxId, myUnit, pTranpayId);

        SELECT req_type_id, tranval, tranval_currency_id, tranpay_group_id, chart_of_accounts_id, schedule_id
          INTO vRequestTypeId, vTranval, vTranvalCurrencyId, vTranpayGroupId, vChartOfAccountsId, vScheduleId
          FROM tranpays
         WHERE id = pTranpayId;

        slog.debug(pkgCtxId, myUnit, 'Found data for ' || pTranpayId);

        CheckMandatoryTranpayData(pRequestTypeId => vRequestTypeId);

        CheckOptionalTranpayData(
                pTranval => vTranval,
                pTranvalCurrencyId => vTranvalCurrencyId,
                pTranpayGroupId => vTranpayGroupId,
                pChartOfAccountsId => vChartOfAccountsId,
                pScheduleId => vScheduleId);

    EXCEPTION
        WHEN no_data_found THEN
            slog.error(pkgCtxId, myUnit, cERR_InvalidTranpay, pTranpayId);
            sspkg.raiseError(cERR_InvalidTranpay, null, pkgCtxId, myunit);
    END CheckExistingTranpayData;

	-- INTERNAL
	PROCEDURE CheckExistingTranpayDataINT(
		pTranpayId tranpays.id%TYPE,
		pRequestTypeId tranpays.req_type_id%TYPE,
		pTranval tranpays.tranval%TYPE,
		pTranvalCurrencyId tranpays.tranval_currency_id%TYPE,
		pTranpayGroupId tranpays.tranpay_group_id%TYPE,
		pChartOfAccountsId tranpays.chart_of_accounts_id%TYPE,
		pScheduleId tranpays.schedule_id%TYPE)
    IS
        myunit CONSTANT VARCHAR2(27) := 'CheckExistingTranpayDataINT';
    BEGIN
        slog.debug(pkgCtxId, myUnit, pTranpayId);

        CheckMandatoryTranpayData(pRequestTypeId => pRequestTypeId);

        CheckOptionalTranpayData(
                pTranval => pTranval,
                pTranvalCurrencyId => pTranvalCurrencyId,
                pTranpayGroupId => pTranpayGroupId,
                pChartOfAccountsId => pChartOfAccountsId,
                pScheduleId => pScheduleId);
    END CheckExistingTranpayDataINT;

    PROCEDURE CheckNewTranpayData(
        pRequestTypeId tranpays.req_type_id%TYPE,
        pTranval tranpays.tranval%TYPE,
        pTranvalCurrencyId tranpays.tranval_currency_id%TYPE,
        pTranpayGroupId tranpays.tranpay_group_id%TYPE,
        pChartOfAccountsId tranpays.chart_of_accounts_id%TYPE,
        pScheduleId tranpays.schedule_id%TYPE)
    IS
    BEGIN
      CheckMandatoryTranpayData(pRequestTypeId => pRequestTypeId);
      CheckOptionalTranpayData(
        pTranval           => pTranval,
        pTranvalCurrencyId => pTranvalCurrencyId,
        pTranpayGroupId    => pTranpayGroupId,
        pChartOfAccountsId => pChartOfAccountsId,
        pScheduleId        => pScheduleId);

    END CheckNewTranpayData;

    FUNCTION getSumOfSignPercentages(pTranpayId tranpays.id%TYPE)
    RETURN NUMBER IS
       vSumOfSignPercentages NUMBER := 0;
       myunit CONSTANT VARCHAR2(23) := 'getSumOfSignPercentages';
    BEGIN
        slog.debug(pkgCtxId, myUnit, pTranpayId);
        common_pck.CommonSecurityChecks;

        SELECT SUM(asd.pct_signed)
          INTO vSumOfSignPercentages
          FROM vw$all_signature_data asd
         WHERE asd.tranpay_id = pTranpayId;

        slog.debug(pkgCtxId, myUnit, 'Got ' || NVL(vSumOfSignPercentages,0));

        RETURN NVL(vSumOfSignPercentages,0);
    EXCEPTION
        WHEN no_data_found THEN
            slog.debug(pkgCtxId, myUnit, 'No data found for ' || pTranpayId);
            RETURN 0;
    END getSumOfSignPercentages;

    FUNCTION getFirstSchedule(pTranpayId tranpays.id%TYPE)
    RETURN DATE IS
        myunit CONSTANT VARCHAR2(16) := 'getFirstSchedule';
        vSchedulerId tranpays.schedule_id%TYPE;

        vExecuteAt DATE;
        vLastDay DATE;
        vCurrentDate DATE := SYSDATE;
        vNextWeekday PLS_INTEGER;

        TYPE vSchedulerRec IS RECORD (
          schedule_type_type schedules.schedule_type_type%TYPE,
          not_before schedules.not_before%TYPE,
          not_after schedules.not_after%TYPE,
          hour schedules.ph0%TYPE,
          minute schedules.ph1%TYPE,
          day schedules.ph2%TYPE,
          month schedules.ph3%TYPE,
          year schedules.ph4%TYPE);

        vScheduler vSchedulerRec;

        FUNCTION attachTimeToDate(pDatum DATE, pHour PLS_INTEGER, pMinute PLS_INTEGER)
        RETURN DATE IS
        BEGIN
            RETURN TRUNC(pDatum) + (60 * NVL(pHour,0)  + NVL(pMinute,0)) / 1440;
        END attachTimeToDate;

        FUNCTION canExecuteToday(pHour schedules.ph0%TYPE, pMinute schedules.ph1%TYPE)
        RETURN BOOLEAN
        IS
        BEGIN
            RETURN SYSDATE < attachTimeToDate(SYSDATE, pHour, pMinute);
        END canExecuteToday;


    BEGIN
        slog.debug(pkgCtxId, myUnit, pTranpayId);
        common_pck.CommonSecurityChecks;

        BEGIN
        SELECT t.schedule_id
          INTO vSchedulerId
          FROM tranpays t
         WHERE t.id = pTranpayId;
         slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Assigned schedule with ID ' || vSchedulerId);
        EXCEPTION
            WHEN no_data_found THEN
                slog.error(pkgCtxId, myUnit, cERR_InvalidTranpay, pTranpayId);
                sspkg.raiseError(cERR_InvalidTranpay, null, pkgCtxId, myunit);
        END;

        IF vSchedulerId IS NULL THEN
        --  If no schedule attached, then execute tranpay immediately
            slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': No schedule assigned! Run immediate!');
            RETURN SYSDATE;
        END IF;

        BEGIN
        SELECT schedule_type_type,
               s.not_before not_before, s.not_after not_after,
               TO_NUMBER(NVL(ph0,TO_CHAR(vCurrentDate,'HH24'))) hour,
               TO_NUMBER(NVL(ph1,TO_CHAR(vCurrentDate,'MI'))) minute,
               TO_NUMBER(NVL(ph2,TO_CHAR(vCurrentDate,'DD'))) day,
               TO_NUMBER(NVL(ph3,TO_CHAR(vCurrentDate,'MM'))) month,
               TO_NUMBER(NVL(ph4,TO_CHAR(vCurrentDate,'YYYY'))) year
          INTO vScheduler
          FROM schedules s
         WHERE s.id = vSchedulerId
           AND s.active = 1;

        slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Schedule ' || vSchedulerId
            || ' : Hour ' || vScheduler.hour);
        slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Schedule ' || vSchedulerId
            || ' : Minute ' || vScheduler.minute);
        slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Schedule ' || vSchedulerId
            || ' : Day ' || vScheduler.day);
        slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Schedule ' || vSchedulerId
            || ' : Month ' || vScheduler.month);
        slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Schedule ' || vSchedulerId
            || ' : Year ' || vScheduler.year);

        EXCEPTION
            WHEN no_data_found THEN
                slog.error(pkgCtxId, myUnit, cERR_InvalidSchedule, pTranpayId || ':' || vSchedulerId);
                sspkg.raiseError(cERR_InvalidSchedule, null, pkgCtxId, myunit);
        END;

        IF vScheduler.schedule_type_type = common_pck.cSCHDTYPE_IMMEDIATE THEN
            vExecuteAt := vCurrentDate;
            slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Schedule ' || vSchedulerId || ' : ' || vScheduler.schedule_type_type || ' : Execute at ' || TO_CHAR(vExecuteAt, common_pck.cDATETIME_MASK));

        ELSIF vScheduler.schedule_type_type = common_pck.cSCHDTYPE_LATER THEN
            vExecuteAt := TO_DATE(vScheduler.day  || '.'|| vScheduler.month || '.' || vScheduler.year || ' ' || vScheduler.hour || ':' || vScheduler.minute, 'DD.MM.YYYY HH24:MI');
            slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Schedule ' || vSchedulerId || ' : '  || vScheduler.schedule_type_type
                || ' : Execute at ' || TO_CHAR(vExecuteAt, common_pck.cDATETIME_MASK));
        ELSIF vScheduler.schedule_type_type = common_pck.cSCHDTYPE_DAILY THEN

            vExecuteAt := attachTimeToDate(vCurrentDate, vScheduler.hour, vScheduler.minute);
            slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Schedule ' || vSchedulerId || ' : ' || vScheduler.schedule_type_type
                || ' : Execute at ' || TO_CHAR(vExecuteAt, common_pck.cDATETIME_MASK));
            slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Schedule ' || vSchedulerId || ' : ' || vScheduler.schedule_type_type
                || ' : Current date ' || TO_CHAR(vCurrentDate, common_pck.cDATETIME_MASK));
            slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Schedule ' || vSchedulerId || ' : ' || vScheduler.schedule_type_type
                || ' : Not before ' || TO_CHAR(vScheduler.not_before, common_pck.cDATETIME_MASK));

            IF vCurrentDate > vExecuteAt THEN
                vExecuteAt := vExecuteAt + 1;
                slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Schedule ' || vSchedulerId || ' : ' || vScheduler.schedule_type_type
                    || ' : Execute at ' || TO_CHAR(vExecuteAt, common_pck.cDATETIME_MASK));
            END IF;

            IF vExecuteAt < vScheduler.not_before THEN
                vExecuteAt := attachTimeToDate(vScheduler.not_before, vScheduler.hour, vScheduler.minute);
                slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Schedule ' || vSchedulerId || ' : ' || vScheduler.schedule_type_type
                    || ' : Execute at ' || TO_CHAR(vExecuteAt, common_pck.cDATETIME_MASK));
            END IF;

        ELSIF vScheduler.schedule_type_type = common_pck.cSCHDTYPE_WEEKLY THEN
            slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Schedule ' || vSchedulerId || ' : ' || vScheduler.schedule_type_type
                || ' : Current date ' || TO_CHAR(vCurrentDate, common_pck.cDATETIME_MASK));
            slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Schedule ' || vSchedulerId || ' : ' || vScheduler.schedule_type_type
                || ' : Not before ' || TO_CHAR(vScheduler.not_before, common_pck.cDATETIME_MASK));

            -- Determine the next day in week
            IF vScheduler.not_before IS NOT NULL AND vCurrentDate < vScheduler.not_before THEN
                vCurrentDate := vScheduler.not_before;
                slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Schedule ' || vSchedulerId || ' : ' || vScheduler.schedule_type_type
                    || ' : Current date ' || TO_CHAR(vCurrentDate, common_pck.cDATETIME_MASK));
            END IF;

            slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Schedule ' || vSchedulerId || ' : ' || vScheduler.schedule_type_type
                    || ' : Day in week for current day ' || util.getDayOfWeek(trim(to_char(vCurrentDate, 'DAY', 'NLS_DATE_LANGUAGE=AMERICAN'))));

            vNextWeekday := mod(7+ TO_NUMBER(vScheduler.day - util.getDayOfWeek(trim(to_char(vCurrentDate, 'DAY', 'NLS_DATE_LANGUAGE=AMERICAN')))), 7);

            slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Schedule ' || vSchedulerId || ' : ' || vScheduler.schedule_type_type
                    || ' : Next weekday ' || vNextWeekday);

            IF vNextWeekday = 0 AND TRUNC(sysdate) >= NVL(vScheduler.not_before, TRUNC(sysdate)) THEN
                IF NOT canExecuteToday(vScheduler.hour, vScheduler.minute) THEN
                    vNextWeekday := 7;
                END IF;
                slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Schedule ' || vSchedulerId || ' : ' || vScheduler.schedule_type_type
                    || ' : Next weekday ' || vNextWeekday);
            END IF;

            vExecuteAt := vCurrentDate + vNextWeekday;
            slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Schedule ' || vSchedulerId || ' : ' || vScheduler.schedule_type_type
                || ' : Execute at ' || TO_CHAR(vExecuteAt, common_pck.cDATETIME_MASK));

            vExecuteAt := attachTimeToDate(vExecuteAt, vScheduler.hour, vScheduler.minute);
            slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Schedule ' || vSchedulerId || ' : ' || vScheduler.schedule_type_type
                || ' : Execute at ' || TO_CHAR(vExecuteAt, common_pck.cDATETIME_MASK));

        ELSIF vScheduler.schedule_type_type = common_pck.cSCHDTYPE_MONTHLY THEN
            -- Determine the last date in month
            slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Schedule ' || vSchedulerId || ' : ' || vScheduler.schedule_type_type
                || ' : Current date ' || TO_CHAR(vCurrentDate, common_pck.cDATETIME_MASK));
            slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Schedule ' || vSchedulerId || ' : ' || vScheduler.schedule_type_type
                || ' : Not before ' || TO_CHAR(vScheduler.not_before, common_pck.cDATETIME_MASK));
			
			IF vScheduler.month = 0 THEN 
				vScheduler.month := TO_NUMBER(TO_CHAR(sysdate,'MM'));
			END IF;
			
			IF vScheduler.year = 0 THEN 
				vScheduler.year := TO_NUMBER(TO_CHAR(sysdate,'YYYY'));
			END IF;
			
            IF vScheduler.not_before IS NOT NULL AND vCurrentDate < vScheduler.not_before THEN
                vCurrentDate := vScheduler.not_before;
                slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Schedule ' || vSchedulerId || ' : ' || vScheduler.schedule_type_type
                    || ' : Current date ' || TO_CHAR(vCurrentDate, common_pck.cDATETIME_MASK));

            END IF;

            vLastDay := LAST_DAY(TRUNC(vCurrentDate));
            slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Schedule ' || vSchedulerId || ' : ' || vScheduler.schedule_type_type
                || ' : Last day ' || TO_CHAR(vLastDay, common_pck.cDATETIME_MASK));

            IF TO_NUMBER(TO_CHAR(vCurrentDate, 'DD')) < vScheduler.day THEN
            slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Schedule ' || vSchedulerId || ' : ' || vScheduler.schedule_type_type
                || ' : TO_NUMBER(TO_CHAR(vCurrentDate, ''DD'')) < vScheduler.day');
                -- We stay in current month (month determined py pDatum)
                -- Scenario A: DAY(pDatum) < i.day -> Schedule at desired date
                -- 1. Determine last day in current month
                IF TO_NUMBER(TO_CHAR(vLastDay, 'DD')) <= vScheduler.day THEN
                    slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Schedule ' || vSchedulerId || ' : ' || vScheduler.schedule_type_type
                    || ' : TO_NUMBER(TO_CHAR(vLastDay, ''DD'')) <= vScheduler.day');

                    -- We have situation where customer wants to schedule tranpay on non existing day
                    -- for example on 29.02.2011, 31.04, 31.06, etc.
                    -- In this case, we schedule the tranpay at last possible day in month
                    vExecuteAt := vLastDay;
                    slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Schedule ' || vSchedulerId || ' : ' || vScheduler.schedule_type_type
                        || ' : Execute at ' || TO_CHAR(vExecuteAt, common_pck.cDATETIME_MASK));
                ELSE
                    vExecuteAt := TO_DATE(vScheduler.day || '.' || TO_CHAR(vCurrentDate,'MM.YYYY'), common_pck.cDATE_MASK);
                    slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Schedule ' || vSchedulerId || ' : ' || vScheduler.schedule_type_type
                        || ' : Execute at ' || TO_CHAR(vExecuteAt, common_pck.cDATETIME_MASK));
                END IF;
            ELSIF TO_NUMBER(TO_CHAR(vCurrentDate, 'DD')) = vScheduler.day AND 
			(TO_NUMBER(TO_CHAR(vCurrentDate, 'MM')) > vScheduler.month OR (TO_NUMBER(TO_CHAR(vCurrentDate, 'MM')) <= vScheduler.month AND TO_NUMBER(TO_CHAR(vCurrentDate, 'YYYY')) > vScheduler.year))
            THEN
                vExecuteAt := TRUNC(vCurrentDate);
				slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Schedule ' || vSchedulerId || ' : ' || vScheduler.schedule_type_type
                        || ' : Execute at ' || TO_CHAR(vExecuteAt, common_pck.cDATETIME_MASK));
			-- samo ako se nalog treba izvrsiti na danasnji dan provjeravaj vrijeme
			ELSIF TO_NUMBER(TO_CHAR(vCurrentDate, 'DD')) = vScheduler.day AND TO_NUMBER(TO_CHAR(vCurrentDate, 'MM')) = vScheduler.month AND 
			TO_NUMBER(TO_CHAR(vCurrentDate, 'YYYY')) = vScheduler.year AND canExecuteToday(vScheduler.hour, vScheduler.minute)
			THEN
                vExecuteAt := TRUNC(vCurrentDate);
                slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Schedule ' || vSchedulerId || ' : ' || vScheduler.schedule_type_type
                        || ' : Execute at ' || TO_CHAR(vExecuteAt, common_pck.cDATETIME_MASK));
			ELSIF TO_NUMBER(TO_CHAR(vCurrentDate, 'DD')) = vScheduler.day AND TO_NUMBER(TO_CHAR(vCurrentDate, 'MM')) = vScheduler.month AND 
			TO_NUMBER(TO_CHAR(vCurrentDate, 'YYYY')) = vScheduler.year AND TRUNC(sysdate) < NVL(vScheduler.not_before, TRUNC(sysdate))
			THEN
                vExecuteAt := TRUNC(vCurrentDate);
                slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Schedule ' || vSchedulerId || ' : ' || vScheduler.schedule_type_type
                        || ' : Execute at ' || TO_CHAR(vExecuteAt, common_pck.cDATETIME_MASK));
            ELSE
                    -- Scenario B: i.day < DAY(pDatum) (most common situation),
                    -- so next conversion works anytime
                    vExecuteAt := TO_DATE(vScheduler.day || '.' || TO_CHAR(vCurrentDate,'MM.YYYY'), common_pck.cDATE_MASK);
                    slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Schedule ' || vSchedulerId || ' : ' || vScheduler.schedule_type_type
                        || ' : Execute at ' || TO_CHAR(vExecuteAt, common_pck.cDATETIME_MASK));

                    vExecuteAt := ADD_MONTHS(vExecuteAt, 1);
                    slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Schedule ' || vSchedulerId || ' : ' || vScheduler.schedule_type_type
                        || ' : Execute at ' || TO_CHAR(vExecuteAt, common_pck.cDATETIME_MASK));

            END IF;
            vExecuteAt := attachTimeToDate(vExecuteAt, vScheduler.hour, vScheduler.minute);
            slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Schedule ' || vSchedulerId || ' : ' || vScheduler.schedule_type_type
                || ' : Execute at ' || TO_CHAR(vExecuteAt, common_pck.cDATETIME_MASK));

        ELSIF vScheduler.schedule_type_type = common_pck.cSCHDTYPE_YEARLY THEN
            slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Schedule ' || vSchedulerId || ' : ' || vScheduler.schedule_type_type
                || ' : Current date ' || TO_CHAR(vCurrentDate, common_pck.cDATETIME_MASK));
            slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Schedule ' || vSchedulerId || ' : ' || vScheduler.schedule_type_type
                || ' : Not before ' || TO_CHAR(vScheduler.not_before, common_pck.cDATETIME_MASK));

            IF vScheduler.not_before IS NOT NULL AND vCurrentDate < vScheduler.not_before THEN
                vCurrentDate := vScheduler.not_before;
                slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Schedule ' || vSchedulerId || ' : ' || vScheduler.schedule_type_type
                    || ' : Current date ' || TO_CHAR(vCurrentDate, common_pck.cDATETIME_MASK));

            END IF;

            vExecuteAt := TO_DATE(vScheduler.day || '.' || vScheduler.month || '.' || TO_CHAR(vCurrentDate,'YYYY'), common_pck.cDATE_MASK);
            slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Schedule ' || vSchedulerId || ' : ' || vScheduler.schedule_type_type
                || ' : Execute at ' || TO_CHAR(vExecuteAt, common_pck.cDATETIME_MASK));

            IF vExecuteAt < TRUNC(vCurrentDate) OR (vExecuteAt = TRUNC(vCurrentDate) AND TRUNC(sysdate) >= NVL(vScheduler.not_before, TRUNC(sysdate)) AND NOT canExecuteToday(vScheduler.hour, vScheduler.minute)) THEN
                vExecuteAt := ADD_MONTHS(TRUNC(vExecuteAt), 12);
                slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Schedule ' || vSchedulerId || ' : ' || vScheduler.schedule_type_type
                    || ' : Execute at ' || TO_CHAR(vExecuteAt, common_pck.cDATETIME_MASK));

            END IF;
            vExecuteAt := attachTimeToDate(vExecuteAt, vScheduler.hour, vScheduler.minute);
            slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Schedule ' || vSchedulerId || ' : ' || vScheduler.schedule_type_type
                || ' : Execute at ' || TO_CHAR(vExecuteAt, common_pck.cDATETIME_MASK));

        END IF;

        -- Check if projected date lies in allowed scheduler windows
        -- Will become null when beyond allowed scheduler window
        IF vScheduler.not_after IS NOT NULL AND vExecuteAt > (vScheduler.not_after + 1 )THEN
            vExecuteAt := NULL;
            slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Schedule ' || vSchedulerId || ' : ' || vScheduler.schedule_type_type
                        || ' : Execute at NULL');

        END IF;

        IF vExecuteAt IS NULL THEN
            -- If we are out of allowed scheduler windows, then schedule not possible!
            sspkg.raiseError(cERR_SchedulePeriodExceeded, null, pkgCtxId, myunit);
        END IF;
        RETURN vExecuteAt;
    END getFirstSchedule;

    PROCEDURE isValidRequestTypeForApp(pAppIdentifier IN VARCHAR2, pReqTypeId request_types.id%TYPE)
    IS
        vPom VARCHAR2(1);
        myunit CONSTANT VARCHAR2(24) := 'isValidRequestTypeForApp';
		vReqTypeId request_types.id%TYPE;
    BEGIN
	  slog.debug(pkgCtxId, myUnit, pAppIdentifier || ':' || pReqTypeId);
	  
	  	vReqTypeId := NVL(pReqTypeId,'%') ;

	  SELECT NULL
	  INTO vPom
	  FROM generic_code_books gcb
	  WHERE gcb.cb_table = pAppIdentifier AND gcb.cb_val_0 =  vReqTypeId;

	EXCEPTION
		WHEN no_data_found THEN
			sspkg.raiseError(cERR_invalidReqType, null, pkgCtxId, myunit);
    END isValidRequestTypeForApp;

	PROCEDURE MarkForSigning(
		pTranpayId IN tranpays.id%TYPE,
		pCheckRequestType IN BOOLEAN DEFAULT FALSE,
		pAppIdentifier IN VARCHAR2 DEFAULT NULL,
		pBalanceCheck IN BOOLEAN DEFAULT TRUE)
    IS
        myunit CONSTANT VARCHAR2(14) := 'MarkForSigning';
        vAccountId tranpays.account_id%TYPE;
        vReqTypeId tranpays.req_type_id%TYPE;
		vTranval tranpays.tranval%TYPE;
		vTranvalCurrencyId tranpays.tranval_currency_id%TYPE;
		vScheduleType schedules.schedule_type_type%TYPE;
    BEGIN
        slog.debug(pkgCtxId, myUnit, pTranpayId);

        common_pck.CommonSecurityChecks;
        tranpays_pck.CheckStatusTransition(pTranpayId, common_pck.cTRPSTS_US);

        BEGIN
			SELECT t.account_id, t.req_type_id, t.tranval, t.tranval_currency_id, NVL(s.schedule_type_type, common_pck.cSCHDTYPE_IMMEDIATE) schedule_type_type
			INTO vAccountId, vReqTypeId, vTranval, vTranvalCurrencyId, vScheduleType
			FROM tranpays t LEFT JOIN schedules s ON (s.id = t.schedule_id)
			WHERE t.id = pTranpayId;
		EXCEPTION
			WHEN no_data_found THEN
			    slog.error(pkgCtxId, myUnit, cERR_InvalidTranpay, pTranpayId);
				sspkg.raiseError(cERR_InvalidTranpay, null, pkgCtxId, myunit);
			WHEN OTHERS THEN
				slog.error(pkgCtxId, myUnit, SQLERRM || ' : ' || pTranpayId);
				sspkg.raiseError(cERR_InvalidTranpay, null, pkgCtxId, myunit);
		END;
        slog.debug(pkgCtxId, myUnit, vAccountId || ':' || vReqTypeId);

		IF pCheckRequestType THEN
			isValidRequestTypeForApp(pAppIdentifier, vReqTypeId);
		END IF;

        IF NOT authorization_pck.hasGrantOnSignTranpay(vAccountId, vReqTypeId) THEN
            sspkg.raiseError(cERR_NoSignPrivilege, null, pkgCtxId, myunit);
        END IF;

		-- 12607
		slog.debug(pkgCtxId, myUnit, 'Do tranpay limit check for ' || pTranpayId || ':' || vReqTypeId);
		tranpayLimitCheck(
			pTranpayId => pTranpayId,
			pAccountId => vAccountId,
			pRequestTypeId => vReqTypeId,
			pTranval => vTranval,
			pTranvalCurrency => vTranvalCurrencyId,
			pEndUserId => mcauth.auth.getClientId,
			pApplicationId => mcauth.auth.getApplicationId);

		-- 12451
		IF vScheduleType = common_pck.cSCHDTYPE_IMMEDIATE AND pBalanceCheck THEN
			-- web app always use function with pTranpayList, for single and group orders
			-- for web app balanceCheck only for single tranpay
					
		slog.debug(pkgCtxId, myUnit, 'Do balance check for ' || pTranpayId || ':' || vReqTypeId);
				balanceCheck(
					pTranpayId => pTranpayId,
					pAccountId => vAccountId,
					pRequestTypeId => vReqTypeId,
					pTranval => vTranval,
					pTranvalCurrency => vTranvalCurrencyId);
		END IF;

        INSERT INTO tmp$signing_candidates(tranpay_id, account_id)
        VALUES(pTranpayId, vAccountId);

    END MarkForSigning;


   FUNCTION MarkForSigning(pTranpayList table_of_integer,
        pCheckRequestType IN BOOLEAN DEFAULT FALSE,
		pAppIdentifier IN VARCHAR2 DEFAULT NULL)
        RETURN tranpay_import_result_list
	IS
		vTranpaysUnableToSign tranpay_import_result_list := tranpay_import_result_list();	
		vImportResult tranpay_import_result;		
		myunit CONSTANT VARCHAR2(18) := 'MarkForSigningBULK';
		vBalanceCheck BOOLEAN := FALSE;
	BEGIN
		slog.debug(pkgCtxId, myUnit);
		
		IF pTranpayList.COUNT = 1 THEN 
		   vBalanceCheck := TRUE;	
		END IF;
		
		FOR i IN 1..pTranpayList.COUNT LOOP
			slog.debug(pkgCtxId, myUnit, 'Calling function markForSigning for : ' || pTranpayList(i));
		
				BEGIN
				vImportResult := tranpay_import_result(pTranpayList(i));
				
				MarkForSigning(
				pTranpayList(i),
				pCheckRequestType,
				pAppIdentifier,
				vBalanceCheck);			
				
				EXCEPTION
					WHEN sspkg.sysexception THEN
					vImportResult.status := 'GRESKA';
					vImportResult.message := NVL(sspkg.getErrorUserMessage, mlang.trans(lang => mcauth.auth.getLang, messageId => sspkg.getErrorCode));
					vImportResult.tranpay_id := pTranpayList(i);
					slog.error(pkgCtxId, myunit, vImportResult.message || pTranpayList(i));
					vTranpaysUnableToSign.EXTEND;
					vTranpaysUnableToSign(vTranpaysUnableToSign.COUNT) := vImportResult;
					WHEN OTHERS THEN
					slog.error(pkgCtxId, myUnit, SQLERRM || ' ' || pTranpayList(i));
					vImportResult.status := 'GRESKA';
					vImportResult.message := mlang.trans(lang => mcauth.auth.getLang, messageId => cERR_internalError);
					vImportResult.tranpay_id := pTranpayList(i);
					vTranpaysUnableToSign.EXTEND;
					vTranpaysUnableToSign(vTranpaysUnableToSign.COUNT) := vImportResult;
				END;
				
			END LOOP;
			
			RETURN vTranpaysUnableToSign;
     END MarkForSigning;


    FUNCTION SignTranpay( pChallenge varchar2 := NULL,
                          pResponse varchar2 := NULL,
                          pOtp varchar2 := NULL,
                          pSourceData varchar2 := NULL,
                          pSignature VARCHAR2 := NULL)
    RETURN PLS_INTEGER IS
        myunit CONSTANT VARCHAR2(11) := 'SignTranpay';

        vNumberOfSignedRecords PLS_INTEGER          := 0;

        CURSOR c_candidates IS
        SELECT tranpay_id, tranpay_group_id
          FROM tmp$signing_candidates;

    BEGIN
        slog.debug(pkgCtxId, myUnit, pChallenge || ':' || pResponse || ':' || pOtp || ':' || pSourceData ||':'||pSignature);

        common_pck.CommonSecurityChecks;

        FOR i IN c_candidates LOOP
            tranpays_pck.CheckStatusTransition(i.tranpay_id, common_pck.cTRPSTS_US);
        END LOOP;

            -- Check signature and generate ID for signature record
        vNumberOfSignedRecords := authorization_pck.createSignature(pChallenge => pChallenge,
                                              pResponse => pResponse,
                                              pOtp => pOtp,
                                              pSourceData => pSourceData,
                                              pSignature => pSignature);

        FOR i IN c_candidates LOOP
            tranpays_pck.MarkTranpayAsSigned(i.tranpay_id);
        END LOOP;

        RETURN vNumberOfSignedRecords;
    EXCEPTION
      WHEN sspkg.sysException THEN
        RAISE;
      WHEN OTHERS THEN
        RAISE;
    END SignTranpay;

    PROCEDURE MarkTranpayAsSigned(pTranpayId tranpays.id%TYPE)
    IS
        myunit CONSTANT VARCHAR2(19) := 'MarkTranpayAsSigned';
        vCanSign BOOLEAN                            := FALSE;
        vSumOfSignPercentages NUMBER                := 0;
        vExecuteAt DATE;
		-- vAccountId tranpays.account_id%TYPE;

        PROCEDURE writeActionLog(
          pLogMessage IN VARCHAR2,
          pRefObject IN VARCHAR2 DEFAULT NULL
        )
        IS
            vActionid actions.id%TYPE;
        BEGIN
          IF mcauth.auth.getApplicationId = common_pck.cAPP_SMS THEN
            vActionid := common_pck.cACT_SMSPotpis;
          ELSE
            vActionid := common_pck.cACT_SignTranpays;
          END IF;

          authorization_pck.writeActionLog(
              pActionId => vActionid,
              pLogMessage => pTranpayId ||  ':-' || pLogMessage,
              pRefClass => cREF_CLASS|| myunit,
              pRefObject => pRefObject);
        END writeActionLog;

    BEGIN
        slog.debug(pkgCtxId, myUnit,  pTranpayId);
        common_pck.CommonSecurityChecks;

        -- Calculate (SUM) the total sum of sign percentages for given request
        vSumOfSignPercentages := NVL(getSumOfSignPercentages(pTranpayId),0);

        slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Total signature percentage ' || vSumOfSignPercentages);
        -- If calculated sum is less then 100%, additional signatures are required, e.g.
        -- request cannot be marked as signed
        -- else, request can be marked as signed (status and date)
        IF vSumOfSignPercentages >= 100 THEN
           vCanSign := TRUE;
        END IF;

        -- If we can sign request, we should do it
        IF vCanSign THEN
        BEGIN
			-- Determine next exection date
			vExecuteAt := getFirstSchedule(pTranpayId);
			slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Planned execution set on ' || TO_CHAR(vExecuteAt,common_pck.cDATETIME_MASK));

			UPDATE tranpays
				SET status = common_pck.cTRPSTS_US,
					execute_at = vExecuteAt,
					date_signed = SYSDATE
			  WHERE id = pTranpayId;
			  --RETURNING account_id INTO vAccountId;

			writeActionLog(pLogMessage => 'Tranpay marked as signed!', pRefObject => vSumOfSignPercentages);

			slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Successfull signed!');

			-- HSAFET: #17936 Integracija s SPLUNK sistemom
			-- RegisterSplunkNotification(pTranpayId IN tranpays.id%TYPE, pAccountId IN bank_accounts.id%TYPE, pTranpayAmount IN NUMBER, pTranpayCurrency IN VARCHAR2, pActionId IN VARCHAR2)
			-- RegisterSplunkNotification(pTranpayId => pTranpayId, pAccountId => vAccountId, pTranpayAmount => , pTranpayCurrency => , pActionId => common_pck.cACT_SignTranpays);
			RegisterSplunkNotification(pTranpayId => pTranpayId, pActionId => common_pck.cACT_SignTranpays);
							
        END;
        ELSE
            slog.debug(pkgCtxId, myUnit, 'Tranpay ' || pTranpayId || ': Not signed due insufficcient signature percentage!');
            writeActionLog(pLogMessage => 'Not signed due insufficcient signature percentage!', pRefObject => vSumOfSignPercentages);
        END IF;
    EXCEPTION
      WHEN sspkg.sysException THEN
        writeActionLog(
          pLogMessage => sspkg.getErrorMessage,
          pRefObject => pTranpayId);
        RAISE;
      WHEN OTHERS THEN
		slog.error(pkgCtxId, myUnit, pTranpayId || ':' || sqlcode || ':' || sqlerrm);
        writeActionLog(
          pLogMessage => sqlerrm,
          pRefObject => pTranpayId);
        RAISE;
    END MarkTranpayAsSigned;

    FUNCTION getTranpayRequestType(pTranpayId tranpays.id%TYPE)
    RETURN tranpays.req_type_id%TYPE
    IS
        myunit CONSTANT VARCHAR2(21) := 'getTranpayRequestType';
        vRequestTypeId tranpays.req_type_id%TYPE;
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pTranpayId);
        common_pck.CommonSecurityChecks;

        SELECT req_type_id
          INTO vRequestTypeId
          FROM tranpays
        WHERE id = pTranpayId;

        slog.debug(pkgCtxId, myUnit, 'Request type:' || vRequestTypeId);

        RETURN vRequestTypeId;
    EXCEPTION
        WHEN no_data_found THEN
			slog.error(pkgCtxId, myUnit, cERR_InvalidTranpay, pTranpayId);
            sspkg.raiseError(cERR_InvalidTranpay, null, pkgCtxId, myunit);
    END getTranpayRequestType;

    PROCEDURE validateBICCode(pRequestTypeId  IN tranpays.req_type_id%TYPE,
                              pAttribId       IN tranpay_details.attrib_id%TYPE,
                              pDataVCHAR      IN tranpay_details.data_vchar%TYPE)
    IS
      vPom PLS_INTEGER;

      myunit CONSTANT VARCHAR2(15) := 'validateBICCode';
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pRequestTypeId || ':' || pAttribId || ':' || pDataVCHAR);
		SELECT NULL INTO vPom FROM DUAL
		WHERE EXISTS (
			SELECT NULL
			FROM generic_code_books
			WHERE cb_table = 'BANKE'
			AND cb_val_0 = pDataVCHAR);
    EXCEPTION
        WHEN no_data_found THEN
			slog.error(pkgCtxId, myUnit, cERR_invalidBIC, pRequestTypeId || ':' || pAttribId || ':' || pDataVCHAR);
            sspkg.raiseError(cERR_invalidBIC, null, pkgCtxId, myunit);
    END validateBICCode;

	PROCEDURE CheckDetailsData(   pRequestTypeId    tranpays.req_type_id%TYPE,
                                  pAttribId         tranpay_details.attrib_id%TYPE,
                                  pDataVCHAR        tranpay_details.data_vchar%TYPE,
                                  pDataBLOB         tranpay_details.data_blob%TYPE,
                                  pIgnoreRequired   BOOLEAN)
    IS
        TYPE TranpayAttribsRec IS RECORD (
            basic_datatype tranpay_attribs.basic_datatype%TYPE,
            maxsize        tranpay_attribs.maxsize%TYPE,
            value_required tranpay_attribs.value_required%TYPE,
            description    tranpay_attribs.description%TYPE,
            regexp        tranpay_attribs.regexp%TYPE);

        vTranpayAttribs TranpayAttribsRec;

        vNumberValue   NUMBER;
        vDateTimeValue DATE;

        myunit CONSTANT VARCHAR2(16) := 'CheckDetailsData';

        vErrorMessageId VARCHAR2(1024) := NULL;

        FUNCTION translateErrorMessage
        RETURN VARCHAR2 IS
        BEGIN
            RETURN mlang.trans(
                        lang => mcauth.auth.getLang,
                        messageId => vErrorMessageId,
                        s0 =>
                            getTranpayAttribute(pRequestTypeId => pRequestTypeId,
                                 pTranpayAttribId => pAttribId,
                                 pColumnName => 'DESCRIPTION')
                        );
        END;
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pRequestTypeId || ':' || pAttribId || ':' || pDataVCHAR);

        SELECT basic_datatype, maxsize, value_required, description, regexp
          INTO vTranpayAttribs
          FROM tranpay_attribs ta
         WHERE ta.id          = pAttribId
           AND ta.req_type_id = pRequestTypeId;

        -- <TODO: Implementirati regexp provjeru
        /*
         Data verification - basic rules
        1. IF value_required = 1 THEN
                 pDataVCHAR OR pDataBLOB HAS TO BE NOT NULL
           END IF;

        2. IF basic_datatype != BLOB THEN
             LENGTH(pDataVCHAR) HAS TO BE LESS OR EQUAL TO maxsize

        3. CASE basic_datatype
                 WHEN NUMBER THEN TO_NUMBER(pDataVCHAR) SHOULD NOT FAIL
                 WHEN VCHAR THEN DO NOT CHECK!
                 WHEN DATETIME THEN TO_DATE(pDataVCHAR, <FORMAT>) SHOULD NOT FAIL
                 WHEN INT THEN TRUNC(TO_NUMBER(pDataVCHAR)) SHOULD BE EQUAL TO TO_NUMBER(pDataVCHAR)
            END CASE
        */

        -- 1.
        IF (NOT pIgnoreRequired) AND vTranpayAttribs.value_required = 1 THEN
           IF pDataVCHAR IS NULL AND pDataBLOB IS NULL THEN
                vErrorMessageId := cERR_RequiredAttributeMissing;
				slog.error(pkgCtxId, myUnit, vErrorMessageId, pRequestTypeId || ':' || pAttribId || ':' || pDataVCHAR);
                sspkg.raiseError(vErrorMessageId, translateErrorMessage, pkgCtxId, myunit);
           END IF;
        END IF;

        -- 2.
        IF vTranpayAttribs.basic_datatype <> 'BLOB' THEN
            IF LENGTH(pDataVCHAR) > vTranpayAttribs.maxsize THEN
                vErrorMessageId := common_pck.cERR_AtributeMaxsizeExceeded;
				slog.error(pkgCtxId, myUnit, vErrorMessageId, pRequestTypeId || ':' || pAttribId || ':' || pDataVCHAR || ':' || LENGTH(pDataVCHAR) || ':' || vTranpayAttribs.maxsize);
                sspkg.raiseError(vErrorMessageId, translateErrorMessage, pkgCtxId, myunit);
            END IF;
        END IF;

        IF vTranpayAttribs.basic_datatype = 'NUMBER' THEN
            BEGIN
                vNumberValue := TO_NUMBER(pDataVCHAR);
            EXCEPTION
                WHEN OTHERS THEN
                    vErrorMessageId := common_pck.cERR_AttFormatExceptionNUMBER;
					slog.error(pkgCtxId, myUnit, vErrorMessageId, pRequestTypeId || ':' || pAttribId || ':' || pDataVCHAR || ':' || sqlcode || ':' || sqlerrm);
                    sspkg.raiseError(vErrorMessageId, translateErrorMessage, pkgCtxId, myunit);
            END;
        ELSIF vTranpayAttribs.basic_datatype = 'DATETIME' THEN
            BEGIN
                vDateTimeValue := TO_DATE(pDataVCHAR, common_pck.cDATETIME_MASK);
            EXCEPTION
                WHEN OTHERS THEN
                    vErrorMessageId := common_pck.cERR_AttFormatExceptionDATETIM;
					slog.error(pkgCtxId, myUnit, vErrorMessageId, pRequestTypeId || ':' || pAttribId || ':' || pDataVCHAR || ':' || sqlcode || ':' || sqlerrm);
                    sspkg.raiseError(vErrorMessageId, translateErrorMessage, pkgCtxId, myunit);
            END;
        ELSIF vTranpayAttribs.basic_datatype = 'INT' THEN
            BEGIN
                vNumberValue := TO_NUMBER(pDataVCHAR);
                IF TRUNC(vNumberValue) <> vNumberValue THEN
                    vErrorMessageId := common_pck.cERR_AttFormatExceptionINT;
					slog.error(pkgCtxId, myUnit, vErrorMessageId, pRequestTypeId || ':' || pAttribId || ':' || pDataVCHAR);
                    sspkg.raiseError(vErrorMessageId, translateErrorMessage, pkgCtxId, myunit);
                END IF;
            EXCEPTION
                WHEN OTHERS THEN
                   vErrorMessageId := common_pck.cERR_AttFormatExceptionNUMBER;
				   slog.error(pkgCtxId, myUnit, vErrorMessageId, pRequestTypeId || ':' || pAttribId || ':' || pDataVCHAR || ':' || sqlcode || ':' || sqlerrm);
                   sspkg.raiseError(vErrorMessageId, translateErrorMessage, pkgCtxId, myunit);
            END;
        END IF;

        -- Check if destination account for transfers is not in status ZUBO
        DECLARE
            vAccountStatus accounts_pck.accountStatus;
        BEGIN
            IF pRequestTypeId = common_pck.cRTI_TRANSFER AND pAttribId = common_pck.cTRANPAY_ATT_RACUN AND pDataVCHAR IS NOT NULL THEN
                vAccountStatus := accounts_pck.getAccountStatus(pAccountId => pDataVCHAR);

                IF vAccountStatus.status IN ('Z','U') THEN
					slog.error(pkgCtxId, myUnit, cERR_InvTrnsfDestAccount, pRequestTypeId || ':' || pAttribId || ':' || pDataVCHAR || ':' || vAccountStatus.status);
                    sspkg.raiseError(cERR_InvTrnsfDestAccount, null, pkgCtxId, myunit);
                END IF;
            END IF;
            --IF
            --SELECT
        END;

        IF vTranpayAttribs.regexp IS NOT NULL AND
          vTranpayAttribs.basic_datatype <> 'BLOB' AND
          NOT REGEXP_LIKE(pDataVCHAR, vTranpayAttribs.regexp, 'c')
        THEN
          vErrorMessageId := common_pck.cERR_AttFormatExceptionREGEXP;
		  slog.error(pkgCtxId, myUnit, vErrorMessageId, pRequestTypeId || ':' || pAttribId || ':' || pDataVCHAR || ':' || vTranpayAttribs.regexp);
          sspkg.raiseError(vErrorMessageId, translateErrorMessage, pkgCtxId, myunit);
        END IF;

		IF vEnforceBICCode IS NULL THEN
			vEnforceBICCode := sspkg.readBool('/Core/Main/TranPays/enforceBIC');
		END IF;
		-- Failover
		IF vEnforceBICCode IS NULL THEN
			slog.info(pkgCtxId, 'Package initialization', 'Missing parameter /Core/Main/TranPays/vEnforceBICCode for tranpays! Using default value FALSE!');
			vEnforceBICCode := FALSE;
		END IF;

		IF vEnforceRevenueType IS NULL THEN
			vEnforceRevenueType     := sspkg.readBool('/Core/Main/TranPays/enforceRevenueType');
		END IF;
		-- Failover
		IF vEnforceRevenueType IS NULL THEN
			slog.info(pkgCtxId, 'Package initialization', 'Missing parameter /Core/Main/TranPays/enforceRevenueType for tranpays! Using default value FALSE!');
			vEnforceRevenueType := FALSE;
		END IF;

		IF vEnforceMuncipalityCode IS NULL THEN
			vEnforceMuncipalityCode := sspkg.readBool('/Core/Main/TranPays/enforceMuncipalityCode');
		END IF;
		-- Failover
		IF vEnforceMuncipalityCode IS NULL THEN
			slog.info(pkgCtxId, 'Package initialization', 'Missing parameter /Core/Main/TranPays/enforceMuncipalityCode for tranpays! Using default value FALSE!');
			vEnforceMuncipalityCode := FALSE;
		END IF;

        -- Validate BIC Code if required
		IF pRequestTypeId = common_pck.cRTI_PPI AND pAttribId = common_pck.cTRANPAY_ATT_BIC_KOD_BANKE AND pDataVCHAR IS NOT NULL AND vEnforceBICCode THEN
			validateBICCode(pRequestTypeId => pRequestTypeId,
                        pAttribId      => pAttribId,
                        pDataVCHAR     => pDataVCHAR);
		END IF;

		IF pRequestTypeId = common_pck.cRTI_UPP AND pAttribId = common_pck.cTRANPAY_ATT_VRSTA_PRIHODA AND pDataVCHAR IS NOT NULL AND vEnforceRevenueType THEN
			validateRevenueType(pRequestTypeId => pRequestTypeId,
                        pAttribId      => pAttribId,
                        pDataVCHAR     => pDataVCHAR);
		END IF;

        IF pRequestTypeId = common_pck.cRTI_UPP AND pAttribId = common_pck.cTRANPAY_ATT_OPCINA AND pDataVCHAR IS NOT NULL AND vEnforceMuncipalityCode THEN
			validateMunicipalityCode(pRequestTypeId => pRequestTypeId,
                        pAttribId      => pAttribId,
                        pDataVCHAR     => pDataVCHAR);
		END IF;
    EXCEPTION
        WHEN no_data_found THEN
			slog.error(pkgCtxId, myUnit,  cERR_InvalidTranpayAttribute, pRequestTypeId || ':' || pAttribId || ':' || pDataVCHAR);
            sspkg.raiseError(cERR_InvalidTranpayAttribute, null, pkgCtxId, myunit);
    END CheckDetailsData;

    PROCEDURE CheckDetailsData(   pRequestTypeId    tranpays.req_type_id%TYPE,
                                  pAttribId         tranpay_details.attrib_id%TYPE,
                                  pDataVCHAR        tranpay_details.data_vchar%TYPE,
                                  pDataBLOB         tranpay_details.data_blob%TYPE)
    IS
    BEGIN
        CheckDetailsData(pRequestTypeId     => pRequestTypeId,
                         pAttribId          => pAttribId,
                         pDataVCHAR         => pDataVCHAR,
                         pDataBLOB          => pDataBLOB,
                         pIgnoreRequired    => FALSE);
    END CheckDetailsData;

    PROCEDURE TranpayValidation(pTranpayId tranpays.id%TYPE, pRequestTypeId tranpays.req_type_id%TYPE, pAccountId tranpays.account_id%TYPE) IS
        CURSOR c_missingData IS
            SELECT ta.id, ta.description
              FROM tranpay_attribs ta
             WHERE ta.req_type_id = pRequestTypeId
               AND ta.value_required = 1
               AND NOT exists (SELECT 1
                               FROM tranpay_details td
                               WHERE td.req_type_id = pRequestTypeId
                               AND td.tranpay_id = pTranpayId
                               AND td.attrib_id = ta.id);

        CURSOR c_existingData IS
            SELECT attrib_id, data_vchar, data_blob
              FROM tranpay_details td
             WHERE td.req_type_id = pRequestTypeId
               AND td.tranpay_id = pTranpayId
               AND td.attrib_id NOT IN (common_pck.cTRANPAY_ATT_CLEARING_DATE, common_pck.cQRPay_SENT_NOTIFICATION, common_pck.cTOPUP_SENT_NOTIFICATION, common_pck.cME2YOU_SENT_NOTIF_SENDER, common_pck.cME2YOU_SENT_NOTIF_RECEIVER, common_pck.cTRPAY_ATT_SENT_NOTIFICATION);

		TYPE tt_existingData IS TABLE OF c_existingData%ROWTYPE INDEX BY PLS_INTEGER;
		existingDataTable tt_existingData;

        myunit CONSTANT VARCHAR2(17) := 'TranpayValidation';
        vMissingAttributes VARCHAR2(4000) := NULL;
		vRSLimit CONSTANT PLS_INTEGER := 20;
		validateOrderInstruction VARCHAR2(1000);
		vStatus INTEGER;
		vStatusMessage VARCHAR2(1000);
		vDestinationAccount tranpay_details.data_vchar%TYPE;
		vSourceAccount account_details.data_vchar%TYPE;

    BEGIN
        slog.debug(pkgCtxId, myUnit,  pTranpayId);
        common_pck.CommonSecurityChecks;

        IF pAccountId IS NULL THEN
            sspkg.raiseError(cERR_NoAccount, null, pkgCtxId, myunit);
        END IF;

		-- Normalize data by populating details which are expected to be populate in background
		AppendBackgroundDetails(pTranpayId => pTranpayId,
                                pReqTypeId => pRequestTypeId,
                                pAccountId => pAccountId);

        FOR i IN c_missingData LOOP
            vMissingAttributes := vMissingAttributes || i.id || ' - ' || i.description || chr(10);
        END LOOP;

        IF vMissingAttributes IS NOT NULL THEN
            sspkg.raiseError(cERR_RequiredAttributeMissing,
                        mlang.trans(lang => mcauth.auth.getLang,
                            messageId => cERR_RequiredAttributeMissing)
                             || chr(10) || vMissingAttributes,
                    pkgCtxId, myunit);
        END IF;

		OPEN c_existingData;
		LOOP
			FETCH c_existingData BULK COLLECT INTO existingDataTable LIMIT vRSLimit;
			FOR i IN 1..existingDataTable.COUNT LOOP
				CheckDetailsData(pRequestTypeId => pRequestTypeId, pAttribId => existingDataTable(i).attrib_id, pDataVCHAR => existingDataTable(i).data_vchar, pDataBLOB => existingDataTable(i).data_blob);
				
				IF existingDataTable(i).attrib_id = common_pck.cTRANPAY_ATT_RACUN_PRIMAOCA THEN
					vDestinationAccount := existingDataTable(i).data_vchar;
				END IF;
				
				IF existingDataTable(i).attrib_id = common_pck.cTRPAY_ATT_EXT_ID THEN
					vSourceAccount := existingDataTable(i).data_vchar;
				END IF;
				
			END LOOP;
			EXIT WHEN c_existingData%NOTFOUND;
		END LOOP;
		CLOSE c_existingData;
		
		
		IF sspkg.readBool('/Core/Main/TranPays/checkSendAndRecAccEquality') AND pRequestTypeId = common_pck.cRTI_UPP AND vDestinationAccount = vSourceAccount THEN
		
			slog.error(pkgCtxId, myUnit, cERR_SendAndRecAccEquality, pTranpayId || ':' || vSourceAccount || ':' || vDestinationAccount);
			sspkg.raiseError(cERR_SendAndRecAccEquality, null, pkgCtxId, myunit);
		
		END IF;
		
		validateOrderInstruction := sspkg.readVchar(pkgCtxId || '/CoreDataValidationInstruction');
			
		IF sspkg.ReadBool(pkgCtxId || '/CoreDataValidationEnabled') THEN
		
			slog.debug(pkgCtxId, myUnit, 'Core data validation enabled!');
	
			execute immediate validateOrderInstruction using pTranpayId, out vStatus, out vStatusMessage;
			
			slog.debug(pkgCtxId, myUnit, 'vStatus :' || vStatus);
			
			slog.debug(pkgCtxId, myUnit, 'vStatusMessage: ' || vStatusMessage);
			
			IF vStatus = 0 THEN
				slog.error(pkgCtxId, myUnit, cERR_CoreDataValidationError, pTranpayId);
				sspkg.raiseError(cERR_CoreDataValidationError, vStatusMessage, pkgCtxId, myunit);
			END IF;

		END IF;
		
    END TranpayValidation;

    FUNCTION getTranpayTemplateRequestType(pTranpayTemplateId tranpay_templates.id%TYPE)
    RETURN tranpay_templates.request_type_id%TYPE IS
        vRequestTypeId tranpay_templates.request_type_id%TYPE;
        myunit CONSTANT VARCHAR2(29) := 'getTranpayTemplateRequestType';
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pTranpayTemplateId);
        common_pck.CommonSecurityChecks;

        SELECT request_type_id
          INTO vRequestTypeId
          FROM tranpay_templates tp
         WHERE tp.id = pTranpayTemplateId;

        RETURN vRequestTypeId;
    EXCEPTION
      WHEN no_data_found THEN
		slog.error(pkgCtxId, myUnit, cERR_InvalidTranpayTemplate, pTranpayTemplateId);
		sspkg.raiseError(cERR_InvalidTranpayTemplate, null, pkgCtxId, myunit);
    END getTranpayTemplateRequestType;

    FUNCTION p_isValidTranpayGroup(pTranpayGroupId tranpay_groups.id%TYPE)
    RETURN PLS_INTEGER IS
        vPom PLS_INTEGER;
        myunit CONSTANT VARCHAR2(21) := 'p_isValidTranpayGroup';
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pTranpayGroupId);

        SELECT 1
          INTO vPom
          FROM tranpay_groups
         WHERE id = pTranpayGroupId;
        RETURN 1;
    EXCEPTION
        WHEN no_data_found THEN
            RETURN 0;
    END p_isValidTranpayGroup;

    FUNCTION isValidTranpayGroup(pTranpayGroupId tranpay_groups.id%TYPE)
    RETURN BOOLEAN IS
    BEGIN
        RETURN p_isValidTranpayGroup(pTranpayGroupId) > 0;
    END isValidTranpayGroup;

    FUNCTION p_isValidCOA(pCoaId charts_of_accounts.id%TYPE)
    RETURN PLS_INTEGER IS
        vPom PLS_INTEGER;
        myunit CONSTANT VARCHAR2(12) := 'p_isValidCOA';
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pCoaId);

        SELECT 1
          INTO vPom
          FROM charts_of_accounts
         WHERE id = pCoaId;
        RETURN 1;
    EXCEPTION
        WHEN no_data_found THEN
            RETURN 0;
    END p_isValidCOA;

    FUNCTION isValidCOA(pCoaId charts_of_accounts.id%TYPE)
    RETURN BOOLEAN IS
    BEGIN
        RETURN p_isValidCOA(pCoaId) > 0;
    END isValidCOA;

    FUNCTION p_isValidSchedule(pSchedulesId schedules.id%TYPE)
    RETURN PLS_INTEGER IS
        vPom PLS_INTEGER;
        myunit CONSTANT VARCHAR2(17) := 'p_isValidSchedule';
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pSchedulesId);

        SELECT 1
          INTO vPom
          FROM schedules
         WHERE id = pSchedulesId;
        RETURN 1;
    EXCEPTION
        WHEN no_data_found THEN
            RETURN 0;
    END p_isValidSchedule;

    FUNCTION isValidSchedule(pSchedulesId schedules.id%TYPE)
    RETURN BOOLEAN IS
    BEGIN
        RETURN p_isValidSchedule(pSchedulesId) > 0;
    END isValidSchedule;

    -- Internal function. Should not be exposed to public
    FUNCTION p_tranpayExists(pTranpayId tranpays.id%TYPE, pTranpayStatus tranpays.status%TYPE)
    RETURN PLS_INTEGER IS
        vPom PLS_INTEGER := 0;
        myunit CONSTANT VARCHAR2(16) := 'p_tranpayExists2';
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pTranpayId || ':' || pTranpayStatus);

        IF pTranpayStatus IN (common_pck.cTRPSTS_UW, common_pck.cTRPSTS_VB) THEN
			IF mcauth.auth.getAccountOwner = '%' THEN
				SELECT NULL INTO vPom FROM vw$user_tranpays_uw_vb WHERE id = pTranpayId AND status = pTranpayStatus;
			ELSE
				SELECT NULL INTO vPom FROM vw$user_tranpays_uw_vb_acc WHERE id = pTranpayId AND status = pTranpayStatus;
			END IF;
        ELSIF pTranpayStatus = common_pck.cTRPSTS_UD THEN
            SELECT NULL INTO vPom FROM vw$user_tranpays_ud WHERE id = pTranpayId;
        ELSIF pTranpayStatus = common_pck.cTRPSTS_US THEN
            SELECT NULL INTO vPom FROM vw$user_tranpays_us WHERE id = pTranpayId;
        ELSIF pTranpayStatus IN (common_pck.cTRPSTS_BP, common_pck.cTRPSTS_BR) THEN
            SELECT NULL INTO vPom FROM vw$user_tranpays_bx WHERE id = pTranpayId;
        ELSIF pTranpayStatus = common_pck.cTRPSTS_BA THEN
            SELECT NULL INTO vPom FROM vw$user_tranpays_ba WHERE id = pTranpayId;
        ELSIF pTranpayStatus = common_pck.cTRPSTS_UC THEN
            SELECT NULL INTO vPom FROM vw$user_tranpays_uc WHERE id = pTranpayId;
        END IF;

        RETURN 1;
    EXCEPTION
        WHEN no_data_found THEN
            RETURN 0;
    END p_tranpayExists;

    -- Internal function. Should not be exposed to public
    FUNCTION tranpayExists(pTranpayId tranpays.id%TYPE, pTranpayStatus tranpays.status%TYPE)
    RETURN BOOLEAN IS
    BEGIN
        RETURN p_tranpayExists(pTranpayId => pTranpayId, pTranpayStatus => pTranpayStatus) > 0;
    END tranpayExists;

    FUNCTION p_tranpayExists(pTranpayId tranpays.id%TYPE)
    RETURN PLS_INTEGER IS
        vTranpayStatus tranpays.status%TYPE;
        myunit CONSTANT VARCHAR2(15) := 'p_tranpayExists';
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pTranpayId);
        common_pck.CommonSecurityChecks;

        SELECT t.status
          INTO vTranpayStatus
            FROM tranpays t
         WHERE id = pTranpayId;

        RETURN p_tranpayExists(pTranpayId => pTranpayId, pTranpayStatus => vTranpayStatus);

    EXCEPTION
        WHEN no_data_found THEN
            RETURN 0;
    END p_tranpayExists;

    FUNCTION tranpayExists(pTranpayId tranpays.id%TYPE)
    RETURN BOOLEAN IS
    BEGIN
        RETURN p_tranpayExists(pTranpayId) > 0;
    END tranpayExists;

    FUNCTION p_isValidTranpayAttribute(pRequestTypeId tranpay_attribs.req_type_id%TYPE,
                                       pTranpayAttribId tranpay_attribs.id%TYPE)
    RETURN PLS_INTEGER IS
        vPom PLS_INTEGER;
        myunit CONSTANT VARCHAR2(25) := 'p_isValidTranpayAttribute';
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pRequestTypeId || ':' || pTranpayAttribId);
		
		SELECT NULL INTO vPom FROM DUAL
		WHERE EXISTS (
			SELECT NULL
			FROM tranpay_attribs
			WHERE id = pTranpayAttribId
			AND req_type_id = pRequestTypeId);
        RETURN 1;
    EXCEPTION
        WHEN no_data_found THEN
            RETURN 0;
    END p_isValidTranpayAttribute;

    FUNCTION isValidTranpayAttribute(pRequestTypeId tranpay_attribs.req_type_id%TYPE,
                                     pTranpayAttribId tranpay_attribs.id%TYPE)
    RETURN BOOLEAN IS
    BEGIN
        RETURN p_isValidTranpayAttribute(pRequestTypeId, pTranpayAttribId) > 0;
    END isValidTranpayAttribute;

    -- TEMPLATE PACKAGES
    -- Creating and manipulating template packages
    FUNCTION CreateNewTemplatePackage(
        pName template_packages.name%TYPE,
        pDescription template_packages.description%TYPE)
    RETURN template_packages.id%TYPE
    IS
        myunit CONSTANT VARCHAR2(24) := 'CreateNewTemplatePackage';
        vId template_packages.id%TYPE;
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pName || ':' || pDescription);
        common_pck.CommonSecurityChecks;

        INSERT INTO template_packages
            (name, description, valid)
        VALUES
            (pName, pDescription, 1)
        RETURNING id INTO vId;

        slog.debug(pkgCtxId, myUnit,  pName || ':' || pDescription || '->' || vId);

        RETURN vId;

    EXCEPTION
      WHEN dup_val_on_index THEN
        slog.error(pkgCtxId, myUnit, cERR_DupValOnTempPackageName || ':' || pName);
        sspkg.raiseError(cERR_DupValOnTempPackageName, null, pkgCtxId, myunit);
    END CreateNewTemplatePackage;

    PROCEDURE UpdateExistingTemplatePackage(
        pId template_packages.id%TYPE,
        pName template_packages.name%TYPE,
        pDescription template_packages.description%TYPE,
        pValid template_packages.valid%TYPE)
    IS
        myunit CONSTANT VARCHAR2(29) := 'UpdateExistingTemplatePackage';
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pId || ':' || pName || ':' || pDescription || ':' || pValid);
        common_pck.CommonSecurityChecks;

        UPDATE template_packages
           SET name = pName,
               description = pDescription,
               valid = pValid,
               date_modified = SYSDATE,
               user_id = mcauth.auth.getClientId
         WHERE id = pId;

    EXCEPTION
      WHEN dup_val_on_index THEN
        slog.error(pkgCtxId, myUnit, cERR_DupValOnTempPackageName || ':' || pName);
        sspkg.raiseError(cERR_DupValOnTempPackageName, null, pkgCtxId, myunit);

    END UpdateExistingTemplatePackage;

    PROCEDURE RemoveTemplatePackage(
        pId template_packages.id%TYPE,
        pForceRemoval BOOLEAN := FALSE)
    IS
        myunit CONSTANT VARCHAR2(21) := 'RemoveTemplatePackage';
        vPom PLS_INTEGER;
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pId);
        common_pck.CommonSecurityChecks;

        BEGIN
        SELECT NULL INTO vPom FROM DUAL
         WHERE EXISTS (SELECT NULL FROM vw$user_template_packages WHERE id = pId);
        EXCEPTION
          WHEN no_data_found THEN
			slog.error(pkgCtxId, myUnit, cERR_InvTranpayTemplatePackage, pId);
            sspkg.raiseError(cERR_InvTranpayTemplatePackage, null, pkgCtxId, myunit);
        END;

        IF pForceRemoval THEN
            UPDATE tranpay_templates
               SET template_package_id = NULL
             WHERE template_package_id = pId;

            DELETE FROM template_packages
             WHERE id = pId;
        ELSE
            -- 1. Check first if there exists tranpay templates referencing given package
            SELECT COUNT(*)
              INTO vPom
              FROM tranpay_templates
             WHERE template_package_id = pId;

            IF vPom > 0 THEN
			  slog.error(pkgCtxId, myUnit, cERR_BoundTemplatePackage, pId || ':' || vPom);
              sspkg.raiseError(cERR_BoundTemplatePackage, null, pkgCtxId, myunit);
            ELSE
              BEGIN
                DELETE FROM template_packages
                WHERE id = pId;
              EXCEPTION
                WHEN others THEN
					slog.error(pkgCtxId, myUnit, cERR_BoundTemplatePackage, pId || ':' || sqlcode || ':' || sqlerrm);
                    sspkg.raiseError(cERR_BoundTemplatePackage, null, pkgCtxId, myunit);
              END;
            END IF;
        END IF;
    END RemoveTemplatePackage;

    -- TRANPAY TEMPLATES
    -- Creating and manipulating tranpay templates
    FUNCTION CreateNewTranpayTemplate(
            pName tranpay_templates.name%TYPE,
            pDescription tranpay_templates.description%TYPE,
            pRequest_type_id tranpay_templates.request_type_id%TYPE,
            pTranpay_description tranpay_templates.tranpay_description%TYPE,
            pInternal_description tranpay_templates.internal_description%TYPE,
            pAccount_id tranpay_templates.account_id%TYPE,
            pTranval tranpay_templates.tranval%TYPE,
            pCurrency_id tranpay_templates.currency_id%TYPE,
            pTest_record tranpay_templates.test_record%TYPE,
            pTemplate_package_id tranpay_templates.template_package_id%TYPE)
    RETURN tranpay_templates.id%TYPE
    IS
            myunit CONSTANT VARCHAR2(24) := 'CreateNewTranpayTemplate';
            vId tranpay_templates.id%TYPE;
            vAccountOwnerId account_owners.id%TYPE;
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pName || ':' || pRequest_type_id || ':' || pAccount_id || ':' || pTranval || ':' || pCurrency_id || ':' || pTemplate_package_id);
        common_pck.CommonSecurityChecks;

        DECLARE
            vRequestTypeId tranpay_templates.request_type_id%TYPE;
        BEGIN
            IF pTemplate_package_id IS NOT NULL THEN
                SELECT DISTINCT request_type_id
                  INTO vRequestTypeId
                  FROM tranpay_templates
                 WHERE template_package_id = pTemplate_package_id;
            END IF;
        EXCEPTION
            WHEN too_many_rows THEN
				slog.error(pkgCtxId, myUnit, cERR_VariousTrpTmplReqTypes, pTemplate_package_id);
                sspkg.raiseError(cERR_VariousTrpTmplReqTypes, null, pkgCtxId, myunit);
        END;

        IF pAccount_id IS NOT NULL THEN
          vAccountOwnerId := accounts_pck.getAccountOwner(pAccountId => pAccount_id);
        ELSE
          vAccountOwnerId := NULL;
        END IF;

        INSERT INTO tranpay_templates (
                name, description, request_type_id,
                tranpay_description, internal_description, account_id,
                tranval, currency_id, test_record, valid,
                template_package_id, account_owner_id)
         VALUES(pName, pDescription, pRequest_type_id,
                pTranpay_description, pInternal_description, pAccount_id,
                pTranval, pCurrency_id, pTest_record, 1,
                pTemplate_package_id, vAccountOwnerId)
        RETURNING id INTO vId;

        slog.debug(pkgCtxId, myUnit, 'Create tranpay template with ID : ' || vId);
        RETURN vId;
    EXCEPTION
      WHEN dup_val_on_index THEN
		slog.error(pkgCtxId, myUnit, cERR_TmplWithGivenNameExists, mcauth.auth.getApplicationId || ':' ||mcauth.auth.getClientId || ':' || pRequest_type_id || ':' || pName);
        sspkg.raiseError(cERR_TmplWithGivenNameExists, null, pkgCtxId, myunit);
    END CreateNewTranpayTemplate;

    FUNCTION CreateTemplateFromTranpay(
                pTranpayId tranpays.id%TYPE,
                pName tranpay_templates.name%TYPE,
                pDescription tranpay_templates.description%TYPE,
                pTemplate_package_id tranpay_templates.template_package_id%TYPE)
    RETURN tranpay_templates.id%TYPE
    IS
        myunit CONSTANT VARCHAR2(25) := 'CreateTemplateFromTranpay';
        vTranpayTemplateId tranpay_templates.id%TYPE;

        --vTranpayRec tranpays%ROWTYPE;
        TYPE TranpayRecord IS RECORD (
            req_type_id          tranpays.req_type_id%TYPE,
            description          tranpays.description%TYPE,
            internal_description tranpays.internal_description%TYPE,
            tranval              tranpays.tranval%TYPE,
            tranval_currency_id  tranpays.tranval_currency_id%TYPE,
            account_id           tranpays.account_id%TYPE,
            test_record          tranpays.test_record%TYPE,
            status               tranpays.status%TYPE);

        vTranpayRec TranpayRecord;

        CURSOR cTranpayDetails(pRequestTypeId tranpay_attribs.req_type_id%TYPE) IS
            SELECT ta.req_type_id,
                   ta.id attrib_id,
                   NVL(td.description, ta.description) description,
                   td.data_vchar, td.data_blob
            FROM tranpay_attribs ta
            LEFT JOIN tranpay_details td ON (td.attrib_id = ta.id AND td.req_type_id = ta.req_type_id AND td.tranpay_id = pTranpayId)
            WHERE ta.req_type_id LIKE pRequestTypeId AND ta.for_copy = 1;

        PROCEDURE writeActionLog(
          pLogMessage IN VARCHAR2,
          pRefObject IN VARCHAR2 DEFAULT NULL
        ) IS
        BEGIN
          authorization_pck.writeActionLog(
              pActions => actions_list(common_pck.cACT_ViewTranpays),
              pLogMessage => pTranpayId || ':' || pName || ':' || pDescription || ':' || pTemplate_package_id || ':-' || pLogMessage,
              pRefClass => cREF_CLASS|| myunit,
              pRefObject => pRefObject);
        END writeActionLog;

    BEGIN
        slog.debug(pkgCtxId, myUnit,  pTranpayId || ':' || pName || ':' || pDescription || pTemplate_package_id);

        common_pck.CommonSecurityChecks;

        SELECT req_type_id,
                description, internal_description, tranval,
                tranval_currency_id, account_id,
                0 test_record, status
        INTO vTranpayRec
        FROM tranpays
        WHERE id = pTranpayId;

        writeActionLog(pLogMessage => 'Retrieved data for template', pRefObject => NULL);

        IF NOT tranpayExists(pTranpayId => pTranpayId, pTranpayStatus => vTranpayRec.status) THEN
			slog.error(pkgCtxId, myUnit, cERR_InvalidTranpay, pTranpayId || ':' ||vTranpayRec.status);
            sspkg.raiseError(cERR_InvalidTranpay, null, pkgCtxId, myunit);
        END IF;

        vTranpayTemplateId := CreateNewTranpayTemplate(
            pName => pName,
            pDescription => pDescription,
            pRequest_type_id => vTranpayRec.req_type_id,
            pTranpay_description => vTranpayRec.description,
            pInternal_description => vTranpayRec.internal_description,
            pAccount_id => vTranpayRec.account_id,
            pTranval => vTranpayRec.tranval,
            pCurrency_id => vTranpayRec.tranval_currency_id,
            pTest_record => vTranpayRec.test_record,
            pTemplate_package_id => pTemplate_package_id);

        FOR i IN cTranpayDetails(pRequestTypeId => vTranpayRec.req_type_id) LOOP
            BEGIN
                AppendTranpayTemplateDetailInt(pTranpayTemplateId => vTranpayTemplateId,
                                            pAttributeReqTypeId => i.req_type_id,
                                            pAttributeId => i.attrib_id,
                                            pTranpayTemplateDetDesc => i.description,
                                            pTranpayTemplateDetDataVChar => i.data_vchar,
                                            pTranpayTemplateDetDataBlob => i.data_blob);
            EXCEPTION
                WHEN others THEN
                    NULL;
            END;
        END LOOP;

        RETURN vTranpayTemplateId;
    EXCEPTION
        WHEN no_data_found THEN
            writeActionLog(pLogMessage => sspkg.readVChar(cERR_InvalidTranpay), pRefObject => pTranpayId);
			slog.error(pkgCtxId, myUnit, cERR_InvalidTranpay, pTranpayId || ':' ||vTranpayRec.status);
            sspkg.raiseError(cERR_InvalidTranpay, null, pkgCtxId, myunit);
    END CreateTemplateFromTranpay;

    PROCEDURE UpdateExistingTranpayTemplate(
            pId tranpay_templates.id%TYPE,
            pName tranpay_templates.name%TYPE,
            pDescription tranpay_templates.description%TYPE,
            pRequest_type_id tranpay_templates.request_type_id%TYPE,
            pTranpay_description tranpay_templates.tranpay_description%TYPE,
            pInternal_description tranpay_templates.internal_description%TYPE,
            pAccount_id tranpay_templates.account_id%TYPE,
            pTranval tranpay_templates.tranval%TYPE,
            pCurrency_id tranpay_templates.currency_id%TYPE,
            pTest_record tranpay_templates.test_record%TYPE,
            pTemplate_package_id tranpay_templates.template_package_id%TYPE,
            pValid tranpay_templates.valid%TYPE)
    IS
            myunit CONSTANT VARCHAR2(29) := 'UpdateExistingTranpayTemplate';
            vAccountOwnerId account_owners.id%TYPE;
			vROWID ROWID;
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pId ||':' || pRequest_type_id || ':' || pAccount_id || ':' || pTranval || ':' || pCurrency_id || ':' || pTemplate_package_id);
        common_pck.CommonSecurityChecks;

        DECLARE
            vRequestTypeId tranpay_templates.request_type_id%TYPE;
        BEGIN
            IF pTemplate_package_id IS NOT NULL THEN
                SELECT DISTINCT request_type_id
                  INTO vRequestTypeId
                  FROM tranpay_templates
                 WHERE template_package_id = pTemplate_package_id;
            END IF;
        EXCEPTION
            WHEN no_data_found THEN
                null; -- new record
            WHEN too_many_rows THEN
				slog.error(pkgCtxId, myUnit, cERR_VariousTrpTmplReqTypes, pTemplate_package_id);
                sspkg.raiseError(cERR_VariousTrpTmplReqTypes, null, pkgCtxId, myunit);
        END;

        BEGIN
          SELECT ROWID INTO vROWID from tranpay_templates WHERE id = pId and user_id = mcauth.auth.getClientId;
        EXCEPTION
          WHEN no_data_found THEN
            slog.error(pkgCtxId, myUnit, cERR_InvalidTranpayTemplate, pId || ':' || mcauth.auth.getClientId);
            sspkg.raiseError(cERR_InvalidTranpayTemplate, null, pkgCtxId, myunit);
        END;

        IF pAccount_id IS NOT NULL THEN
          vAccountOwnerId := accounts_pck.getAccountOwner(pAccountId => pAccount_id);
        ELSE
          vAccountOwnerId := NULL;
        END IF;

        UPDATE tranpay_templates
           SET name                     = pName,
               description              = pDescription,
               request_type_id          = pRequest_type_id,
               tranpay_description      = pTranpay_description,
               internal_description     = pInternal_description,
               account_id               = pAccount_id,
               tranval                  = pTranval,
               currency_id              = pCurrency_id,
               test_record              = pTest_record,
               template_package_id      = pTemplate_package_id,
               account_owner_id         = vAccountOwnerId
         WHERE ROWID = vROWID;

    EXCEPTION
      WHEN dup_val_on_index THEN
        slog.error(pkgCtxId, myUnit, cERR_TmplWithGivenNameExists, pId || ':' || mcauth.auth.getClientId);
        sspkg.raiseError(cERR_TmplWithGivenNameExists, null, pkgCtxId, myunit);

    END UpdateExistingTranpayTemplate;

    PROCEDURE addTranpayTemplateToPackage(pId tranpay_templates.id%TYPE,
            pTemplate_package_id tranpay_templates.template_package_id%TYPE)
    IS
        myunit CONSTANT VARCHAR2(27) := 'addTranpayTemplateToPackage';
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pId || ':' || pTemplate_package_id);
        common_pck.CommonSecurityChecks;

        DECLARE
            vRequestTypeId tranpay_templates.request_type_id%TYPE;
        BEGIN
            IF pTemplate_package_id IS NOT NULL THEN
                SELECT DISTINCT request_type_id
                  INTO vRequestTypeId
                  FROM tranpay_templates
                 WHERE template_package_id = pTemplate_package_id;
            END IF;
        EXCEPTION
            WHEN no_data_found THEN
                null; -- new record
            WHEN too_many_rows THEN
				slog.error(pkgCtxId, myUnit, cERR_VariousTrpTmplReqTypes, pTemplate_package_id);
                sspkg.raiseError(cERR_VariousTrpTmplReqTypes, null, pkgCtxId, myunit);
        END;

        UPDATE tranpay_templates
           SET template_package_id      = pTemplate_package_id
         WHERE id = pId;

    END addTranpayTemplateToPackage;

    PROCEDURE remTranpayTemplateFromPackage(pId tranpay_templates.id%TYPE)
    IS
        myunit CONSTANT VARCHAR2(29) := 'remTranpayTemplateFromPackage';
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pId);
        common_pck.CommonSecurityChecks;

        UPDATE tranpay_templates
           SET template_package_id = NULL
         WHERE id = pId;

    END remTranpayTemplateFromPackage;

    PROCEDURE removeTemplatesFromPackage(pId IN template_packages.id%TYPE)
    IS
        myunit CONSTANT VARCHAR2(26) := 'removeTemplatesFromPackage';
        vPom PLS_INTEGER;
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pId);
        common_pck.CommonSecurityChecks;

        BEGIN
        SELECT null INTO vPom FROM DUAL
        WHERE EXISTS (
          SELECT NULL
          FROM vw$user_template_packages
         WHERE id = pId);
        EXCEPTION
          WHEN no_data_found THEN
			slog.error(pkgCtxId, myUnit, cERR_InvTranpayTemplatePackage, pId);
            sspkg.raiseError(cERR_InvTranpayTemplatePackage, null, pkgCtxId, myunit);
        END;

        UPDATE tranpay_templates
           SET template_package_id = NULL
         WHERE template_package_id = pId;

    END removeTemplatesFromPackage;

    PROCEDURE removeTranpayTemplate(pId tranpay_templates.id%TYPE)
    IS
        myunit CONSTANT VARCHAR2(21) := 'removeTranpayTemplate';
        vPom VARCHAR2(1);
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pId);
        common_pck.CommonSecurityChecks;

        SELECT NULL
        INTO vPom
        FROM vw$user_tranpay_templates
        WHERE id = pId;

        DELETE FROM tranpay_template_details
        WHERE tranpay_template_id = pId;

        DELETE FROM tranpay_templates
        WHERE id = pId;

    EXCEPTION
        WHEN no_data_found THEN
			slog.error(pkgCtxId, myUnit, cERR_InvalidTranpayTemplate, pId);
            sspkg.raiseError(cERR_InvalidTranpayTemplate, null, pkgCtxId, myunit);
    END removeTranpayTemplate;

    -- TRANPAY TEMPLATE DETAILS
    -- Tranpays Templates details manipulation
	-- Internal procedure. DO NOT EXPOSE TO PUBLIC. Use AppendTranpayTemplateDetail instead
    PROCEDURE AppendTranpayTemplateDetailInt(pTranpayTemplateId tranpay_template_details.tranpay_template_id%TYPE,
                                        pAttributeReqTypeId tranpay_template_details.tranpay_attrib_req_type_id%TYPE,
                                        pAttributeId tranpay_template_details.tranpay_attrib_id%TYPE,
                                        pTranpayTemplateDetDesc tranpay_template_details.description%TYPE,
                                        pTranpayTemplateDetDataVChar tranpay_template_details.data_vchar%TYPE,
                                        pTranpayTemplateDetDataBlob tranpay_template_details.data_blob%TYPE
                                        )
    IS
        myunit CONSTANT VARCHAR2(30) := 'AppendTranpayTemplateDetailInt';

    BEGIN
        slog.debug(pkgCtxId, myUnit,  pTranpayTemplateId ||':' ||pAttributeReqTypeId ||':'||pAttributeId||':'||pTranpayTemplateDetDataVChar);

        MERGE INTO tranpay_template_details ttd
           USING (SELECT id, req_type_id, for_copy
                    FROM tranpay_attribs
                    WHERE   id = pAttributeId
                        AND req_type_id = pAttributeReqTypeId) ta
            ON (   ttd.tranpay_attrib_id = ta.id
               AND ttd.tranpay_attrib_req_type_id = ta.req_type_id
               AND ttd.tranpay_template_id = pTranpayTemplateId)
       WHEN MATCHED THEN
            UPDATE SET description  = pTranpayTemplateDetDesc,
                       data_vchar   = pTranpayTemplateDetDataVChar,
                       data_blob    = pTranpayTemplateDetDataBlob
       WHEN NOT MATCHED THEN
            INSERT (tranpay_attrib_req_type_id, tranpay_attrib_id,
                    tranpay_template_id, description, data_vchar, data_blob)
            VALUES (ta.req_type_id, ta.id, pTranpayTemplateId,
                    pTranpayTemplateDetDesc, pTranpayTemplateDetDataVChar, pTranpayTemplateDetDataBlob)
			WHERE ta.for_copy = 1;

    END AppendTranpayTemplateDetailInt;

	PROCEDURE AppendTranpayTemplateDetail(pTranpayTemplateId tranpay_template_details.tranpay_template_id%TYPE,
                                        pAttributeReqTypeId tranpay_template_details.tranpay_attrib_req_type_id%TYPE,
                                        pAttributeId tranpay_template_details.tranpay_attrib_id%TYPE,
                                        pTranpayTemplateDetDesc tranpay_template_details.description%TYPE,
                                        pTranpayTemplateDetDataVChar tranpay_template_details.data_vchar%TYPE,
                                        pTranpayTemplateDetDataBlob tranpay_template_details.data_blob%TYPE
                                        )
    AS
    BEGIN
		common_pck.CommonSecurityChecks;
		AppendTranpayTemplateDetailInt(pTranpayTemplateId, pAttributeReqTypeId, pAttributeId, pTranpayTemplateDetDesc, pTranpayTemplateDetDataVChar, pTranpayTemplateDetDataBlob);
	END AppendTranpayTemplateDetail;

    PROCEDURE VerifyTemplate(pTranpayTemplateId tranpay_templates.id%TYPE) IS
        CURSOR c_existingData(pRequestTypeId tranpay_templates.request_type_id%TYPE) IS
            SELECT ta.id, ta.description
              FROM tranpay_attribs ta
             WHERE ta.req_type_id = pRequestTypeId
               AND ta.value_required = 1
               AND NOT exists (SELECT 1
                               FROM tranpay_template_details td
                               WHERE td.TRANPAY_ATTRIB_REQ_TYPE_ID = pRequestTypeId
                               AND td.TRANPAY_TEMPLATE_ID = pTranpayTemplateId
                               AND td.TRANPAY_ATTRIB_ID = ta.id);

        vMissingAttributes VARCHAR2(4000) := NULL;
        vRequestTypeId tranpay_templates.request_type_id%TYPE;
        myunit CONSTANT VARCHAR2(14) := 'VerifyTemplate';
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pTranpayTemplateId);
        common_pck.CommonSecurityChecks;

        vRequestTypeId := getTranpayTemplateRequestType(pTranpayTemplateId);

        FOR i IN c_existingData(vRequestTypeId) LOOP
            vMissingAttributes := vMissingAttributes || i.id || ' - ' || i.description || chr(10);
        END LOOP;
        IF vMissingAttributes IS NOT NULL THEN
			slog.error(pkgCtxId, myUnit, cERR_RequiredAttributeMissing, pTranpayTemplateId || ':' || vRequestTypeId);
            sspkg.raiseError(cERR_RequiredAttributeMissing, 'Missing values for attributes: ' || chr(10) || vMissingAttributes, pkgCtxId, myunit);
        END IF;
    END VerifyTemplate;

    -- Procedure za upravljanjem vremenskim okvirima (scheduler)
    FUNCTION CreateNewSchedule(pScheduleType schedule_types.type%TYPE,
            pActive schedules.active%TYPE,
            pExecuteNotBefore schedules.not_before%TYPE,
            pExecuteNotAfter schedules.not_after%TYPE,
            pMaxRetryCount schedules.max_retry_count%TYPE,
            pRetryInterval schedules.retry_interval%TYPE,
            pRetryIntervalUnit schedules.retry_int_unit%TYPE,
            pMinBalance schedules.min_balance%TYPE,
            pHour PLS_INTEGER,
            pMinute PLS_INTEGER,
            pDay PLS_INTEGER,
            pMonth PLS_INTEGER,
            pYear PLS_INTEGER)
    RETURN schedules.id%TYPE IS
        vScheduleId schedules.id%TYPE;
        myunit CONSTANT VARCHAR2(17) := 'CreateNewSchedule';
        vMaxRetryCounter PLS_INTEGER;
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pScheduleType ||':' ||pActive ||':'||pExecuteNotBefore||':'||pExecuteNotAfter
        ||':'||pMaxRetryCount||':'||pRetryInterval||':'||pRetryIntervalUnit||':'||pMinBalance||':'||pHour
        ||':'||pMinute||':'||pDay||':'||pMonth||':'||pYear);
        common_pck.CommonSecurityChecks;

        BEGIN
            vMaxRetryCounter := sspkg.ReadInt('/Core/Main/TranPays/maxRetryCounter');

            vMaxRetryCounter := LEAST(pMaxRetryCount, vMaxRetryCounter);
        EXCEPTION
            WHEN others THEN
                NULL;
        END;

        INSERT INTO schedules(schedule_type_type, active,
               not_before, not_after, max_retry_count, retry_interval,
               retry_int_unit, min_balance, ph0, ph1, ph2, ph3, ph4)
        VALUES(pScheduleType, pActive,
            pExecuteNotBefore, pExecuteNotAfter,
            vMaxRetryCounter, pRetryInterval, pRetryIntervalUnit, pMinBalance,
            pHour, pMinute, pDay, pMonth, pYear)
        RETURN id INTO vScheduleId;

        RETURN vScheduleId;
    END CreateNewSchedule;

    PROCEDURE CancelSchedules(pScheduleId tranpays.schedule_id%TYPE,
                    pStatusMessage tranpays.status_message%TYPE := NULL,
                    pCancelFutureTranpays VARCHAR2 := 'N'
    )
    IS
        myunit CONSTANT VARCHAR2(15) := 'CancelSchedules';

        TYPE tranpay_id_tab IS TABLE OF tranpays.id%TYPE;
        affected_tranpays tranpay_id_tab;

        PROCEDURE writeActionLog(
          pLogMessage IN VARCHAR2,
          pRefObject IN VARCHAR2 DEFAULT NULL
        ) IS
        BEGIN
          authorization_pck.writeActionLog(
              pActions => actions_list(common_pck.cACT_CancelTranpay,common_pck.cACT_CancelSignedTranpay),
              pLogMessage => pScheduleId || ':' || pStatusMessage || ':' ||
                            pCancelFutureTranpays || ':-' || pLogMessage,
              pRefClass => cREF_CLASS|| myunit,
              pRefObject => pRefObject);
        END writeActionLog;
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pScheduleId ||':' ||pStatusMessage ||':'||pCancelFutureTranpays);
        common_pck.CommonSecurityChecks;

        UPDATE schedules
           SET active = 0
         WHERE id = pScheduleId;

        writeActionLog(
          pLogMessage => 'Schedule successfully canceled!',
          pRefObject => pScheduleId);

        IF pCancelFutureTranpays = 'Y' THEN
          UPDATE tranpays
              SET status = common_pck.cTRPSTS_UC,
                  status_message = pStatusMessage
          WHERE schedule_id = pScheduleId
            AND status IN (common_pck.cTRPSTS_UW, common_pck.cTRPSTS_VB, common_pck.cTRPSTS_UD, common_pck.cTRPSTS_US)
          RETURNING id BULK COLLECT INTO affected_tranpays;

          FOR i IN 1..affected_tranpays.COUNT LOOP
            writeActionLog(
              pLogMessage => 'Tranpay canceled!',
              pRefObject => affected_tranpays(i));
          END LOOP;
        END IF;

    EXCEPTION
      WHEN sspkg.sysException THEN
        writeActionLog(
          pLogMessage => sspkg.getErrorMessage,
          pRefObject => NULL);
        RAISE;
      WHEN OTHERS THEN
		slog.error(pkgCtxId, myUnit, pScheduleId ||':' ||pStatusMessage ||':'||pCancelFutureTranpays || ':' || sqlcode || ':' || sqlerrm);
        writeActionLog(
          pLogMessage => sqlerrm,
          pRefObject => NULL);
        RAISE;
    END CancelSchedules;

    -- TRANPAY GROUPS
    FUNCTION CreateNewTranpayGroup(pName tranpay_groups.name%TYPE, pDescription tranpay_groups.description%TYPE, pTranpayGroupTypeId tranpay_group_type.id%TYPE DEFAULT NULL)
    RETURN tranpay_groups.id%TYPE IS
        myunit CONSTANT VARCHAR2(21) := 'CreateNewTranpayGroup';
        vTranpayGroupId tranpay_groups.id%TYPE;
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pName ||':' ||pDescription);
        common_pck.CommonSecurityChecks;

        INSERT INTO tranpay_groups(name, description, valid, tranpay_group_type_id)
        VALUES(pName, pDescription, 1, pTranpayGroupTypeId)
        RETURNING ID INTO vTranpayGroupId;

        RETURN vTranpayGroupId;
    END CreateNewTranpayGroup;

    FUNCTION getTranpayAttribute(pRequestTypeId tranpay_attribs.req_type_id%TYPE,
                                 pTranpayAttribId tranpay_attribs.id%TYPE,
                                 pColumnName VARCHAR2)
    RETURN VARCHAR2 IS
        tranpay_attrib tranpay_attrib_rec;
        myunit CONSTANT VARCHAR2(19) := 'getTranpayAttribute';
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pRequestTypeId ||':' ||pTranpayAttribId || ':' || pColumnName);

        IF pColumnName IS NULL THEN
            RETURN NULL;
        END IF;

        SELECT description, basic_datatype, maxsize, value_required, blob_mimetype, regexp
         INTO tranpay_attrib
         FROM tranpay_attribs
        WHERE id = pTranpayAttribId
          AND req_type_id = pRequestTypeId;

        CASE UPPER(pColumnName)
         WHEN 'DESCRIPTION' THEN RETURN tranpay_attrib.description;
         WHEN 'BASIC_DATATYPE' THEN RETURN tranpay_attrib.basic_datatype;
         WHEN 'MAXSIZE' THEN RETURN tranpay_attrib.maxsize;
         WHEN 'VALUE_REQUIRED' THEN RETURN tranpay_attrib.value_required;
         WHEN 'BLOB_MIMETYPE' THEN RETURN tranpay_attrib.blob_mimetype;
         WHEN 'REGEXP' THEN RETURN tranpay_attrib.regexp;
         ELSE RETURN NULL;
        END CASE;
    EXCEPTION
      WHEN no_data_found THEN
		slog.error(pkgCtxId, myUnit, cERR_RequiredAttributeMissing, pTranpayAttribId || ':' || pRequestTypeId || ':' || pColumnName);
        sspkg.raiseError(cERR_InvalidTranpayAttribute, null, pkgCtxId, myunit);
    END getTranpayAttribute;

    -- TRANPAYS
    FUNCTION CreateNewTranPayInt(pRequestTypeId tranpays.req_type_id%TYPE,
        pDescription tranpays.description%TYPE,
        pInternalDescription tranpays.internal_description%TYPE,
        pTranval tranpays.tranval%TYPE,
        pTranvalCurrencyId tranpays.tranval_currency_id%TYPE,
        pTranpayGroupId tranpays.tranpay_group_id%TYPE,
        pAccountId tranpays.account_id%TYPE,
        pChartOfAccountsId tranpays.chart_of_accounts_id%TYPE,
        pScheduleId tranpays.schedule_id%TYPE,
        pTestRecord tranpays.test_record%TYPE,
		pExtRef tranpays.extref%TYPE DEFAULT NULL)

    RETURN tranpays.id%TYPE IS
        vTranPayId tranpays.id%TYPE;
        myunit CONSTANT VARCHAR2(19) := 'CreateNewTranPayInt';
        vPom VARCHAR2(1);

        PROCEDURE writeActionLog(
          pLogMessage IN VARCHAR2,
          pRefObject IN VARCHAR2 DEFAULT NULL
        )
        IS
        BEGIN
          authorization_pck.writeActionLog(
              pActionId => common_pck.cACT_CreateTranpay,
              pLogMessage => pRequestTypeId ||  ':' || pDescription ||  ':' || pInternalDescription ||  ':'|| pTranval ||  ':'
              || pTranvalCurrencyId ||  ':'|| pTranpayGroupId ||  ':'|| pAccountId ||  ':'|| pChartOfAccountsId ||  ':'
              || pScheduleId ||  ':'|| pTestRecord ||  ':-' || pLogMessage,
              pRefClass => cREF_CLASS|| myunit,
              pRefObject => pRefObject);
        END writeActionLog;

    BEGIN
        slog.debug(pkgCtxId, myUnit,  pRequestTypeId ||':' ||pTranval || ':' || pTranvalCurrencyId
			|| ':' || pTranpayGroupId || ':' || pAccountId || ':' || pScheduleId || ':' || pTestRecord);

        IF pAccountId IS NULL THEN
          sspkg.raiseError(cERR_NoAccount, null, pkgCtxId, myunit);
        END IF;

        IF
        (NOT authorization_pck.hasGrantOnCreateTranpay(pAccountId, pRequestTypeId))
        AND (NOT authorization_pck.hasGrantOnSmsPlacanje(pAccountId, pRequestTypeId))
         THEN
			slog.error(pkgCtxId, myUnit, cERR_NoCreatePrivilege, pAccountId || ':' || pRequestTypeId);
            sspkg.raiseError(cERR_NoCreatePrivilege, null, pkgCtxId, myunit);
        END IF;

        CheckNewTranpayData(
                    pRequestTypeId      => pRequestTypeId,
                    pTranval            => pTranval,
                    pTranvalCurrencyId  => pTranvalCurrencyId,
                    pTranpayGroupId     => pTranpayGroupId,
                    pChartOfAccountsId  => pChartOfAccountsId,
                    pScheduleId         => pScheduleId);

        INSERT INTO tranpays(
            req_type_id, status,
            description, internal_description, tranval,
            tranval_currency_id,
            tranpay_group_id, account_id,
            chart_of_accounts_id, schedule_id,
            test_record, extref)
        VALUES(pRequestTypeId, common_pck.cTRPSTS_UW,
                pDescription, pInternalDescription, pTranval,
                pTranvalCurrencyId,
                pTranpayGroupId, pAccountId,
                pChartOfAccountsId, pScheduleId,
                pTestRecord, pExtRef)
        RETURNING id INTO vTranPayId;

        slog.debug(pkgCtxId, myUnit, 'Created new tranpay with ID:'||vTranPayId);

        AppendBackgroundDetails(pTranpayId => vTranPayId,
                                pReqTypeId => pRequestTypeId,
                                pAccountId => pAccountId);
        slog.debug(pkgCtxId, myUnit, 'Background tranpay details populated');

        writeActionLog(
          pLogMessage => 'Created tranpay',
          pRefObject => vTranPayId);
		  
		  -- integracija splunk-a za kreiranje trajnog/zakazanog naloga
		  BEGIN
		  
		  	SELECT null INTO vPom FROM DUAL WHERE EXISTS (SELECT NULL FROM tranpays t join schedules s on s.id = t.schedule_id where t.id = vTranPayId and s.schedule_type_type not in (common_pck.cSCHDTYPE_IMMEDIATE, common_pck.cSCHDTYPE_LATER));

			RegisterSplunkNotification(vTranPayId, common_pck.cACT_StandingOrder);
		  
		  EXCEPTION
			WHEN no_data_found THEN
				slog.debug(pkgCtxId, myUnit, 'No schedule for tranpay: ', vTranPayId);
		  END;

        RETURN vTranPayId;
    EXCEPTION
    WHEN sspkg.sysException THEN
      writeActionLog(
          pLogMessage => sspkg.getErrorMessage,
          pRefObject => NULL);
      RAISE;
    WHEN OTHERS THEN
	  slog.error(pkgCtxId, myUnit, pRequestTypeId ||':' ||pTranval || ':' || pTranvalCurrencyId
			|| ':' || pTranpayGroupId || ':' || pAccountId || ':' || pScheduleId || ':' || pTestRecord || ':' || sqlcode || ':' || sqlerrm);
      writeActionLog(
          pLogMessage => sqlerrm,
          pRefObject => NULL);
      RAISE;
    END CreateNewTranPayInt;

	FUNCTION CreateNewTranPay(pRequestTypeId tranpays.req_type_id%TYPE,
        pDescription tranpays.description%TYPE,
        pInternalDescription tranpays.internal_description%TYPE,
        pTranval tranpays.tranval%TYPE,
        pTranvalCurrencyId tranpays.tranval_currency_id%TYPE,
        pTranpayGroupId tranpays.tranpay_group_id%TYPE,
        pAccountId tranpays.account_id%TYPE,
        pChartOfAccountsId tranpays.chart_of_accounts_id%TYPE,
        pScheduleId tranpays.schedule_id%TYPE,
        pTestRecord tranpays.test_record%TYPE,
		pExtRef tranpays.extref%TYPE DEFAULT NULL)

    RETURN tranpays.id%TYPE IS
	BEGIN
		common_pck.CommonSecurityChecks;
		RETURN CreateNewTranPayInt(pRequestTypeId, pDescription, pInternalDescription, pTranval, pTranvalCurrencyId, pTranpayGroupId, pAccountId, pChartOfAccountsId, pScheduleId, pTestRecord, pExtRef);
	END CreateNewTranPay;

    FUNCTION CreateTranpaysForTemplPackage(pTemplatePackageId template_packages.id%TYPE)
    RETURN PLS_INTEGER
    IS
        CURSOR c_template IS
         SELECT tt.id, tt.request_type_id,
               tt.tranpay_description, tt.internal_description, tt.account_id,
               tt.tranval, tt.currency_id, tt.user_id
          FROM tranpay_templates tt
         WHERE tt.template_package_id = pTemplatePackageId
           AND tt.valid = 1
		AND tt.account_owner_id like mcauth.auth.getAccountOwner;

        CURSOR c_template_detail(pRequestTypeId tranpay_attribs.req_type_id%TYPE,
                                 pTranpayTemplateId tranpay_templates.id%TYPE) IS
        SELECT  ttd.tranpay_attrib_id, ttd.description, ttd.data_vchar, ttd.data_blob
        FROM    tranpay_template_details ttd,
                tranpay_templates tt
         WHERE ttd.tranpay_template_id = tt.id
           AND tt.id = pTranpayTemplateId
           AND ttd.tranpay_attrib_req_type_id = pRequestTypeId;

        myunit CONSTANT VARCHAR2(29) := 'CreateTranpaysForTemplPackage';
        vTranpayId tranpays.id%TYPE;
        vNoOfCreatedTranpays PLS_INTEGER := 0;
		vNazivPosiljaoca tranpay_details.data_vchar%TYPE;
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pTemplatePackageId);
        common_pck.CommonSecurityChecks;

        FOR i IN c_template LOOP
            vTranpayId := CreateNewTranPayInt(pRequestTypeId => i.request_type_id,
                            pDescription => i.tranpay_description,
                            pInternalDescription => i.internal_description,
                            pTranval => i.tranval,
                            pTranvalCurrencyId => i.currency_id,
                            pTranpayGroupId => NULL,
                            pAccountId => i.account_id,
                            pChartOfAccountsId => NULL,
                            pScheduleId => NULL,
                            pTestRecord => 0);

            FOR j IN c_template_detail(i.request_type_id, i.id) LOOP
                AppendTranPayDetailInt(pTranpayId => vTranpayId,
                                    pRequestTypeId => i.request_type_id,
                                    pAttribId => j.tranpay_attrib_id,
                                    pDescription => j.description,
                                    pDataVCHAR => j.data_vchar,
                                    pDataBLOB  => j.data_blob);																
            END LOOP;
	
			IF isValidTranpayAttribute(i.request_type_id, common_pck.cTRANPAY_ATT_NAZIV_POSILJAOCA) THEN

				vNazivPosiljaoca := prepareTranpaySenderData(pUserID => i.user_id, pAccountID => i.account_id);

				INSERT INTO tranpay_details (req_type_id, tranpay_id, attrib_id, data_vchar)
				VALUES (i.request_type_id, vTranpayId, common_pck.cTRANPAY_ATT_NAZIV_POSILJAOCA, vNazivPosiljaoca);
			ELSE
				slog.debug(pkgCtxId, myUnit, 'Sender name not settable : ' || i.request_type_id || ':' || common_pck.cTRANPAY_ATT_NAZIV_POSILJAOCA);
			END IF;
				
            vNoOfCreatedTranpays := vNoOfCreatedTranpays + 1;
        END LOOP;
        RETURN vNoOfCreatedTranpays;
    END CreateTranpaysForTemplPackage;

	PROCEDURE UpdateTranpayDescription(pTranpayId tranpays.id%TYPE,
		pDescription tranpays.description%TYPE)
    IS
		myunit CONSTANT VARCHAR2(24) := 'UpdateTranpayDescription';
		cg$rec cg$tranpays.cg$row_type;
		cg$ind cg$tranpays.cg$ind_type;
	BEGIN
		slog.debug(pkgCtxId, myUnit,  pTranpayId || ':' || pDescription);
		common_pck.CommonSecurityChecks;

		cg$rec.id := pTranpayId;

		BEGIN
			cg$tranpays.slct(cg$sel_rec => cg$rec);
		EXCEPTION
			WHEN no_data_found THEN
				slog.error(pkgCtxId, myUnit, cERR_InvalidTranpay, pTranpayId);
                sspkg.raiseError(cERR_InvalidTranpay, null, pkgCtxId, myunit);
		END;

		IF NOT authorization_pck.hasGrantOnActionId(pActionId => common_pck.cACT_CreateTranpay, pAccountId => cg$rec.account_id, pRequestTypeId => cg$rec.req_type_id) THEN
			slog.error(pkgCtxId, myUnit, cERR_NoCreatePrivilege, cg$rec.account_id || ':' || cg$rec.req_type_id);
            sspkg.raiseError(cERR_NoCreatePrivilege, null, pkgCtxId, myunit);
		END IF;

		cg$rec.description := pDescription;
		cg$ind.description := TRUE;

		cg$tranpays.upd(cg$rec, cg$ind, TRUE);

	END UpdateTranpayDescription;

	PROCEDURE UpdateBeneficiary(
		pTranpayId IN tranpays.id%TYPE,
		pBeneficiaryAccountId IN VARCHAR2,
		pBeneficiaryName IN VARCHAR2)
	IS
		myunit CONSTANT VARCHAR2(17) := 'UpdateBeneficiary';
		cg$rec cg$tranpays.cg$row_type;

	BEGIN
		slog.debug(pkgCtxId, myUnit,  pTranpayId || ':' || pBeneficiaryAccountId || ':' || pBeneficiaryName);

		IF (pBeneficiaryAccountId IS NULL OR pBeneficiaryName IS NULL) THEN
			slog.error(pkgCtxId, myUnit, pTranpayId || ':' || pBeneficiaryAccountId || ':' || pBeneficiaryName);
			sspkg.raiseError(common_pck.cERR_MissingParameter, null, pkgCtxId, myunit);
		END IF;

		common_pck.CommonSecurityChecks;

		cg$rec.id := pTranpayId;

		BEGIN
			cg$tranpays.slct(cg$sel_rec => cg$rec);
		EXCEPTION
			WHEN no_data_found THEN
				slog.error(pkgCtxId, myUnit, cERR_InvalidTranpay, pTranpayId);
                sspkg.raiseError(cERR_InvalidTranpay, null, pkgCtxId, myunit);
		END;

		IF NOT authorization_pck.hasGrantOnActionId(pActionId => common_pck.cACT_CreateTranpay, pAccountId => cg$rec.account_id, pRequestTypeId => cg$rec.req_type_id) THEN
			slog.error(pkgCtxId, myUnit, cERR_NoCreatePrivilege, cg$rec.account_id || ':' || cg$rec.req_type_id);
            sspkg.raiseError(cERR_NoCreatePrivilege, null, pkgCtxId, myunit);
		END IF;

		update mcore.tranpay_details set data_vchar = pBeneficiaryAccountId where tranpay_id = cg$rec.id AND attrib_id = common_pck.cTRANPAY_ATT_RACUN_PRIMAOCA;
		update mcore.tranpay_details set data_vchar = pBeneficiaryName where tranpay_id = cg$rec.id AND attrib_id = common_pck.cTRANPAY_ATT_NAZIV_PRIMAOCA;

	END UpdateBeneficiary;

    PROCEDURE UpdateTranpay(pTranpayId tranpays.id%TYPE,
        pDescription tranpays.description%TYPE,
        pInternalDescription tranpays.internal_description%TYPE,
        pTranval tranpays.tranval%TYPE,
        pTranvalCurrencyId tranpays.tranval_currency_id%TYPE,
        pTranpayGroupId tranpays.tranpay_group_id%TYPE,
        pAccountId tranpays.account_id%TYPE,
        pChartOfAccountsId tranpays.chart_of_accounts_id%TYPE,
        pScheduleId tranpays.schedule_id%TYPE,
        pTestRecord tranpays.test_record%TYPE,
        pRequestTypeId tranpays.req_type_id%TYPE)
    IS
        myunit CONSTANT VARCHAR2(13) := 'UpdateTranpay';

        PROCEDURE writeActionLog(
          pLogMessage IN VARCHAR2,
          pRefObject IN VARCHAR2 DEFAULT NULL
        ) IS
        BEGIN
          authorization_pck.writeActionLog(
              pActions => actions_list(common_pck.cACT_CreateTranpay),
              pLogMessage => pTranpayId || ':' || pRequestTypeId || ':' || pDescription || ':' || pInternalDescription || ':' || pTranval || ':' ||
              pTranvalCurrencyId || ':' || pTranpayGroupId || ':' || pAccountId || ':' || pChartOfAccountsId || ':' ||
              pScheduleId || ':' || pTestRecord || ':-' || pLogMessage,
              pRefClass => cREF_CLASS|| myunit,
              pRefObject => pRefObject);
        END writeActionLog;
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pTranpayId || ':' || pRequestTypeId ||':' ||pTranval || ':' || pTranvalCurrencyId
        || ':' || pTranpayGroupId || ':' || pAccountId || ':' || pScheduleId || ':' || pTestRecord);

        common_pck.CommonSecurityChecks;

        IF pAccountId IS NULL THEN
          sspkg.raiseError(cERR_NoAccount, null, pkgCtxId, myunit);
        END IF;

        -- Check if updating is possible
        CheckStatusTransition(pTranpayId => pTranpayId, pNewStatus => common_pck.cTRPSTS_UW);

        -- Check if all data meet common criteria
        CheckNewTranpayData(
                    pRequestTypeId => pRequestTypeId,
                    pTranval => pTranval,
                    pTranvalCurrencyId => pTranvalCurrencyId,
                    pTranpayGroupId => pTranpayGroupId,
                    pChartOfAccountsId => pChartOfAccountsId,
                    pScheduleId => pScheduleId);

        UPDATE tranpays
           SET description = pDescription,
               internal_description = pInternalDescription,
               tranval              = pTranval,
               tranval_currency_id  = pTranvalCurrencyId,
               tranpay_group_id     = pTranpayGroupId,
               account_id           = pAccountId,
               chart_of_accounts_id = pChartOfAccountsId,
               schedule_id          = pScheduleId,
               test_record          = pTestRecord
        WHERE id = pTranpayId;

        AppendBackgroundDetails(pTranpayId => pTranpayId,
                                pReqTypeId => pRequestTypeId,
                                pAccountId => pAccountId);

        writeActionLog(
          pLogMessage => 'Tranpay updated!',
          pRefObject => pTranpayId);
    EXCEPTION
      WHEN sspkg.sysException THEN
        writeActionLog(
          pLogMessage => sspkg.getErrorMessage,
          pRefObject => pTranpayId);
        RAISE;
      WHEN no_data_found THEN
        writeActionLog(
          pLogMessage => sspkg.ReadVChar(cERR_InvalidTranpay),
          pRefObject => NULL);
		slog.error(pkgCtxId, myUnit, cERR_InvalidTranpay, pTranpayId);
        sspkg.raiseError(cERR_InvalidTranpay, null, pkgCtxId, myunit);
      WHEN OTHERS THEN
		slog.error(pkgCtxId, myUnit, pTranpayId || ':' || pRequestTypeId ||':' ||pTranval || ':' || pTranvalCurrencyId
			|| ':' || pTranpayGroupId || ':' || pAccountId || ':' || pScheduleId || ':' || pTestRecord || ':' || sqlcode || ':' || sqlerrm);
        writeActionLog(
          pLogMessage => sqlerrm,
          pRefObject => pTranpayId);
        RAISE;
    END UpdateTranpay;

    PROCEDURE addTranpayToGroup(pTranpayId tranpays.id%TYPE,
                                pTranpayGroupId tranpays.tranpay_group_id%TYPE)
    IS
        myunit CONSTANT VARCHAR2(17) := 'addTranpayToGroup';
        vTranpayStatus tranpays.status%TYPE;

        PROCEDURE writeActionLog(
          pLogMessage IN VARCHAR2,
          pRefObject IN VARCHAR2 DEFAULT NULL
        ) IS
        BEGIN
          authorization_pck.writeActionLog(
              pActions => actions_list(common_pck.cACT_CreateTranpay),
              pLogMessage => pTranpayId || ':' || pTranpayGroupId  ||
                             ':-' || pLogMessage,
              pRefClass => cREF_CLASS|| myunit,
              pRefObject => pRefObject);
        END writeActionLog;
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pTranpayId || ':' || pTranpayGroupId);
        common_pck.CommonSecurityChecks;

        -- Check if updating is possible
        vTranpayStatus := getTranpayCurrentStatus(pTranpayId);
        IF vTranpayStatus NOT IN (common_pck.cTRPSTS_UW, common_pck.cTRPSTS_UD) THEN
			slog.error(pkgCtxId, myUnit, cERR_InvalidStatus, pTranpayId || ':' || pTranpayGroupId || ':' || vTranpayStatus);
            sspkg.raiseError(cERR_InvalidStatus, null, pkgCtxId, myunit);
        END IF;

        IF NOT tranpayExists(pTranpayId => pTranpayId, pTranpayStatus => vTranpayStatus) THEN
			slog.error(pkgCtxId, myUnit, cERR_InvalidTranpay, pTranpayId || ':' || pTranpayGroupId || ':' || vTranpayStatus);
            sspkg.raiseError(cERR_InvalidTranpay, null, pkgCtxId, myunit);
        END IF;

        -- Tranpay group has to be specified, and it has to be valid
        IF pTranpayGroupId IS NULL OR (NOT isValidTranpayGroup(pTranpayGroupId)) THEN
			slog.error(pkgCtxId, myUnit, cERR_InvalidTranpayGroup, pTranpayId || ':' || pTranpayGroupId || ':' || vTranpayStatus);
            sspkg.raiseError(cERR_InvalidTranpayGroup, null, pkgCtxId, myunit);
        END IF;

        UPDATE tranpays
           SET tranpay_group_id     = pTranpayGroupId
        WHERE id = pTranpayId;

        writeActionLog(
          pLogMessage => 'FINISH',
          pRefObject => pTranpayId);

    EXCEPTION
      WHEN sspkg.sysException THEN
        writeActionLog(
          pLogMessage => sspkg.getErrorMessage,
          pRefObject => pTranpayId);
        RAISE;
      WHEN no_data_found THEN
        writeActionLog(
          pLogMessage => sspkg.ReadVChar(cERR_InvalidTranpay),
          pRefObject => pTranpayId);
	    slog.error(pkgCtxId, myUnit, cERR_InvalidTranpay, pTranpayId || ':' || pTranpayGroupId || ':' || vTranpayStatus);
        sspkg.raiseError(cERR_InvalidTranpay, null, pkgCtxId, myunit);
      WHEN OTHERS THEN
		slog.error(pkgCtxId, myUnit, pTranpayId || ':' || pTranpayGroupId || ':' || sqlcode || ':' || sqlerrm);
        writeActionLog(
          pLogMessage => sqlerrm,
          pRefObject => pTranpayId);
        RAISE;
    END addTranpayToGroup;

    FUNCTION addTranpayToGroup(pTranpayList table_of_integer, pTranpayGroupId tranpays.tranpay_group_id%TYPE)
	RETURN table_of_integer
	IS
	
		vTranpaysUnableToAddToGroup table_of_integer := table_of_integer();
				
		myunit CONSTANT VARCHAR2(21) := 'addTranpayToGroupBULK';

	BEGIN

		slog.debug(pkgCtxId, myUnit);
    	
		FOR i IN 1..pTranpayList.COUNT LOOP
			slog.debug(pkgCtxId, myUnit, 'Calling function addTranpayToGroup for : ' || pTranpayList(i));
		
				BEGIN
			
				addTranpayToGroup(pTranpayList(i), pTranpayGroupId);
				
				EXCEPTION
					WHEN OTHERS THEN
						vTranpaysUnableToAddToGroup.EXTEND;
						vTranpaysUnableToAddToGroup(vTranpaysUnableToAddToGroup.COUNT) := pTranpayList(i);
				END;
			END LOOP;
			
			RETURN vTranpaysUnableToAddToGroup;
			
	END addTranpayToGroup;


   PROCEDURE removeTranpayFromGroup(pTranpayId tranpays.id%TYPE)
    IS
        myunit CONSTANT VARCHAR2(22) := 'removeTranpayFromGroup';
        vTranpayStatus tranpays.status%TYPE;

        PROCEDURE writeActionLog(
          pLogMessage IN VARCHAR2,
          pRefObject IN VARCHAR2 DEFAULT NULL
        ) IS
        BEGIN
          authorization_pck.writeActionLog(
              pActions => actions_list(common_pck.cACT_CreateTranpay),
              pLogMessage => pTranpayId || ':-' || pLogMessage,
              pRefClass => cREF_CLASS|| myunit,
              pRefObject => pRefObject);
        END writeActionLog;
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pTranpayId);
        common_pck.CommonSecurityChecks;

        -- Check if updating is possible
        -- CheckStatusTransition(pTranpayId, common_pck.cTRPSTS_UW);

        vTranpayStatus := getTranpayCurrentStatus(pTranpayId => pTranpayId);

        IF vTranpayStatus NOT IN (common_pck.cTRPSTS_UW,common_pck.cTRPSTS_UD) THEN
			slog.error(pkgCtxId, myUnit, cERR_InvalidStatus, pTranpayId || ':' || vTranpayStatus);
            sspkg.raiseError(cERR_InvalidStatus, null, pkgCtxId, myunit);
        END IF;

        IF NOT tranpayExists(pTranpayId => pTranpayId, pTranpayStatus => vTranpayStatus) THEN
			slog.error(pkgCtxId, myUnit, cERR_InvalidTranpay, pTranpayId || ':' || vTranpayStatus);
            sspkg.raiseError(cERR_InvalidTranpay, null, pkgCtxId, myunit);
        END IF;

        UPDATE tranpays
           SET tranpay_group_id     = NULL
        WHERE id = pTranpayId;

        writeActionLog(
            pLogMessage => 'Tranpay removed from group',
            pRefObject => NULL);
    EXCEPTION
      WHEN sspkg.sysException THEN
        writeActionLog(
          pLogMessage => sspkg.getErrorMessage,
          pRefObject => NULL);
        RAISE;
      WHEN no_data_found THEN
        writeActionLog(
          pLogMessage => sspkg.ReadVChar(cERR_InvalidTranpay),
          pRefObject => NULL);
		  slog.error(pkgCtxId, myUnit, cERR_InvalidTranpay, pTranpayId || ':' || vTranpayStatus);
        sspkg.raiseError(cERR_InvalidTranpay, null, pkgCtxId, myunit);
      WHEN OTHERS THEN
		slog.error(pkgCtxId, myUnit, pTranpayId || ':' || vTranpayStatus || ':' ||sqlcode || ':' || sqlerrm);
        writeActionLog(
          pLogMessage => sqlerrm,
          pRefObject => NULL);
        RAISE;
    END removeTranpayFromGroup;


    FUNCTION removeTranpayFromGroup(pTranpayList table_of_integer)
	RETURN table_of_integer
	IS
		vTranpaysUnableToRemove table_of_integer := table_of_integer();	
		myunit CONSTANT VARCHAR2(26) := 'removeTranpayFromGroupBULK';

	BEGIN

		slog.debug(pkgCtxId, myUnit);
    	
		FOR i IN 1..pTranpayList.COUNT LOOP
			slog.debug(pkgCtxId, myUnit, 'Calling function removeTranpayFromGroup for : ' || pTranpayList(i));
		
				BEGIN
			
				removeTranpayFromGroup(pTranpayList(i));
				
				EXCEPTION
					WHEN OTHERS THEN
						vTranpaysUnableToRemove.EXTEND;
						vTranpaysUnableToRemove(vTranpaysUnableToRemove.COUNT) := pTranpayList(i);
				END;
			END LOOP;
			
			RETURN vTranpaysUnableToRemove;
			
    END removeTranpayFromGroup;


    PROCEDURE CancelTranpay(pTranpayId tranpays.id%TYPE, pStatusMessage tranpays.status_message%TYPE := NULL,
       pCancelFutureTranpays VARCHAR2 := 'Y')
    IS
        myunit CONSTANT VARCHAR2(13) := 'CancelTranpay';
        vScheduleId schedules.id%TYPE;

        PROCEDURE writeActionLog(
          pLogMessage IN VARCHAR2,
          pRefObject IN VARCHAR2 DEFAULT NULL
        ) IS
        BEGIN
          authorization_pck.writeActionLog(
              pActions => actions_list(common_pck.cACT_CancelSignedTranpay, common_pck.cACT_CancelTranpay),
              pLogMessage => pTranpayId || ':' || pStatusMessage || ':' || pCancelFutureTranpays || ':-' || pLogMessage,
              pRefClass => cREF_CLASS|| myunit,
              pRefObject => pRefObject);
        END writeActionLog;
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pTranpayId || ':' || pStatusMessage || ':' || pCancelFutureTranpays);
        common_pck.CommonSecurityChecks;

        IF NOT tranpayExists(pTranpayId) THEN
			slog.error(pkgCtxId, myUnit, cERR_InvalidTranpay, pTranpayId || ':' || pStatusMessage || ':' || pCancelFutureTranpays);
            sspkg.raiseError(cERR_InvalidTranpay, null, pkgCtxId, myunit);
        END IF;

        -- Check if canceling is possible
        IF (pCancelFutureTranpays = 'Y') THEN
            SELECT schedule_id
              INTO vScheduleId
              FROM tranpays
             WHERE id = pTranpayId;

            UPDATE schedules
               SET active = 0
             WHERE id = vScheduleId;

            writeActionLog(pLogMessage => 'Schedule canceled', pRefObject => vScheduleId);

            UPDATE tranpays
               SET status = common_pck.cTRPSTS_UC,
                   status_message = pStatusMessage
            WHERE (id = pTranpayId OR schedule_id = vScheduleId)
            AND status IN (common_pck.cTRPSTS_UW, common_pck.cTRPSTS_UD, common_pck.cTRPSTS_US);

            writeActionLog(pLogMessage => 'Tranpay canceled');
        ELSE
            CheckStatusTransition(pTranpayId, common_pck.cTRPSTS_UC);
            UPDATE tranpays
               SET status = common_pck.cTRPSTS_UC,
                   status_message = pStatusMessage
            WHERE id = pTranpayId;

            writeActionLog(pLogMessage => 'Tranpay canceled');
        END IF;

        invoice_mgmt_pck.changeInvoiceStatus(pTranpayId => pTranpayId, pStatus => 0);

    EXCEPTION
      WHEN sspkg.sysException THEN
        writeActionLog(
          pLogMessage => sspkg.getErrorMessage,
          pRefObject => pTranpayId);
        RAISE;
      WHEN no_data_found THEN
        writeActionLog(
          pLogMessage => sspkg.ReadVChar(cERR_InvalidTranpay),
          pRefObject => pTranpayId);
		slog.error(pkgCtxId, myUnit, cERR_InvalidTranpay, pTranpayId || ':' || pStatusMessage || ':' || pCancelFutureTranpays);
        sspkg.raiseError(cERR_InvalidTranpay, null, pkgCtxId, myunit);
      WHEN OTHERS THEN
		slog.error(pkgCtxId, myUnit, pTranpayId || ':' || pStatusMessage || ':' || pCancelFutureTranpays || ':' ||sqlcode || ':' || sqlerrm);
        writeActionLog(
          pLogMessage => sqlerrm,
          pRefObject => pTranpayId);
        RAISE;
    END CancelTranpay;


	FUNCTION CancelTranpay(pTranpayList table_of_integer, pStatusMessage tranpays.status_message%TYPE := NULL,
       pCancelFutureTranpays VARCHAR2 := 'Y')
	RETURN table_of_integer
	IS

		vTranpaysUnableToCancel table_of_integer := table_of_integer();	
		myunit CONSTANT VARCHAR2(17) := 'CancelTranpayBULK';

	BEGIN

		slog.debug(pkgCtxId, myUnit);
    	
		FOR i IN 1..pTranpayList.COUNT LOOP
			slog.debug(pkgCtxId, myUnit, 'Calling function CancelTranpay for : ' || pTranpayList(i));
		
				BEGIN
			
				CancelTranpay(pTranpayList(i), pStatusMessage, pCancelFutureTranpays);
				
				EXCEPTION
					WHEN OTHERS THEN
						vTranpaysUnableToCancel.EXTEND;
						vTranpaysUnableToCancel(vTranpaysUnableToCancel.COUNT) := pTranpayList(i);
				END;
			END LOOP;
			
			RETURN vTranpaysUnableToCancel;
			
	END CancelTranpay;


   PROCEDURE balanceCheckInternal(pTranpayId IN tranpays.id%TYPE, pAccountId IN tranpays.account_id%TYPE, pRequestTypeId IN tranpays.req_type_id%TYPE, pTranval IN tranpays.tranval%TYPE, pTranvalCurrency IN tranpays.tranval_currency_id%TYPE)
	IS
        myunit CONSTANT VARCHAR2(30) := 'balanceCheckInternal';
		vCurrencyId bank_accounts.currency_id%TYPE;
		vAvailableBalance NUMBER;
		cNLS_NUMERIC_CHARACTERS VARCHAR2(10);
		cNUMBER_FORMAT_MASK VARCHAR2(40);
		vTranval NUMBER;

	BEGIN
		-- Parametar pTranpayId mora moÄ‚Â¦i primati i vrijednost NULL (npr. za provjeru stanja po raÄ‚Â¨unu kod TopUp dopuna)
		slog.debug(pkgCtxId, myUnit, pTranpayId || ':' || pAccountId || ':' || pRequestTypeId ||':' || pTranval);
		
		cNLS_NUMERIC_CHARACTERS := NVL(sspkg.readVChar('/Api/NLS_NUMERIC_CHARACTERS'),'.,');
		cNUMBER_FORMAT_MASK := NVL(sspkg.readVChar('/Api/NUMBER_FORMAT_MASK'),'**************.00');

		IF pTranval IS NULL OR pTranval <= 0 THEN
			slog.error(pkgCtxId, myUnit, cERR_InvalidTranpayValue, pTranval);
			sspkg.raiseError(cERR_InvalidTranpayValue, null, pkgCtxId, myunit);
		END IF;
		
		vCurrencyId := accounts_pck.getCurrency(pAccountId);

		vAvailableBalance := accounts_pck.getAvailableBalance(pAccountId, vCurrencyId, TRUE);
	
		slog.debug(pkgCtxId, myUnit, 'Available balance used: ' || vAvailableBalance);
		-- pTranval IS problematic
		-- In case of UPP orders, it is accounted in domestic currency
		-- In case of PPI orders, it doesn't have to be in account currency

		-- Convert pTranval into account currency to be comparable
		vTranval := NVL(conversion_pck.ConvertCurrency(pFromCurrencyId => vCurrencyId,
                               pToCurrencyId => pTranvalCurrency,
                               pAmmount => pTranval,
                               pConversionDate => TRUNC(SYSDATE),
							pSourceCurrencyId => pTranvalCurrency),0);

		IF vAvailableBalance < vTranval THEN
			slog.error(pkgCtxId, myUnit, cERR_InvalidBalance, pTranpayId || ':' || pAccountId || ':' || pRequestTypeId || ':' || vAvailableBalance || ':' || pTranval || ':' || pTranvalCurrency || ':' || vTranval || ':' || vCurrencyId);
			sspkg.raiseError(cERR_InvalidBalance, null, pkgCtxId, myunit);
		END IF;

		slog.debug(pkgCtxId, myUnit, 'Balance check succeeded');
	END balanceCheckInternal;

    -- Procedure used to perform balance check which throws cERR_InvalidBalance if there is no enough money to execute order
	-- Provjera se vrši iskljuèivo za naloge za koje parametar /enforce_pRequestTypeId_BalanceCheck (enforceUPPBalanceCheck, enforcePPIBalanceCheck, enforceTRANSFERBalanceCheck) ima vrijednost TRUE
	PROCEDURE balanceCheck(pTranpayId IN tranpays.id%TYPE, pAccountId IN tranpays.account_id%TYPE, pRequestTypeId IN tranpays.req_type_id%TYPE, pTranval IN tranpays.tranval%TYPE, pTranvalCurrency IN tranpays.tranval_currency_id%TYPE)
	IS
        myunit CONSTANT VARCHAR2(12) := 'balanceCheck';
	BEGIN
		-- Parametar pTranpayId mora moÄ‚Â¦i primati i vrijednost NULL (npr. za provjeru stanja po raÄ‚Â¨unu kod TopUp dopuna)
		slog.debug(pkgCtxId, myUnit, pTranpayId || ':' || pAccountId || ':' || pRequestTypeId ||':' || pTranval);

		-- '/enforce' || pRequestTypeId || 'BalanceCheck'
		IF sspkg.readBool(pkgCtxId || '/enforce' || pRequestTypeId || 'BalanceCheck') THEN
			balanceCheckInternal(pTranpayId, pAccountId, pRequestTypeId, pTranval, pTranvalCurrency);
		END IF;
	END balanceCheck;

	PROCEDURE tranpayLimitCheck(
		pTranpayId IN tranpays.id%TYPE,
		pAccountId IN tranpays.account_id%TYPE,
		pRequestTypeId IN tranpays.req_type_id%TYPE,
		pTranval IN tranpays.tranval%TYPE,
		pTranvalCurrency IN tranpays.tranval_currency_id%TYPE,
		pEndUserId IN end_users.id%TYPE,
		pApplicationId IN applications.id%TYPE)
	IS
        myunit CONSTANT VARCHAR2(17) := 'tranpayLimitCheck';
		vCurrencyId bank_accounts.currency_id%TYPE;
		vTranpayLimit NUMBER;
		vTranval NUMBER;

	BEGIN
		slog.debug(pkgCtxId, myUnit, pAccountId || ':' || pRequestTypeId ||':' || pTranval);
		

		-- '/enforce' || pRequestTypeId || 'BalanceCheck'
		IF sspkg.readBool(pkgCtxId || '/enforce' || pRequestTypeId || 'TranpayLimitCheck') THEN
			slog.debug(pkgCtxId, myUnit, 'Do tranpay limit check for ' || pRequestTypeId);

			IF pTranval IS NULL OR pTranval <= 0 THEN
				slog.error(pkgCtxId, myUnit, cERR_InvalidTranpayValue, pTranval);
				sspkg.raiseError(cERR_InvalidTranpayValue, null, pkgCtxId, myunit);
			END IF;
			
			BEGIN
				SELECT currency_id
				INTO vCurrencyId
				FROM mcore.bank_accounts ba
				WHERE ba.id = pAccountId;
			EXCEPTION
				WHEN no_data_found THEN
					slog.error(pkgCtxId, myUnit, cERR_InvalidAccount || ':' || pAccountId);
					sspkg.raiseError(cERR_InvalidAccount, null, pkgCtxId, myunit);
				WHEN OTHERS THEN
					slog.error(pkgCtxId, myUnit, sqlerrm);
					sspkg.raiseError(cERR_InvalidAccount, null, pkgCtxId, myunit);
			END;
			slog.debug(pkgCtxId, myUnit, 'vCurrencyId:' || vCurrencyId);
		
			-- pTranval IS problematic
			-- In case of UPP orders, it is accounted in domestic currency
			-- In case of PPI orders, it doesn't have to be in account currency

			-- Convert pTranval into account currency to be comparable
			vTranval := getTranpayValueInDomCur(pTranpayId => pTranpayId);
			slog.debug(pkgCtxId, myUnit, 'Tranval in domestic currency:' || vTranval);

			vTranpayLimit := authorization_pck.getTranpayLimit(pAccountId => pAccountId,
                            pEndUserId => pEndUserId,
                            pRequestTypeId => pRequestTypeId,
                            pActionId => mcore.common_pck.cACT_SignTranpays,
							pApplicationId => pApplicationId);
			slog.debug(pkgCtxId, myUnit, 'Tranpay limit: ' || vTranpayLimit);

			IF vTranpayLimit < vTranval THEN
				slog.error(pkgCtxId, myUnit, cERR_TranpayLimit, pAccountId || ':' || pRequestTypeId || ':' || vTranpayLimit || ':' || pTranval || ':' || pTranvalCurrency || ':' || vTranval || ':' || vCurrencyId);

				sspkg.raiseError(cERR_TranpayLimit,
                        mlang.trans(lang => mcauth.auth.getLang,
                            messageId => cERR_TranpayLimit,
							s0 => vTranpayLimit),
                    pkgCtxId, myunit);

			END IF;
			slog.debug(pkgCtxId, myUnit, 'Tranval check succeeded');
		END IF;
	END tranpayLimitCheck;

    PROCEDURE MarkAsDone(pTranpayId tranpays.id%TYPE)
    IS
        myunit CONSTANT VARCHAR2(10) := 'MarkAsDone';
        vAccountId      tranpays.account_id%TYPE;
        vRequestTypeId  tranpays.req_type_id%TYPE;
        vBasicType request_types.basic_type%TYPE;
        vTranpayStatus tranpays.status%TYPE;
	vDescription tranpays.description%TYPE;
        vNewStatus VARCHAR2(2);

	vTranval tranpays.tranval%TYPE;
        vTranvalCurrencyId tranpays.tranval_currency_id%TYPE;
        vTranpayGroupId tranpays.tranpay_group_id%TYPE;
        vChartOfAccountsId tranpays.chart_of_accounts_id%TYPE;
        vScheduleId schedules.id%TYPE;
	vScheduleType schedules.schedule_type_type%TYPE;

        PROCEDURE writeActionLog(
          pLogMessage IN VARCHAR2,
          pRefObject IN VARCHAR2 DEFAULT NULL)
        IS
        BEGIN
          authorization_pck.writeActionLog(
              pActionId => common_pck.cACT_CreateTranpay,
              pLogMessage => pTranpayId ||  ':-' || pLogMessage,
              pRefClass => cREF_CLASS|| myunit,
              pRefObject => pRefObject);
        END writeActionLog;
    BEGIN
        slog.debug(pkgCtxId, myUnit, pTranpayId);
        common_pck.CommonSecurityChecks;

        BEGIN
			slog.debug(pkgCtxId, myUnit, 'Get tranpay data');
            SELECT t.account_id, t.req_type_id, t.status, t.tranval, t.tranval_currency_id, t.description, t.tranpay_group_id, t.chart_of_accounts_id,
				s.id, NVL(s.schedule_type_type, common_pck.cSCHDTYPE_IMMEDIATE)
              INTO vAccountId, vRequestTypeId, vTranpayStatus, vTranval, vTranvalCurrencyId, vDescription, vTranpayGroupId, vChartOfAccountsId,
				vScheduleId, vScheduleType
              FROM tranpays t LEFT JOIN mcore.schedules s ON (s.id = t.schedule_id)
             WHERE t.id = pTranpayId;
			slog.debug(pkgCtxId, myUnit, vAccountId || ':' || vRequestTypeId || ':' || vTranpayStatus || ':' || vTranval ||':'||vTranvalCurrencyId || ':' || vTranpayGroupId ||':' || vChartOfAccountsId || ':' || vScheduleId || ':' || vScheduleType);

			slog.debug(pkgCtxId, myUnit, 'Check authorization');
            IF ((NOT authorization_pck.hasGrantOnCreateTranpay(vAccountId, vRequestTypeId))
              AND (NOT authorization_pck.hasGrantOnSmsPlacanje(vAccountId, vRequestTypeId))
            ) THEN
				slog.error(pkgCtxId, myUnit, cERR_CannotFinishTranpay, pTranpayId || ':' || vAccountId || ':' || vRequestTypeId);
                sspkg.raiseError(cERR_CannotFinishTranpay, null, pkgCtxId, myunit);
            END IF;

        EXCEPTION
            WHEN no_data_found THEN
				slog.error(pkgCtxId, myUnit, cERR_CannotFinishTranpay, pTranpayId || ':' || vAccountId || ':' || vRequestTypeId);
                sspkg.raiseError(cERR_InvalidTranpay, null, pkgCtxId, myunit);
        END;

        -- HSAFET: 01.10.2014 - Prevent tranpay without tranval to pass validation
        IF vTranval IS NULL THEN
			slog.error(pkgCtxId, myUnit, cERR_InvalidTranpayValue, pTranpayId);
			sspkg.raiseError(cERR_InvalidTranpayValue, null, pkgCtxId, myunit);
        END IF;

	-- DZLEJLA: 19.08.2020 - Prevent tranpay without description to pass validation
        IF vDescription IS NULL THEN
			slog.error(pkgCtxId, myUnit, cERR_MissingTranpayDescription, pTranpayId);
			sspkg.raiseError(cERR_MissingTranpayDescription, null, pkgCtxId, myunit);
        END IF;

        IF NOT tranpayExists(pTranpayId, vTranpayStatus) THEN
			slog.error(pkgCtxId, myUnit, cERR_CannotFinishTranpay, pTranpayId || ':' || vAccountId || ':' || vRequestTypeId || ':' || vTranpayStatus);
            sspkg.raiseError(cERR_InvalidTranpay, null, pkgCtxId, myunit);
        END IF;

		slog.debug(pkgCtxId, myUnit, 'Determine basic type ...');
        vBasicType := requests_pck.getRequestBasicType(vRequestTypeId);
		slog.debug(pkgCtxId, myUnit, 'vBasicType:' || vBasicType);

        IF vBasicType = common_pck.cBRT_WUT THEN
            vNewStatus := common_pck.cTRPSTS_VB;
        ELSE
            vNewStatus := common_pck.cTRPSTS_UD;
        END IF;

		slog.debug(pkgCtxId, myUnit, 'CheckStatusTransition:' || vNewStatus);
        CheckStatusTransition(pTranpayId, vNewStatus);

		DELETE FROM tranpay_details
		WHERE tranpay_id = pTranpayId
		  AND attrib_id IN (common_pck.cTRPAY_ATT_SENT_NOTIFICATION, common_pck.cTRANPAY_ATT_CLEARING_DATE, common_pck.cTRPY_ATT_REGISTERED_FOR_EXEC, common_pck.cQRPay_SENT_NOTIFICATION, common_pck.cTOPUP_SENT_NOTIFICATION, common_pck.cME2YOU_SENT_NOTIF_SENDER, common_pck.cME2YOU_SENT_NOTIF_RECEIVER);

        -- CheckExistingTranpayData(pTranpayId);
		slog.debug(pkgCtxId, myUnit, 'CheckExistingTranpayDataINT');
		CheckExistingTranpayDataINT(
			pTranpayId => pTranpayId,
			pRequestTypeId => vRequestTypeId,
			pTranval => vTranval,
			pTranvalCurrencyId => vTranvalCurrencyId,
			pTranpayGroupId => vTranpayGroupId,
			pChartOfAccountsId => vChartOfAccountsId,
			pScheduleId => vScheduleId);

		-- TODO: Populate missing tranpay details in background

        -- TranpayValidation(pTranpayId);
		slog.debug(pkgCtxId, myUnit, 'TranpayValidation');
		TranpayValidation(pTranpayId, vRequestTypeId, vAccountId);

		-- #12073 - pogrešno
		/*IF vScheduleType = common_pck.cSCHDTYPE_IMMEDIATE THEN
			slog.debug(pkgCtxId, myUnit, 'Do balance check for ' || vRequestTypeId);
			balanceCheck(pTranpayId => pTranpayId, pAccountId => vAccountId, pRequestTypeId => vRequestTypeId, pTranval => vTranval, pTranvalCurrency => vTranvalCurrencyId);
		END IF;*/
		
		IF NOT vScheduleType = common_pck.cSCHDTYPE_IMMEDIATE AND hasAdditionalTranpays(pTranpayId) THEN
			slog.error(pkgCtxId, myUnit, cERR_unblCrtSchdlWithAddTrnpys, pTranpayId);
			sspkg.raiseError(cERR_unblCrtSchdlWithAddTrnpys, null, pkgCtxId, myunit);
		END IF;

		slog.debug(pkgCtxId, myUnit, 'Mark tranpay as done');
        UPDATE tranpays
           SET status = vNewStatus,
		       status_message = null,
			   status_code = null
         WHERE id = pTranpayId;

		-- HSAFET: #17936 Integracija s SPLUNK sistemom
		-- RegisterSplunkNotification(pTranpayId IN tranpays.id%TYPE, pAccountId IN bank_accounts.id%TYPE, pTranpayAmount IN NUMBER, pTranpayCurrency IN VARCHAR2, pActionId IN VARCHAR2)
		RegisterSplunkNotification(pTranpayId => pTranpayId, pActionId => common_pck.cACT_CreateTranpay);

        writeActionLog(
          pLogMessage => 'Tranpay marked as done!');
    EXCEPTION
      WHEN sspkg.sysException THEN
        writeActionLog(
          pLogMessage => sspkg.getErrorMessage,
          pRefObject => NULL);
        RAISE;
      WHEN OTHERS THEN
		slog.error(pkgCtxId, myUnit, pTranpayId || ':' || vAccountId || ':' || vRequestTypeId || ':' || vTranpayStatus || ':' || vBasicType || ':' || sqlcode || ':' || sqlerrm);
        writeActionLog(
          pLogMessage => sqlerrm,
          pRefObject => NULL);
        RAISE;
    END MarkAsDone;


	FUNCTION MarkAsDone(pTranpayList table_of_integer)
	RETURN tranpay_import_result_list
	IS

		myunit CONSTANT VARCHAR2(14) := 'MarkAsDoneBULK';

		CURSOR cTranpayData IS
			SELECT t.id, rt.id req_type_id, rt.basic_type, t.account_id,
					t.status, t.tranval, t.tranval_currency_id, t.description, t.tranpay_group_id, t.chart_of_accounts_id,
					s.id schedule_id, NVL(s.schedule_type_type, common_pck.cSCHDTYPE_IMMEDIATE) schedule_type_type
				FROM vw$user_tranpays_uw_vb t JOIN TABLE(pTranpayList) s ON (t.id = s.column_value)
				JOIN request_types rt ON (t.req_type_id = rt.id)
				LEFT JOIN mcore.schedules s ON (s.id = t.schedule_id)
				WHERE status = common_pck.cTRPSTS_UW;

		CURSOR cTranpayDataAcc IS
			SELECT t.id, rt.id req_type_id, rt.basic_type, t.account_id,
					t.status, t.tranval, t.tranval_currency_id, t.description, t.tranpay_group_id, t.chart_of_accounts_id,
					s.id schedule_id, NVL(s.schedule_type_type, common_pck.cSCHDTYPE_IMMEDIATE) schedule_type_type
				FROM vw$user_tranpays_uw_vb_acc t JOIN TABLE(pTranpayList) s ON (t.id = s.column_value)
				JOIN request_types rt ON (t.req_type_id = rt.id)
				LEFT JOIN mcore.schedules s ON (s.id = t.schedule_id)
				WHERE status = common_pck.cTRPSTS_UW;

		--TYPE rt_tranpayData IS RECORD (id NUMBER(38,0), req_type_id VARCHAR2(40 CHAR), basic_type VARCHAR2(29 CHAR), account_id VARCHAR2(40 CHAR),
--			status, tranval, tranval_currency_id, tranpay_group_id, chart_of_accounts_id, schedule_id);
		TYPE tt_tranpayData IS TABLE OF cTranpayData%ROWTYPE INDEX BY PLS_INTEGER;
		vTranpayData tt_tranpayData;

		vTranpaysUnableToFinish tranpay_import_result_list := tranpay_import_result_list();
		vImportResult tranpay_import_result;
		vFound BOOLEAN;

		PROCEDURE writeActionLog(
          pLogMessage IN VARCHAR2,
          pRefObject IN VARCHAR2 DEFAULT NULL
        )
        IS
        BEGIN
          authorization_pck.writeActionLog(
              pActionId => common_pck.cACT_CreateTranpay,
              pLogMessage => pLogMessage,
              pRefClass => cREF_CLASS|| myunit,
              pRefObject => pRefObject);
        END writeActionLog;
	BEGIN

		slog.debug(pkgCtxId, myUnit);
        common_pck.CommonSecurityChecks;

		BEGIN
			IF mcauth.auth.getAccountOwner = '%' THEN
				OPEN cTranpayData;
				FETCH cTranpayData BULK COLLECT INTO vTranpayData;
				CLOSE cTranpayData;
			ELSE
				OPEN cTranpayDataAcc;
				FETCH cTranpayDataAcc BULK COLLECT INTO vTranpayData;
				CLOSE cTranpayDataAcc;
			END IF;
        END;

		-- Check if all tranpays send as arguments could be retrieved!
		FOR i IN 1..pTranpayList.COUNT LOOP
			vFound := FALSE;
			FOR j IN 1..vTranpayData.COUNT LOOP
				IF pTranpayList(i) = vTranpayData(j).id THEN
					vFound := TRUE;
					CONTINUE;
				END IF;
			END LOOP;
			IF NOT vFound THEN
				vImportResult := tranpay_import_result(pTranpayList(i));
				slog.error(pkgCtxId, myunit, 'Unable to retrieve ' || pTranpayList(i));
				vImportResult.status := 'GRESKA';
				vImportResult.message := mlang.trans(lang => mcauth.auth.getLang, messageId => cERR_unableToRetrieveTranpay);
				vImportResult.tranpay_id := pTranpayList(i);
				vTranpaysUnableToFinish.EXTEND;
				vTranpaysUnableToFinish(vTranpaysUnableToFinish.COUNT) := vImportResult;
			END IF;
		END LOOP;


		FOR i IN 1..vTranpayData.COUNT LOOP
			slog.debug(pkgCtxId, myUnit, 'Check data for : ' || vTranpayData(i).id || ':' || vTranpayData(i).account_id || ':' || vTranpayData(i).req_type_id || ':' || vTranpayData(i).tranval || ':' || vTranpayData(i).schedule_type_type);
			slog.debug(pkgCtxId, myUnit, 'Check authorization');
			vImportResult := tranpay_import_result(vTranpayData(i).id);
			IF ((NOT authorization_pck.hasGrantOnCreateTranpay(vTranpayData(i).account_id, vTranpayData(i).req_type_id))
			AND (NOT authorization_pck.hasGrantOnSmsPlacanje(vTranpayData(i).account_id, vTranpayData(i).req_type_id))
			) THEN
				slog.error(pkgCtxId, myunit, 'User has not required authorization');
				vImportResult.status := 'GRESKA';
				vImportResult.message := mlang.trans(lang => mcauth.auth.getLang, messageId => cERR_noRequiredAuthorization);
				vImportResult.tranpay_id := vTranpayData(i).id;
				vTranpaysUnableToFinish.EXTEND;
				vTranpaysUnableToFinish(vTranpaysUnableToFinish.COUNT) := vImportResult;
				CONTINUE;
			END IF;

			DELETE FROM tranpay_details
			WHERE tranpay_id = vTranpayData(i).id
			AND attrib_id IN (common_pck.cTRPAY_ATT_SENT_NOTIFICATION, common_pck.cTRANPAY_ATT_CLEARING_DATE, common_pck.cTRPY_ATT_REGISTERED_FOR_EXEC, common_pck.cQRPay_SENT_NOTIFICATION, common_pck.cTOPUP_SENT_NOTIFICATION, common_pck.cME2YOU_SENT_NOTIF_SENDER, common_pck.cME2YOU_SENT_NOTIF_RECEIVER);

			BEGIN
				-- HSAFET: 01.10.2014 - Prevent tranpay without tranval to pass validation
				slog.debug(pkgCtxId, myUnit, 'Check tranval');
				IF vTranpayData(i).tranval IS NULL THEN
					sspkg.raiseError(cERR_InvalidTranpayValue, null, pkgCtxId, myunit);
				END IF;
				-- DZLEJLA: 19.08.2020 - Prevent tranpay without description to pass validation
                		slog.debug(pkgCtxId, myUnit, 'Check description');
                		IF vTranpayData(i).description IS NULL THEN
					sspkg.raiseError(cERR_MissingTranpayDescription, null, pkgCtxId, myunit);
				END IF;
				IF  NOT vTranpayData(i).schedule_type_type = common_pck.cSCHDTYPE_IMMEDIATE  AND hasAdditionalTranpays(vTranpayData(i).id) THEN
					slog.error(pkgCtxId, myUnit, cERR_unblCrtSchdlWithAddTrnpys, vTranpayData(i).id);
					sspkg.raiseError(cERR_unblCrtSchdlWithAddTrnpys, null, pkgCtxId, myunit);
				END IF;
				slog.debug(pkgCtxId, myUnit, 'CheckExistingTranpayDataINT');
				CheckExistingTranpayDataINT(
					pTranpayId => vTranpayData(i).id,
					pRequestTypeId => vTranpayData(i).req_type_id,
					pTranval => vTranpayData(i).tranval,
					pTranvalCurrencyId => vTranpayData(i).tranval_currency_id,
					pTranpayGroupId => vTranpayData(i).tranpay_group_id,
					pChartOfAccountsId => vTranpayData(i).chart_of_accounts_id,
					pScheduleId => vTranpayData(i).schedule_id);

				slog.debug(pkgCtxId, myUnit, 'TranpayValidation');
				TranpayValidation(vTranpayData(i).id, vTranpayData(i).req_type_id, vTranpayData(i).account_id);
				

			EXCEPTION
				WHEN sspkg.sysexception THEN
					slog.error(pkgCtxId, myunit, 'Invalid data ' || vTranpayData(i).id);
					vImportResult.status := 'GRESKA';
					vImportResult.message := NVL(sspkg.getErrorUserMessage, mlang.trans(lang => mcauth.auth.getLang, messageId => sspkg.getErrorCode));
					vImportResult.tranpay_id := vTranpayData(i).id;
					vTranpaysUnableToFinish.EXTEND;
					vTranpaysUnableToFinish(vTranpaysUnableToFinish.COUNT) := vImportResult;
				WHEN OTHERS THEN
					slog.error(pkgCtxId, myUnit, SQLERRM || ' ' || vTranpayData(i).id);
					vImportResult.status := 'GRESKA';
					vImportResult.message := mlang.trans(lang => mcauth.auth.getLang, messageId => cERR_internalError);
					vImportResult.tranpay_id := vTranpayData(i).id;
					vTranpaysUnableToFinish.EXTEND;
					vTranpaysUnableToFinish(vTranpaysUnableToFinish.COUNT) := vImportResult;
			END;

		
		END LOOP;

		IF vTranpaysUnableToFinish.COUNT > 0 THEN
			RETURN vTranpaysUnableToFinish;
		END IF;

        FORALL i IN 1..vTranpayData.COUNT
			UPDATE tranpays
			   SET status = DECODE(vTranpayData(i).basic_type, common_pck.cBRT_WUT, common_pck.cTRPSTS_VB, common_pck.cTRPSTS_UD)
			 WHERE id = vTranpayData(i).id;

        FOR i IN 1..pTranpayList.COUNT LOOP
			writeActionLog(pLogMessage => 'Order ' || pTranpayList(i) || ' marked as done!');
		END LOOP;

		RETURN vTranpaysUnableToFinish;

	END MarkAsDone;

    FUNCTION getTranpayCurrentStatus(pTranpayId tranpays.id%TYPE)
    RETURN tranpays.status%TYPE IS
        vTranpayStatus tranpays.status%TYPE;
        myunit CONSTANT VARCHAR2(23) := 'getTranpayCurrentStatus';
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pTranpayId);
        common_pck.CommonSecurityChecks;

        SELECT status
        INTO vTranpayStatus
        FROM tranpays
        WHERE id = pTranpayId;

        RETURN vTranpayStatus;

    EXCEPTION
        WHEN no_data_found THEN
			slog.error(pkgCtxId, myUnit, cERR_InvalidTranpay, pTranpayId);
            sspkg.raiseError(cERR_InvalidTranpay, null, pkgCtxId, myunit);
    END getTranpayCurrentStatus;

    PROCEDURE getTranpayData(
                    pTranpayId IN tranpays.id%TYPE,
                    pRequestType OUT tranpays.req_type_id%TYPE,
                    pTranpayStatus OUT tranpays.status%TYPE,
                    pAccountId OUT tranpays.account_id%TYPE)
    IS
        myunit CONSTANT VARCHAR2(14) := 'getTranpayData';
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pTranpayId);
        common_pck.CommonSecurityChecks;

        SELECT req_type_id, status, account_id
        INTO pRequestType, pTranpayStatus, pAccountId
        FROM tranpays
        WHERE id = pTranpayId;

        slog.debug(pkgCtxId, myUnit,  pTranpayId || '->' || pRequestType || ':' || pTranpayStatus || ':' || pAccountId);

    EXCEPTION
        WHEN no_data_found THEN
			slog.error(pkgCtxId, myUnit, cERR_InvalidTranpay, pTranpayId);
            sspkg.raiseError(cERR_InvalidTranpay, null, pkgCtxId, myunit);
    END getTranpayData;

    PROCEDURE getTranpayCurrentStatus(
                    pTranpayId IN tranpays.id%TYPE,
                    pRequestType OUT tranpays.req_type_id%TYPE,
                    pTranpayStatus OUT tranpays.status%TYPE
    )
    IS
        myunit CONSTANT VARCHAR2(23) := 'getTranpayCurrentStatus';
        vAccountId tranpays.account_id%TYPE;
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pTranpayId);

        getTranpayData(pTranpayId, pRequestType, pTranpayStatus, vAccountId);
    END getTranpayCurrentStatus;

    FUNCTION getTranpayAccount(pTranpayId tranpays.id%TYPE)
    RETURN tranpays.account_id%TYPE IS
        vAccountId tranpays.account_id%TYPE;
        myunit CONSTANT VARCHAR2(17) := 'getTranpayAccount';
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pTranpayId);
        common_pck.CommonSecurityChecks;

        SELECT account_id
        INTO vAccountId
        FROM tranpays
        WHERE id = pTranpayId;

        RETURN vAccountId;

    EXCEPTION
        WHEN no_data_found THEN
			slog.error(pkgCtxId, myUnit, cERR_InvalidTranpay, pTranpayId);
            sspkg.raiseError(cERR_InvalidTranpay, null, pkgCtxId, myunit);
    END getTranpayAccount;

    FUNCTION prepareTranpaySenderData (pUserID end_users.id%TYPE, pAccountID bank_accounts.id%TYPE, pRequestTypeId tranpays.req_type_id%TYPE DEFAULT NULL)
    RETURN tranpay_details.data_vchar%TYPE
    IS
        myunit CONSTANT VARCHAR2(24) := 'prepareTranpaySenderData';

        vNazivPosiljaoca tranpay_details.data_vchar%TYPE;
        vSenderInfoSource VARCHAR2(40);
        cSenderInfoSource_currentUser CONSTANT VARCHAR2(12) := 'current_user';
        vIncludeSenderAddress BOOLEAN;
        vIncludeSenderPhone BOOLEAN;
		vNazivPosiljaocaMaxSize NUMBER;

    BEGIN

        slog.debug(pkgCtxId, myUnit, pUserID || ':' || pAccountID || ':' || pRequestTypeId);
		
		vSenderInfoSource := sspkg.readvchar(pkgCtxId || '/senderInfoSource');
		vIncludeSenderAddress := sspkg.readBool(pkgCtxId || '/includeSenderAddress');
        vIncludeSenderPhone := sspkg.readBool(pkgCtxId || '/includeSenderPhone');
		
		vNazivPosiljaocaMaxSize:= getTranpayAttribute(NVL(pRequestTypeId, common_pck.cRTI_UPP), common_pck.cTRANPAY_ATT_NAZIV_POSILJAOCA, 'MAXSIZE');

        IF vSenderInfoSource = cSenderInfoSource_currentUser THEN

            DECLARE
                vFirstName end_users.first_name%TYPE := NULL;
                vLastName end_users.last_name%TYPE := NULL;
                vCityName end_users.city%TYPE := NULL;
                vZipCode end_users.zip_code%TYPE := NULL;
                vAddress end_users.address%TYPE := NULL;
                vContactPhone end_users.contact_phone%TYPE := NULL;
            BEGIN
                end_user_pck.GetTranpaySenderRequiredData(
                    pUserId => pUserID,
                     pFirstName => vFirstName,
                     pLastName => vLastName,
                     pCityName => vCityName,
                     pZipCode => vZipCode,
                     pAddress => vAddress,
                     pContactPhone => vContactPhone);

                vNazivPosiljaoca := vFirstName || ', ' ||  vLastName;

                IF vIncludeSenderAddress THEN
                    vNazivPosiljaoca := vNazivPosiljaoca || chr(10) || vCityName || ', ' || vZipCode || ', ' || vAddress;
                END IF;

                IF vIncludeSenderPhone THEN
                    vNazivPosiljaoca := vNazivPosiljaoca || chr(10) || vContactPhone;
                END IF;
            END;


        ELSE
            DECLARE
                vAccountOwnerName 		account_owners.name%type;
                vAccountOwnerAddress 	account_owners.ph2%type;
                vAccountOwnerCity 		account_owners.ph5%type;
                vAccountOwnerTelephone 	account_owners.ph3%type;
            BEGIN
                accounts_pck.GetTranpaySenderRequiredData(
                    pAccountId => pAccountID,
                    pAccountOwnerId => NULL,
                    pAccountOwnerName => vAccountOwnerName,
                    pAccountOwnerAddress => vAccountOwnerAddress,
                    pAccountOwnerCity => vAccountOwnerCity,
                    pAccountOwnerTelephone => vAccountOwnerTelephone);

                vNazivPosiljaoca := vAccountOwnerName;

                IF vIncludeSenderAddress THEN
                    vNazivPosiljaoca := vNazivPosiljaoca || chr(10) || vAccountOwnerAddress || ', ' || vAccountOwnerCity;
                END IF;

                IF vIncludeSenderPhone THEN
                    vNazivPosiljaoca := vNazivPosiljaoca || chr(10) || vAccountOwnerTelephone;
                END IF;
            END;
        END IF;

        RETURN SUBSTR(vNazivPosiljaoca, 0, vNazivPosiljaocaMaxSize);
    END prepareTranpaySenderData;

    FUNCTION CopyTranpay(pTranpayId tranpays.id%TYPE)
    RETURN tranpays.id%TYPE IS
        vTranpayId tranpays.id%TYPE;
        myunit CONSTANT VARCHAR2(11) := 'CopyTranpay';
        vTranpayRec tranpays%ROWTYPE;

		vNazivPosiljaoca tranpay_details.data_vchar%TYPE;

        PROCEDURE writeActionLog(
          pLogMessage IN VARCHAR2,
          pRefObject IN VARCHAR2 DEFAULT NULL
        )
        IS
        BEGIN
          authorization_pck.writeActionLog(
              pActionId => common_pck.cACT_CreateTranpay,
              pLogMessage => pTranpayId ||  ':-' || pLogMessage,
              pRefClass => cREF_CLASS|| myunit,
              pRefObject => pRefObject);
        END writeActionLog;
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pTranpayId);
        common_pck.CommonSecurityChecks;

        /*IF NOT tranpayExists(pTranpayId) THEN
            sspkg.raiseError(cERR_InvalidTranpay, null, pkgCtxId, myunit);
        END IF;*/

        -- Create new tranpay using data from previous one,
        -- save id for further use
        BEGIN
			SELECT *
			INTO vTranpayRec
			FROM tranpays
			WHERE id = pTranpayId;
		EXCEPTION
			WHEN no_data_found THEN
				slog.error(pkgCtxId, myUnit, cERR_InvalidTranpay, pTranpayId);
				sspkg.raiseError(cERR_InvalidTranpay, null, pkgCtxId, myunit);
		END;

		IF NOT tranpayExists(pTranpayId, vTranpayRec.status) THEN
			slog.error(pkgCtxId, myUnit, cERR_InvalidTranpay, pTranpayId || ':' || vTranpayRec.status);
            sspkg.raiseError(cERR_InvalidTranpay, null, pkgCtxId, myunit);
        END IF;

        -- HSAFET: 01.10.2014: Prilikom kopiranja WU naloga, TRANVAL ne kopirati
		IF vTranpayRec.req_type_id IN (common_pck.cRTI_WUPAYOUT, common_pck.cRTI_WUPAYMENT) THEN
		  vTranpayRec.tranval := NULL;
		END IF;

        INSERT INTO tranpays (req_type_id, status,
                        description, internal_description, tranval,
                        tranval_currency_id, account_id,
                        chart_of_accounts_id, test_record )
        VALUES (vTranpayRec.req_type_id, common_pck.cTRPSTS_UW,
                vTranpayRec.description, vTranpayRec.internal_description, vTranpayRec.tranval,
                vTranpayRec.tranval_currency_id, vTranpayRec.account_id,
                vTranpayRec.chart_of_accounts_id, vTranpayRec.test_record)
        RETURNING id INTO vTranpayId;

        -- Duplicate tranpay details
		INSERT INTO tranpay_details(req_type_id, tranpay_id, attrib_id, description, data_vchar, data_blob)
		SELECT td.req_type_id, vTranpayId, td.attrib_id, td.description, td.data_vchar, td.data_blob
		FROM tranpay_details td join tranpay_attribs ta on (td.attrib_id = ta.id AND td.req_type_id = ta.req_type_id)
		WHERE td.tranpay_id = pTranpayId
			AND ta.for_copy = 1;

		-- HSAFET: 24.03.2018 #17259 - Nemaju sve vrste naloga naziv posiljaoca
     
		IF isValidTranpayAttribute(vTranpayRec.req_type_id, common_pck.cTRANPAY_ATT_NAZIV_POSILJAOCA) THEN

			vNazivPosiljaoca := prepareTranpaySenderData (
                                    pUserID => NVL(vTranpayRec.user_modified, vTranpayRec.user_id),
                                    pAccountID => vTranpayRec.account_id);

			INSERT INTO tranpay_details (req_type_id, tranpay_id, attrib_id, data_vchar)
				VALUES (vTranpayRec.req_type_id, vTranpayId, common_pck.cTRANPAY_ATT_NAZIV_POSILJAOCA, vNazivPosiljaoca);
		ELSE
			slog.debug(pkgCtxId, myUnit, 'Sender name not settable : ' || vTranpayRec.req_type_id || ':' || common_pck.cTRANPAY_ATT_NAZIV_POSILJAOCA);
		END IF;

        writeActionLog(
          pLogMessage => 'Tranpay successfully duplicated!',
          pRefObject => vTranpayId);

        RETURN vTranpayId;

    EXCEPTION
      WHEN sspkg.sysException THEN
        writeActionLog(
          pLogMessage => sspkg.getErrorMessage,
          pRefObject => NULL);
        RAISE;
      WHEN OTHERS THEN
		slog.error(pkgCtxId, myUnit, pTranpayId || ':' || vTranpayRec.status || ':' || sqlcode || ':' || sqlerrm);
        writeActionLog(
          pLogMessage => sqlerrm,
          pRefObject => NULL);
        RAISE;
    END CopyTranpay;

FUNCTION CopyTranpay(pTranpayList table_of_integer)
	RETURN table_of_integer
	IS
	
		vTranpaysSuccCopied table_of_integer := table_of_integer();
		vTranpayId tranpays.id%TYPE;
				
		myunit CONSTANT VARCHAR2(15) := 'CopyTranpayBULK';

	BEGIN

		slog.debug(pkgCtxId, myUnit);
		FOR i IN 1..pTranpayList.COUNT LOOP
        
			slog.debug(pkgCtxId, myUnit, 'Calling function CopyTranpay for : ' || pTranpayList(i));
		
				BEGIN
				
				vTranpayId := CopyTranpay(pTranpayList(i));
				vTranpaysSuccCopied.EXTEND;
				vTranpaysSuccCopied(vTranpaysSuccCopied.COUNT) := vTranpayId;
				
				EXCEPTION
					WHEN OTHERS THEN
						CONTINUE;
				END;
		END LOOP;
		RETURN vTranpaysSuccCopied;
			
	END CopyTranpay;
    

    -- TRANPAY DETAILS
    PROCEDURE AppendTranPayDetailInt(pTranpayId        tranpays.id%TYPE,
                                  pRequestTypeId    tranpays.req_type_id%TYPE,
                                  pAttribId         tranpay_details.attrib_id%TYPE,
                                  pDescription      tranpay_details.description%TYPE,
                                  pDataVCHAR        tranpay_details.data_vchar%TYPE,
                                  pDataBLOB         tranpay_details.data_blob%TYPE)
    IS
        myunit CONSTANT VARCHAR2(22) := 'AppendTranPayDetailInt';

        /*PROCEDURE writeActionLog(
          pLogMessage IN VARCHAR2,
          pRefObject IN VARCHAR2 DEFAULT NULL
        )
        IS
        BEGIN
          authorization_pck.writeActionLog(
              pActionId => common_pck.cACT_CreateTranpay,
              pLogMessage => pTranpayId ||  ':' ||
              pRequestTypeId ||  ':' ||pAttribId ||  ':' ||pDescription ||  ':' ||pDataVCHAR || ':-' || pLogMessage,
              pRefClass => cREF_CLASS|| myunit,
              pRefObject => pRefObject);
        END writeActionLog;*/

    BEGIN
        slog.debug(pkgCtxId, myUnit,  pTranpayId || ':' || pRequestTypeId || ':' || pAttribId || ':' || NVL(pDataVCHAR,'NULL'));
        --common_pck.CommonSecurityChecks;

        CheckDetailsData(pRequestTypeId     => pRequestTypeId,
                         pAttribId          => pAttribId,
                         pDataVCHAR         => pDataVCHAR,
                         pDataBLOB          => pDataBLOB,
                         pIgnoreRequired    => TRUE);

        MERGE INTO tranpay_details td
           USING (SELECT id, req_type_id
                    FROM tranpay_attribs
                    WHERE   id          = pAttribId
                      AND   req_type_id = pRequestTypeId) ta
            ON (   td.attrib_id   = ta.id
               AND td.req_type_id = ta.req_type_id
               AND td.tranpay_id  = pTranpayId)
       WHEN MATCHED THEN
            UPDATE SET description  = pDescription,
                        data_vchar   = pDataVCHAR,
                        data_blob    = pDataBLOB
       WHEN NOT MATCHED THEN
            INSERT (req_type_id, tranpay_id, attrib_id, description, data_vchar, data_blob)
            VALUES (ta.req_type_id, pTranpayId, ta.id,
                    pDescription, pDataVCHAR, pDataBLOB);

      /*writeActionLog(
          pLogMessage => 'Tranpay detail successfully applied!',
          pRefObject => pTranpayId);*/

    /*EXCEPTION
      WHEN sspkg.sysException THEN
        writeActionLog(
          pLogMessage => sspkg.getErrorMessage,
          pRefObject => pTranpayId);
        RAISE;
      WHEN OTHERS THEN
        writeActionLog(
          pLogMessage => sqlerrm,
          pRefObject => pTranpayId);
        RAISE;*/
    END AppendTranPayDetailInt;

	PROCEDURE AppendTranPayDetail(pTranpayId        tranpays.id%TYPE,
                                  pRequestTypeId    tranpays.req_type_id%TYPE,
                                  pAttribId         tranpay_details.attrib_id%TYPE,
                                  pDescription      tranpay_details.description%TYPE,
                                  pDataVCHAR        tranpay_details.data_vchar%TYPE,
                                  pDataBLOB         tranpay_details.data_blob%TYPE)
    IS BEGIN
		--common_pck.CommonSecurityChecks;
		 AppendTranPayDetailInt(pTranpayId, pRequestTypeId, pAttribId, pDescription, pDataVCHAR, pDataBLOB);
	END AppendTranPayDetail;
	
	PROCEDURE AppendTranPayDetailAutonom(pTranpayId        tranpays.id%TYPE,
                                  pRequestTypeId    tranpays.req_type_id%TYPE,
                                  pAttribId         tranpay_details.attrib_id%TYPE,
                                  pDescription      tranpay_details.description%TYPE,
                                  pDataVCHAR        tranpay_details.data_vchar%TYPE,
                                  pDataBLOB         tranpay_details.data_blob%TYPE)
    IS 
		PRAGMA autonomous_transaction;
	BEGIN
		--common_pck.CommonSecurityChecks;
		 AppendTranPayDetailInt(pTranpayId, pRequestTypeId, pAttribId, pDescription, pDataVCHAR, pDataBLOB);
		 COMMIT;
	EXCEPTION
		WHEN OTHERS THEN
			ROLLBACK;
			RAISE;
	END AppendTranPayDetailAutonom;	

    PROCEDURE AppendBackgroundDetails(pTranpayId IN tranpays.id%TYPE,
                                       pReqTypeId IN tranpays.req_type_id%TYPE,
                                       pAccountId IN tranpays.account_id%TYPE)
    IS
      myunit CONSTANT VARCHAR2(23) := 'AppendBackgroundDetails';
      vExtId VARCHAR2(40);
      vCurrFrom VARCHAR2(40);
    BEGIN
      slog.debug(pkgCtxId, myUnit,  pReqTypeId || ':' || pTranpayId || ':' || pAccountId);

      IF pReqTypeId NOT IN (common_pck.cRTI_UPP, common_pck.cRTI_PPI, common_pck.cRTI_TRANSFER) THEN
        RETURN;
      END IF;

      IF pAccountId IS NULL THEN
        vExtId    := NULL;
        vCurrFrom := NULL;
      ELSE
        vExtId := accounts_pck.getAccountDomPaymentId(pAccountId);
        vCurrFrom := accounts_pck.getDomesticCurrencyId();
      END IF;

	  IF pReqTypeId IN (common_pck.cRTI_UPP, common_pck.cRTI_PPI) THEN
		AppendTranPayDetailInt(pTranpayId => pTranpayId,
                          pRequestTypeId => pReqTypeId,
                          pAttribId => common_pck.cTRPAY_ATT_EXT_ID,
                          pDescription => NULL,
						  pDataVCHAR => vExtId,
                          pDataBLOB => NULL);
	  END IF;

      IF pReqTypeId = common_pck.cRTI_UPP THEN
		AppendTranPayDetailInt(pTranpayId => pTranpayId,
                          pRequestTypeId => pReqTypeId,
                          pAttribId => common_pck.cTRPAY_ATT_VALUTA_POKRICA_ALIA,
                          pDescription => NULL,
                          pDataVCHAR => vCurrFrom,
                          pDataBLOB => NULL);

      END IF;

	  IF pReqTypeId = common_pck.cRTI_TRANSFER THEN
	    slog.debug(pkgCtxId, myUnit, 'Populate bank account type to');

		DECLARE
			vBankAccountIdTo bank_accounts.id%TYPE;
			vBankAccountTypeIdTo bank_accounts.bank_account_type_id%TYPE;
		BEGIN
		  vBankAccountIdTo := getTranpayDetailVCharData(pTranpayId, pReqTypeId, common_pck.cTRANPAY_ATT_RACUN);
		  slog.debug(pkgCtxId, myUnit, 'vBankAccountIdTo: ' || vBankAccountIdTo);

		  IF vBankAccountIdTo IS NOT NULL THEN
			vBankAccountTypeIdTo := accounts_pck.getBankAccountTypeId(pID => vBankAccountIdTo);
	  		slog.debug(pkgCtxId, myUnit, 'vBankAccountTypeIdTo: ' || vBankAccountTypeIdTo);

			AppendTranPayDetailInt(pTranpayId => pTranpayId,
                          pRequestTypeId => pReqTypeId,
                          pAttribId => common_pck.cTRANPAY_ATT_ACC_TYPE_ID_TO,
                          pDescription => NULL,
                          pDataVCHAR => vBankAccountTypeIdTo,
                          pDataBLOB => NULL);
			slog.debug(pkgCtxId, myUnit, 'Detail appended');
		  END IF;
		END;
      END IF;
    END AppendBackgroundDetails;

    FUNCTION getTranpayAttributeVCHARData(pTranpayId tranpays.id%TYPE,
                                          pAttributeId tranpay_attribs.id%TYPE)
    RETURN tranpay_details.data_vchar%TYPE
    IS
        myunit CONSTANT VARCHAR2(28) := 'getTranpayAttributeVCHARData';
        vDataVCHAR tranpay_details.data_vchar%TYPE;
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pTranpayId || ':' || pAttributeId);
        common_pck.CommonSecurityChecks;

        SELECT data_vchar
          INTO vDataVCHAR
          FROM tranpay_details
         WHERE tranpay_id = pTranpayId
           AND attrib_id = pAttributeId;

        RETURN vDataVCHAR;
    EXCEPTION
      WHEN no_data_found THEN
		 slog.error(pkgCtxId, myUnit, cERR_InvalidTranpay, pTranpayId || ':' || pAttributeId);
         sspkg.raiseError(cERR_InvalidTranpay, null, pkgCtxId, myunit);
    END getTranpayAttributeVCHARData;

    PROCEDURE createRequiredDetails(pTranpayId tranpays.id%TYPE)
    IS
        myunit CONSTANT VARCHAR2(21) := 'createRequiredDetails';
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pTranpayId);
        common_pck.CommonSecurityChecks;

        INSERT INTO tranpay_details(req_type_id, tranpay_id, attrib_id, description)
        SELECT t.req_type_id, pTranpayId, ta.id, ta.description
        FROM tranpay_attribs ta, tranpays t
        WHERE ta.req_type_id = t.req_type_id
          AND t.id = pTranpayId;
    END createRequiredDetails;

    FUNCTION getCommonReqTypeForTemplPck(pTemplatePackage tranpay_templates.template_package_id%TYPE)
    RETURN tranpay_templates.request_type_id%TYPE
    IS
        myunit CONSTANT VARCHAR2(27) := 'getCommonReqTypeForTemplPck';
        vRequestTypeId tranpay_templates.request_type_id%TYPE;
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pTemplatePackage);
        common_pck.CommonSecurityChecks;

        SELECT DISTINCT request_type_id
          INTO vRequestTypeId
          FROM tranpay_templates
         WHERE template_package_id = pTemplatePackage;

        RETURN vRequestTypeId;

    EXCEPTION
      WHEN no_data_found THEN
		slog.error(pkgCtxId, myUnit, cERR_InvalidTranpay, pTemplatePackage);
        sspkg.raiseError(cERR_NoTrpTmplForGivenPackage, null, pkgCtxId, myunit);
      WHEN too_many_rows THEN
		slog.error(pkgCtxId, myUnit, cERR_InvalidTranpay, pTemplatePackage);
        sspkg.raiseError(cERR_VariousTrpTmplReqTypes, null, pkgCtxId, myunit);
    END getCommonReqTypeForTemplPck;

    FUNCTION createCOA(pAccOwnerId charts_of_accounts.acc_owner_id%TYPE,
                       pShortDescription charts_of_accounts.short_description%TYPE,
                       pDescription charts_of_accounts.description%TYPE,
                       pSortCode charts_of_accounts.sort_code%TYPE,
                       pParentId charts_of_accounts.parent_id%TYPE)
    RETURN charts_of_accounts.id%TYPE
    IS
        myunit CONSTANT VARCHAR2(9) := 'createCOA';
        vCoaId charts_of_accounts.id%TYPE;
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pAccOwnerId);
        common_pck.CommonSecurityChecks;

        INSERT INTO charts_of_accounts(acc_owner_id, id, short_description, description, sort_code, parent_id)
        VALUES (pAccOwnerId, CHARTS_OF_ACCOUNTS_ID_SEQ.NEXTVAL, pShortDescription, pDescription, pSortCode, pParentId)
        RETURNING id INTO vCoaId;

        RETURN vCoaId;
    END createCOA;

    PROCEDURE updateCOA(pCoaId charts_of_accounts.id%TYPE,
                       pShortDescription charts_of_accounts.short_description%TYPE,
                       pDescription charts_of_accounts.description%TYPE,
                       pSortCode charts_of_accounts.sort_code%TYPE,
                       pParentId charts_of_accounts.parent_id%TYPE)
    IS
        myunit CONSTANT VARCHAR2(9) := 'updateCOA';

    BEGIN
        slog.debug(pkgCtxId, myUnit,  pCoaId || ':' || pParentId);
        common_pck.CommonSecurityChecks;

        UPDATE charts_of_accounts
           SET short_description = pShortDescription,
               description = pDescription,
               sort_code = pSortCode,
               parent_id = pParentId
         WHERE id = pCoaId;

    END updateCOA;

    PROCEDURE deleteCOA(pCoaId charts_of_accounts.id%TYPE)
    IS
        myunit CONSTANT VARCHAR2(9) := 'deleteCOA';
    BEGIN
       slog.debug(pkgCtxId, myUnit,  pCoaId);
        common_pck.CommonSecurityChecks;

        DELETE FROM charts_of_accounts
         WHERE id = pCoaId;

    END deleteCOA;

    FUNCTION GenerateRefCode(pTranpayId tranpays.id%TYPE)
    RETURN tranpays.ref_code%TYPE
    IS
        myunit CONSTANT VARCHAR2(15) := 'GenerateRefCode';
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pTranpayId);

        RETURN(SUBSTR(rawtohex(dbms_crypto.hash(src => utl_raw.cast_to_raw(pTranpayId) || dbms_crypto.RandomBytes(8), typ => dbms_crypto.HASH_SH1)),1,30));

    END GenerateRefCode;

    FUNCTION getTranpayForRefCode(pTranpayRefCode tranpays.ref_code%TYPE)
    RETURN tranpays.id%TYPE
    IS
        myunit CONSTANT VARCHAR2(20) := 'getTranpayForRefCode';
        vTranpayId tranpays.id%TYPE;

    BEGIN
        slog.debug(pkgCtxId, myUnit,  pTranpayRefCode);
        common_pck.CommonSecurityChecks;

        SELECT id
          INTO vTranpayId
          FROM tranpays
         WHERE ref_code like (pTranpayRefCode || '%');

        RETURN vTranpayId;
    EXCEPTION
      WHEN no_data_found THEN
		slog.error(pkgCtxId, myUnit, cERR_InvalidRefCode, pTranpayRefCode);
        sspkg.raiseError(cERR_InvalidRefCode, null, pkgCtxId, myunit);
    END getTranpayForRefCode;

    FUNCTION getTranpayRefCode(pTranpayId tranpays.id%TYPE)
    RETURN tranpays.ref_code%TYPE
    IS
        myunit CONSTANT VARCHAR2(17) := 'getTranpayRefCode';
        vTranpayRefCode tranpays.ref_code%TYPE;

    BEGIN
        slog.debug(pkgCtxId, myUnit,  pTranpayId);
        common_pck.CommonSecurityChecks;

        SELECT ref_code
          INTO vTranpayRefCode
          FROM tranpays
         WHERE id = pTranpayId;

        RETURN vTranpayRefCode;
    EXCEPTION
      WHEN no_data_found THEN
		slog.error(pkgCtxId, myUnit, cERR_InvalidTranpay, pTranpayId);
        sspkg.raiseError(cERR_InvalidTranpay, null, pkgCtxId, myunit);
    END getTranpayRefCode;

    FUNCTION getLastTranpayForSchedule(pScheduleId tranpays.schedule_id%TYPE)
    RETURN tranpays.id%TYPE
    IS
        myunit CONSTANT VARCHAR2(25) := 'getLastTranpayForSchedule';
        vTranpayId tranpays.id%TYPE;
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pScheduleId);
        common_pck.CommonSecurityChecks;

        IF pScheduleId IS NULL THEN
            RETURN NULL;
        END IF;


        SELECT MAX(ID)
          INTO vTranpayId
          -- FROM vw$user_tranpays
          FROM tranpays
         WHERE schedule_id = pScheduleId;

        RETURN vTranpayId;
    EXCEPTION
        WHEN no_data_found THEN
			slog.error(pkgCtxId, myUnit, cERR_InvalidSchedule, pScheduleId);
            sspkg.raiseError(cERR_InvalidSchedule, null, pkgCtxId, myunit);
    END getLastTranpayForSchedule;


   FUNCTION isValidGCBValue(pTableName IN VARCHAR2,
                                 pValue IN VARCHAR2)
   RETURN PLS_INTEGER
   IS
      vPom VARCHAR2(1);
   BEGIN
      SELECT NULL
        INTO vPom
        FROM DUAL
       WHERE EXISTS (SELECT NULL
        FROM generic_code_books
       WHERE cb_table = pTableName
         AND cb_val_0 = pValue);
      RETURN 1;
   EXCEPTION
    WHEN no_data_found THEN
      RETURN 0;
   END isValidGCBValue;

   PROCEDURE validateRevenueType( pRequestTypeId  IN tranpays.req_type_id%TYPE,
                                  pAttribId       IN tranpay_details.attrib_id%TYPE,
                                  pDataVCHAR      IN tranpay_details.data_vchar%TYPE)
   IS
      myunit CONSTANT VARCHAR2(19) := 'validateRevenueType';
   BEGIN
		slog.debug(pkgCtxId, myUnit,  pRequestTypeId || ':' || pAttribId || ':' || pDataVCHAR);

		IF isValidGCBValue(pTableName => common_pck.cTRPAY_ATT_VRSTA_PRIHODA_UPP, pValue => pDataVCHAR) = 0 THEN
			slog.error(pkgCtxId, myUnit, cERR_InvalidRevenueType, pRequestTypeId || ':' || pAttribId || ':' || pDataVCHAR);
			sspkg.raiseError(cERR_InvalidRevenueType, null, pkgCtxId, myunit);
		END IF;
   END validateRevenueType;

   PROCEDURE validateMunicipalityCode(pRequestTypeId  IN tranpays.req_type_id%TYPE,
                                      pAttribId       IN tranpay_details.attrib_id%TYPE,
                                      pDataVCHAR      IN tranpay_details.data_vchar%TYPE)
   IS
      myunit CONSTANT VARCHAR2(24) := 'validateMunicipalityCode';

   BEGIN

    slog.debug(pkgCtxId, myUnit,  pRequestTypeId || ':' || pAttribId || ':' || pDataVCHAR);

    IF isValidGCBValue(pTableName => common_pck.cTRPAY_ATT_OPSTINA_UPP, pValue => pDataVCHAR) = 0 THEN
		slog.error(pkgCtxId, myUnit, cERR_InvalidMunicipalityCode, pRequestTypeId || ':' || pAttribId || ':' || pDataVCHAR);
		sspkg.raiseError(cERR_InvalidMunicipalityCode, null, pkgCtxId, myunit);
    END IF;
   END validateMunicipalityCode;

FUNCTION getTemplatePackagesListCount(pRequestTypeId tranpay_templates.request_type_id%TYPE)
    RETURN PLS_INTEGER
    IS
        myunit CONSTANT VARCHAR2(28) := 'getTemplatePackagesListCount';
        vCount PLS_INTEGER := 0;
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pRequestTypeId);

        SELECT COUNT(vw$utp.id)
          INTO vCount
          FROM vw$user_template_packages vw$utp
         WHERE EXISTS (SELECT 1
                         FROM VW$USER_TRANPAY_TEMPLATES vw$utt
                        WHERE vw$utt.template_package_id = vw$utp.id
                          AND vw$utt.request_type_id     = pRequestTypeId);
            RETURN vCount;
    END getTemplatePackagesListCount;

    FUNCTION getTemplatePackagesList(pOffset PLS_INTEGER := 1, pArraySize PLS_INTEGER := 10)
    RETURN sys_refcursor
    IS
        myunit CONSTANT VARCHAR2(23) := 'getTemplatePackagesList';

        vOffset PLS_INTEGER;
        vArraySize PLS_INTEGER;

        rez sys_refcursor;
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pOffset || ':' || pArraySize);
        common_pck.CommonSecurityChecks;

        common_pck.SetOffsetArraySize(pkgCtxId, myunit,
                                      pOffset, pArraySize,
                                      vOffset, vArraySize);

        OPEN rez FOR
          SELECT id, name, description, valid FROM
            (SELECT DISTINCT vw$utp.id,
                   vw$utp.name,
                   vw$utp.description,
                   vw$utp.valid,
                   ROW_NUMBER() OVER (ORDER BY vw$utp.name ASC) rn
              FROM vw$user_template_packages vw$utp)
          WHERE rn BETWEEN vOffset AND (vOffset + vArraySize)
          ORDER BY rn ASC;

          RETURN rez;
    END getTemplatePackagesList;

    FUNCTION getTranpayTemplateDetails(pTranpayTemplateId tranpay_templates.id%TYPE,
                                pReqestTypeId tranpay_attribs.req_type_id%TYPE)
    RETURN sys_refcursor
    IS
        myunit CONSTANT VARCHAR2(25) := 'getTranpayTemplateContent';
        rez sys_refcursor;
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pTranpayTemplateId || ':' || pReqestTypeId);
        common_pck.CommonSecurityChecks;

        OPEN rez FOR
        SELECT  ta.req_type_id, ta.id, ta.description AttribDescription, ta.basic_datatype, ta.maxsize,
                ta.value_required, ta.blob_mimetype, ta.regexp,
                ttd.description DetailDescription, ttd.data_vchar, ttd.data_blob
        FROM    tranpay_attribs ta,
                tranpay_template_details ttd,
                VW$USER_TRANPAY_TEMPLATES tt
         WHERE ttd.tranpay_attrib_req_type_id = ta.req_type_id
           AND ttd.tranpay_attrib_id = ta.id
           AND ttd.tranpay_template_id = tt.id
           AND tt.id = pTranpayTemplateId
           AND ta.req_type_id = pReqestTypeId;

        RETURN rez;
    END getTranpayTemplateDetails;

    FUNCTION getTranpaySchedule(pTranpayId tranpays.id%TYPE)
    RETURN sys_refcursor IS
        myunit CONSTANT VARCHAR2(18) := 'getTranpaySchedule';
        rez sys_refcursor;
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pTranpayId);

        common_pck.CommonSecurityChecks;
        OPEN rez FOR
            SELECT  t.id tranpay_id, s.id, s.schedule_type_type, s.active, s.execution_date,
                    s.not_before, s.not_after, s.max_retry_count, s.retry_interval,
                    s.retry_int_unit, s.min_balance, s.date_created, s.date_modified,
                    s.ph0, s.ph1, s.ph2, s.ph3, s.ph4, s.ph5, s.ph6, s.ph7, s.ph8, s.ph9
             FROM schedules s,
                  tranpays t
           WHERE t.schedule_id = s.id
             AND t.id = pTranpayId;

        RETURN rez;
    END getTranpaySchedule;

    FUNCTION getTranpaySchedule
    RETURN sys_refcursor IS
        myunit CONSTANT VARCHAR2(19) := 'getTranpaySchedule2';
        rez sys_refcursor;
    BEGIN
        slog.debug(pkgCtxId, myUnit);

        common_pck.CommonSecurityChecks;
        OPEN rez FOR
            SELECT  t.id tranpay_id, s.id, s.schedule_type_type, s.active, s.execution_date,
                    s.not_before, s.not_after, s.max_retry_count, s.retry_interval,
                    s.retry_int_unit, s.min_balance, s.date_created, s.date_modified,
                    s.ph0, s.ph1, s.ph2, s.ph3, s.ph4, s.ph5, s.ph6, s.ph7, s.ph8, s.ph9
             FROM tranpays t JOIN tmp$tranpays tmp ON (tmp.id = t.id)
                  JOIN schedules s ON (s.id = t.schedule_id);

        RETURN rez;
    END getTranpaySchedule;

    FUNCTION getSchedules(pScheduleId schedules.id%TYPE)
    RETURN sys_refcursor IS
        myunit CONSTANT VARCHAR2(12) := 'getSchedules';
        rez sys_refcursor;
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pScheduleId);

        common_pck.CommonSecurityChecks;
        OPEN rez FOR
            SELECT sh.id, sh.success, sh.scheduler_message_id,
                sh.execution_details, s.schedule_type_type, s.active, s.execution_date,
                s.not_before, s.not_after, s.max_retry_count, s.retry_interval,
                s.retry_int_unit, s.min_balance, s.ph0 hour, s.ph1 minute, s.ph2 day, s.ph3 month, s.ph4 year
            FROM schedule_histories sh JOIN schedules s ON (s.id = sh.schedule_id)
            WHERE s.id = pScheduleId
            ORDER BY sh.id ASC;

        RETURN rez;
    END getSchedules;

    FUNCTION getTranpayGroupData(pTranpayGroupId tranpay_groups.id%TYPE)
    RETURN sys_refcursor IS
        myunit CONSTANT VARCHAR2(19) := 'getTranpayGroupData';
        rez sys_refcursor;
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pTranpayGroupId);
        common_pck.CommonSecurityChecks;

		IF mcauth.auth.getAccountOwner = '%' THEN
			OPEN rez FOR
				SELECT name, description, valid
				FROM VW$USER_TRANPAY_GROUPS
				WHERE id = pTranpayGroupId;
		ELSE
			OPEN rez FOR
				SELECT name, description, valid
				FROM VW$USER_TRANPAY_GROUPS_ACC
				WHERE id = pTranpayGroupId;
		END IF;

        RETURN rez;
    END getTranpayGroupData;

    FUNCTION getTranpayGroupsListCount(pRtBasicType request_types.basic_type%TYPE := '%')
    RETURN PLS_INTEGER IS
        myunit CONSTANT VARCHAR2(25) := 'getTranpayGroupsListCount';

        vCount PLS_INTEGER;
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pRtBasicType);
        common_pck.CommonSecurityChecks;

		IF mcauth.auth.getAccountOwner = '%' THEN
			SELECT COUNT(id)
				INTO vCount
				FROM VW$USER_TRANPAY_GROUPS
				WHERE rt_basic_type like pRtBasicType;
		ELSE
			SELECT COUNT(id)
				INTO vCount
				FROM VW$USER_TRANPAY_GROUPS_ACC
				WHERE rt_basic_type like pRtBasicType;
		END IF;

        RETURN vCount;
    END getTranpayGroupsListCount;

    FUNCTION getTranpayGroupsList(pOffset PLS_INTEGER := 1, pArraySize PLS_INTEGER := 10, pRtBasicType request_types.basic_type%TYPE := '%')
    RETURN sys_refcursor IS
        myunit CONSTANT VARCHAR2(20) := 'getTranpayGroupsList';

        vOffset PLS_INTEGER;
        vArraySize PLS_INTEGER;

        -- I shall to be sure that pRtBasicType is NOT NULL!
        vRtBasicType request_types.basic_type%TYPE;

        rez sys_refcursor;
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pOffset ||':'|| pArraySize|| ':' || pRtBasicType);
		
		vRtBasicType := NVL(pRtBasicType, '%');

        common_pck.CommonSecurityChecks;

        common_pck.SetOffsetArraySize(pkgCtxId, myunit,
                                      pOffset, pArraySize,
                                      vOffset, vArraySize);

		IF mcauth.auth.getAccountOwner = '%' THEN
			OPEN rez FOR
				SELECT id, name, description, valid, rt_basic_type FROM
				(SELECT id, name, description, valid, rt_basic_type,
						ROW_NUMBER() OVER (ORDER BY name ASC) rn
				FROM VW$USER_TRANPAY_GROUPS
				WHERE rt_basic_type like vRtBasicType)
				WHERE rn BETWEEN vOffset AND (vOffset + vArraySize)
				ORDER BY rn ASC;
		ELSE
			OPEN rez FOR
				SELECT id, name, description, valid, rt_basic_type FROM
				(SELECT id, name, description, valid, rt_basic_type,
						ROW_NUMBER() OVER (ORDER BY name ASC) rn
				FROM VW$USER_TRANPAY_GROUPS_ACC
				WHERE rt_basic_type like vRtBasicType)
				WHERE rn BETWEEN vOffset AND (vOffset + vArraySize)
				ORDER BY rn ASC;
		END IF;

        RETURN rez;
    END getTranpayGroupsList;

    FUNCTION getTranpayAttribute(pRequestTypeId tranpay_attribs.req_type_id%TYPE,
                                 pTranpayAttribId tranpay_attribs.id%TYPE)
    RETURN sys_refcursor IS
        myunit CONSTANT VARCHAR2(19) := 'getTranpayAttribute';

        rez sys_refcursor;
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pRequestTypeId ||':'|| pTranpayAttribId);
        common_pck.CommonSecurityChecks;

        OPEN rez FOR
        SELECT description, basic_datatype, maxsize, value_required, blob_mimetype, regexp
         FROM tranpay_attribs
        WHERE id = pTranpayAttribId
          AND req_type_id = pRequestTypeId;

        RETURN rez;
    END getTranpayAttribute;

    FUNCTION getTranpayAttributesListCount(pRequestTypeId tranpay_attribs.req_type_id%TYPE DEFAULT '%')
    RETURN PLS_INTEGER IS
        myunit CONSTANT VARCHAR2(29) := 'getTranpayAttributesListCount';
        vCount PLS_INTEGER;
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pRequestTypeId);
        common_pck.CommonSecurityChecks;

        SELECT COUNT(id)
          INTO vCount
         FROM tranpay_attribs
        WHERE req_type_id LIKE pRequestTypeId;

        RETURN vCount;
    END getTranpayAttributesListCount;

    FUNCTION getTranpayAttributesList(pRequestTypeId tranpay_attribs.req_type_id%TYPE DEFAULT '%')
    RETURN sys_refcursor IS
        myunit CONSTANT VARCHAR2(24) := 'getTranpayAttributesList';
        rez sys_refcursor;
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pRequestTypeId);

        OPEN rez FOR
        SELECT id, req_type_id, description, basic_datatype, maxsize, value_required, blob_mimetype, regexp
         FROM tranpay_attribs
        WHERE req_type_id LIKE pRequestTypeId;

        RETURN rez;
    END getTranpayAttributesList;

    FUNCTION getTranpayDetails(pTranpayId tranpays.id%TYPE)
    RETURN sys_refcursor IS
        myunit CONSTANT VARCHAR2(17) := 'getTranpayDetails';
        rez sys_refcursor;

       PROCEDURE writeActionLog(
          pLogMessage IN VARCHAR2,
          pRefObject IN VARCHAR2 DEFAULT NULL
        ) IS
        BEGIN
          authorization_pck.writeActionLog(
              pActions => actions_list(common_pck.cACT_ViewTranpays),
              pLogMessage => pTranpayId || ':-' || pLogMessage,
              pRefClass => cREF_CLASS|| myunit,
              pRefObject => pRefObject);
        END writeActionLog;

    BEGIN
        slog.debug(pkgCtxId, myUnit,  pTranpayId);

        common_pck.CommonSecurityChecks;

        IF NOT tranpayExists(pTranpayId) THEN
			slog.error(pkgCtxId, myUnit, cERR_InvalidTranpay, pTranpayId);
            sspkg.raiseError(cERR_InvalidTranpay, null, pkgCtxId, myunit);
        END IF;

        OPEN rez FOR
        SELECT vw$utd.tranpay_id tranpay_id,
               vw$utd.attrib_id attrib_id,
               vw$utd.req_type_id req_type_id,
               vw$utd.detail_description detail_description,
               vw$utd.attrib_description attrib_description,
               vw$utd.basic_datatype basic_datatype,
               vw$utd.maxsize maxsize,
               vw$utd.value_required value_required,
               vw$utd.blob_mimetype blob_mimetype,
               vw$utd.data_vchar data_vchar,
               vw$utd.data_blob data_blob
          FROM VW$ALL_TRANPAY_DETAILS vw$utd
         WHERE vw$utd.tranpay_id = pTranpayId;

        writeActionLog(pLogMessage => 'FINISH', pRefObject => NULL);

        RETURN rez;
    EXCEPTION
      WHEN sspkg.sysException THEN
        writeActionLog(
          pLogMessage => sspkg.getErrorMessage,
          pRefObject => NULL);
        RAISE;
      WHEN OTHERS THEN
        writeActionLog(
          pLogMessage => sqlerrm,
          pRefObject => NULL);
        RAISE;
    END getTranpayDetails;

    FUNCTION getTranpayDetails
    RETURN sys_refcursor IS
        myunit CONSTANT VARCHAR2(18) := 'getTranpayDetails2';
        rez sys_refcursor;

    BEGIN
        slog.debug(pkgCtxId, myUnit);

        common_pck.CommonSecurityChecks;

        OPEN rez FOR
          SELECT td.tranpay_id,
            td.attrib_id,
            td.req_type_id,
            td.description detail_description,
            ta.description attrib_description,
            ta.basic_datatype,
            ta.maxsize,
            ta.value_required,
            ta.blob_mimetype,
            td.data_vchar,
            td.data_blob
          FROM tranpay_details td JOIN tmp$tranpays t ON (t.id = td.tranpay_id)
          JOIN tranpay_attribs ta ON (ta.id = td.attrib_id AND ta.req_type_id = td.req_type_id);

        RETURN rez;
    END getTranpayDetails;

    FUNCTION getTranpayDetailVCharData(pTranpayId tranpay_details.tranpay_id%TYPE, pReqTypeId IN tranpay_details.req_type_id%TYPE, pAttribId tranpay_details.attrib_id%TYPE)
    RETURN tranpay_details.data_vchar%TYPE IS
        myunit CONSTANT VARCHAR2(25) := 'getTranpayDetailVCharData';
		vDataVchar tranpay_details.data_vchar%TYPE;
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pTranpayId || ':' || pAttribId);

		SELECT data_vchar
		  INTO vDataVchar
		  FROM tranpay_details td
		  WHERE td.tranpay_id = pTranpayId
		    AND td.req_type_id = pReqTypeId
		    AND td.attrib_id = pAttribId;

		RETURN vDataVchar;

    EXCEPTION
      WHEN no_data_found THEN
        RETURN NULL;
    END getTranpayDetailVCharData;

	PROCEDURE setTranpayDetailVCharData(pTranpayId tranpay_details.tranpay_id%TYPE, pReqTypeId IN tranpay_details.req_type_id%TYPE, pAttribId IN tranpay_details.attrib_id%TYPE, pDataVChar IN tranpay_details.data_vchar%TYPE)
	IS
		myunit CONSTANT VARCHAR2(25) := 'setTranpayDetailVCharData';

        cg$rec cg$TRANPAY_DETAILS.cg$row_type;
        cg$ind cg$TRANPAY_DETAILS.cg$ind_type;

        v_cg_result BOOLEAN;
        vMSG VARCHAR2(512);
	    vERROR VARCHAR2(1);
	    vMSG_TYPE VARCHAR2(3);
	    vMSGID INTEGER;
	    vLOC VARCHAR2(2000 BYTE);

	BEGIN
		slog.debug(pkgCtxId, myUnit,  pTranpayId || ':' || pReqTypeId || ':' || pAttribId || ':' || pDataVChar);

        cg$TRANPAY_DETAILS.val$length (
            pREQ_TYPE_ID => pReqTypeId, pTRANPAY_ID => pTranpayId
            ,pATTRIB_ID => pAttribId, pDESCRIPTION => NULL
            ,pDATA_VCHAR => pDataVChar);

        cg$rec.REQ_TYPE_ID := pReqTypeId;
        cg$rec.TRANPAY_ID := pTranpayId;
        cg$rec.ATTRIB_ID := pAttribId;
        cg$rec.DESCRIPTION := NULL;
        cg$rec.DATA_VCHAR := pDataVChar;

        cg$ind.REQ_TYPE_ID := TRUE;
        cg$ind.TRANPAY_ID := TRUE;
        cg$ind.ATTRIB_ID := TRUE;
        cg$ind.DESCRIPTION := TRUE;
        cg$ind.DATA_VCHAR := TRUE;

        BEGIN
			slog.debug(pkgCtxId, myUnit, 'Try update');
            cg$TRANPAY_DETAILS.upd(cg$rec => cg$rec, cg$ind => cg$ind, do_upd => TRUE);
        EXCEPTION
            -- Fetched also when record does not exists -- should try insert !
            WHEN cg$errors.cg$error THEN
                -- Retrieve error message to determine error cause !
                v_cg_result := cg$errors.pop(msg => vMSG
                    ,error => vERROR
                    ,msg_type => vMSG_TYPE
                    ,msgid => vMSGID
                    ,loc => vLOC);

                -- If API ERROR with code 100 - no_data_found, then ...
                IF vERROR = 'E' AND vMSG_TYPE = 'ORA' AND vMSGID = 100 THEN
                    -- Clear errors and ...
                    cg$errors.clear;
                    -- Try to insert new one !
					slog.debug(pkgCtxId, myUnit, 'Try insert');
                    cg$TRANPAY_DETAILS.ins(cg$rec => cg$rec, cg$ind => cg$ind, do_ins => TRUE);
					slog.debug(pkgCtxId, myUnit, 'Insert done');
                ELSE
                    -- Else an unexpected error has occured, so tell it to the caller!
					slog.error(pkgCtxId, myUnit, vMSG);
                    RAISE;
                END IF;
        END;
		slog.debug(pkgCtxId, myUnit);
	END setTranpayDetailVCharData;

    FUNCTION getTranpaySignatures(pTranpayId tranpays.id%TYPE)
    RETURN sys_refcursor
    IS
        myunit CONSTANT VARCHAR2(20) := 'getTranpaySignatures';
        rez sys_refcursor;
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pTranpayId);
        common_pck.CommonSecurityChecks;

        OPEN rez FOR
          SELECT a.id, a.user_id, c.last_name, c.first_name, a.date_signed, a.source_data, a.signature,
                 a.signature_method, b.pct_signed, b.tranpay_id, a.application_id
            FROM signatures a JOIN signers b ON (b.signature_id = a.id)
            JOIN end_users c ON (a.user_id = c.id)
           WHERE b.tranpay_id = pTranpayId;

        RETURN rez;
    END getTranpaySignatures;

    FUNCTION getTranpaySignatures
    RETURN sys_refcursor
    IS
        myunit CONSTANT VARCHAR2(21) := 'getTranpaySignatures2';
        rez sys_refcursor;
    BEGIN
        slog.debug(pkgCtxId, myUnit);
        common_pck.CommonSecurityChecks;

        OPEN rez FOR
          SELECT a.id, a.user_id, c.last_name, c.first_name, a.date_signed, a.source_data, a.signature,
                 a.signature_method, b.pct_signed, b.tranpay_id, a.application_id
            FROM tmp$tranpays t JOIN signers b ON (b.tranpay_id = t.id)
             JOIN signatures a  ON (b.signature_id = a.id)
             JOIN end_users c ON (a.user_id = c.id);

        RETURN rez;
    END getTranpaySignatures;

    FUNCTION getActiveSchedules(pOffset PLS_INTEGER := 1,
                        pArraySize PLS_INTEGER := 10)
    RETURN sys_refcursor IS
        myunit CONSTANT VARCHAR2(18) := 'getActiveSchedules';
        vOffset PLS_INTEGER;
        vArraySize PLS_INTEGER;
		vCurrentDate DATE := SYSDATE;

        rez sys_refcursor;

        PROCEDURE writeActionLog(
          pLogMessage IN VARCHAR2,
          pRefObject IN VARCHAR2 DEFAULT NULL
        ) IS
        BEGIN
          authorization_pck.writeActionLog(
              pActions => actions_list(common_pck.cACT_ViewTranpays),
              pLogMessage => pOffset || ':' || pArraySize || ':-' || pLogMessage,
              pRefClass => cREF_CLASS|| myunit,
              pRefObject => pRefObject);
        END writeActionLog;
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pOffset || ':' || pArraySize);

        common_pck.CommonSecurityChecks;

        common_pck.SetOffsetArraySize(pkgCtxId, myunit,
                                      pOffset, pArraySize,
                                      vOffset, vArraySize);

        OPEN rez FOR
        SELECT  id,
            schedule_type_type,
            req_type_id,
            tranval,
            tranval_currency_id,
            sender_account,
            description,
			execute_at,
            tranpay_id,
            REFERENCA_PLACANJA,
            NAZIV_POSILJAOCA,
            RACUN_PRIMAOCA,
            NAZIV_PRIMAOCA,
            HOUR, MINUTE, DAY, MONTH, YEAR,
			not_before, not_after, max_retry_count, retry_interval,
            retry_int_unit, min_balance
       FROM(
        SELECT s.id, s.schedule_type_type,
            t.req_type_id,
            t.tranval,
            t.tranval_currency_id,
            ba.ph3 sender_account,
            t.description,
            s.ph0 HOUR,
            s.ph1 MINUTE,
            s.ph2 DAY,
            s.ph3 MONTH,
            s.ph4 YEAR,
			s.not_before, s.not_after, s.max_retry_count, s.retry_interval,
            s.retry_int_unit, s.min_balance,
            max(t.id) tranpay_id,
            max (decode(td.attrib_id, common_pck.cTRPAY_ATT_REFERENCA_PLACANJA, td.data_vchar, NULL)) REFERENCA_PLACANJA,
            max (decode(td.attrib_id, common_pck.cTRANPAY_ATT_NAZIV_POSILJAOCA, td.data_vchar, NULL)) NAZIV_POSILJAOCA,
            max (decode(td.attrib_id, common_pck.cTRANPAY_ATT_RACUN_PRIMAOCA, td.data_vchar, NULL)) RACUN_PRIMAOCA,
            max (decode(td.attrib_id, common_pck.cTRANPAY_ATT_NAZIV_PRIMAOCA, td.data_vchar, NULL)) NAZIV_PRIMAOCA,
			max(t.execute_at) execute_at,
            ROW_NUMBER() OVER (ORDER BY s.id DESC) rn
        FROM schedules s
             JOIN vw$user_tranpays_us_bx t ON (t.schedule_id = s.id)
			 JOIN bank_accounts ba on ba.id = t.account_id
             LEFT OUTER JOIN tranpay_details td ON (td.tranpay_id = t.id)
        where s.active = 1
          and s.schedule_type_type <> common_pck.cSCHDTYPE_IMMEDIATE
		  and decode(s.schedule_type_type, common_pck.cSCHDTYPE_LATER, t.execute_at, vCurrentDate) >= vCurrentDate
        group by s.id, s.schedule_type_type,
            t.req_type_id, t.tranval, t.tranval_currency_id,
            ba.ph3,
            t.description,
            s.ph0,
            s.ph1,
            s.ph2,
            s.ph3,
            s.ph4,
			s.not_before, s.not_after, s.max_retry_count, s.retry_interval,
            s.retry_int_unit, s.min_balance)
        WHERE rn BETWEEN vOffset AND (vOffset + vArraySize)
        ORDER BY rn ASC;

        writeActionLog(pLogMessage => 'FINISH', pRefObject => NULL);

        RETURN rez;
    EXCEPTION
      WHEN sspkg.sysException THEN
        writeActionLog(
          pLogMessage => sspkg.getErrorMessage,
          pRefObject => NULL);
        RAISE;
      WHEN OTHERS THEN
        writeActionLog(
          pLogMessage => sqlerrm,
          pRefObject => NULL);
        RAISE;
    END getActiveSchedules;


    FUNCTION translateWuErrorMessage(vErrorMessage VARCHAR2)
    RETURN VARCHAR2
    IS
        vPom VARCHAR2(4000);
        vMessagePart VARCHAR2(4000);
        vTranslatedMessage VARCHAR2(4000);
        vErrorCode VARCHAR2(4000);
        vNewLineDelimiter CONSTANT VARCHAR2(2) := chr(10);
        vErrorMessageDelimiter CONSTANT VARCHAR2(2) := ',,';
        vPomLength pls_integer := 0;
        myunit CONSTANT VARCHAR2(23) := 'translateWuErrorMessage';

    BEGIN
        slog.debug(pkgCtxId, myUnit,  vErrorMessage);

        vPom := vErrorMessage;
        vPomLength := LENGTH(vPom);
        WHILE(vPomLength > 0) LOOP

            vMessagePart := NVL(SUBSTR(vPom, 1, INSTR(vPom, vNewLineDelimiter)-1), vPom);
            vErrorCode := substr(vMessagePart, 1, INSTR(vMessagePart, vErrorMessageDelimiter)-1);
            IF vErrorCode IS NOT NULL THEN
				vErrorCode := pkgCtxId || '/err/WUErrorMessages/' || vErrorCode;
                IF sspkg.isExistingNode(vErrorCode) THEN
					vTranslatedMessage := vTranslatedMessage ||  mlang.trans(mcauth.auth.getLang, vErrorCode, substr(vMessagePart, INSTR(vMessagePart, vErrorMessageDelimiter)+2)) || vNewLineDelimiter;
                ELSE
                    vTranslatedMessage := vTranslatedMessage || substr(vMessagePart, INSTR(vMessagePart,vErrorMessageDelimiter)+2)  || vNewLineDelimiter;
                END IF;
            ELSE
                vTranslatedMessage := vMessagePart || vNewLineDelimiter;
            END IF;

            IF INSTR(vPom, vNewLineDelimiter) > 0 THEN
                vPom := SUBSTR(vPom, INSTR(vPom, vNewLineDelimiter)+LENGTH(vNewLineDelimiter));
            ELSE
                vPom := NULL;
            END IF;

            vPomLength := LENGTH(vPom);

        END LOOP;
        RETURN SUBSTR(vTranslatedMessage, 1, LENGTH(vTranslatedMessage)-LENGTH(vNewLineDelimiter));
    EXCEPTION
        WHEN OTHERS THEN
            RETURN vErrorMessage;
    END translateWuErrorMessage;

    FUNCTION getFilteredTranpays(pBasicType request_types.basic_type%TYPE)
    RETURN sys_refcursor IS
      rez sys_refcursor;
      myunit CONSTANT VARCHAR2(30) := 'getFilteredTranpays';
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pBasicType);
		
		OPEN rez FOR
        SELECT
          t.id id,
          t.req_type_id req_type_id,
          t.status status,
          (CASE
                 WHEN pBasicType = common_pck.cBRT_WUT THEN tranpays_pck.translateWuErrorMessage(t.status_message)
                 ELSE t.status_message
                 END) status_message,
          t.status_code status_code,
		  (CASE 
			WHEN tmp.aggr_tranpay = 1 THEN tmp.description
			ELSE t.description 
			END
		  ) description,
          t.internal_description internal_description,
          (CASE 
			WHEN tmp.aggr_tranpay = 1 THEN tmp.tranval
			ELSE t.tranval 
			END
		  ) tranval,
          t.tranval_currency_id tranval_currency_id,
          t.cost cost,
          t.cost_currency_id cost_currency_id,
          t.date_created date_created,
          t.date_signed date_signed,
          t.date_processed date_processed,
          t.tranpay_group_id tranpay_group_id,
          tg.name tg_name,
          tg.description tg_description,
          t.account_id account_id,
          t.chart_of_accounts_id chart_of_accounts_id,
          t.schedule_id schedule_id,
          t.execute_at execute_at,
          t.date_modified date_modified,
          t.retry_counter retry_counter,
          t.ref_code ref_code,
          t.user_modified user_modified,
          ec.first_name || ', ' || ec.last_name user_created_name,
          NVL(em.first_name || ', ' || em.last_name, ec.first_name || ', ' || ec.last_name) user_modified_name,
          t.parent_id,
		  tmp.aggr_tranpay
        FROM tranpays t
			JOIN end_users ec ON (ec.id = t.user_id)
			JOIN end_users em ON (em.id = t.user_modified)
               JOIN tmp$tranpays tmp ON (tmp.id = t.id)
          LEFT JOIN tranpay_groups tg ON (tg.id = t.tranpay_group_id)
        ORDER BY tmp.ROWNUMBER ASC;

        RETURN rez;

    END getFilteredTranpays;

    FUNCTION getTranpayValueInDomCur(pTranpayId IN tranpays.id%TYPE)
    RETURN NUMBER
    IS
        myunit CONSTANT VARCHAR2(23) := 'getTranpayValueInDomCur';
        vTranpaysAmmount NUMBER := 0.00;
		vDomesticCurrencyId currencies.id%TYPE;

       PROCEDURE writeActionLog(
          pLogMessage IN VARCHAR2,
          pRefObject IN VARCHAR2 DEFAULT NULL
        ) IS
        BEGIN
          authorization_pck.writeActionLog(
              pActions => actions_list(common_pck.cACT_ViewTranpays),
              pLogMessage => pLogMessage,
              pRefClass => cREF_CLASS|| myunit,
              pRefObject => pRefObject);
        END writeActionLog;
    BEGIN
        slog.debug(pkgCtxId, myUnit);
		
		vDomesticCurrencyId := accounts_pck.GetDomesticCurrencyId();

		SELECT
		SUM(DECODE(req_type_id,
			common_pck.cRTI_UPP, tranval,
			common_pck.cRTI_PPI, conversion_pck.ConvertCurrency(
					pFromCurrencyId => vDomesticCurrencyId,
					pToCurrencyId => tranval_currency_id,
					pAmmount => tranval,
					pConversionDate => TRUNC(SYSDATE),
					pSourceCurrencyId => tranval_currency_id),
			common_pck.cRTI_TRANSFER, conversion_pck.ConvertCurrency(
					pFromCurrencyId => DECODE(FIXED_CURRENCY,common_pck.cTRANSFER_FixedAmnt_Source, tranval_currency_id, vDomesticCurrencyId),
					pToCurrencyId => DECODE(FIXED_CURRENCY, common_pck.cTRANSFER_FixedAmnt_Source, vDomesticCurrencyId, TRAN_CURRENCY_BUY),
					pAmmount => DECODE(FIXED_CURRENCY,common_pck.cTRANSFER_FixedAmnt_Source,tranval, TRAN_AMOUNT_BUY),
					pConversionDate => TRUNC(SYSDATE),
					pSourceCurrencyId => DECODE(FIXED_CURRENCY,common_pck.cTRANSFER_FixedAmnt_Source, tranval_currency_id, TRAN_CURRENCY_BUY)))) zbir
			INTO vTranpaysAmmount
		FROM
		(	SELECT t.id,
			t.req_type_id,
			t.tranval_currency_id, --UPP_CURRENCY_SELL and PPI_CURRENCY_BUY and TRAN_CURRENCY_SELL
			NVL(t.tranval,0) tranval, -- UPP_AMOUNT_SELL and PPI_AMOUNT_BUY and TRAN_AMOUNT_SELL
			MAX (DECODE(td.attrib_id, 'IZNOS_KUPOVINE', TO_NUMBER(td.data_vchar,'9999999999999999.999999999999'), NULL)) TRAN_AMOUNT_BUY,
			MAX (DECODE(td.attrib_id, 'RACUN_VALUTA', td.data_vchar, NULL)) TRAN_CURRENCY_BUY,
			MAX (DECODE(td.attrib_id, 'FIKSNO', td.data_vchar, NULL)) FIXED_CURRENCY
			FROM tranpays t LEFT JOIN tranpay_details td ON (td.tranpay_id = t.id and td.attrib_id IN ('VALUTA_POKRICA','IZNOS_KUPOVINE','RACUN_VALUTA','FIKSNO'))
			WHERE t.id = pTranpayId
			group by t.id, t.req_type_id, t.tranval_currency_id, NVL(t.tranval,0));

        writeActiONLog(
            pLogMessage => 'FINISH',
            pRefObject => vTranpaysAmmount);

        RETURN NVL(vTranpaysAmmount, 0);
    END getTranpayValueInDomCur;

    FUNCTION getMarkedTranpaysValueInDomCur
    RETURN NUMBER
    IS
        myunit CONSTANT VARCHAR2(30) := 'getMarkedTranpaysValueInDomCur';
        vTranpaysAmmount NUMBER := 0.00;
		vDomesticCurrencyId currencies.id%TYPE;

       PROCEDURE writeActionLog(
          pLogMessage IN VARCHAR2,
          pRefObject IN VARCHAR2 DEFAULT NULL
        ) IS
        BEGIN
          authorization_pck.writeActionLog(
              pActions => actions_list(common_pck.cACT_ViewTranpays),
              pLogMessage => pLogMessage,
              pRefClass => cREF_CLASS|| myunit,
              pRefObject => pRefObject);
        END writeActionLog;
    BEGIN
        slog.debug(pkgCtxId, myUnit);
		
		vDomesticCurrencyId := accounts_pck.GetDomesticCurrencyId();

		SELECT
		SUM(DECODE(req_type_id,
			common_pck.cRTI_UPP, tranval,
			common_pck.cRTI_PPI, conversion_pck.ConvertCurrency(
					pFromCurrencyId => vDomesticCurrencyId,
					pToCurrencyId => tranval_currency_id,
					pAmmount => tranval,
					pConversionDate => TRUNC(SYSDATE),
					pSourceCurrencyId => tranval_currency_id),
			common_pck.cRTI_TRANSFER, conversion_pck.ConvertCurrency(
					pFromCurrencyId => DECODE(FIXED_CURRENCY,common_pck.cTRANSFER_FixedAmnt_Source, tranval_currency_id, vDomesticCurrencyId),
					pToCurrencyId => DECODE(FIXED_CURRENCY, common_pck.cTRANSFER_FixedAmnt_Source, vDomesticCurrencyId, TRAN_CURRENCY_BUY),
					pAmmount => DECODE(FIXED_CURRENCY,common_pck.cTRANSFER_FixedAmnt_Source,tranval, TRAN_AMOUNT_BUY),
					pConversionDate => TRUNC(SYSDATE),
					pSourceCurrencyId => DECODE(FIXED_CURRENCY,common_pck.cTRANSFER_FixedAmnt_Source, tranval_currency_id, TRAN_CURRENCY_BUY)))) zbir
			INTO vTranpaysAmmount
		FROM
		(	SELECT t.id,
			t.req_type_id,
			t.tranval_currency_id, --UPP_CURRENCY_SELL and PPI_CURRENCY_BUY and TRAN_CURRENCY_SELL
			NVL(t.tranval,0) tranval, -- UPP_AMOUNT_SELL and PPI_AMOUNT_BUY and TRAN_AMOUNT_SELL
			MAX (DECODE(td.attrib_id, 'IZNOS_KUPOVINE', TO_NUMBER(td.data_vchar,'9999999999999999.999999999999'), NULL)) TRAN_AMOUNT_BUY,
			MAX (DECODE(td.attrib_id, 'RACUN_VALUTA', td.data_vchar, NULL)) TRAN_CURRENCY_BUY,
			MAX (DECODE(td.attrib_id, 'FIKSNO', td.data_vchar, NULL)) FIXED_CURRENCY
			FROM tranpays t LEFT JOIN tranpay_details td ON (td.tranpay_id = t.id and td.attrib_id IN ('VALUTA_POKRICA','IZNOS_KUPOVINE','RACUN_VALUTA','FIKSNO'))
			JOIN tmp$signing_candidates sc ON (t.id = sc.tranpay_id)
			group by t.id, t.req_type_id, t.tranval_currency_id, NVL(t.tranval,0));

        writeActiONLog(
            pLogMessage => 'FINISH',
            pRefObject => vTranpaysAmmount);

        RETURN NVL(vTranpaysAmmount, 0);
    END getMarkedTranpaysValueInDomCur;

  PROCEDURE removeExpiredTrpAttachments
  IS
    myunit CONSTANT VARCHAR2(27) := 'removeExpiredTrpAttachments';
    vFileHandle BFILE;

    CURSOR c IS
      SELECT at.id FROM attachments at JOIN tranpays t ON (t.id = at.tranpay_id)
      where t.status in (commON_pck.cTRPSTS_UC, commON_pck.cTRPSTS_BA, commON_pck.cTRPSTS_BR);

    pragma autONomous_transactiON;
  BEGIN
    slog.debug(pkgCtxId, myUnit);

    FOR rec IN c LOOP
     BEGIN
      DELETE FROM attachments WHERE id = rec.id
      RETURN cONtent INTO vFileHandle;

      util.removeFile(pFileHandle => vFileHandle);
      COMMIT;
     EXCEPTION
      WHEN OTHERS THEN
        ROLLBACK;
     END;
    END LOOP;
    COMMIT;
  END removeExpiredTrpAttachments;

  PROCEDURE assignExtRefId2TRanpay(
		pTranpaysId tranpay_extref.tranpays_id%TYPE,
		pExtRef_Id tranpay_extref.extref_id%TYPE,
		pExtRef_Name tranpay_extref.extref_name%TYPE,
		pExtRef_Status tranpay_extref.extref_status%TYPE)
	IS
		myunit CONSTANT VARCHAR2(22) := 'assignExtRefId2TRanpay';
		cg$rec cg$tranpay_extref.cg$row_type;
		cg$ind cg$tranpay_extref.cg$ind_type;
		v_cg_result BOOLEAN;

	BEGIN
		slog.debug(pkgCtxId, myUnit,  pTranpaysId || ':' || pExtRef_Id || ':' || pExtRef_Name || ':' || pExtRef_Status);

		cg$rec.tranpays_id := pTranpaysId;
		cg$rec.extref_name := pExtRef_Name;
		cg$rec.extref_id := pExtRef_Id;
		cg$rec.extref_status := pExtRef_Status;
		cg$tranpay_extref.upd(cg$rec, cg$ind, TRUE);
	EXCEPTION
		-- Fetched also when record does not exists -- should try insert !
		WHEN cg$errors.cg$error THEN
			-- Retrieve error message to determine error cause !
			v_cg_result := cg$errors.pop(msg => MSG
						,error =>ERROR
						,msg_type => MSG_TYPE
						,msgid =>MSGID
						,loc => LOC);
			-- If API ERROR with code 100 - no_data_found, then ...
			IF ERROR = 'E' AND MSG_TYPE = 'ORA' AND MSGID = 100 THEN
				-- Clear errors and ...
				cg$errors.clear;
				-- Try to insert new ONe !
				cg$tranpay_extref.ins(cg$rec => cg$rec, cg$ind => cg$ind, do_ins => TRUE);
			ELSE
				sspkg.raiseOraError(pkgCtxId, myUnit);
			END IF;
	END assignExtRefId2TRanpay;


	FUNCTION isNotificationSent(pTranpayId tranpay_details.tranpay_id%TYPE, pReqTypeId IN tranpay_details.req_type_id%TYPE, pAttribId tranpay_details.attrib_id%TYPE)
    RETURN BOOLEAN IS
        myunit CONSTANT VARCHAR2(18) := 'isNotificationSent';
		vNotificationSent PLS_INTEGER;
		
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pTranpayId || ':' || pAttribId);

		SELECT null INTO vNotificationSent FROM DUAL WHERE EXISTS (SELECT NULL FROM mcore.tranpay_details where attrib_id = pAttribId and req_type_id = pReqTypeId and tranpay_id = pTranpayId);
		RETURN TRUE;
    EXCEPTION
      WHEN no_data_found THEN
        RETURN FALSE;
    END isNotificationSent;


	/*
		sendTranpayNotification
		- Procedura radi za pojedinaène naloge identifikovane njihovim ID-em
		- Oèekuje se da pozivaoc vodi raèuna o tome da notifikaciju ne šalje višestruko, tj. da provjerava da li je notifikacija veæ proslijeðena odnosno
		  osigura da se notifikacija pošalje samo jednom - procedura upisuje atribut NOTIFICATION_SENT_AT kao detalj naloga po uspiješnom slanju
		- Trenutno je podržano slanje notifikacije u sluèaju odbijenog naloga i odobrenog naloga za slucaj INTESA
	*/
	PROCEDURE sendTranpayNotification(cg$rec IN cg$tranpays.cg$row_type)
    IS
        channelId VARCHAR2(100);
        v_from_address VARCHAR2(100);
		vExtRefName tranpay_extref.extref_name%TYPE;
	    vExtRefId tranpay_extref.extref_id%TYPE;

        v_subject VARCHAR2(4000);
        v_body VARCHAR2(4000);


        CURSOR cContactList IS
            SELECT 'bs' lang, contact_email
            FROM mcore.end_users WHERE
            valid = 1 AND
            id IN (SELECT sn.user_id
                FROM mcore.signatures sn join mcore.signers s ON (s.signature_id = sn.id)
                WHERE s.tranpay_id = cg$rec.id
                UNION ALL
                SELECT user_id FROM mcore.tranpays_jn WHERE id = cg$rec.id AND status = mcore.common_pck.cTRPSTS_UD);
        TYPE TT$ContactEmailList IS TABLE OF cContactList%ROWTYPE;
        vContactEmailList TT$ContactEmailList;

        myUnit CONSTANT VARCHAR2(23) := 'sendTranpayNotification';

    BEGIN
        slog.debug(pkgCtxId, myUnit, cg$rec.id || ':' || cg$rec.status);
		
		channelId := sspkg.readvchar(pkgCtxId || '/Notifications/MsgChannelId');
        v_from_address := sspkg.readvchar(pkgCtxId || '/Notifications/FromAddr');

        IF NOT sspkg.readbool(pkgCtxId || '/Notifications/enabled') THEN
            slog.debug(pkgCtxId, myUnit, 'Mail notification not enabled!');
            RETURN;
        END IF;

        IF cg$rec.status NOT IN (common_pck.cTRPSTS_BA, common_pck.cTRPSTS_BR) THEN
            RETURN;
        END IF;

        IF cg$rec.status = common_pck.cTRPSTS_BR THEN

            IF sspkg.readbool(pkgCtxId || '/Notifications/sendBRTranpayNotification') THEN

              OPEN cContactList;
              slog.debug(pkgCtxId, myUnit, 'Contact cursor opened');
              FETCH cContactList BULK COLLECT INTO vContactEmailList;
              slog.debug(pkgCtxId, myUnit, 'Contact data fetched');
			  CLOSE cContactList;
              IF vContactEmailList.COUNT = 0 THEN
                slog.debug(pkgCtxId, myUnit, 'No contact data found!');
                -- Log error - no contacts
                -- Possible cases - invalid
              ELSE
                
              
                  slog.debug(pkgCtxId, myUnit, 'Mail configuration: ' || v_from_address || ':' || channelId);
    
				  IF NOT isNotificationSent(cg$rec.id, cg$rec.req_type_id, common_pck.cTRPAY_ATT_SENT_NOTIFICATION)  THEN 
					
					FOR i IN 1..vContactEmailList.COUNT LOOP
						slog.debug(pkgCtxId, myUnit, 'Prepare subject');
						v_subject := mlang.trans(vContactEmailList(i).lang, pkgCtxId || '/Notifications/Subject_TranpayRejected');
						slog.debug(pkgCtxId, myUnit, 'Prepare body');
						v_body  := mlang.trans(vContactEmailList(i).lang, pkgCtxId || '/Notifications/Message_TranpayRejected', cg$rec.id, cg$rec.execute_at, cg$rec.tranval, cg$rec.tranval_currency_id, cg$rec.status_message);
						slog.debug(pkgCtxId, myUnit, 'Send mail to ' || vContactEmailList(i).contact_email);
									    	
						BEGIN
						mcore.send_mail(
							fromAddr => v_from_address,
							toAddr => vContactEmailList(i).contact_email,
							subject => v_subject,
							bodyMsg => v_body,
							MC_ID => channelId);
							slog.debug(pkgCtxId, myUnit, 'Notification sent');
							setTranpayDetailVCharData(pTranpayId => cg$rec.id, pReqTypeId => cg$rec.req_type_id, pAttribId => common_pck.cTRPAY_ATT_SENT_NOTIFICATION, pDataVChar => TO_CHAR(SYSDATE, common_pck.cDATETIME_MASK));
        
						EXCEPTION
							WHEN OTHERS THEN
							slog.error(pkgCtxId, myUnit, 'Unable to send email notification for rejected tranpay:' || cg$rec.id || ':' || vContactEmailList(i).contact_email);
							slog.error(pkgCtxId, myUnit, SQLERRM);
						END;	
					
					END LOOP;
				  ELSE
					slog.debug(pkgCtxId, myUnit, 'Notification for rejected tranpay already sent');
				  END IF;
              END IF;
                          
            ELSE
              slog.debug(pkgCtxId, myUnit, 'Mail notification for rejected tranpay not enabled!');
              
            END IF;

	    
              DECLARE
                vTopUpNotificationReceiver VARCHAR2(400);
                vQRPayNotificationReceiver VARCHAR2(400);
              BEGIN
			  
			    vTopUpNotificationReceiver := sspkg.readVchar('/Customization/TopUp/notificationEMailAddress');
                vQRPayNotificationReceiver := sspkg.readVchar('/Customization/QRPay/notificationEMailAddress');
				
                SELECT extref_id, extref_name
                INTO vExtRefId, vExtRefName
                FROM tranpay_extref
                WHERE tranpays_id = cg$rec.id;

                slog.debug(pkgCtxId, myUnit, 'ExtRefName :'|| vExtRefName);
                slog.debug(pkgCtxId, myUnit, 'Notification receiver address :'|| vTopUpNotificationReceiver);

                IF vExtRefName = common_pck.cRTI_TOPUP AND vTopUpNotificationReceiver IS NOT NULL THEN

                  slog.debug(pkgCtxId, myUnit, 'Send email notification for tranpay associated with external service');
                  slog.debug(pkgCtxId, myUnit, 'Prepare subject');
                  v_subject := sspkg.readVchar('/Customization/TopUp/notificationEMailSubject');

                  slog.debug(pkgCtxId, myUnit, 'Prepare body');
                  v_body := mlang.trans('bs', '/Customization/TopUp/notificationEMailBody', cg$rec.id, cg$rec.execute_at, cg$rec.tranval, cg$rec.tranval_currency_id, cg$rec.status_message, vExtRefId);

  				
                  slog.debug(pkgCtxId, myUnit, 'Send mail to ' || vTopUpNotificationReceiver);
				  
				  IF NOT isNotificationSent(cg$rec.id, cg$rec.req_type_id, common_pck.cTOPUP_SENT_NOTIFICATION) THEN
					BEGIN
						mcore.send_mail(
						fromAddr => v_from_address,
						toAddr => vTopUpNotificationReceiver,
						subject => v_subject,
						bodyMsg => v_body,
						MC_ID => channelId);
						slog.debug(pkgCtxId, myUnit, 'Notification sent');
						setTranpayDetailVCharData(pTranpayId => cg$rec.id, pReqTypeId => cg$rec.req_type_id, pAttribId => common_pck.cTOPUP_SENT_NOTIFICATION, pDataVChar => TO_CHAR(SYSDATE, common_pck.cDATETIME_MASK));
					EXCEPTION
						WHEN OTHERS THEN
						slog.error(pkgCtxId, myUnit, 'Unable to send additional email notification for rejected tranpay associated with external service (' || common_pck.cRTI_TOPUP || ') :' || cg$rec.id || ':' || vTopUpNotificationReceiver);
						slog.error(pkgCtxId, myUnit, SQLERRM);
					END;
				  ELSE 
				  		slog.debug(pkgCtxId, myUnit, 'Notification for TOPUP already sent');
				  END IF;

                ELSIF vExtRefName = common_pck.cAPP_QRPay AND vQRPayNotificationReceiver IS NOT NULL THEN

                  slog.debug(pkgCtxId, myUnit, 'Send email notification for tranpay associated with external service');
                  slog.debug(pkgCtxId, myUnit, 'Prepare subject');
                  v_subject := sspkg.readVchar('/Customization/QRPay/notificationEMailSubject');

                  slog.debug(pkgCtxId, myUnit, 'Prepare body');
                  v_body := mlang.trans('bs', '/Customization/QRPay/notificationEMailBody', cg$rec.id, common_pck.formatDate(cg$rec.execute_at), common_pck.formatMoney(cg$rec.tranval), cg$rec.tranval_currency_id, cg$rec.status_message, vExtRefId);

				 
                  slog.debug(pkgCtxId, myUnit, 'Send mail to ' || vQRPayNotificationReceiver);
				  IF NOT isNotificationSent(cg$rec.id, cg$rec.req_type_id, common_pck.cQRPay_SENT_NOTIFICATION) THEN
					BEGIN
						mcore.send_mail(
						fromAddr => v_from_address,
						toAddr => vQRPayNotificationReceiver,
						subject => v_subject,
						bodyMsg => v_body,
						MC_ID => channelId);
						slog.debug(pkgCtxId, myUnit, 'Notification sent');
						setTranpayDetailVCharData(pTranpayId => cg$rec.id, pReqTypeId => cg$rec.req_type_id, pAttribId => common_pck.cQRPay_SENT_NOTIFICATION, pDataVChar => TO_CHAR(SYSDATE, common_pck.cDATETIME_MASK));
					EXCEPTION
						WHEN OTHERS THEN
                      slog.error(pkgCtxId, myUnit, 'Unable to send additional email notification for rejected tranpay associated with external service (' || common_pck.cAPP_QRPay || ') :' || cg$rec.id || ':' || vQRPayNotificationReceiver);
                      slog.error(pkgCtxId, myUnit, SQLERRM);
					END;
				 ELSE 
				 	 slog.debug(pkgCtxId, myUnit, 'Notification for QRPay already sent');
				 END IF;
				 

                ELSIF vExtRefName = common_pck.cAPP_ME2YOU AND sspkg.readbool(pkgCtxId || '/Notifications/sendBRMe2YouNotification') THEN

                	DECLARE
                		CURSOR cSenderData IS
				            SELECT c.id, c.gsm
				            FROM mcauth.client c
				            JOIN mcore.signatures sn ON (c.id = sn.user_id)
				            JOIN mcore.signers s ON (s.signature_id = sn.id)
				            WHERE s.tranpay_id = cg$rec.id;

						vSenderData cSenderData%ROWTYPE;
					    vMessage VARCHAR2(1000);
						vChannelId VARCHAR2(100);
  						vFromAddr VARCHAR2(100);
  						vToAddr VARCHAR2(100);
  						res integer;
					BEGIN
						slog.debug(pkgCtxId, myUnit, 'Sending sms notification for tranpay associated with external service');
						
						vChannelId := sspkg.readvchar('/Core/Auth/Plugin/SMSOTP/MsgChannelId');
  						vFromAddr := sspkg.readvchar('/Core/Auth/Plugin/SMSOTP/FromAddr');
  						vToAddr := sspkg.readvchar('/Core/Auth/Plugin/SMSOTP/ToAddr');

						OPEN cSenderData;
              			FETCH cSenderData INTO vSenderData;
              			CLOSE cSenderData;

						IF vSenderData.gsm IS NULL THEN
					     sspkg.raiseError('/Core/Auth/err/PluginError', 'Client "'||vSenderData.id||'" has no gsm phone configured.', pkgCtxId, myUnit);
						END IF;

						IF NOT isNotificationSent(cg$rec.id, cg$rec.req_type_id, common_pck.cME2YOU_SENT_NOTIF_SENDER) THEN
				 
						vMessage := mlang.trans('bs', pkgCtxId || '/Notifications/Message_Me2YouRejected', cg$rec.id, common_pck.formatDateTime(cg$rec.execute_at), common_pck.formatMoney(cg$rec.tranval), cg$rec.tranval_currency_id, cg$rec.status_message);

						res := msging.send(
							fromAddr=>vFromAddr, 
							toAddr=>vToAddr, 
							subject=>vSenderData.gsm, 
							bodyMsg=> vMessage, 
							MC_ID=>vChannelId, 
							toExtUser=>vSenderData.id);
							setTranpayDetailVCharData(pTranpayId => cg$rec.id, pReqTypeId => cg$rec.req_type_id, pAttribId => common_pck.cME2YOU_SENT_NOTIF_SENDER, pDataVChar => TO_CHAR(SYSDATE, common_pck.cDATETIME_MASK));
						ELSE						
							slog.debug(pkgCtxId, myUnit, 'SMS already sent to sender');
						END IF;

					EXCEPTION
						WHEN OTHERS THEN
							slog.error(pkgCtxId, myUnit, 'Unable to send additional sms notification for rejected tranpay associated with external service (' || common_pck.cAPP_ME2YOU || ') :' || cg$rec.id || ':' || vSenderData.gsm);
                      		slog.error(pkgCtxId, myUnit, SQLERRM);
					END;

                END IF;
              EXCEPTION
                WHEN no_data_found THEN
                  slog.debug(pkgCtxId, myUnit, 'No ExtRef found! Don''t send additional email');
              END;

        ELSIF cg$rec.status = common_pck.cTRPSTS_BA THEN

            IF sspkg.readbool(pkgCtxId || '/Notifications/sendBATranpayNotification') THEN

              DECLARE
                vEmailAddress VARCHAR2(400 CHAR);
                vSubject VARCHAR2(4000);
                vBodyMsg VARCHAR2(4000);
                vPublicPaymentReviewURL VARCHAR2(4000);
              BEGIN
                SELECT data_vchar
                INTO vEmailAddress
                FROM tranpay_details
                WHERE tranpay_id = cg$rec.id AND
                      attrib_id = 'NOTIFY_EMAIL' AND
                      req_type_id = cg$rec.req_type_id;

                IF vEmailAddress IS NULL THEN  --> ako nema vrijednosti u tom detalju
                  RAISE no_data_found;
                END IF;


                vSubject := mlang.trans('bs', pkgCtxId || '/Notifications/BATranpayEMailSubject');
                vPublicPaymentReviewURL := sspkg.readVchar('/Core/Main/TranPays/PublicPaymentReviewURL');
                vBodyMsg := mlang.trans('bs', pkgCtxId || '/Notifications/BATranpayEMailBody', cg$rec.id, cg$rec.execute_at, cg$rec.tranval, cg$rec.tranval_currency_id, vPublicPaymentReviewURL, cg$rec.ref_code);
				

				IF NOT isNotificationSent(cg$rec.id, cg$rec.req_type_id, common_pck.cTRPAY_ATT_SENT_NOTIFICATION) THEN
					BEGIN
					send_mail(
					fromAddr => v_from_address,
					toAddr => vEmailAddress,
					subject => vSubject,
					bodyMsg => vBodyMsg,
					MC_ID => channelId,
					sendAfter => SYSDATE);

					slog.debug(pkgCtxId, myUnit, 'Notification sent');
					setTranpayDetailVCharData(pTranpayId => cg$rec.id, pReqTypeId => cg$rec.req_type_id, pAttribId => common_pck.cTRPAY_ATT_SENT_NOTIFICATION, pDataVChar => TO_CHAR(SYSDATE, common_pck.cDATETIME_MASK));
					EXCEPTION
						WHEN OTHERS THEN
                      slog.error(pkgCtxId, myUnit, 'Unable to send additional email notification for approved tranpay ' || cg$rec.id);
                      slog.error(pkgCtxId, myUnit, SQLERRM);
					END;
				ELSE 
					slog.debug(pkgCtxId, myUnit, 'Notification already sent');
				END IF;
				
					
              EXCEPTION
                WHEN no_data_found THEN
                  slog.debug(pkgCtxId, myUnit, 'No email for notification defined! Don''t send approved tranpay notification email for tranpay ' || cg$rec.id);
                WHEN OTHERS THEN
                  slog.error(pkgCtxId, myUnit, SQLERRM);
              END;

	 	ELSE
              slog.debug(pkgCtxId, myUnit, 'Mail notification for approved tranpay not enabled!');
        END IF;

  

	 BEGIN
                  SELECT extref_id, extref_name
                  INTO vExtRefId, vExtRefName
                  FROM tranpay_extref
                  WHERE tranpays_id = cg$rec.id;
                EXCEPTION
                    WHEN no_data_found THEN
                        vExtRefId := NULL;
                        vExtRefName := NULL;
                END;

              slog.debug(pkgCtxId, myUnit, 'ExtRefName :'|| vExtRefName);

              <<FastPay>> 
              IF vExtRefName = common_pck.cAPP_ME2YOU AND sspkg.readbool(pkgCtxId || '/Notifications/sendBAMe2YouNotification') THEN

	            DECLARE
	              CURSOR cSenderData IS
				          SELECT c.id, c.gsm, eu.first_name || ' ' || eu.last_name full_name
				            FROM mcauth.client c
				            JOIN mcore.signatures sn ON (c.id = sn.user_id)
				            JOIN mcore.signers s ON (s.signature_id = sn.id)
				            JOIN mcore.end_users eu ON (eu.id = c.id)
				            WHERE s.tranpay_id = cg$rec.id;

				  vSenderData cSenderData%ROWTYPE;
			      vMessage VARCHAR2(1000);
				  vChannelId VARCHAR2(100);
				  vFromAddr VARCHAR2(100);
				  vToAddr VARCHAR2(100);
				  res integer;
			    BEGIN
				  slog.debug(pkgCtxId, myUnit, 'Sending sms notification for tranpay associated with external service');
				  
				  vChannelId := sspkg.readvchar('/Core/Auth/Plugin/SMSOTP/MsgChannelId');
				  vFromAddr := sspkg.readvchar('/Core/Auth/Plugin/SMSOTP/FromAddr');
				  vToAddr := sspkg.readvchar('/Core/Auth/Plugin/SMSOTP/ToAddr');

				  OPEN cSenderData;
	          	  FETCH cSenderData INTO vSenderData;
	          	  CLOSE cSenderData;

				  IF vSenderData.gsm IS NOT NULL THEN
					slog.debug(pkgCtxId, myUnit, 'sending sms to sender...');
					
					IF NOT isNotificationSent(cg$rec.id, cg$rec.req_type_id, common_pck.cME2YOU_SENT_NOTIF_SENDER) THEN	
					vMessage := mlang.trans('bs', pkgCtxId || '/Notifications/Message_Me2YouAccepted', cg$rec.id, common_pck.formatDateTime(cg$rec.execute_at), common_pck.formatMoney(cg$rec.tranval), cg$rec.tranval_currency_id);
				    res := msging.send(fromAddr=>vFromAddr, toAddr=>vToAddr, subject=>vSenderData.gsm, bodyMsg=> vMessage, MC_ID=>vChannelId, toExtUser=>vSenderData.id);
					
					setTranpayDetailVCharData(pTranpayId => cg$rec.id, pReqTypeId => cg$rec.req_type_id, pAttribId => common_pck.cME2YOU_SENT_NOTIF_SENDER, pDataVChar => TO_CHAR(SYSDATE, common_pck.cDATETIME_MASK));
					ELSE
						slog.debug(pkgCtxId, myUnit, 'sms to sender already sent');
					END IF;

				  END IF;

				  slog.debug(pkgCtxId, myUnit, 'sending sms to receiver...');
				 
					
				  IF NOT isNotificationSent(cg$rec.id, cg$rec.req_type_id, common_pck.cME2YOU_SENT_NOTIF_RECEIVER) THEN
				  vMessage := mlang.trans('bs', pkgCtxId || '/Notifications/Message_Me2YouPayment', cg$rec.id, common_pck.formatDateTime(cg$rec.execute_at), common_pck.formatMoney(cg$rec.tranval), cg$rec.tranval_currency_id, vSenderData.full_name, cg$rec.description);
				  res := msging.send(fromAddr=>vFromAddr, toAddr=>vToAddr, subject=>vExtRefId, bodyMsg=> vMessage, MC_ID=>vChannelId, toExtUser=>vSenderData.id);
				  
				  setTranpayDetailVCharData(pTranpayId => cg$rec.id, pReqTypeId => cg$rec.req_type_id, pAttribId => common_pck.cME2YOU_SENT_NOTIF_RECEIVER, pDataVChar => TO_CHAR(SYSDATE, common_pck.cDATETIME_MASK));
				  ELSE	
					slog.debug(pkgCtxId, myUnit, 'sms to receiver already sent');
				  END IF;
				  
			    EXCEPTION
				  WHEN OTHERS THEN
					slog.error(pkgCtxId, myUnit, 'Unable to send additional sms notification for rejected tranpay associated with external service');
	                slog.error(pkgCtxId, myUnit, SQLERRM);
				  END;

              END IF;

	END IF;


    END sendTranpayNotification;

	PROCEDURE sendTranpayNotification(pTranpayId IN tranpays.id%TYPE)
	IS
		CURSOR cTranpays IS
			SELECT req_type_id, status, status_message, execute_at, tranval, tranval_currency_id
			FROM mcore.tranpays t
			WHERE t.id = pTranpayId;
		vTranpayData cTranpays%ROWTYPE;

		myUnit CONSTANT VARCHAR2(24) := 'sendTranpayNotification2';
    	cg$t_rec cg$TRANPAYS.cg$row_type;
	BEGIN
		slog.debug(pkgCtxId, myUnit, pTranpayId);

		IF NOT sspkg.readbool(pkgCtxId || '/Notifications/enabled') THEN
			slog.debug(pkgCtxId, myUnit, 'Mail notification not enabled!');
			RETURN;
		END IF;

		OPEN cTranpays;
		slog.debug(pkgCtxId, myUnit, 'Tranpays cursor opened');
		FETCH cTranpays INTO vTranpayData;
		slog.debug(pkgCtxId, myUnit, 'Tranpays data fetched');
		IF cTranpays%NOTFOUND THEN
			slog.debug(pkgCtxId, myUnit, 'No tranpay found!');
			CLOSE cTranpays;
			RETURN;
		END IF;
		CLOSE cTranpays;

    	cg$t_rec.id := pTranpayId;
    	cg$TRANPAYS.slct(cg$sel_rec => cg$t_rec);

		sendTranpayNotification(cg$t_rec);

	END sendTranpayNotification;

  FUNCTION UploadTranpays(pTranpayList obj$tranpay_table)
	RETURN tranpay_import_result_list IS
		myUnit CONSTANT VARCHAR2(14) := 'UploadTranpays';
		vTranpayId tranpays.id%TYPE;
		vImportResultList tranpay_import_result_list := tranpay_import_result_list();
		vImportResult tranpay_import_result;

		o$tranpay obj$tranpay;
		o$tranpay_details obj$tranpay_details_table;
		o$tranpay_detail obj$tranpay_detail;
	BEGIN
		slog.debug(pkgCtxId, myUnit);

		IF pTranpayList IS NOT NULL THEN
      IF pTranpayList.COUNT = 0 THEN
        slog.debug(pkgCtxId, myUnit, 'Collection empty ' || pTranpayList.COUNT);
        RETURN vImportResultList;
      END IF;
    ELSE
        slog.debug(pkgCtxId, myUnit, 'Collection empty');
        RETURN vImportResultList;
		END IF;

    <<LoopTranpayList>>
		FOR t IN pTranpayList.FIRST..pTranpayList.LAST LOOP
			SAVEPOINT tranpay_begin;

			slog.debug(pkgCtxId, myUnit, 'Processing ' || t || ' record');
			slog.debug(pkgCtxId, myUnit, 'Fetch o$tranpay object ...');
			o$tranpay := pTranpayList(t);

      IF o$tranpay IS NULL THEN
        slog.debug(pkgCtxId, myUnit, 'Empty object. Skip');
        CONTINUE;
      END IF;

			slog.debug(pkgCtxId, myUnit, 'Instatiated result for current record (' || o$tranpay.id || ')');
			vImportResult := tranpay_import_result(o$tranpay.id);

      slog.debug(pkgCtxId, myUnit, 'Fetch tranpay details table ...');
      o$tranpay_details := o$tranpay.details;

      IF o$tranpay_details IS NOT NULL THEN
          IF o$tranpay_details.COUNT = 0 THEN
            slog.error(pkgCtxId, myUnit, 'Details collection for ' || o$tranpay.id || '. tranpay doesn''t contain any elements');
            ROLLBACK TO SAVEPOINT tranpay_begin;
            vImportResult.status := 'GRESKA';
            vImportResult.message := 'Missing order details for order!';
          END IF;
      ELSE
            slog.error(pkgCtxId, myUnit, 'Empty details collection for ' || o$tranpay.id || '. tranpay');
            ROLLBACK TO SAVEPOINT tranpay_begin;
            vImportResult.status := 'GRESKA';
            vImportResult.message := 'Missing order details for order!';
      END IF;

      IF vImportResult.status IS NULL THEN

        <<TranpayBlock>>
        BEGIN
          slog.debug(pkgCtxId, myUnit, 'Create new tranpay ...');
          vTranpayId := CreateNewTranPay(
                  pRequestTypeId => o$tranpay.req_type_id,
                  pDescription => o$tranpay.description,
                  pInternalDescription => o$tranpay.internal_description,
                  pTranval => o$tranpay.tranval,
                  pTranvalCurrencyId => o$tranpay.tranval_currency_id,
                  pTranpayGroupId => o$tranpay.tranpay_group_id,
                  pAccountId => o$tranpay.account_id,
                  pChartOfAccountsId => o$tranpay.chart_of_accounts_id,
                  pScheduleId => o$tranpay.schedule_id,
                  pTestRecord => o$tranpay.test_record,
                  pExtRef => o$tranpay.extref);

          slog.debug(pkgCtxId, myUnit, 'New tranpay got ID ' || vTranpayId);


          <<TranpayDetailsBlock>>
          BEGIN

            <<LoopTranpayDetails>>
            FOR td IN o$tranpay_details.FIRST..o$tranpay_details.LAST LOOP
              slog.debug(pkgCtxId, myUnit, 'Processing ' || td || ' detail record');
              slog.debug(pkgCtxId, myUnit, 'Fetch o$tranpay_detail object');

              o$tranpay_detail := o$tranpay_details(td);
              IF o$tranpay_detail IS NULL THEN
                slog.debug(pkgCtxId, myUnit, 'Empty detail object. Skip');
                CONTINUE;
              END IF;

              slog.debug(pkgCtxId, myUnit, 'Append tranpay detail');
              AppendTranPayDetail(
                pTranpayId        => vTranpayId,
                pRequestTypeId    => o$tranpay_detail.req_type_id,
                pAttribId         => o$tranpay_detail.attrib_id,
                pDescription      => NULL,
                pDataVCHAR        => o$tranpay_detail.data_vchar,
                pDataBLOB         => NULL);

            END LOOP LoopTranpayDetails;
          EXCEPTION
            WHEN sspkg.sysException THEN
              slog.error(pkgCtxId, myUnit, 'Error when appending tranpay detail for ' || o$tranpay.id || '. tranpay. Failed attrib ' || o$tranpay_detail.attrib_id || ':' || o$tranpay_detail.data_vchar);
              ROLLBACK TO SAVEPOINT tranpay_begin;
              vImportResult.status := 'GRESKA';
              vImportResult.message := NVL(sspkg.getErrorUserMessage, sspkg.getErrorMessage);

            WHEN OTHERS THEN
              slog.error(pkgCtxId, myUnit, 'Error when appending tranpay detail for ' || o$tranpay.id || '. tranpay. Failed attrib ' || o$tranpay_detail.attrib_id || ':' || o$tranpay_detail.data_vchar);
              ROLLBACK TO SAVEPOINT tranpay_begin;
              slog.error(pkgCtxId, myUnit, SQLERRM);
              vImportResult.status := 'GRESKA';
              vImportResult.message := SQLERRM;

          END TranpayDetailsBlock;

          IF vImportResult.status IS NULL THEN
            slog.debug(pkgCtxId, myUnit, 'Order ' || o$tranpay.id || '. successfully imported as tranpay ' || vTranpayId);
			vImportResult.tranpay_id := vTranpayId;
            vImportResult.status := 'OK';
            vImportResult.message := NULL;
          END IF;

        EXCEPTION
          WHEN sspkg.sysException THEN
            slog.error(pkgCtxId, myUnit, 'Error when creating ' || o$tranpay.id || '. tranpay');
            ROLLBACK TO SAVEPOINT tranpay_begin;
            vImportResult.status := 'GRESKA';
            vImportResult.message := NVL(sspkg.getErrorUserMessage, sspkg.getErrorMessage);

          WHEN OTHERS THEN
            slog.error(pkgCtxId, myUnit, 'Error when creating ' || o$tranpay.id || '. tranpay');
            ROLLBACK TO SAVEPOINT tranpay_begin;
            slog.error(pkgCtxId, myUnit, SQLERRM);
            vImportResult.status := 'GRESKA';
            vImportResult.message := SQLERRM;
        END TranpayBlock;
      END IF;

			slog.debug(pkgCtxId, myUnit, 'Extending result list');
			vImportResultList.EXTEND;

			slog.debug(pkgCtxId, myUnit, 'Append import result on result list');
			vImportResultList(vImportResultList.LAST) := vImportResult;

			slog.debug(pkgCtxId, myUnit, 'Processing tranpay with row# ' || o$tranpay.id || ' finished');
		END LOOP LoopTranpayList;

    RETURN vImportResultList;

	END UploadTranpays;

	-- PROCEDURE RegisterSplunkNotification(pTranpayId IN tranpays.id%TYPE, pAccountId IN bank_accounts.id%TYPE, pTranpayAmount IN NUMBER, pTranpayCurrency IN VARCHAR2, pActionId IN VARCHAR2) IS
	PROCEDURE RegisterSplunkNotification(pTranpayId IN tranpays.id%TYPE, pActionId IN VARCHAR2) IS
		myUnit CONSTANT VARCHAR2(26) := 'RegisterSplunkNotification';
		vAccountId tranpays.account_id%TYPE;
		vAccountMasterId bank_accounts.ph4%TYPE;
		vAccountOwnerIdAffected bank_accounts.account_owner_id%TYPE;
		vAccountOwnerId bank_accounts.account_owner_id%TYPE;
		vTranval tranpays.tranval%TYPE;
		vTranvalCurrencyId tranpays.tranval_currency_id%TYPE;
		vNumTranvalCurrId currencies.numeric_id%TYPE;
		vBeneficiaryAccount tranpay_details.data_vchar%TYPE := NULL;
		vReqTypeId tranpays.req_type_id%TYPE;
		vUserId tranpays.user_id%TYPE;
		vBankAccountTypeId bank_accounts.bank_account_type_id%TYPE;
		vMessage strmadmin.splunk_serviceaccessdata := strmadmin.splunk_serviceaccessdata();
		vClientActionsDataNtf strmadmin.splunk_clientactionsdata := strmadmin.splunk_clientactionsdata();
	BEGIN
		slog.debug(pkgCtxId, myUnit, pTranpayId || ':' || pActionId);
		IF sspkg.ReadBool(common_pck.cSPK_HEC_INTERFACE_Context || '/SERVICE_ACCESS_DATA/enabled') THEN
			slog.debug(pkgCtxId, myUnit, 'SPLUNK Notification enabled');
			BEGIN
				SELECT account_id, tranval, tranval_currency_id, req_type_id
				INTO vAccountId, vTranval, vTranvalCurrencyId, vReqTypeId
				FROM tranpays
				WHERE id = pTranpayId;

				slog.debug(pkgCtxId, myUnit, 'Tranpay data :' || vAccountId || ':' || vTranval || ':' || vTranvalCurrencyId);
			EXCEPTION
				WHEN no_data_found THEN
				slog.error(pkgCtxId, myUnit, cERR_InvalidTranpay, pTranpayId || ':' || pActionId);
                RETURN;
			END;

			IF vReqTypeId NOT IN (common_pck.cRTI_UPP, common_pck.cRTI_PPI) THEN
				slog.debug(pkgCtxId, myUnit, 'Unsupported tranpay request type ' || vReqTypeId || ' for splunk notification');
				RETURN;
			END IF;

			BEGIN

				SELECT ba.ph4, ba.account_owner_id
				INTO vAccountMasterId, vAccountOwnerId
				FROM bank_accounts ba
				WHERE ba.id = vAccountId;

				slog.debug(pkgCtxId, myUnit, 'Account data :' || vAccountMasterId || ':' || vAccountOwnerId);
			EXCEPTION
				WHEN no_data_found THEN
					slog.error(pkgCtxId, myUnit, common_pck.cERR_UnknownAccount, pTranpayId || ':' || pActionId || ':' || vAccountId);
					RETURN;
			END;

			BEGIN
				SELECT data_vchar
				  INTO vBeneficiaryAccount
				  FROM tranpay_details
				 WHERE tranpay_id = pTranpayId
				   AND attrib_id = common_pck.cTRANPAY_ATT_RACUN_PRIMAOCA;

				slog.debug(pkgCtxId, myUnit, 'Beneficiary account :' || vBeneficiaryAccount);
			EXCEPTION
				WHEN no_data_found THEN
					slog.error(pkgCtxId, myUnit, cERR_InvalidTranpayAttribute, pTranpayId || ':' || pActionId);
					RETURN;
			END;

			/*
TYPE mcore.splunk_serviceaccessdata AS OBJECT
( datetime date,
  service_type_id VARCHAR2(40 CHAR),
  account_id VARCHAR2(40 CHAR),
  acc_owner_id VARCHAR2(40 CHAR),
  transaction_id NUMBER(40 CHAR),
  tranpay_target_account_id VARCHAR2(60),
  tranpay_amount NUMBER,
  client_ip VARCHAR2(40 CHAR),
  user_id NUMBER,
  action_id VARCHAR2(40 CHAR),
			*/

			vMessage.datetime := sysdate;
			vMessage.service_type_id := mcauth.auth.getApplicationId;
			vMessage.account_id := vAccountMasterId;
			vMessage.acc_owner_id := vAccountOwnerId;
			vMessage.transaction_id := pTranpayId;
			vMessage.tranpay_target_account_id := vBeneficiaryAccount;
			vMessage.tranpay_amount := vTranval;
			vMessage.tranpay_currency := vTranvalCurrencyId;
			vMessage.client_ip := mcauth.auth.getIPAddress;
			vMessage.user_id := mcauth.auth.getClientId;
			vMessage.action_id := pActionId;

			strmadmin.EnqueueSPLUNKSrvcAccsNtfMsg(vMessage);
			slog.debug(pkgCtxId, myUnit, 'Notification for tranpay ' || pTranpayId);
		END IF;
		
		-- akcija za zakazivanje trajnog naloga od strane klijenta, za novi splunk je bitna samo akcija zakazivanja trajnog naloga
		IF sspkg.ReadBool(common_pck.cSPK_HEC_INTERFACE_Context || '/CLIENT_ACTIONS_DATA/enabled') and pActionId = common_pck.cACT_StandingOrder THEN
			slog.debug(pkgCtxId, myUnit, 'SPLUNK client action ntf enabled');
			BEGIN
				SELECT t.account_id, c.numeric_id, t.req_type_id, t.user_id
				INTO vAccountId, vNumTranvalCurrId, vReqTypeId, vUserId
				FROM tranpays t JOIN currencies c on c.id = t.tranval_currency_id
				WHERE t.id = pTranpayId;

				slog.debug(pkgCtxId, myUnit, 'Tranpay data :' || vAccountId || ':' || vTranval || ':' || vTranvalCurrencyId);
			EXCEPTION
				WHEN no_data_found THEN
				slog.error(pkgCtxId, myUnit, cERR_InvalidTranpay, pTranpayId || ':' || pActionId);
                RETURN;
			END;

			IF vReqTypeId NOT IN (common_pck.cRTI_UPP, common_pck.cRTI_PPI) THEN
				slog.debug(pkgCtxId, myUnit, 'Unsupported tranpay request type ' || vReqTypeId || ' for splunk notification');
				RETURN;
			END IF;

			BEGIN

				SELECT ba.ph4, ba.bank_account_type_id, ba.account_owner_id
				INTO vAccountMasterId, vBankAccountTypeId, vAccountOwnerIdAffected
				FROM bank_accounts ba
				WHERE ba.id = vAccountId;

				slog.debug(pkgCtxId, myUnit, 'Account data :' || vAccountId);
			EXCEPTION
				WHEN no_data_found THEN
					slog.error(pkgCtxId, myUnit, common_pck.cERR_UnknownAccount, pTranpayId || ':' || pActionId || ':' || vAccountId);
					RETURN;
			END;


			/*
 TYPE strmadmin.splunk_clientactionsdata AS OBJECT
( id VARCHAR2(40 CHAR),
  client_id VARCHAR2 (20 CHAR),
  acc_owner_id VARCHAR2 (20 CHAR),
  action_date DATE,
  ip_address VARCHAR2(15 CHAR),
  session_id VARCHAR2(40 CHAR),
  action_type VARCHAR2(20 CHAR),
  service_type VARCHAR2(40 CHAR),
  account_id VARCHAR2(20 CHAR),
  account_currency_id NUMBER,
  CONSTRUCTOR FUNCTION splunk_clientactionsdata RETURN SELF AS RESULT
)
			*/
			IF vBankAccountTypeId in ('21','80','81') THEN
				-- ukoliko se radi o merkat_full licenci gdje client FL (koji ima upisanog licnog komitenta) moze imati prava i po racunu nekog drugog komitenta, za -- client_id se salje sifra komitenta FL (koji ima prijavu na Elbu), a za account_id_affected se salje komitent po cijem je racunu kreiran nalog
				SELECT eu.ph1
                INTO vAccountOwnerId
                FROM mcore.end_users eu
                WHERE eu.id = vUserId;
						
				vClientActionsDataNtf.acc_owner_id := vAccountOwnerId;
				
				IF vAccountOwnerId IS NULL THEN
					vClientActionsDataNtf.acc_owner_id := vAccountOwnerIdAffected;
				END IF;
			
				vClientActionsDataNtf.id := strmadmin.splunk_client_actions_seq.nextval;
				
				vClientActionsDataNtf.action_date := SYSDATE;	
				
				vClientActionsDataNtf.ip_address := mcauth.auth.getIPAddress();
				
				vClientActionsDataNtf.session_id := mcsm.getSessionId();
				
				vClientActionsDataNtf.action_type := pActionId;
				
				vClientActionsDataNtf.client_id := vUserId;
								
				vClientActionsDataNtf.service_type := 'mobile';	
				
				IF mcauth.auth.getApplicationId() = common_pck.cAPP_THIN THEN	
				
					vClientActionsDataNtf.service_type := 'internet';
					
				END IF;
				
				vClientActionsDataNtf.account_id := vAccountMasterId;
				
				vClientActionsDataNtf.account_currency_id := vNumTranvalCurrId;
				
				vClientActionsDataNtf.acc_owner_id_affected := vAccountOwnerIdAffected;
						
				strmadmin.EnqSPLUNKClientActionsNtfMsg(pMessage => vClientActionsDataNtf);
				
				slog.debug(pkgCtxId, myUnit, 'SPLUNK client action ntf for tranpay: ' || pTranpayId);
			END IF;
		END IF;
	EXCEPTION


		WHEN OTHERS THEN
			slog.error(pkgCtxId, myUnit, sqlcode||':'||sqlerrm);
	END RegisterSplunkNotification;

	PROCEDURE MarkOrderAsProcessing (pOrderId IN NUMBER) IS
		myunit CONSTANT VARCHAR2(30) := 'MarkOrderAsProcessing';
	BEGIN
		slog.debug(pkgCtxId, myunit, pOrderId);
		UPDATE tranpays
			SET status = mcore.common_pck.cTRPSTS_BP,
			    date_processed = SYSDATE
			WHERE id = pOrderId
			  AND status = mcore.common_pck.cTRPSTS_US;
	END MarkOrderAsProcessing;

	PROCEDURE MarkOrderAsAccepted (pOrderId IN NUMBER, pCost IN NUMBER, pCostCurrencyId IN VARCHAR2, pStatusMessage IN VARCHAR2, pDateOfClearing IN DATE) IS
		myunit CONSTANT VARCHAR2(30) := 'MarkOrderAsAccepted';
		vPom PLS_INTEGER;
		v_cg_result BOOLEAN;
		cg$rec     cg$TRANPAYS.cg$row_type;
		cg$ind     cg$TRANPAYS.cg$ind_type;
	BEGIN
		slog.debug(pkgCtxId, myunit, pOrderId || ':' || pCost || ':' || pCostCurrencyId);

		cg$rec.id := pOrderId;
		
		BEGIN
			cg$tranpays.slct(cg$rec);
		EXCEPTION
			WHEN cg$errors.cg$error THEN
				-- Retrieve error message to determine error cause !

				v_cg_result := cg$errors.pop(msg => MSG
					,error => ERROR
					,msg_type => MSG_TYPE
					,msgid => MSGID
					,loc => LOC);
				slog.error(pkgCtxId, myunit, 'Updating tranpay cg$error:' || pOrderId || ':' || MSG || ':' || ERROR || ':' || MSG_TYPE || ':' || MSGID || ':' || LOC);
				RETURN;
			WHEN OTHERS THEN
				slog.error(pkgCtxId, myunit, 'Updating tranpay :' || pOrderId || ':' || sqlcode|| ':' || sqlerrm);
				RETURN;
		END;
		
		BEGIN
			IF cg$rec.status <> mcore.common_pck.cTRPSTS_BA THEN
			
				cg$rec.status := mcore.common_pck.cTRPSTS_BA;
				cg$rec.status_message := pStatusMessage;
				cg$rec.cost := pCost;
				cg$rec.cost_currency_id := pCostCurrencyId;
				cg$rec.date_processed := sysdate;
				
				cg$ind.status := TRUE;
				cg$ind.status_message := TRUE;
				cg$ind.cost := TRUE;
				cg$ind.cost_currency_id := TRUE;
				cg$ind.date_processed := TRUE;
			
				cg$TRANPAYS.upd(cg$rec => cg$rec, cg$ind => cg$ind, do_upd => TRUE);

			END IF;
			
		EXCEPTION
			WHEN cg$errors.cg$error THEN
				-- Retrieve error message to determine error cause !

				v_cg_result := cg$errors.pop(msg => MSG
					,error => ERROR
					,msg_type => MSG_TYPE
					,msgid => MSGID
					,loc => LOC);
				slog.error(pkgCtxId, myunit, 'Updating tranpay cg$error:' || pOrderId || ':' || MSG || ':' || ERROR || ':' || MSG_TYPE || ':' || MSGID || ':' || LOC);
				RETURN;
			WHEN OTHERS THEN
				slog.error(pkgCtxId, myunit, 'Updating tranpay :' || pOrderId || ':' || sqlcode|| ':' || sqlerrm);
				RETURN;
		END;

		IF pDateOfClearing IS NOT NULL THEN
			slog.debug(pkgCtxId, myunit, 'DateOfClearing provided');
			
			IF NOT isValidTranpayAttribute(cg$rec.req_type_id, common_pck.cTRANPAY_ATT_CLEARING_DATE) THEN 
				slog.debug(pkgCtxId, myunit, 'DateOfClearing not supported for ' || cg$rec.req_type_id);
				RETURN;
			END IF;

			BEGIN
				slog.debug(pkgCtxId, myunit, 'Insert dateOfClearance');
				INSERT INTO tranpay_details (REQ_TYPE_ID, TRANPAY_ID, ATTRIB_ID, DATA_VCHAR)
				VALUES (cg$rec.req_type_id, pOrderId, common_pck.cTRANPAY_ATT_CLEARING_DATE, TO_CHAR(pDateOfClearing, common_pck.cDATETIME_MASK));
			EXCEPTION
				WHEN dup_val_on_index THEN
					slog.debug(pkgCtxId, myunit, 'dateOfClearance already exists');
					RETURN;
			END;

		END IF;
	END MarkOrderAsAccepted;

	PROCEDURE MarkOrderAsRejected (pOrderId IN NUMBER, pStatusMessage IN VARCHAR2, pStatusCode IN VARCHAR2 DEFAULT NULL) IS
		myunit CONSTANT VARCHAR2(30) := 'MarkOrderAsRejected';
		v_cg_result BOOLEAN;
	BEGIN
		slog.debug(pkgCtxId, myunit, pOrderId);

		UPDATE tranpays
			SET status = mcore.common_pck.cTRPSTS_BR,
				status_code = pStatusCode,
				status_message = pStatusMessage,
				cost = NULL,
				cost_currency_id = NULL,
				date_processed = sysdate
			WHERE id = pOrderId
			AND status <> mcore.common_pck.cTRPSTS_BR;
	EXCEPTION
		WHEN cg$errors.cg$error THEN
			-- Retrieve error message to determine error cause !

			v_cg_result := cg$errors.pop(msg => MSG
					,error => ERROR
					,msg_type => MSG_TYPE
					,msgid => MSGID
					,loc => LOC);
			slog.error(pkgCtxId, myunit, 'Updating tranpay cg$error:' || pOrderId || ':' || MSG || ':' || ERROR || ':' || MSG_TYPE || ':' || MSGID || ':' || LOC);

		WHEN OTHERS THEN
			slog.error(pkgCtxId, myunit, 'Updating tranpay :' || pOrderId || ':' || sqlcode|| ':' || sqlerrm);
	END MarkOrderAsRejected;
	
	-- Internal functions introduced with Feature #495
  PROCEDURE prepSelectedTranpaysUW(pTranpaysSequence IN table_of_integer)
  IS
        myunit CONSTANT VARCHAR2(22) := 'prepSelectedTranpaysUW';
  BEGIN
    slog.debug(pkgCtxId, myUnit);

    IF mcauth.auth.getAccountOwner = '%' THEN
        INSERT INTO tmp$tranpays (id)
        SELECT ut.id tranpay_id
        FROM vw$user_tranpays_uw_vb ut JOIN TABLE(pTranpaysSequence) s ON (ut.id = s.column_value)
		WHERE ut.status = common_pck.cTRPSTS_UW;
    ELSE
        INSERT INTO tmp$tranpays (id)
        SELECT ut.id tranpay_id
        FROM vw$user_tranpays_uw_vb_acc ut JOIN TABLE(pTranpaysSequence) s ON (ut.id = s.column_value)
		WHERE ut.status = common_pck.cTRPSTS_UW;
    END IF;

  END prepSelectedTranpaysUW;

  PROCEDURE prepSelectedTranpaysUD(pTranpaysSequence IN table_of_integer)
  IS
        myunit CONSTANT VARCHAR2(22) := 'prepSelectedTranpaysUD';
  BEGIN
    slog.debug(pkgCtxId, myUnit);

    INSERT INTO tmp$tranpays (id)
    SELECT ut.id tranpay_id
    FROM vw$user_tranpays_ud ut JOIN TABLE(pTranpaysSequence) s ON (ut.id = s.column_value);

  END prepSelectedTranpaysUD;

  PROCEDURE prepSelectedTranpaysUS(pTranpaysSequence IN table_of_integer)
  IS
        myunit CONSTANT VARCHAR2(22) := 'prepSelectedTranpaysUS';
  BEGIN
    slog.debug(pkgCtxId, myUnit);

    INSERT INTO tmp$tranpays (id)
    SELECT ut.id tranpay_id
    FROM vw$user_tranpays_us ut JOIN TABLE(pTranpaysSequence) s ON (ut.id = s.column_value);

  END prepSelectedTranpaysUS;

  PROCEDURE prepSelectedTranpaysBX(pStatus IN tranpays.status%TYPE, pTranpaysSequence IN table_of_integer)
  IS
        myunit CONSTANT VARCHAR2(22) := 'prepSelectedTranpaysBP';
  BEGIN
    slog.debug(pkgCtxId, myUnit);

    INSERT INTO tmp$tranpays (id)
    SELECT ut.id tranpay_id
    FROM vw$user_tranpays_bx ut JOIN TABLE(pTranpaysSequence) s ON (ut.id = s.column_value)
    WHERE ut.status = pStatus;

  END prepSelectedTranpaysBX;
  
  PROCEDURE prepSelectedTranpaysBA(pStatus IN tranpays.status%TYPE, pTranpaysSequence IN table_of_integer)
  IS
        myunit CONSTANT VARCHAR2(22) := 'prepSelectedTranpaysBP';
  BEGIN
    slog.debug(pkgCtxId, myUnit);

    INSERT INTO tmp$tranpays (id)
    SELECT ut.id tranpay_id
    FROM vw$user_tranpays_ba ut JOIN TABLE(pTranpaysSequence) s ON (ut.id = s.column_value)
    WHERE ut.status = pStatus;

  END prepSelectedTranpaysBA;

  PROCEDURE prepSelectedTranpaysUC(pTranpaysSequence IN table_of_integer)
  IS
        myunit CONSTANT VARCHAR2(22) := 'prepSelectedTranpaysUC';
  BEGIN
    slog.debug(pkgCtxId, myUnit);

    INSERT INTO tmp$tranpays (id)
    SELECT ut.id tranpay_id
    FROM vw$user_tranpays_uc ut JOIN TABLE(pTranpaysSequence) s ON (ut.id = s.column_value);

  END prepSelectedTranpaysUC;
	
	
	FUNCTION getSelectedTranpays(pStatus IN tranpays.status%TYPE, pTranpaysSequence table_of_integer)
    RETURN sys_refcursor IS
        myunit CONSTANT VARCHAR2(19) := 'getSelectedTranpays';
        rez sys_refcursor;
    BEGIN
        slog.debug(pkgCtxId, myUnit, pStatus);
    ROLLBACK;

    CASE pStatus
      WHEN common_pck.cTRPSTS_UW THEN prepSelectedTranpaysUW(pTranpaysSequence => pTranpaysSequence);
      WHEN common_pck.cTRPSTS_UD THEN prepSelectedTranpaysUD(pTranpaysSequence => pTranpaysSequence);
      WHEN common_pck.cTRPSTS_US THEN prepSelectedTranpaysUS(pTranpaysSequence => pTranpaysSequence);
      WHEN common_pck.cTRPSTS_BP THEN prepSelectedTranpaysBX(pStatus => pStatus, pTranpaysSequence => pTranpaysSequence);
      WHEN common_pck.cTRPSTS_BA THEN prepSelectedTranpaysBA(pStatus => pStatus, pTranpaysSequence => pTranpaysSequence);
      WHEN common_pck.cTRPSTS_BR THEN prepSelectedTranpaysBX(pStatus => pStatus, pTranpaysSequence => pTranpaysSequence);
      WHEN common_pck.cTRPSTS_UC THEN prepSelectedTranpaysUC(pTranpaysSequence => pTranpaysSequence);
      ELSE -- Invalid status requested !!
         sspkg.raiseError(cERR_InvalidStatus, null, pkgCtxId, myunit);
    END CASE;

        OPEN rez FOR
        SELECT ID,
               STATUS, common_pck.decodeStatus(STATUS) STATUS_DECODED,
               STATUS_MESSAGE,
               IZNOS,
               RACUN_POSILJAOCA,
               OPIS_PLACANJA, NAZIV_POSILJAOCA, RACUN_PRIMAOCA, NAZIV_PRIMAOCA,
               IZVORNA_VALUTA, ODREDISNA_VALUTA,
               REFERENCA_PLACANJA,
               HITNO, IDPOROBV, VRSTA_UPLATE, VRSTA_PRIHODA, PORPERIOD_OD, PORPERIOD_DO,
               OPCINA, BUDZETSKA_ORGANIZACIJA, POZIV_NA_BROJ, CLEARING_DATE,
               TROSKOVI,
               TROSKOVI_DESC,
               BANKA_ID, BANKA_NAZ, ADRESA, VALUTA_POKRICA, IZNOS_POKRICA, ADRESA2,
               VRIJEME_IZVRSENJA_ALIAS,
               FIKSNO, RACUN_POKRICA, REQ_TYPE_ID,
               DATUM_KREIRANJA,
               DATUM_ZADNJE_IZMJENE,
               DATUM_POTPISA,
               DATUM_OBRADE,
               IZNOS_KUPOVINE, IZVORNA_REFERENCA,
               REF_CODE, OSNOV_PLACANJA
               FROM (
                SELECT t.id,
                t.status, t.status_message,
                t.req_type_id, t.tranval IZNOS,
                  decode(t.req_type_id,
                    mcore.common_pck.cRTI_TRANSFER, ba.ph0 || ' - ' || max (decode(td.attrib_id, 'ACCOUNT_ALIAS', td.data_vchar, NULL)),
                    max (decode(td.attrib_id, 'EXT_ID', td.data_vchar, NULL))) RACUN_POSILJAOCA,
                  t.description OPIS_PLACANJA,
                  t.date_created DATUM_KREIRANJA,
                  t.date_modified DATUM_ZADNJE_IZMJENE,
                  t.date_signed DATUM_POTPISA,
                  t.date_processed DATUM_OBRADE,
                  t.ref_code,
                  decode(t.req_type_id,
                    common_pck.cRTI_UPP, max (decode(td.attrib_id, 'REFERENCA_PLACANJA', td.data_vchar, NULL)),
                    NULL) REFERENCA_PLACANJA,
                  decode(t.req_type_id,
                    common_pck.cRTI_TRANSFER, NULL,
                    max (decode(td.attrib_id, 'NAZIV_POSILJAOCA', td.data_vchar, NULL))) NAZIV_POSILJAOCA,
                  decode(t.req_type_id,
                    common_pck.cRTI_TRANSFER, max (decode(td.attrib_id, 'ACCOUNT_VALUTA', td.data_vchar, NULL)),
                    max (decode(td.attrib_id, 'VALUTA_POKRICA_ALIAS', td.data_vchar, NULL))) IZVORNA_VALUTA,
                  decode(t.req_type_id,
                    common_pck.cRTI_UPP, t.tranval_currency_id,
                    common_pck.cRTI_PPI, max (decode(td.attrib_id, 'VALUTA_NALOGA_ALIAS', td.data_vchar, NULL)),
                    common_pck.cRTI_TRANSFER, max (decode(td.attrib_id, 'RACUN_VALUTA', td.data_vchar, NULL))) ODREDISNA_VALUTA,
                  decode(t.req_type_id,
                    common_pck.cRTI_TRANSFER,
                    max (decode(td.attrib_id, 'RACUN_ALIAS', td.data_vchar, NULL)),
                    max (decode(td.attrib_id, 'RACUN_PRIMAOCA', td.data_vchar, NULL))) RACUN_PRIMAOCA,
                  decode(t.req_type_id,
                    common_pck.cRTI_TRANSFER, NULL,
                    max (decode(td.attrib_id, 'NAZIV_PRIMAOCA', td.data_vchar, NULL))) NAZIV_PRIMAOCA,
                  decode(t.req_type_id,
                    common_pck.cRTI_UPP, max (decode(td.attrib_id, 'HITNO', td.data_vchar, NULL)),
                    NULL) HITNO,
                  decode(t.req_type_id,
                    common_pck.cRTI_UPP, max (decode(td.attrib_id, 'IDPOROBV', td.data_vchar, NULL)),
                    NULL) IDPOROBV,
                  decode(t.req_type_id,
                    common_pck.cRTI_UPP, max (decode(td.attrib_id, 'VRSTA_UPLATE', td.data_vchar, NULL)),
                    NULL) VRSTA_UPLATE,
                  decode(t.req_type_id,
                    common_pck.cRTI_UPP, max (decode(td.attrib_id, 'VRSTA_PRIHODA', td.data_vchar, NULL)),
                    NULL) VRSTA_PRIHODA,
                  decode(t.req_type_id,
                    common_pck.cRTI_UPP, max (decode(td.attrib_id, 'PORPERIOD_OD', td.data_vchar, NULL)),
                    NULL) PORPERIOD_OD,
                  decode(t.req_type_id,
                    common_pck.cRTI_UPP, max (decode(td.attrib_id, 'PORPERIOD_DO', td.data_vchar, NULL)),
                    NULL) PORPERIOD_DO,
                  decode(t.req_type_id,
                    common_pck.cRTI_UPP, max (decode(td.attrib_id, 'OPCINA', td.data_vchar, NULL)),
                    NULL) OPCINA,
                  decode(t.req_type_id,
                    common_pck.cRTI_UPP, max (decode(td.attrib_id, 'BUDZETSKA_ORGANIZACIJA', td.data_vchar, NULL)),
                    NULL) BUDZETSKA_ORGANIZACIJA,
                  decode(t.req_type_id,
                    common_pck.cRTI_UPP, max (decode(td.attrib_id, 'POZIV_NA_BROJ', td.data_vchar, NULL)),
                    NULL) POZIV_NA_BROJ,
                  decode(t.req_type_id,
                    common_pck.cRTI_TRANSFER, NULL,
                    max (decode(td.attrib_id, 'CLEARING_DATE', td.data_vchar, NULL))) CLEARING_DATE,
                  decode(t.req_type_id,
                    common_pck.cRTI_PPI, max (decode(td.attrib_id, 'TROSAK', td.data_vchar, NULL)),
                    NULL) TROSKOVI,
                  decode(t.req_type_id,
                    common_pck.cRTI_PPI, max (decode(td.attrib_id, 'TROSAK_ALIAS', td.data_vchar, NULL)),
                    NULL) TROSKOVI_DESC,
                  decode(t.req_type_id,
                    common_pck.cRTI_PPI, max (decode(td.attrib_id, 'BIC_KOD_BANKE', td.data_vchar, NULL)),
                    NULL) BANKA_ID,
                  decode(t.req_type_id,
                    common_pck.cRTI_PPI, max (decode(td.attrib_id, 'NAZIV_BANKE', td.data_vchar, NULL)),
                    NULL) BANKA_NAZ,
                  decode(t.req_type_id,
                    common_pck.cRTI_PPI, max (decode(td.attrib_id, 'ADRESA', td.data_vchar, NULL)),
                    NULL) ADRESA,
                  decode(t.req_type_id,
                    common_pck.cRTI_PPI, max (decode(td.attrib_id, 'VALUTA_POKRICA_ALIAS', td.data_vchar, NULL)),
                    common_pck.cRTI_TRANSFER, max (decode(td.attrib_id, 'VALUTA_POKRICA', td.data_vchar, NULL)),
                    NULL) VALUTA_POKRICA,
                  decode(t.req_type_id,
                    common_pck.cRTI_PPI, max (decode(td.attrib_id, 'IZNOS_POKRICA', td.data_vchar, NULL)),
                    NULL) IZNOS_POKRICA,
                  decode(t.req_type_id,
                    common_pck.cRTI_PPI, max (decode(td.attrib_id, 'ADRESA2', td.data_vchar, NULL)),
                    NULL) ADRESA2,
                  decode(t.req_type_id,
                    common_pck.cRTI_PPI, max (decode(td.attrib_id, 'VRIJEME_IZVRSENJA_ALIAS', td.data_vchar, NULL)),
                    NULL) VRIJEME_IZVRSENJA_ALIAS,
                  decode(t.req_type_id,
                    common_pck.cRTI_TRANSFER, max (decode(td.attrib_id, 'FIKSNO', td.data_vchar, NULL)),
                    NULL) FIKSNO,
                  decode(t.req_type_id,
                    common_pck.cRTI_TRANSFER, max (decode(td.attrib_id, 'RACUN_POKRICA_ALIAS', td.data_vchar, NULL)),
                    NULL) RACUN_POKRICA,
                  decode(t.req_type_id,
                    common_pck.cRTI_TRANSFER, max (decode(td.attrib_id, 'IZNOS_KUPOVINE', td.data_vchar, NULL)),
                    NULL) IZNOS_KUPOVINE,
					max (decode(td.attrib_id, 'COREID', td.data_vchar, NULL)) IZVORNA_REFERENCA,
                  decode(t.req_type_id,
                    common_pck.cRTI_PPI, max (decode(td.attrib_id, 'OSNOV_PLACANJA_ALIAS', td.data_vchar, NULL)),
                    NULL) OSNOV_PLACANJA					
                FROM (tranpays t LEFT OUTER JOIN mcore.tranpay_details td ON (td.tranpay_id = t.id))
                JOIN tmp$tranpays tmp on (tmp.id = t.id)
                JOIN bank_accounts ba ON (ba.id = t.account_id)
                GROUP BY t.id, t.status, t.status_message, ba.ph0, t.req_type_id, t.tranval, t.description, t.date_modified, t.date_created, t.date_signed, t.date_processed, t.tranval_currency_id, t.ref_code)
                ORDER BY decode(STATUS,
                  common_pck.cTRPSTS_UW, NVL(DATUM_ZADNJE_IZMJENE, DATUM_KREIRANJA),
                  common_pck.cTRPSTS_VB, NVL(DATUM_ZADNJE_IZMJENE, DATUM_KREIRANJA),
                  common_pck.cTRPSTS_UD, DATUM_ZADNJE_IZMJENE,
                  common_pck.cTRPSTS_US, DATUM_POTPISA,
                  common_pck.cTRPSTS_BP, DATUM_OBRADE,
                  common_pck.cTRPSTS_BA, DATUM_OBRADE,
                  common_pck.cTRPSTS_BR, DATUM_OBRADE,
                  common_pck.cTRPSTS_UC, DATUM_ZADNJE_IZMJENE) DESC;

        RETURN rez;

  END getSelectedTranpays;
  
  
	PROCEDURE ValidateTranpay (pTranpay IN obj$tranpay, pStatus OUT INTEGER, pStatusMessage OUT VARCHAR2, pProvisionFee OUT NUMBER)
	IS 
		myunit CONSTANT VARCHAR2(30) := 'ValidateOrder';
		validateOrderInstruction VARCHAR2(1000);
		tranpay_details OBJ$TRANPAY_DETAILS_TABLE;
        objOrder obj$order := new obj$order();
	BEGIN
		slog.debug(pkgCtxId, myUnit);
		
		objOrder.ID := pTranpay.ID;
		objOrder.REQ_TYPE_ID := pTranpay.REQ_TYPE_ID;
		objOrder.ACCOUNT_ID := pTranpay.ACCOUNT_ID;
		objOrder.TRANVAL := pTranpay.TRANVAL;
		objOrder.TRANVAL_CURRENCY_ID := pTranpay.TRANVAL_CURRENCY_ID;
		objOrder.DESCRIPTION := NVL(pTranpay.DESCRIPTION, pTranpay.INTERNAL_DESCRIPTION);
		objOrder.APPLICATION_ID := NVL(pTranpay.APPLICATION_ID, mcauth.auth.getApplicationId);
		objOrder.EXTREF_NAME := pTranpay.EXTREF_NAME;
		objOrder.EXTREF_ID := pTranpay.EXTREF;
			
		tranpay_details := pTranpay.DETAILS;
		
		FOR i IN tranpay_details.FIRST..tranpay_details.LAST LOOP
			IF tranpay_details(i).attrib_id = 'NAZIV_POSILJAOCA' THEN
				objOrder.NAZIV_POSILJAOCA := tranpay_details(i).data_vchar;
				CONTINUE;
			END IF;
			IF tranpay_details(i).attrib_id = 'RACUN_PRIMAOCA' THEN
				objOrder.RACUN_PRIMAOCA := tranpay_details(i).data_vchar;
				CONTINUE;
			END IF;
			IF tranpay_details(i).attrib_id = 'RACUN' THEN
				objOrder.RACUN := tranpay_details(i).data_vchar;
				CONTINUE;
			END IF;
			IF tranpay_details(i).attrib_id = 'RACUN_VALUTA' THEN
				objOrder.RACUN_VALUTA := tranpay_details(i).data_vchar;
				CONTINUE;
			END IF;
			IF tranpay_details(i).attrib_id = 'NAZIV_PRIMAOCA' THEN
				objOrder.NAZIV_PRIMAOCA := tranpay_details(i).data_vchar;
				CONTINUE;
			END IF;
			IF tranpay_details(i).attrib_id = 'HITNO' THEN
				objOrder.HITNO := tranpay_details(i).data_vchar;
				CONTINUE;
			END IF;
			IF tranpay_details(i).attrib_id = 'IZNOS_KUPOVINE' THEN
				objOrder.IZNOS_KUPOVINE := tranpay_details(i).data_vchar;
				CONTINUE;
			END IF;
			IF tranpay_details(i).attrib_id = 'VALUTA_POKRICA' THEN
				objOrder.VALUTA_POKRICA := tranpay_details(i).data_vchar;
				CONTINUE;
			END IF;
			IF tranpay_details(i).attrib_id = 'RACUN_POKRICA' THEN
				objOrder.RACUN_POKRICA := tranpay_details(i).data_vchar;
				CONTINUE;
			END IF;
			IF tranpay_details(i).attrib_id = 'IDPOROBV' THEN
				objOrder.IDPOROBV := tranpay_details(i).data_vchar;
				CONTINUE;
			END IF;
			IF tranpay_details(i).attrib_id = 'VRSTA_UPLATE' THEN
				objOrder.VRSTA_UPLATE := tranpay_details(i).data_vchar;
				CONTINUE;
			END IF;
			IF tranpay_details(i).attrib_id = 'VRSTA_PRIHODA' THEN
				objOrder.VRSTA_PRIHODA := tranpay_details(i).data_vchar;
				CONTINUE;
			END IF;
			IF tranpay_details(i).attrib_id = 'PORPERIOD_OD' THEN
				objOrder.PORPERIOD_OD := tranpay_details(i).data_vchar;
				CONTINUE;
			END IF;
			IF tranpay_details(i).attrib_id = 'PORPERIOD_OD' THEN
				objOrder.PORPERIOD_OD := tranpay_details(i).data_vchar;
				CONTINUE;
			END IF;
			IF tranpay_details(i).attrib_id = 'OPCINA' THEN
				objOrder.OPCINA := tranpay_details(i).data_vchar;
				CONTINUE;
			END IF;
			IF tranpay_details(i).attrib_id = 'BUDZETSKA_ORGANIZACIJA' THEN
				objOrder.BUDZETSKA_ORGANIZACIJA := tranpay_details(i).data_vchar;
				CONTINUE;
			END IF;
			IF tranpay_details(i).attrib_id = 'POZIV_NA_BROJ' THEN
				objOrder.POZIV_NA_BROJ := tranpay_details(i).data_vchar;
				CONTINUE;
			END IF;
			IF tranpay_details(i).attrib_id = 'TROSAK' THEN
				objOrder.TROSAK := tranpay_details(i).data_vchar;
				CONTINUE;
			END IF;
			IF tranpay_details(i).attrib_id = 'BIC_KOD_BANKE' THEN
				objOrder.BIC_KOD_BANKE := tranpay_details(i).data_vchar;
				CONTINUE;
			END IF;
			IF tranpay_details(i).attrib_id = 'NAZIV_BANKE' THEN
				objOrder.NAZIV_BANKE := tranpay_details(i).data_vchar;
				CONTINUE;
			END IF;
			IF tranpay_details(i).attrib_id = 'JAVNI_PRIHODI' THEN
				objOrder.JAVNI_PRIHODI := tranpay_details(i).data_vchar;
				CONTINUE;
			END IF;
			IF tranpay_details(i).attrib_id = 'REFERENCA_PLACANJA' THEN
				objOrder.REFERENCA_PLACANJA := tranpay_details(i).data_vchar;
				CONTINUE;
			END IF;
			IF tranpay_details(i).attrib_id = 'ADRESA' THEN
				objOrder.ADRESA := tranpay_details(i).data_vchar;
				CONTINUE;
			END IF;
			IF tranpay_details(i).attrib_id = 'ADRESA2' THEN
				objOrder.ADRESA2 := tranpay_details(i).data_vchar;
				CONTINUE;
			END IF;
			IF tranpay_details(i).attrib_id = 'FIKSNO' THEN
				objOrder.FIKSNO := tranpay_details(i).data_vchar;
				CONTINUE;
			END IF;
			IF tranpay_details(i).attrib_id = 'VRIJEME_IZVRSENJA' THEN
				objOrder.VRIJEME_IZVRSENJA := tranpay_details(i).data_vchar;
				CONTINUE;
			END IF;
			IF tranpay_details(i).attrib_id = 'ACCOUNT_VALUTA' THEN
				objOrder.ACCOUNT_VALUTA := tranpay_details(i).data_vchar;
				CONTINUE;
			END IF;
			IF tranpay_details(i).attrib_id = 'IZNOS_POKRICA' THEN
				objOrder.IZNOS_POKRICA := tranpay_details(i).data_vchar;
				CONTINUE;
			END IF;
			IF tranpay_details(i).attrib_id = 'OSNOV_PLACANJA' THEN
				objOrder.OSNOV_PLACANJA := tranpay_details(i).data_vchar;
				CONTINUE;
			END IF;
			IF tranpay_details(i).attrib_id = 'BANK_ACCOUNT_TYPE_ID_TO' THEN
				objOrder.BANK_ACCOUNT_TYPE_ID_TO := tranpay_details(i).data_vchar;
				CONTINUE;
			END IF;
		END LOOP;
		
		IF sspkg.ReadBool(pkgCtxId || '/CoreDataValidationWithProvisionEnabled') THEN
		
			slog.debug(pkgCtxId, myUnit, 'Core data validation with provision enabled!');
			
			validateOrderInstruction := sspkg.readVchar(pkgCtxId || '/CoreDataValidationWithProvisionInstruction');
			
			IF validateOrderInstruction IS NULL THEN
				slog.error(pkgCtxId, myUnit, common_pck.cERR_NotSupported);
				sspkg.raiseError(common_pck.cERR_NotSupported, null, pkgCtxId, myUnit);
			END IF;
		
			execute immediate validateOrderInstruction using objOrder, out pStatus, out pStatusMessage, out pProvisionFee;
			
			slog.debug(pkgCtxId, myUnit, 'pStatus :' || pStatus);	
			slog.debug(pkgCtxId, myUnit, 'pStatusMessage: ' || pStatusMessage);
			slog.debug(pkgCtxId, myUnit, 'pProvisionFee: ' || pProvisionFee);
			
		ELSE 
			pStatus := 0; -- postavljamo status na 0 kako bi mobilna znati hendlati slucaj kada nije ukljucena validacija
		END IF;
	END;
	
	 FUNCTION addTranpayAttachment(pTranpayId NUMBER, pDescription VARCHAR2, pSourceFileName VARCHAR2, pBlobData BLOB, pBlob_MimeType  VARCHAR2)
	 RETURN NUMBER IS
		myunit CONSTANT VARCHAR2(30) := 'addTranpayAttachment';
		vPom PLS_INTEGER;
		vFileName VARCHAR2(1024);
		vFileHandle BFILE;
		vAttachmentId mcore.attachments.id%TYPE;
	BEGIN
		slog.debug(pkgCtxId, myUnit,  pTranpayId || ':' || SUBSTR(pDescription, 1, 30) || ':' || pSourceFileName ||':'|| pBlob_MimeType);

		IF pDescription IS NULL THEN
			sspkg.raiseError(common_pck.cERR_MissAttDescription, null, pkgCtxId, myunit);
		END IF;

		IF dbms_lob.getlength(pBlobData) = 0 THEN
			sspkg.raiseError(common_pck.cERR_FilesizeZero, null, pkgCtxId, myunit);
		END IF;

		IF p_tranpayExists(pTranpayId => pTranpayId, pTranpayStatus => common_pck.cTRPSTS_UW) = 0 THEN
			sspkg.raiseError(cERR_InvalidTranpay, null, pkgCtxId, myunit);
		END IF;

		vAttachmentId := mcore.ATTACHMENTS_ID_SEQ.nextval;

		vPom := instr(pSourceFileName, '.', -1);

		IF vPom > 0 THEN
			vFileName := vAttachmentId || substr(pSourceFileName, vPom);
		ELSE
			vFileName := vAttachmentId;
		END IF;

		vFileHandle := mcore.util.WriteBLOBToFILE (pDestDirectory => mcore.common_pck.cDIR_ATTACHMENTS, pFileName => vFileName, sourceData => pBlobData);

		INSERT INTO attachments(id, description, tranpay_id, ext_id, service, content, mime_Type, user_id, date_created, filename)
		VALUES (vAttachmentId, SUBSTR(pDescription,1,1000), pTranpayId, pTranpayId, cTRANPAY_TYPE_ATT, vFileHandle, pBlob_MimeType, mcauth.auth.getClientId, sysdate, SUBSTR(pSourceFileName,1,400));

		RETURN vAttachmentId;
  END addTranpayAttachment;

	PROCEDURE removeTranpayAttachment(pTranpayId NUMBER, pAttachmentId NUMBER)
	IS
		myunit CONSTANT VARCHAR2(30) := 'removeTranpayAttachment';
		vFileHandle BFILE;
		pragma autonomous_transaction;
	BEGIN
		slog.debug(pkgCtxId, myUnit,  pTranpayId || ':' || pAttachmentId);

		IF p_tranpayExists(pTranpayId => pTranpayId, pTranpayStatus => mcore.common_pck.cTRPSTS_UW) = 0 THEN
			sspkg.raiseError(cERR_InvalidTranpay, null, pkgCtxId, myunit);
		END IF;

		DELETE FROM mcore.attachments WHERE id = pAttachmentId AND tranpay_id = pTranpayId
		RETURN content INTO vFileHandle;

		COMMIT;

		util.removeFile(pFileHandle => vFileHandle);
	EXCEPTION
	WHEN OTHERS THEN
		ROLLBACK;
		RAISE;
  END removeTranpayAttachment;
  
  PROCEDURE getTranpaysForDetail(pTranpayAttribId tranpay_attribs.id%TYPE, 
								 pDataVCHAR tranpay_details.data_vchar%TYPE DEFAULT NULL,
								 pAllTranpays out sys_refcursor,
								 pAllTranpayDetails out sys_refcursor,
								 pOffset PLS_INTEGER := 1,
								 pArraySize PLS_INTEGER := 10)
  IS 
	myunit CONSTANT VARCHAR2(30) := 'getTranpaysForDetail';
	rez1 sys_refcursor;
	rez2 sys_refcursor;
	vOffset PLS_INTEGER;
    vArraySize PLS_INTEGER;
  BEGIN
	
	slog.debug(pkgCtxId, myUnit, pTranpayAttribId || ':' || pDataVCHAR || ':' || pOffset || ':' || pArraySize);
    
    common_pck.SetOffsetArraySize(pkgCtxId, myunit,
                                  pOffset, pArraySize,
                                  vOffset, vArraySize);
									  
    common_pck.CommonSecurityChecks;
	
	OPEN rez1 FOR 
		SELECT  id, req_type_id, status, status_message, status_code, description,
          internal_description, tranval, tranval_currency_id, cost, cost_currency_id,
		  date_created, date_signed, date_processed, tranpay_group_id, tg_name, tg_description,
		  account_id, chart_of_accounts_id, schedule_id, execute_at, date_modified, retry_counter,
          ref_code, user_modified, user_created_name, user_modified_name, parent_id, 0 aggr_tranpay
		  FROM (SELECT
			  t.id id,
			  t.req_type_id req_type_id,
			  t.status status,
			  t.status_message status_message,
			  t.status_code status_code,
			  t.description description,
			  t.internal_description internal_description,
			  t.tranval tranval,
			  t.tranval_currency_id tranval_currency_id,
			  t.cost cost,
			  t.cost_currency_id cost_currency_id,
			  t.date_created date_created,
			  t.date_signed date_signed,
			  t.date_processed date_processed,
			  t.tranpay_group_id tranpay_group_id,
			  tg.name tg_name,
			  tg.description tg_description,
			  t.account_id account_id,
			  t.chart_of_accounts_id chart_of_accounts_id,
			  t.schedule_id schedule_id,
			  t.execute_at execute_at,
			  t.date_modified date_modified,
			  t.retry_counter retry_counter,
			  t.ref_code ref_code,
			  t.user_modified user_modified,
			  c.name user_created_name,
			  c.name user_modified_name,
			  t.parent_id,
			  ROW_NUMBER() OVER (ORDER BY t.id DESC) rn
			FROM tranpays t
			JOIN tranpay_details td ON (td.tranpay_id = t.id AND td.attrib_id = pTranpayAttribId)
			JOIN mcauth.client c ON c.id = t.user_id
			LEFT JOIN tranpay_groups tg ON (tg.id = t.tranpay_group_id)
			JOIN bank_accounts ba ON (ba.id = t.account_id)
			JOIN action_grants ag ON (ag.account_id = ba.ph4)
			JOIN actions ac ON (ac.application_id = ag.application_id AND ac.id = ag.action_id)
			JOIN applications ap ON (ap.id = ac.application_id)
			WHERE ag.valid = 1
			AND ac.valid = 1
			AND ap.valid = 1
			AND ag.user_id = mcauth.auth.getClientId
			AND ap.id = mcauth.auth.getApplicationId
			AND ag.action_id = common_pck.cACT_ViewTranpays	
			AND t.status = common_pck.cTRPSTS_BA
			AND ba.account_owner_id LIKE mcauth.auth.getAccountOwner
			AND td.data_vchar LIKE NVL(pDataVCHAR, '%'))
			WHERE rn BETWEEN vOffset AND (vOffset + vArraySize)
			ORDER BY rn ASC;
		
	OPEN rez2 FOR 
		  SELECT DISTINCT 
			td.tranpay_id,
            td.attrib_id,
            td.req_type_id,
            td.description detail_description,
            td.data_vchar
			FROM 
			(SELECT id FROM (
			  SELECT
			  t.id,
			  ROW_NUMBER() OVER (ORDER BY t.id DESC) rn
			FROM tranpays t
			JOIN tranpay_details td ON (td.tranpay_id = t.id AND td.attrib_id = pTranpayAttribId)
			JOIN mcauth.client c ON c.id = t.user_id
			JOIN bank_accounts ba ON (ba.id = t.account_id)
			JOIN action_grants ag ON (ag.account_id = ba.ph4)
			JOIN actions ac ON (ac.application_id = ag.application_id AND ac.id = ag.action_id)
			JOIN applications ap ON (ap.id = ac.application_id)
			WHERE ag.valid = 1
			AND ac.valid = 1
			AND ap.valid = 1
			AND ag.user_id = mcauth.auth.getClientId
			AND ap.id = mcauth.auth.getApplicationId
			AND ag.action_id = common_pck.cACT_ViewTranpays	
			AND t.status = common_pck.cTRPSTS_BA
			AND ba.account_owner_id LIKE mcauth.auth.getAccountOwner
			AND td.data_vchar LIKE NVL(pDataVCHAR, '%'))
			WHERE rn BETWEEN vOffset AND (vOffset + vArraySize)
			ORDER BY rn ASC) trnpys 
			JOIN tranpay_details td ON td.tranpay_id = trnpys.id
			JOIN tranpay_attribs ta ON td.req_type_id = ta.req_type_id;
			
	pAllTranpays := rez1;
	pAllTranpayDetails := rez2;
	
  END;
  
  FUNCTION getAmountForAdditionalServices(pTranval tranpays.tranval%TYPE, pIpcIdList table_of_integer) 
  RETURN CLOB
  IS 
	myunit CONSTANT VARCHAR2(30) := 'getAmountForAdditionalServices';
	vInvoiceTemplate CLOB;
	vIprId invoice_provider_clients.ipr_id%TYPE;
	vServiceName ipr_details.data_vchar%TYPE;
	vInvoiceTemplateJSONObj json_object_t;
	vGetAdditionalServiceAmountAPI VARCHAR2(1000);	
	vAmount NUMBER;
	vAdditionalTranpayAmount NUMBER;
	vAdditionalServicesAmount JSON_OBJECT_T;
	
  BEGIN 
	slog.debug(pkgCtxId, myUnit);
	
	vAdditionalServicesAmount := JSON_OBJECT_T();
	
	FOR i IN 1..pIpcIdList.COUNT LOOP
	
		slog.debug(pkgCtxId, myUnit, pIpcIdList(i));
		
		BEGIN
			SELECT ipc.ipr_id, ipc.invoice_template, iprdt.data_vchar INTO vIprId, vInvoiceTemplate, vServiceName
			FROM invoice_provider_clients ipc
			JOIN ipr_details iprdt ON (ipc.ipr_id = iprdt.ipr_id)
			WHERE ipc.id = pIpcIdList(i)
			AND iprdt.iab_id = common_pck.cIPR_ATT_ADD_SERVICE_TYPE;
		EXCEPTION
			WHEN no_data_found THEN
				slog.error(pkgCtxId, myUnit, cERR_InvalidIprClient, pIpcIdList(i));
				sspkg.raiseError(cERR_InvalidIprClient, null, pkgCtxId, myunit);
		END;
			
		slog.debug(pkgCtxId, myUnit, 'vIprId' || ':' || vIprId || ';' || 'vInvoiceTemplate' || ':' || vInvoiceTemplate  || ';' || 'vServiceName' || ' ' || vServiceName);		
		
		BEGIN 
			vInvoiceTemplateJSONObj := json_object_t.parse (vInvoiceTemplate);
		EXCEPTION 
			WHEN OTHERS THEN
				slog.error(pkgCtxId, myUnit, 'Unhandled error during json parse' || ' : ' || SQLCODE|| ' : ' || SQLERRM);
				sspkg.raiseError(cERR_internalError, null, pkgCtxId, myunit);
		END;
		
		vAmount := to_number(vInvoiceTemplateJSONObj.get_string ('amount'), '999999999999999D00','NLS_NUMERIC_CHARACTERS=.,');
		
		vGetAdditionalServiceAmountAPI := sspkg.readVchar(pkgCtxId || '/AdditionalServices/' || vServiceName || '/getAdditionalServiceAmountAPI' );
		
		IF vGetAdditionalServiceAmountAPI IS NULL THEN
			sspkg.raiseError('/Core/Main/TranPays/err/missingGetAdditionalServiceAmountAPI', NULL, pkgCtxId, myunit);
		END IF;
		
		slog.debug(pkgCtxId, myUnit, 'vGetAdditionalServiceAmountAPI' || ':' || vGetAdditionalServiceAmountAPI || ';' || 'vAmount' || ':' || vAmount  || ';' || 'pTranval' || ':' || pTranval );
	
		BEGIN 
			execute immediate vGetAdditionalServiceAmountAPI using vAmount, pTranval, out vAdditionalTranpayAmount;
		EXCEPTION 
			WHEN OTHERS THEN
				slog.error(pkgCtxId, myUnit, 'Unable to execute vGetAdditionalServiceAmountAPI' || ':'  || vAmount || ':' || pTranval || ':' || vAdditionalTranpayAmount || ':' ||SQLCODE || ':' || SQLERRM);
				sspkg.raiseError(cERR_internalError, null, pkgCtxId, myunit);
		END;
		
		slog.debug(pkgCtxId, myUnit, 'vAdditionalTranpayAmount' || ':' ||  vAdditionalTranpayAmount);
		
		vAdditionalServicesAmount.PUT(TO_CHAR(pIpcIdList(i)), vAdditionalTranpayAmount);						
	
	END LOOP;
	
	RETURN vAdditionalServicesAmount.to_clob();
	
	END;
	
	FUNCTION getTranpayGroupTypes(pGranteePermissionName grantee_permissions.name%TYPE DEFAULT NULL)
	RETURN sys_refcursor 
	IS 
		myunit CONSTANT VARCHAR2(30) := 'getTranpayGroupTypes';
		rez sys_refcursor;
		vGranteePermissionId grantee_permissions.id%TYPE;
	BEGIN 
		slog.debug(pkgCtxId, myUnit);
		
		common_pck.CommonSecurityChecks;
		
		IF pGranteePermissionName IS NOT NULL THEN 
			BEGIN 
				SELECT ID INTO vGranteePermissionId 
				FROM grantee_permissions
				WHERE name = pGranteePermissionName
				AND valid = 1;
			EXCEPTION 
				WHEN no_data_found THEN 
					slog.error(pkgCtxId, myUnit, cERR_InvalidGranteePermissionName, pGranteePermissionName);
					sspkg.raiseError(cERR_InvalidGranteePermissionName, null, pkgCtxId, myunit);
				WHEN OTHERS THEN 
					slog.error(pkgCtxId, myUnit, sqlcode||':'||sqlerrm);
					sspkg.raiseError(cERR_InternalError, NULL, pkgCtxId, myUnit);
			END; 
			
			OPEN rez FOR 
				SELECT tgt.id, tgt.name, tgt.acc_owner_id, tgt.valid 
				FROM tranpay_group_type tgt
				JOIN grantee_role_permissions grp ON (tgt.id = grp.tranpay_group_type_id)
				JOIN grantee_permissions gp ON (gp.id = grp.permission_id)
				JOIN grantee_role_types grt ON (grt.id = grp.role_id)
				JOIN grantee_roles gr ON (gr.app_role_id = grt.id)
				JOIN grantees g ON (g.id = gr.grantee_id)
				WHERE tgt.acc_owner_id = mcauth.auth.getAccountOwner
				AND gp.name = pGranteePermissionName
				AND grt.acc_owner_id = mcauth.auth.getAccountOwner
				AND g.client_id = mcauth.auth.getClientId
				AND g.acc_owner_id = mcauth.auth.getAccountOwner
				AND g.valid = 1
				AND grt.valid = 1
				AND gp.valid = 1;	
		ELSE 
			OPEN rez FOR 
				SELECT id, name, acc_owner_id, valid 
				FROM tranpay_group_type tgt
				WHERE acc_owner_id = mcauth.auth.getAccountOwner;
		END IF;
		RETURN rez;
	END;
	
	FUNCTION insertTranpayGroupType(pName tranpay_group_type.name%TYPE, pValid tranpay_group_type.valid%TYPE)
	RETURN tranpay_group_type.id%TYPE
	IS 
		myunit CONSTANT VARCHAR2(30) := 'insertTranpayGroupType';
		vTranpayGroupTypeId tranpay_group_type.id%TYPE;
		unique_constraint_violation EXCEPTION;
		PRAGMA EXCEPTION_INIT(unique_constraint_violation, -1);
	BEGIN 
		slog.debug(pkgCtxId, myUnit, mcauth.auth.getAccountOwner);
		
		common_pck.CommonSecurityChecks;
		
		IF NOT grantees_pck.isGranteeAdmin THEN 
		   sspkg.raiseError(cERR_noRequiredAuthorization, NULL, pkgCtxId, myUnit);
		END IF;
		
		INSERT INTO tranpay_group_type(name, acc_owner_id, valid)
		VALUES(pName, mcauth.auth.getAccountOwner, pValid)
		RETURNING id INTO vTranpayGroupTypeId;
		
		RETURN vTranpayGroupTypeId;
	EXCEPTION
		WHEN unique_constraint_violation THEN
			sspkg.raiseError(cERR_UniqueTranpayGroupTypeError, NULL, pkgCtxId, myUnit);
		WHEN OTHERS THEN 
			slog.error(pkgCtxId, myUnit, sqlcode ||':'|| sqlerrm);
			sspkg.raiseError(cERR_InternalError, NULL, pkgCtxId, myUnit);
	END;
	
	PROCEDURE updateTranpayGroupType(pTranpayGroupTypeId tranpay_group_type.id%TYPE, pName tranpay_group_type.name%TYPE DEFAULT NULL, pValid tranpay_group_type.valid%TYPE DEFAULT NULL)
	IS 
		myunit CONSTANT VARCHAR2(30) := 'updateTranpayGroupType';
	BEGIN 
		slog.debug(pkgCtxId, myUnit);
		
		common_pck.CommonSecurityChecks;
		
		IF NOT grantees_pck.isGranteeAdmin THEN 
		   sspkg.raiseError(cERR_noRequiredAuthorization, NULL, pkgCtxId, myUnit);
		END IF;
		
		UPDATE tranpay_group_type
		SET name = NVL(pName, name), valid = NVL(pValid, valid)
		WHERE id = pTranpayGroupTypeId
		AND acc_owner_id = mcauth.auth.getAccountOwner;
	EXCEPTION
		WHEN OTHERS THEN 
			slog.error(pkgCtxId, myUnit, sqlcode||':'||sqlerrm);
			sspkg.raiseError(cERR_InternalError, NULL, pkgCtxId, myUnit);
	END;
	
END;
/
show errors