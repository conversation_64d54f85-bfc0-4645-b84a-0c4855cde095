-- Setup script for automatic payment functionality
-- Run this script to configure the automatic payment system

SET SERVEROUTPUT ON;

BEGIN
    DBMS_OUTPUT.PUT_LINE('=== Setting up Automatic Payment System ===');
    
    -- 1. Set global configuration
    DBMS_OUTPUT.PUT_LINE('1. Setting global configuration...');
    
    -- Enable auto payment globally (set to FALSE initially for safety)
    sspkg.writeBool('/Core/Main/InvoiceManagement/AutoPayment/enabled', FALSE);
    DBMS_OUTPUT.PUT_LINE('   - Auto payment globally disabled (for safety)');
    
    -- Set default from address for notifications
    sspkg.writeVChar('/Core/Main/InvoiceManagement/AutoPayment/fromAddress', '<EMAIL>');
    DBMS_OUTPUT.PUT_LINE('   - Default from address set to: <EMAIL>');
    
    -- 2. Create and schedule the auto payment job
    DBMS_OUTPUT.PUT_LINE('2. Setting up auto payment job...');
    
    BEGIN
        mcore.auto_payment_job_pck.ScheduleAutoPaymentJob;
        DBMS_OUTPUT.PUT_LINE('   - Auto payment job scheduled successfully');
    EXCEPTION
        WHEN OTHERS THEN
            DBMS_OUTPUT.PUT_LINE('   - Error scheduling job: ' || SQLERRM);
    END;
    
    -- 3. Verify job creation
    DECLARE
        vJobCount NUMBER;
    BEGIN
        SELECT COUNT(*)
        INTO vJobCount
        FROM user_scheduler_jobs
        WHERE job_name = 'AUTO_PAYMENT_JOB';
        
        IF vJobCount > 0 THEN
            DBMS_OUTPUT.PUT_LINE('   - Job verification successful');
        ELSE
            DBMS_OUTPUT.PUT_LINE('   - Job verification failed');
        END IF;
    END;
    
    -- 4. Display current configuration
    DBMS_OUTPUT.PUT_LINE('3. Current configuration:');
    DBMS_OUTPUT.PUT_LINE('   - Auto payment enabled: ' || 
        CASE WHEN sspkg.readBool('/Core/Main/InvoiceManagement/AutoPayment/enabled') THEN 'TRUE' ELSE 'FALSE' END);
    DBMS_OUTPUT.PUT_LINE('   - From address: ' || 
        sspkg.readVChar('/Core/Main/InvoiceManagement/AutoPayment/fromAddress'));
    
    DBMS_OUTPUT.PUT_LINE('=== Setup completed ===');
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('IMPORTANT NOTES:');
    DBMS_OUTPUT.PUT_LINE('- Auto payment is initially DISABLED for safety');
    DBMS_OUTPUT.PUT_LINE('- To enable: sspkg.writeBool(''/Core/Main/InvoiceManagement/AutoPayment/enabled'', TRUE);');
    DBMS_OUTPUT.PUT_LINE('- Monitor logs in slog system for auto payment activities');
    DBMS_OUTPUT.PUT_LINE('- Job runs every 5 minutes to process pending auto payments');
    
END;
/

-- Grant necessary permissions for job execution
GRANT EXECUTE ON mcore.auto_payment_job_pck TO PUBLIC;
GRANT EXECUTE ON mcore.invoice_mgmt_pck TO PUBLIC;

-- Create synonyms if needed
-- CREATE OR REPLACE PUBLIC SYNONYM auto_payment_job_pck FOR mcore.auto_payment_job_pck;

COMMIT;

PROMPT Setup completed. Check output above for any errors.
