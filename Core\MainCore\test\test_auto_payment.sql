-- Test script for automatic payment functionality
-- This script tests the implementation of automatic payment for eInvoices

SET SERVEROUTPUT ON;

DECLARE
    vTestUserId end_users.id%TYPE := 1; -- Replace with actual test user ID
    vTestProviderId invoice_providers.id%TYPE := 1; -- Replace with actual provider ID
    vTestAccountId VARCHAR2(40) := 'TEST_ACCOUNT_001'; -- Replace with actual account ID
    vIpcId invoice_provider_clients.id%TYPE;
    vInvoiceId invoices.id%TYPE;
    vResult NUMBER;
    
BEGIN
    DBMS_OUTPUT.PUT_LINE('=== Testing Automatic Payment Functionality ===');
    
    -- Test 1: Register consumer with auto payment enabled
    DBMS_OUTPUT.PUT_LINE('Test 1: Registering consumer with auto payment enabled');
    
    BEGIN
        vIpcId := mcore.invoice_mgmt_pck.RegisterConsumer(
            pInvoiceProviderId => vTestProviderId,
            pEndUserId => vTestUserId,
            pType => mcore.common_pck.cINVPRVCLTYPECONSUMER,
            pUserReference => 'TEST_AUTO_PAY_REF',
            pUserComment => 'Test subscription for auto payment',
            pValid => 1,
            pUserAgreementId => NULL,
            pResponse => NULL,
            pOtp => NULL,
            pSourceData => NULL,
            pSignature => NULL,
            pInvoiceTemplate => NULL,
            pAutoPaymentEnabled => 1,
            pPaymentAccountId => vTestAccountId
        );
        
        DBMS_OUTPUT.PUT_LINE('Consumer registered successfully with ID: ' || vIpcId);
        
    EXCEPTION
        WHEN OTHERS THEN
            DBMS_OUTPUT.PUT_LINE('Error registering consumer: ' || SQLERRM);
    END;
    
    -- Test 2: Update consumer auto payment settings
    DBMS_OUTPUT.PUT_LINE('Test 2: Updating consumer auto payment settings');
    
    BEGIN
        mcore.invoice_mgmt_pck.UpdateConsumer(
            pIpcId => vIpcId,
            pUserReference => 'TEST_AUTO_PAY_REF_UPDATED',
            pUserComment => 'Updated test subscription',
            pInvoiceTemplate => NULL,
            pAutoPaymentEnabled => 1,
            pPaymentAccountId => 'UPDATED_ACCOUNT_002'
        );
        
        DBMS_OUTPUT.PUT_LINE('Consumer updated successfully');
        
    EXCEPTION
        WHEN OTHERS THEN
            DBMS_OUTPUT.PUT_LINE('Error updating consumer: ' || SQLERRM);
    END;
    
    -- Test 3: Verify auto payment settings in database
    DBMS_OUTPUT.PUT_LINE('Test 3: Verifying auto payment settings');
    
    DECLARE
        vAutoPaymentEnabled invoice_provider_clients.auto_payment_enabled%TYPE;
        vPaymentAccountId invoice_provider_clients.payment_account_id%TYPE;
    BEGIN
        SELECT auto_payment_enabled, payment_account_id
        INTO vAutoPaymentEnabled, vPaymentAccountId
        FROM invoice_provider_clients
        WHERE id = vIpcId;
        
        DBMS_OUTPUT.PUT_LINE('Auto payment enabled: ' || vAutoPaymentEnabled);
        DBMS_OUTPUT.PUT_LINE('Payment account ID: ' || vPaymentAccountId);
        
        IF vAutoPaymentEnabled = 1 AND vPaymentAccountId = 'UPDATED_ACCOUNT_002' THEN
            DBMS_OUTPUT.PUT_LINE('Auto payment settings verified successfully');
        ELSE
            DBMS_OUTPUT.PUT_LINE('Auto payment settings verification failed');
        END IF;
        
    EXCEPTION
        WHEN OTHERS THEN
            DBMS_OUTPUT.PUT_LINE('Error verifying auto payment settings: ' || SQLERRM);
    END;
    
    -- Test 4: Test auto payment processing (simulation)
    DBMS_OUTPUT.PUT_LINE('Test 4: Testing auto payment processing');
    
    BEGIN
        -- Enable auto payment globally for testing
        sspkg.writeBool('/Core/Main/InvoiceManagement/AutoPayment/enabled', TRUE);
        
        -- Create a test invoice (this would normally be done through invoice upload)
        INSERT INTO invoices (
            id, ipr_id, client_id, invoice_no, description, tranval, 
            tranval_currency_id, status, date_created
        ) VALUES (
            invoices_seq.NEXTVAL, vTestProviderId, vIpcId, 'TEST_INV_001',
            'Test invoice for auto payment', 100.00, 'BAM', 0, SYSDATE
        ) RETURNING id INTO vInvoiceId;
        
        DBMS_OUTPUT.PUT_LINE('Test invoice created with ID: ' || vInvoiceId);
        
        -- Test auto payment processing
        mcore.invoice_mgmt_pck.ProcessAutoPayment(pInvoiceId => vInvoiceId);
        
        DBMS_OUTPUT.PUT_LINE('Auto payment processing completed');
        
    EXCEPTION
        WHEN OTHERS THEN
            DBMS_OUTPUT.PUT_LINE('Error testing auto payment processing: ' || SQLERRM);
    END;
    
    -- Test 5: Test job scheduling
    DBMS_OUTPUT.PUT_LINE('Test 5: Testing job scheduling');
    
    BEGIN
        mcore.auto_payment_job_pck.ScheduleAutoPaymentJob;
        DBMS_OUTPUT.PUT_LINE('Auto payment job scheduled successfully');
        
        -- Check if job exists
        SELECT COUNT(*) INTO vResult
        FROM user_scheduler_jobs
        WHERE job_name = 'AUTO_PAYMENT_JOB';
        
        IF vResult > 0 THEN
            DBMS_OUTPUT.PUT_LINE('Job verification successful');
        ELSE
            DBMS_OUTPUT.PUT_LINE('Job verification failed');
        END IF;
        
    EXCEPTION
        WHEN OTHERS THEN
            DBMS_OUTPUT.PUT_LINE('Error testing job scheduling: ' || SQLERRM);
    END;
    
    -- Cleanup
    DBMS_OUTPUT.PUT_LINE('Cleaning up test data...');
    
    BEGIN
        -- Remove test invoice
        DELETE FROM invoices WHERE id = vInvoiceId;
        
        -- Remove test subscription
        DELETE FROM invoice_provider_clients WHERE id = vIpcId;
        
        -- Remove job
        mcore.auto_payment_job_pck.RemoveAutoPaymentJob;
        
        DBMS_OUTPUT.PUT_LINE('Cleanup completed');
        
    EXCEPTION
        WHEN OTHERS THEN
            DBMS_OUTPUT.PUT_LINE('Error during cleanup: ' || SQLERRM);
    END;
    
    DBMS_OUTPUT.PUT_LINE('=== Testing completed ===');
    
END;
/
