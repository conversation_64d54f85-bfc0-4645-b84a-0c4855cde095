-- Test script to verify that procedure overloading works correctly
-- This script tests that existing calls still work and new calls work with auto payment

SET SERVEROUTPUT ON;

DECLARE
    vTestUserId end_users.id%TYPE := 1; -- Replace with actual test user ID
    vTestProviderId invoice_providers.id%TYPE := 1; -- Replace with actual provider ID
    vTestAccountId VARCHAR2(40) := 'TEST_ACCOUNT_001'; -- Replace with actual account ID
    vIpcId1 invoice_provider_clients.id%TYPE;
    vIpcId2 invoice_provider_clients.id%TYPE;
    
BEGIN
    DBMS_OUTPUT.PUT_LINE('=== Testing Procedure Overloading ===');
    
    -- Test 1: Call original RegisterConsumer (should work without changes)
    DBMS_OUTPUT.PUT_LINE('Test 1: Calling original RegisterConsumer (without auto payment params)');
    
    BEGIN
        vIpcId1 := mcore.invoice_mgmt_pck.RegisterConsumer(
            pInvoiceProviderId => vTestProviderId,
            pEndUserId => vTestUserId,
            pType => mcore.common_pck.cINVPRVCLTYPECONSUMER,
            pUserReference => 'TEST_ORIGINAL_REF',
            pUserComment => 'Test original subscription',
            pValid => 1,
            pUserAgreementId => NULL,
            pResponse => NULL,
            pOtp => NULL,
            pSourceData => NULL,
            pSignature => NULL,
            pInvoiceTemplate => NULL
        );
        
        DBMS_OUTPUT.PUT_LINE('✓ Original RegisterConsumer works - ID: ' || vIpcId1);
        
    EXCEPTION
        WHEN OTHERS THEN
            DBMS_OUTPUT.PUT_LINE('✗ Error with original RegisterConsumer: ' || SQLERRM);
    END;
    
    -- Test 2: Call overloaded RegisterConsumer (with auto payment params)
    DBMS_OUTPUT.PUT_LINE('Test 2: Calling overloaded RegisterConsumer (with auto payment params)');
    
    BEGIN
        vIpcId2 := mcore.invoice_mgmt_pck.RegisterConsumer(
            pInvoiceProviderId => vTestProviderId,
            pEndUserId => vTestUserId,
            pType => mcore.common_pck.cINVPRVCLTYPECONSUMER,
            pUserReference => 'TEST_OVERLOADED_REF',
            pUserComment => 'Test overloaded subscription',
            pValid => 1,
            pUserAgreementId => NULL,
            pResponse => NULL,
            pOtp => NULL,
            pSourceData => NULL,
            pSignature => NULL,
            pInvoiceTemplate => NULL,
            pAutoPaymentEnabled => 1,
            pPaymentAccountId => vTestAccountId
        );
        
        DBMS_OUTPUT.PUT_LINE('✓ Overloaded RegisterConsumer works - ID: ' || vIpcId2);
        
    EXCEPTION
        WHEN OTHERS THEN
            DBMS_OUTPUT.PUT_LINE('✗ Error with overloaded RegisterConsumer: ' || SQLERRM);
    END;
    
    -- Test 3: Call original UpdateConsumer
    DBMS_OUTPUT.PUT_LINE('Test 3: Calling original UpdateConsumer (without auto payment params)');
    
    IF vIpcId1 IS NOT NULL THEN
        BEGIN
            mcore.invoice_mgmt_pck.UpdateConsumer(
                pIpcId => vIpcId1,
                pUserReference => 'TEST_ORIGINAL_REF_UPDATED',
                pUserComment => 'Updated original subscription',
                pInvoiceTemplate => NULL
            );
            
            DBMS_OUTPUT.PUT_LINE('✓ Original UpdateConsumer works');
            
        EXCEPTION
            WHEN OTHERS THEN
                DBMS_OUTPUT.PUT_LINE('✗ Error with original UpdateConsumer: ' || SQLERRM);
        END;
    END IF;
    
    -- Test 4: Call overloaded UpdateConsumer
    DBMS_OUTPUT.PUT_LINE('Test 4: Calling overloaded UpdateConsumer (with auto payment params)');
    
    IF vIpcId2 IS NOT NULL THEN
        BEGIN
            mcore.invoice_mgmt_pck.UpdateConsumer(
                pIpcId => vIpcId2,
                pUserReference => 'TEST_OVERLOADED_REF_UPDATED',
                pUserComment => 'Updated overloaded subscription',
                pInvoiceTemplate => NULL,
                pAutoPaymentEnabled => 1,
                pPaymentAccountId => 'UPDATED_ACCOUNT_002'
            );
            
            DBMS_OUTPUT.PUT_LINE('✓ Overloaded UpdateConsumer works');
            
        EXCEPTION
            WHEN OTHERS THEN
                DBMS_OUTPUT.PUT_LINE('✗ Error with overloaded UpdateConsumer: ' || SQLERRM);
        END;
    END IF;
    
    -- Test 5: Verify auto payment settings were saved
    DBMS_OUTPUT.PUT_LINE('Test 5: Verifying auto payment settings');
    
    IF vIpcId2 IS NOT NULL THEN
        DECLARE
            vAutoPaymentEnabled invoice_provider_clients.auto_payment_enabled%TYPE;
            vPaymentAccountId invoice_provider_clients.payment_account_id%TYPE;
        BEGIN
            SELECT auto_payment_enabled, payment_account_id
            INTO vAutoPaymentEnabled, vPaymentAccountId
            FROM invoice_provider_clients
            WHERE id = vIpcId2;
            
            DBMS_OUTPUT.PUT_LINE('Auto payment enabled: ' || vAutoPaymentEnabled);
            DBMS_OUTPUT.PUT_LINE('Payment account ID: ' || vPaymentAccountId);
            
            IF vAutoPaymentEnabled = 1 AND vPaymentAccountId = 'UPDATED_ACCOUNT_002' THEN
                DBMS_OUTPUT.PUT_LINE('✓ Auto payment settings verified successfully');
            ELSE
                DBMS_OUTPUT.PUT_LINE('✗ Auto payment settings verification failed');
            END IF;
            
        EXCEPTION
            WHEN OTHERS THEN
                DBMS_OUTPUT.PUT_LINE('✗ Error verifying auto payment settings: ' || SQLERRM);
        END;
    END IF;
    
    -- Cleanup
    DBMS_OUTPUT.PUT_LINE('Cleaning up test data...');
    
    BEGIN
        IF vIpcId1 IS NOT NULL THEN
            DELETE FROM invoice_provider_clients WHERE id = vIpcId1;
        END IF;
        
        IF vIpcId2 IS NOT NULL THEN
            DELETE FROM invoice_provider_clients WHERE id = vIpcId2;
        END IF;
        
        DBMS_OUTPUT.PUT_LINE('✓ Cleanup completed');
        
    EXCEPTION
        WHEN OTHERS THEN
            DBMS_OUTPUT.PUT_LINE('✗ Error during cleanup: ' || SQLERRM);
    END;
    
    DBMS_OUTPUT.PUT_LINE('=== Testing completed ===');
    
END;
/
