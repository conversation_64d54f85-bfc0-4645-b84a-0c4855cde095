PROMPT Creating package body script for package TRANPAYS_PCK
CREATE OR REPLACE
PACKAGE BODY mflex.tranpays_pck
IS

/*
/App-DB/Flex/TranPays/err/InvalidStatus
/App-DB/Flex/TranPays/err/NoCancelPrivilege
/App-DB/Flex/TranPays/err/invalidReqType
/App-DB/Flex/TranPays/err/InvalidSchedule
/App-DB/Flex/TranPays/err/NoCreatePrivilege
/App-DB/Flex/TranPays/err/InvalidTranpay
/App-DB/Flex/TranPays/err/NoSignPrivilege
/App-DB/Flex/TranPays/err/NoPrivsForAccOwner
/App-DB/Flex/TranPays/err/NoAccount
/App-DB/Flex/TranPays/err/InvalidAttachmentId
*/
-- Sistem constants
    cREF_CLASS CONSTANT VARCHAR2(21) := 'MFLEX.TRANPAYS_PCK.';

    cERR_InvalidStatus      CONSTANT VARCHAR2(39) := pkgCtxId || '/err/InvalidStatus';
    cERR_NoCancelPrivilege  CONSTANT VARCHAR2(43) := pkgCtxId || '/err/NoCancelPrivilege';
    cERR_InvalidSchedule    CONSTANT VARCHAR2(41) := pkgCtxId || '/err/InvalidSchedule';
    cERR_NoCreatePrivilege  CONSTANT VARCHAR2(43) := pkgCtxId || '/err/NoCreatePrivilege';
    cERR_InvalidTranpay     CONSTANT VARCHAR2(40) := pkgCtxId || '/err/InvalidTranpay';
    cERR_NoPrivsForAccOwner CONSTANT VARCHAR2(44) := pkgCtxId || '/err/NoPrivsForAccOwner';
    cERR_NoAccount          CONSTANT VARCHAR2(35) := pkgCtxId || '/err/NoAccount';
    cERR_InvalidAttachmentId  CONSTANT VARCHAR2(45) := pkgCtxId || '/err/InvalidAttachmentId';

    FUNCTION stringify_status_list(p_list mcore.table_of_varchar2_60) 
      RETURN VARCHAR2 IS v_result VARCHAR2(4000) := '';
      BEGIN
        IF p_list IS NOT NULL THEN
            FOR i IN 1 .. p_list.COUNT LOOP
                v_result := v_result || p_list(i) || ',';
            END LOOP;
            v_result := RTRIM(v_result, ',');
        END IF;
        RETURN v_result;
    END;

    FUNCTION getFirstSchedule(pTranpayId mcore.tranpays.id%TYPE)
    RETURN DATE IS
    BEGIN
        RETURN mcore.tranpays_pck.getFirstSchedule(pTranpayId);
    END getFirstSchedule;

    FUNCTION getTranpayRequestType(pTranpayId mcore.tranpays.id%TYPE)
    RETURN mcore.tranpays.req_type_id%TYPE
    IS
    BEGIN
        RETURN mcore.tranpays_pck.getTranpayRequestType(pTranpayId);
    END getTranpayRequestType;

    FUNCTION p_isValidTranpayGroup(pTranpayGroupId mcore.tranpay_groups.id%TYPE)
    RETURN PLS_INTEGER IS
    BEGIN
        RETURN mcore.tranpays_pck.p_isValidTranpayGroup(pTranpayGroupId);
    END p_isValidTranpayGroup;

    FUNCTION isValidTranpayGroup(pTranpayGroupId mcore.tranpay_groups.id%TYPE)
    RETURN BOOLEAN IS
    BEGIN
        RETURN p_isValidTranpayGroup(pTranpayGroupId) > 0;
    END isValidTranpayGroup;

    FUNCTION p_isValidCOA(pCoaId mcore.charts_of_accounts.id%TYPE)
    RETURN PLS_INTEGER IS
    BEGIN
        RETURN mcore.tranpays_pck.p_isValidCOA(pCoaId);
    END p_isValidCOA;

    FUNCTION isValidCOA(pCoaId mcore.charts_of_accounts.id%TYPE)
    RETURN BOOLEAN IS
    BEGIN
        RETURN p_isValidCOA(pCoaId) > 0;
    END isValidCOA;

    FUNCTION p_isValidSchedule(pSchedulesId mcore.schedules.id%TYPE)
    RETURN PLS_INTEGER IS
    BEGIN
        RETURN mcore.tranpays_pck.p_isValidSchedule(pSchedulesId);
    END p_isValidSchedule;

    FUNCTION isValidSchedule(pSchedulesId mcore.schedules.id%TYPE)
    RETURN BOOLEAN IS
    BEGIN
        RETURN p_isValidSchedule(pSchedulesId) > 0;
    END isValidSchedule;

    FUNCTION p_tranpayExists(pTranpayId mcore.tranpays.id%TYPE)
    RETURN PLS_INTEGER IS
    BEGIN
        RETURN mcore.tranpays_pck.p_tranpayExists(pTranpayId);
    END p_tranpayExists;

    FUNCTION tranpayExists(pTranpayId mcore.tranpays.id%TYPE)
    RETURN BOOLEAN IS
    BEGIN
        RETURN p_tranpayExists(pTranpayId) > 0;
    END tranpayExists;

    FUNCTION p_tranpayExists(pTranpayId mcore.tranpays.id%TYPE, pTranpayStatus mcore.tranpays.status%TYPE)
    RETURN PLS_INTEGER IS
    BEGIN
        RETURN mcore.tranpays_pck.p_tranpayExists(pTranpayId, pTranpayStatus);
    END p_tranpayExists;

    FUNCTION tranpayExists(pTranpayId mcore.tranpays.id%TYPE, pTranpayStatus mcore.tranpays.status%TYPE)
    RETURN BOOLEAN IS
    BEGIN
        RETURN p_tranpayExists(pTranpayId, pTranpayStatus) > 0;
    END tranpayExists;

    FUNCTION p_isValidTranpayAttribute(pRequestTypeId mcore.tranpay_attribs.req_type_id%TYPE,
                                       pTranpayAttribId mcore.tranpay_attribs.id%TYPE)
    RETURN PLS_INTEGER IS
    BEGIN
		common_pck.CommonSecurityChecks;
        RETURN mcore.tranpays_pck.p_isValidTranpayAttribute(pRequestTypeId, pTranpayAttribId);
    END p_isValidTranpayAttribute;

    FUNCTION isValidTranpayAttribute(pRequestTypeId mcore.tranpay_attribs.req_type_id%TYPE,
                                     pTranpayAttribId mcore.tranpay_attribs.id%TYPE)
    RETURN BOOLEAN IS
    BEGIN
        RETURN p_isValidTranpayAttribute(pRequestTypeId, pTranpayAttribId) > 0;
    END isValidTranpayAttribute;

    -- TEMPLATE PACKAGES
    -- Creating and manipulating template packages
    FUNCTION CreateNewTemplatePackage(
        pName mcore.template_packages.name%TYPE,
        pDescription mcore.template_packages.description%TYPE)
    RETURN mcore.template_packages.id%TYPE
    IS
    BEGIN
        RETURN mcore.tranpays_pck.CreateNewTemplatePackage(pName, pDescription);
    END CreateNewTemplatePackage;

    PROCEDURE UpdateExistingTemplatePackage(
        pId mcore.template_packages.id%TYPE,
        pName mcore.template_packages.name%TYPE,
        pDescription mcore.template_packages.description%TYPE,
        pValid mcore.template_packages.valid%TYPE)
    IS
    BEGIN
        mcore.tranpays_pck.UpdateExistingTemplatePackage(pId, pName, pDescription, pValid);
    END UpdateExistingTemplatePackage;

    -- Template packages retrieving
    FUNCTION getTemplatePackagesListCount(pRequestTypeId mcore.tranpay_templates.request_type_id%TYPE)
    RETURN PLS_INTEGER
    IS
    BEGIN
        RETURN mcore.tranpays_pck.getTemplatePackagesListCount(pRequestTypeId);
    END getTemplatePackagesListCount;

    FUNCTION getTemplatePackagesList(pOffset PLS_INTEGER := 1, pArraySize PLS_INTEGER := 10)
    RETURN sys_refcursor
    IS
    BEGIN
      RETURN mcore.tranpays_pck.getTemplatePackagesList(pOffset, pArraySize);
    END getTemplatePackagesList;

    -- TRANPAY TEMPLATES
    -- Creating and manipulating tranpay templates
    FUNCTION CreateNewTranpayTemplate(
            pName mcore.tranpay_templates.name%TYPE,
            pDescription mcore.tranpay_templates.description%TYPE,
            pRequest_type_id mcore.tranpay_templates.request_type_id%TYPE,
            pTranpay_description mcore.tranpay_templates.tranpay_description%TYPE,
            pInternal_description mcore.tranpay_templates.internal_description%TYPE,
            pAccount_id mcore.tranpay_templates.account_id%TYPE,
            pTranval mcore.tranpay_templates.tranval%TYPE,
            pCurrency_id mcore.tranpay_templates.currency_id%TYPE,
            pTest_record mcore.tranpay_templates.test_record%TYPE,
            pTemplate_package_id mcore.tranpay_templates.template_package_id%TYPE)
    RETURN mcore.tranpay_templates.id%TYPE
    IS
    BEGIN
        RETURN mcore.tranpays_pck.CreateNewTranpayTemplate(pName, pDescription, pRequest_type_id, pTranpay_description, pInternal_description,
                        pAccount_id, pTranval, pCurrency_id, pTest_record, pTemplate_package_id);

    END CreateNewTranpayTemplate;

    FUNCTION CreateTemplateFromTranpay(
                pTranpayId mcore.tranpays.id%TYPE,
                pName mcore.tranpay_templates.name%TYPE,
                pDescription mcore.tranpay_templates.description%TYPE,
                pTemplate_package_id mcore.tranpay_templates.template_package_id%TYPE)
    RETURN mcore.tranpay_templates.id%TYPE
    IS
    BEGIN
        RETURN mcore.tranpays_pck.CreateTemplateFromTranpay (pTranpayId, pName,
                pDescription, pTemplate_package_id);
    END CreateTemplateFromTranpay;

    PROCEDURE UpdateExistingTranpayTemplate(
            pId mcore.tranpay_templates.id%TYPE,
            pName mcore.tranpay_templates.name%TYPE,
            pDescription mcore.tranpay_templates.description%TYPE,
            pRequest_type_id mcore.tranpay_templates.request_type_id%TYPE,
            pTranpay_description mcore.tranpay_templates.tranpay_description%TYPE,
            pInternal_description mcore.tranpay_templates.internal_description%TYPE,
            pAccount_id mcore.tranpay_templates.account_id%TYPE,
            pTranval mcore.tranpay_templates.tranval%TYPE,
            pCurrency_id mcore.tranpay_templates.currency_id%TYPE,
            pTest_record mcore.tranpay_templates.test_record%TYPE,
            pTemplate_package_id mcore.tranpay_templates.template_package_id%TYPE,
            pValid mcore.tranpay_templates.valid%TYPE)
    IS
    BEGIN
        mcore.tranpays_pck.UpdateExistingTranpayTemplate(pId, pName, pDescription, pRequest_type_id, pTranpay_description, pInternal_description,
                        pAccount_id, pTranval, pCurrency_id, pTest_record, pTemplate_package_id, pValid);

    END UpdateExistingTranpayTemplate;

    PROCEDURE addTranpayTemplateToPackage(pId mcore.tranpay_templates.id%TYPE,
            pTemplate_package_id mcore.tranpay_templates.template_package_id%TYPE)
    IS
    BEGIN
        mcore.tranpays_pck.addTranpayTemplateToPackage(pId => pId, pTemplate_package_id => pTemplate_package_id);
    END addTranpayTemplateToPackage;

    PROCEDURE remTranpayTemplateFromPackage(pId mcore.tranpay_templates.id%TYPE)
    IS
    BEGIN
        mcore.tranpays_pck.remTranpayTemplateFromPackage(pId => pId);
    END remTranpayTemplateFromPackage;

    PROCEDURE removeTemplatesFromPackage(pId IN mcore.template_packages.id%TYPE)
    IS
    BEGIN
        mcore.tranpays_pck.removeTemplatesFromPackage(pId => pId);
    END removeTemplatesFromPackage;

    PROCEDURE RemoveTemplatePackage(
        pId mcore.template_packages.id%TYPE,
        pForceRemoval BOOLEAN DEFAULT FALSE)
    IS
    BEGIN
        mcore.tranpays_pck.RemoveTemplatePackage(pId => pId, pForceRemoval => pForceRemoval);
    END;

    PROCEDURE removeTranpayTemplate(pId mcore.tranpay_templates.id%TYPE)
    IS
    BEGIN
        mcore.tranpays_pck.removeTranpayTemplate(pId);
    END removeTranpayTemplate;

    -- Tranpay templates retrieving

    FUNCTION getTranpayTemplatesListCount(pRequestTypeId mcore.tranpay_templates.request_type_id%TYPE)
    RETURN PLS_INTEGER
    IS
        myunit CONSTANT VARCHAR2(28) := 'getTranpayTemplatesListCount';

        vCount PLS_INTEGER := 0;
    BEGIN
        slog.debug(pkgCtxId, myUnit, pRequestTypeId);
        common_pck.CommonSecurityChecks;

        SELECT COUNT(tt.id)
          INTO vCount
          FROM mcore.VW$USER_TRANPAY_TEMPLATES tt
          WHERE request_type_id LIKE pRequestTypeId;

        slog.debug(pkgCtxId, myUnit, 'Request tranpay templates count for user ' || auth.getSCID || ' and account owner ' || auth.getAccountOwner || ' and reqType ' || pRequestTypeId || ' => Will fetch ' || vCount || ' records!');

        RETURN vCount;
    END getTranpayTemplatesListCount;

    FUNCTION getTranpayTemplatesList(pRequestTypeId mcore.tranpay_templates.request_type_id%TYPE,
                                     pOffset PLS_INTEGER := 1, pArraySize PLS_INTEGER := 10)
    RETURN sys_refcursor
    IS
        myunit CONSTANT VARCHAR2(23) := 'getTranpayTemplatesList';

        vOffset PLS_INTEGER;
        vArraySize PLS_INTEGER;

        rez sys_refcursor;

    BEGIN
        slog.debug(pkgCtxId, myUnit, pRequestTypeId || ':' || pOffset || ':' || pArraySize);
        common_pck.CommonSecurityChecks;

        common_pck.SetOffsetArraySize(pkgCtxId, myunit,
                                      pOffset, pArraySize,
                                      vOffset, vArraySize);

        slog.debug(pkgCtxId, myUnit, 'Request tranpay templates for user ' || auth.getSCID || ' and account owner ' || auth.getAccountOwner || ' and reqType ' || pRequestTypeId);

        if (auth.getAccountOwner is null or auth.getSCID is null) then
          slog.warn(pkgCtxId, myUnit, 'Request tranpay templates for user ' || auth.getSCID || ' and account owner ' || auth.getAccountOwner);
        end if;

        OPEN rez FOR
        SELECT id, name, description, request_type_id, tranpay_description, internal_description,
                account_id, tranval, currency_id, template_package_id, valid FROM
          (SELECT tt.id id,
               tt.name name,
               tt.description description,
               tt.request_type_id request_type_id,
               tt.tranpay_description tranpay_description,
               tt.internal_description internal_description,
               tt.account_id account_id,
               tt.tranval tranval,
               tt.currency_id currency_id,
               tt.template_package_id template_package_id,
               tt.valid,
                   ROW_NUMBER() OVER (ORDER BY tt.name ASC) rn
          FROM mcore.VW$USER_TRANPAY_TEMPLATES tt
          WHERE request_type_id LIKE pRequestTypeId)
          WHERE rn BETWEEN vOffset AND (vOffset + vArraySize)
          ORDER BY rn ASC;

        RETURN rez;
    END getTranpayTemplatesList;

    FUNCTION getTranpayTemplatesListForPack(pTemplatePackageId mcore.tranpay_templates.template_package_id%TYPE,
                                pOffset PLS_INTEGER := 1, pArraySize PLS_INTEGER := 10)
    RETURN sys_refcursor
    IS
        myunit CONSTANT VARCHAR2(30) := 'getTranpayTemplatesListForPack';

        vOffset PLS_INTEGER;
        vArraySize PLS_INTEGER;

        rez sys_refcursor;
    BEGIN
        slog.debug(pkgCtxId, myUnit, pTemplatePackageId || ':' || pOffset || ':' || pArraySize);
        common_pck.CommonSecurityChecks;

        common_pck.SetOffsetArraySize(pkgCtxId, myunit,
                                      pOffset, pArraySize,
                                      vOffset, vArraySize);

        OPEN rez FOR
        SELECT id, name, description, request_type_id, tranpay_description, internal_description,
                account_id, tranval, currency_id, template_package_id, valid FROM
          (SELECT tt.id, tt.name, tt.description, tt.request_type_id,
               tt.tranpay_description, tt.internal_description, tt.account_id,
               tt.tranval, tt.currency_id, tt.template_package_id, tt.valid,
               ROW_NUMBER() OVER (ORDER BY tt.name ASC) rn
          FROM mcore.VW$USER_TRANPAY_TEMPLATES tt
         WHERE tt.template_package_id = pTemplatePackageId)
          WHERE rn BETWEEN vOffset AND (vOffset + vArraySize)
          ORDER BY rn ASC;

        RETURN rez;
    END getTranpayTemplatesListForPack;


    -- TRANPAY TEMPLATE DETAILS
    -- Tranpays Templates details manipulation
    PROCEDURE AppendTranpayTemplateDetail(pTranpayTemplateId mcore.tranpay_template_details.tranpay_template_id%TYPE,
                                        pAttributeReqTypeId mcore.tranpay_template_details.tranpay_attrib_req_type_id%TYPE,
                                        pAttributeId mcore.tranpay_template_details.tranpay_attrib_id%TYPE,
                                        pTranpayTemplateDetDesc mcore.tranpay_template_details.description%TYPE,
                                        pTranpayTemplateDetDataVChar mcore.tranpay_template_details.data_vchar%TYPE,
                                        pTranpayTemplateDetDataBlob mcore.tranpay_template_details.data_blob%TYPE
                                        )
    IS
    BEGIN
        mcore.tranpays_pck.AppendTranpayTemplateDetail(pTranpayTemplateId, pAttributeReqTypeId, pAttributeId, pTranpayTemplateDetDesc, pTranpayTemplateDetDataVChar, pTranpayTemplateDetDataBlob);
    END AppendTranpayTemplateDetail;

    PROCEDURE VerifyTemplate(pTranpayTemplateId mcore.tranpay_templates.id%TYPE) IS
    BEGIN
        mcore.tranpays_pck.VerifyTemplate(pTranpayTemplateId);
    END VerifyTemplate;

    -- Tranpay templates details retrieving
    FUNCTION getTranpayTemplateDetails(pTranpayTemplateId mcore.tranpay_templates.id%TYPE,
                                pReqestTypeId mcore.tranpay_attribs.req_type_id%TYPE)
    RETURN sys_refcursor
    IS
    BEGIN
        RETURN mcore.tranpays_pck.getTranpayTemplateDetails(pTranpayTemplateId, pReqestTypeId);
    END getTranpayTemplateDetails;

    FUNCTION getAllTranpayTemplateDetails
    RETURN sys_refcursor
    IS
        myunit CONSTANT VARCHAR2(28) := 'getAllTranpayTemplateDetails';
        rez sys_refcursor;
    BEGIN
        slog.debug(pkgCtxId, myUnit);
        common_pck.CommonSecurityChecks;

        OPEN rez FOR
        SELECT  ta.req_type_id, ta.id, ta.description AttribDescription, ta.basic_datatype, ta.maxsize,
                ta.value_required, ta.blob_mimetype, ta.regexp,
                ttd.tranpay_template_id template_id, ttd.description DetailDescription, ttd.data_vchar, ttd.data_blob
        FROM    mcore.tranpay_attribs ta,
                mcore.tranpay_template_details ttd,
                mcore.VW$USER_TRANPAY_TEMPLATES tt
         WHERE ttd.tranpay_attrib_req_type_id = ta.req_type_id
           AND ttd.tranpay_attrib_id = ta.id
           AND ttd.tranpay_template_id = tt.id;

        RETURN rez;
    END getAllTranpayTemplateDetails;

    -- Procedure za upravljanjem vremenskim okvirima (scheduler)
    FUNCTION CreateNewSchedule(pScheduleType mcore.schedule_types.type%TYPE,
            pActive mcore.schedules.active%TYPE,
            pExecuteNotBefore mcore.schedules.not_before%TYPE,
            pExecuteNotAfter mcore.schedules.not_after%TYPE,
            pMaxRetryCount mcore.schedules.max_retry_count%TYPE,
            pRetryInterval mcore.schedules.retry_interval%TYPE,
            pRetryIntervalUnit mcore.schedules.retry_int_unit%TYPE,
            pMinBalance mcore.schedules.min_balance%TYPE,
            pHour PLS_INTEGER,
            pMinute PLS_INTEGER,
            pDay PLS_INTEGER,
            pMonth PLS_INTEGER,
            pYear PLS_INTEGER)
    RETURN mcore.schedules.id%TYPE IS
    BEGIN
        RETURN mcore.tranpays_pck.CreateNewSchedule(pScheduleType, pActive, pExecuteNotBefore, pExecuteNotAfter,
            pMaxRetryCount, pRetryInterval, pRetryIntervalUnit, pMinBalance, pHour, pMinute, pDay, pMonth, pYear);
    END CreateNewSchedule;

    FUNCTION getTranpaySchedule(pTranpayId mcore.tranpays.id%TYPE)
    RETURN sys_refcursor IS
    BEGIN
        RETURN mcore.tranpays_pck.getTranpaySchedule(pTranpayId);
    END getTranpaySchedule;

    FUNCTION getTranpaySchedule
    RETURN sys_refcursor IS
    BEGIN
        RETURN mcore.tranpays_pck.getTranpaySchedule;
    END getTranpaySchedule;

    FUNCTION getSchedules(pScheduleId mcore.schedules.id%TYPE)
    RETURN sys_refcursor IS
    BEGIN
        RETURN mcore.tranpays_pck.getSchedules(pScheduleId);
    END getSchedules;

    FUNCTION getActiveSchedules(pOffset PLS_INTEGER := 1,
                        pArraySize PLS_INTEGER := 10)
    RETURN sys_refcursor IS
    BEGIN
        RETURN mcore.tranpays_pck.getActiveSchedules(pOffset, pArraySize);
    END getActiveSchedules;

    PROCEDURE CancelSchedules(pScheduleId mcore.tranpays.schedule_id%TYPE, pStatusMessage mcore.tranpays.status_message%TYPE := NULL,
      pCancelFutureTranpays VARCHAR2 := 'N')
    IS
        myunit CONSTANT VARCHAR2(15) := 'CancelSchedules';

        vRequestTypeId mcore.tranpays.req_type_id%TYPE;
        vAccountId mcore.tranpays.account_id%TYPE;

    BEGIN
        slog.debug(pkgCtxId, myUnit, pScheduleId || ':' || pStatusMessage);
        common_pck.CommonSecurityChecks;

        select req_type_id, account_id
        into vRequestTypeId, vAccountId
        from (
                select id, req_type_id, account_id
                  from mcore.tranpays
                 where schedule_id = pScheduleId
                order by id desc)
        where rownum < 2;

        IF NOT mcore.authorization_pck.hasGrantOnCancelTranpay(
                pAccountId => vAccountId,
                pRequestTypeId => vRequestTypeId)
        THEN
            sspkg.raiseError(cERR_NoCancelPrivilege, null, pkgCtxId,myunit);
        END IF;

        mcore.tranpays_pck.CancelSchedules(pScheduleId, pStatusMessage, pCancelFutureTranpays);
    EXCEPTION
        WHEN no_data_found THEN
            sspkg.raiseError(cERR_InvalidSchedule, null, pkgCtxId,myunit);
    END CancelSchedules;

    -- TRANPAY GROUPS
    FUNCTION CreateNewTranpayGroup(pName mcore.tranpay_groups.name%TYPE, pDescription mcore.tranpay_groups.description%TYPE, pTranpayGroupTypeId NUMBER DEFAULT NULL)
    RETURN mcore.tranpay_groups.id%TYPE IS
    BEGIN
        RETURN mcore.tranpays_pck.CreateNewTranpayGroup(pName, pDescription, pTranpayGroupTypeId);
    END CreateNewTranpayGroup;

    FUNCTION getTranpayGroupData(pTranpayGroupId mcore.tranpay_groups.id%TYPE)
    RETURN sys_refcursor IS
    BEGIN
        RETURN mcore.tranpays_pck.getTranpayGroupData(pTranpayGroupId);
    END getTranpayGroupData;

    FUNCTION getTranpayGroupsListCount(pRtBasicType mcore.request_types.basic_type%TYPE := '%')
    RETURN PLS_INTEGER IS
    BEGIN
        RETURN mcore.tranpays_pck.getTranpayGroupsListCount(pRtBasicType);
    END getTranpayGroupsListCount;

    FUNCTION getTranpayGroupsList(pOffset PLS_INTEGER := 1, pArraySize PLS_INTEGER := 10, pRtBasicType mcore.request_types.basic_type%TYPE := '%')
    RETURN sys_refcursor IS
    BEGIN
        RETURN mcore.tranpays_pck.getTranpayGroupsList(pOffset, pArraySize, pRtBasicType);
    END getTranpayGroupsList;

    -- TRANPAY ATTRIBUTES
    FUNCTION getTranpayAttribute(pRequestTypeId mcore.tranpay_attribs.req_type_id%TYPE,
                                 pTranpayAttribId mcore.tranpay_attribs.id%TYPE)
    RETURN sys_refcursor IS
    BEGIN
        RETURN mcore.tranpays_pck.getTranpayAttribute(pRequestTypeId,pTranpayAttribId);
    END getTranpayAttribute;

    FUNCTION getTranpayAttributesListCount(pRequestTypeId mcore.tranpay_attribs.req_type_id%TYPE DEFAULT '%')
    RETURN PLS_INTEGER IS
    BEGIN
        RETURN mcore.tranpays_pck.getTranpayAttributesListCount(pRequestTypeId);
    END getTranpayAttributesListCount;

    FUNCTION getTranpayAttributesList(pRequestTypeId mcore.tranpay_attribs.req_type_id%TYPE DEFAULT '%')
    RETURN sys_refcursor IS
    BEGIN
        RETURN mcore.tranpays_pck.getTranpayAttributesList(pRequestTypeId);
    END getTranpayAttributesList;

    FUNCTION getTranpayAttribute(pRequestTypeId mcore.tranpay_attribs.req_type_id%TYPE,
                                 pTranpayAttribId mcore.tranpay_attribs.id%TYPE,
                                 pColumnName VARCHAR2)
    RETURN VARCHAR2 IS
    BEGIN
		common_pck.CommonSecurityChecks;
        RETURN mcore.tranpays_pck.getTranpayAttribute(pRequestTypeId, pTranpayAttribId, pColumnName);
    END getTranpayAttribute;

    -- TRANPAYS
    FUNCTION CreateNewTranPay(pRequestTypeId mcore.tranpays.req_type_id%TYPE,
        pDescription mcore.tranpays.description%TYPE,
        pInternalDescription mcore.tranpays.internal_description%TYPE,
        pTranval mcore.tranpays.tranval%TYPE,
        pTranvalCurrencyId mcore.tranpays.tranval_currency_id%TYPE,
        pTranpayGroupId mcore.tranpays.tranpay_group_id%TYPE,
        pAccountId mcore.tranpays.account_id%TYPE,
        pChartOfAccountsId mcore.tranpays.chart_of_accounts_id%TYPE,
        pScheduleId mcore.tranpays.schedule_id%TYPE,
        pTestRecord mcore.tranpays.test_record%TYPE,
		pExtRef mcore.tranpays.extref%TYPE DEFAULT NULL)
    RETURN mcore.tranpays.id%TYPE IS
        myunit CONSTANT VARCHAR2(16) := 'CreateNewTranPay';
    BEGIN
        slog.debug(pkgCtxId, myUnit, pRequestTypeId || ':' || pDescription || ':' || pInternalDescription || ':' || pTranval || ':' || pTranvalCurrencyId || ':' || pTranpayGroupId || ':' || pAccountId
          || ':' || pChartOfAccountsId || ':' || pScheduleId || ':' || pTestRecord);
        common_pck.CommonSecurityChecks;

        IF (pAccountId IS NOT NULL) AND (NOT mcore.authorization_pck.hasGrantOnCreateTranpay(
            pAccountId => pAccountId, pRequestTypeId => pRequestTypeId))
        THEN
 	    slog.error(pkgCtxId, myUnit, cERR_NoCreatePrivilege, pAccountId);
            sspkg.raiseError(cERR_NoCreatePrivilege, null, pkgCtxId, myunit);
        END IF;

        RETURN mcore.tranpays_pck.CreateNewTranPay(pRequestTypeId, pDescription, pInternalDescription,
            pTranval, pTranvalCurrencyId, pTranpayGroupId, pAccountId,
            pChartOfAccountsId, pScheduleId, pTestRecord, pExtRef);

    END CreateNewTranPay;

    FUNCTION CreateTranpaysForTemplPackage(pTemplatePackageId mcore.template_packages.id%TYPE)
    RETURN PLS_INTEGER
    IS
        myunit CONSTANT VARCHAR2(29) := 'CreateTranpaysForTemplPackage';
    BEGIN
        slog.debug(pkgCtxId, myUnit, pTemplatePackageId);

        RETURN mcore.tranpays_pck.CreateTranpaysForTemplPackage(pTemplatePackageId);
    END CreateTranpaysForTemplPackage;
	
	PROCEDURE UpdateTranpayDescription(
		pTranpayId mcore.tranpays.id%TYPE,
		pDescription mcore.tranpays.description%TYPE)
	IS
	BEGIN
		mcore.tranpays_pck.UpdateTranpayDescription(pTranpayId, pDescription);
	END UpdateTranpayDescription;	
	
	PROCEDURE UpdateBeneficiary(
		pTranpayId IN mcore.tranpays.id%TYPE,
		pBeneficiaryAccountId IN VARCHAR2,
		pBeneficiaryName IN VARCHAR2)
	IS
	BEGIN
		mcore.tranpays_pck.UpdateBeneficiary(pTranpayId, pBeneficiaryAccountId, pBeneficiaryName);
	END UpdateBeneficiary;

    PROCEDURE UpdateTranpay(pTranpayId mcore.tranpays.id%TYPE,
        pDescription mcore.tranpays.description%TYPE,
        pInternalDescription mcore.tranpays.internal_description%TYPE,
        pTranval mcore.tranpays.tranval%TYPE,
        pTranvalCurrencyId mcore.tranpays.tranval_currency_id%TYPE,
        pTranpayGroupId mcore.tranpays.tranpay_group_id%TYPE,
        pAccountId mcore.tranpays.account_id%TYPE,
        pChartOfAccountsId mcore.tranpays.chart_of_accounts_id%TYPE,
        pScheduleId mcore.tranpays.schedule_id%TYPE,
        pTestRecord mcore.tranpays.test_record%TYPE)
    IS
        myunit CONSTANT VARCHAR2(13) := 'UpdateTranpay';
        vRequestTypeId mcore.tranpays.req_type_id%TYPE;
    BEGIN
        slog.debug(pkgCtxId, myUnit, pTranpayId || ':' || pDescription || ':' || pInternalDescription || ':' || pTranval || ':' || pTranvalCurrencyId || ':' || pTranpayGroupId || ':' || pAccountId
          || ':' || pChartOfAccountsId || ':' || pScheduleId || ':' || pTestRecord);
        common_pck.CommonSecurityChecks;

        vRequestTypeId := getTranpayRequestType(pTranpayId);

        IF (pAccountId IS NOT NULL) AND (NOT mcore.authorization_pck.hasGrantOnCreateTranpay(pAccountId, vRequestTypeId)) THEN
	    slog.error(pkgCtxId, myUnit, cERR_NoCreatePrivilege, pAccountId);
            sspkg.raiseError(cERR_NoCreatePrivilege, null, pkgCtxId,myunit);
        END IF;

        mcore.tranpays_pck.UpdateTranpay(pTranpayId, pDescription, pInternalDescription,
            pTranval, pTranvalCurrencyId, pTranpayGroupId, pAccountId, pChartOfAccountsId,
            pScheduleId, pTestRecord, vRequestTypeId);

    END UpdateTranpay;

    PROCEDURE addTranpayToGroup(pTranpayId mcore.tranpays.id%TYPE,
                                pTranpayGroupId mcore.tranpays.tranpay_group_id%TYPE)
    IS
    BEGIN
        mcore.tranpays_pck.addTranpayToGroup(pTranpayId, pTranpayGroupId);
    END addTranpayToGroup;

    FUNCTION addTranpayToGroup(pTranpayList mcore.table_of_integer,
                                pTranpayGroupId mcore.tranpays.tranpay_group_id%TYPE)
    RETURN mcore.table_of_integer
    IS
    BEGIN
        RETURN mcore.tranpays_pck.addTranpayToGroup(pTranpayList, pTranpayGroupId);
    END addTranpayToGroup;

    PROCEDURE removeTranpayFromGroup(pTranpayId mcore.tranpays.id%TYPE)
    IS
    BEGIN
        mcore.tranpays_pck.removeTranpayFromGroup(pTranpayId);
    END removeTranpayFromGroup;

    FUNCTION removeTranpayFromGroup(pTranpayList mcore.table_of_integer)
    RETURN mcore.table_of_integer
    IS
    BEGIN
         RETURN mcore.tranpays_pck.removeTranpayFromGroup(pTranpayList);
    END removeTranpayFromGroup;

    PROCEDURE CancelTranpay(pTranpayId mcore.tranpays.id%TYPE, pStatusMessage mcore.tranpays.status_message%TYPE := NULL,
      pCancelFutureTranpays VARCHAR2 := 'Y')
    IS
        myunit CONSTANT VARCHAR2(13) := 'CancelTranpay';
        vRequestType mcore.tranpays.req_type_id%TYPE;
        vBasicType mcore.request_types.basic_type%TYPE;
        vTranpayCurrentStatus mcore.tranpays.status%TYPE;
        vAccountId mcore.bank_accounts.id%TYPE;
        vUserId mcore.tranpays.user_id%TYPE;
    BEGIN
        slog.debug(pkgCtxId, myUnit, pTranpayId || ':' || pStatusMessage || ':' || pCancelFutureTranpays);
        common_pck.CommonSecurityChecks;

        SELECT req_type_id, status, account_id, user_id
          INTO vRequestType, vTranpayCurrentStatus, vAccountId, vUserId
          FROM mcore.tranpays a
         WHERE id = pTranpayId;

        vBasicType := requests_pck.getRequestBasicType(vRequestType);

        IF NOT requests_pck.isValidStatusTransition(vBasicType, vTranpayCurrentStatus, mcore.common_pck.cTRPSTS_UC) THEN
            sspkg.raiseError(cERR_InvalidStatus, null, pkgCtxId, myunit);
        END IF;

        IF vTranpayCurrentStatus <> mcore.common_pck.cTRPSTS_UW THEN
            -- If user has no privilege to cancel tranpay, raise error
            IF NOT mcore.authorization_pck.hasGrantOnCancelTranpay(vAccountId, vRequestType) THEN
	      slog.error(pkgCtxId, myUnit, cERR_NoCancelPrivilege, vAccountId);
              sspkg.raiseError(cERR_NoCancelPrivilege, null, pkgCtxId, myunit);
            END IF;
        ELSE
          IF vUserId <> mcauth.auth.getClientId THEN
            sspkg.raiseError(cERR_NoCancelPrivilege, null, pkgCtxId, myunit);
          END IF;
        END IF;

        mcore.tranpays_pck.CancelTranpay(pTranpayId, pStatusMessage, pCancelFutureTranpays);
    EXCEPTION
        WHEN no_data_found THEN
	    slog.error(pkgCtxId, myUnit, cERR_InvalidTranpay, pTranpayId);
            sspkg.raiseError(cERR_InvalidTranpay, null, pkgCtxId, myunit);
    END CancelTranpay;

    FUNCTION CancelTranpay(pTranpayList mcore.table_of_integer, pStatusMessage mcore.tranpays.status_message%TYPE := NULL,
       pCancelFutureTranpays VARCHAR2 := 'Y')
    RETURN mcore.table_of_integer
    IS
    BEGIN
        RETURN mcore.tranpays_pck.CancelTranpay(pTranpayList, pStatusMessage,pCancelFutureTranpays );
    END CancelTranpay;

    PROCEDURE MarkAsDone(pTranpayId mcore.tranpays.id%TYPE)
    IS
    BEGIN
        mcore.tranpays_pck.MarkAsDone(pTranpayId);
    END MarkAsDone;

    -- Experimental
    FUNCTION MarkAsDone(pTranpayList mcore.table_of_integer)
    RETURN mcore.tranpay_import_result_list
    IS
    BEGIN
        -- INSERT INTO mcore.tmp$tranpays (id) SELECT column_value FROM TABLE(pTranpayList);
        RETURN mcore.tranpays_pck.MarkAsDone(pTranpayList);
    END MarkAsDone;

    PROCEDURE MarkForSigning(pTranpayId mcore.tranpays.id%TYPE,
                             pAccountId mcore.bank_accounts.id%TYPE := NULL)
    IS
        myunit CONSTANT VARCHAR2(14) := 'MarkForSigning';
    BEGIN
        slog.debug(pkgCtxId, myUnit, pTranpayId || ':' || pAccountId);
		
		mcore.tranpays_pck.MarkForSigning(
			pTranpayId => pTranpayId, 
			pCheckRequestType => FALSE, 
			pAppIdentifier => NULL);

    END MarkForSigning;

    FUNCTION MarkForSigning(pTranpayList mcore.table_of_integer)
	RETURN mcore.tranpay_import_result_list
    IS
    BEGIN
        RETURN mcore.tranpays_pck.MarkForSigning(
            pTranpayList => pTranpayList, 
            pCheckRequestType => FALSE,
	    pAppIdentifier => NULL);
    END MarkForSigning;

    FUNCTION SignTranpay( pChallenge varchar2 := NULL,
                          pResponse varchar2 := NULL,
                          pOtp varchar2 := NULL,
                          pSourceData varchar2 := NULL,
                          pSignature VARCHAR2 := NULL)
    RETURN PLS_INTEGER IS
        myunit CONSTANT VARCHAR2(11) := 'SignTranpay';
    BEGIN
        slog.debug(pkgCtxId, myUnit, pChallenge || ':' || pResponse || ':' || pOtp || ':' || pSourceData ||':'||pSignature);
		RETURN mcore.tranpays_pck.SignTranpay( pChallenge, pResponse, pOtp, pSourceData, pSignature);
    END SignTranpay;

    FUNCTION getMarkedTranpaysValueInDomCur
    RETURN NUMBER
    IS
    BEGIN
        RETURN mcore.tranpays_pck.getMarkedTranpaysValueInDomCur;
    END getMarkedTranpaysValueInDomCur;

    FUNCTION getTranpaysCount(pPeriodFrom DATE,
                        pPeriodTo DATE,
                        pStatus mcore.tranpays.status%TYPE,
                        pBasicType mcore.request_types.basic_type%TYPE)
    RETURN PLS_INTEGER IS
        myunit CONSTANT VARCHAR2(16) := 'getTranpaysCount';

        vCount PLS_INTEGER := 0;

        vDatumOd DATE;
        vDatumDo DATE;

    BEGIN
        slog.debug(pkgCtxId, myUnit, pPeriodFrom || ':' || pPeriodTo || ':' || pStatus || ':' || pBasicType);
        common_pck.CommonSecurityChecks;

        vDatumOd := NVL(pPeriodFrom, mcore.common_pck.cDATE_PAST);
        vDatumDo := NVL(pPeriodTo + 1, mcore.common_pck.cDATE_FUTURE);

        IF pStatus IN (mcore.common_pck.cTRPSTS_UW, mcore.common_pck.cTRPSTS_VB) THEN
            IF mcauth.auth.getAccountOwner = '%' THEN
                SELECT COUNT(vw$ut.id)
                INTO vCount
                FROM mcore.vw$user_tranpays_uw_vb vw$ut JOIN mcore.request_types rt ON (vw$ut.req_type_id = rt.id)
                WHERE vw$ut.status = pStatus
                  AND rt.basic_type = pBasicType
                  AND vw$ut.date_created between vDatumOd AND vDatumDo;
            ELSE
                -- Accountant !
                SELECT COUNT(vw$ut.id)
                INTO vCount
                FROM mcore.vw$user_tranpays_uw_vb_acc vw$ut JOIN mcore.request_types rt ON (vw$ut.req_type_id = rt.id)
                WHERE vw$ut.status = pStatus
                  AND rt.basic_type = pBasicType
                  AND vw$ut.date_created between vDatumOd AND vDatumDo;
            END IF;

        ELSIF pStatus = mcore.common_pck.cTRPSTS_UD THEN
        SELECT COUNT(vw$ut.id)
          INTO vCount
          FROM mcore.vw$user_tranpays_ud vw$ut JOIN mcore.request_types rt ON (vw$ut.req_type_id = rt.id)
          WHERE rt.basic_type = pBasicType
            AND vw$ut.date_created between vDatumOd AND vDatumDo
            AND vw$ut.status = pStatus;

        ELSIF pStatus = mcore.common_pck.cTRPSTS_US THEN
        SELECT COUNT(vw$ut.id)
          INTO vCount
          FROM mcore.vw$user_tranpays_us vw$ut JOIN mcore.request_types rt ON (vw$ut.req_type_id = rt.id)
          WHERE rt.basic_type = pBasicType
            AND vw$ut.date_created between vDatumOd AND vDatumDo
            AND vw$ut.status = pStatus;

        ELSIF pStatus IN (mcore.common_pck.cTRPSTS_BP, mcore.common_pck.cTRPSTS_BR)  THEN
        SELECT COUNT(vw$ut.id)
          INTO vCount
          FROM mcore.vw$user_tranpays_bx vw$ut JOIN mcore.request_types rt ON (vw$ut.req_type_id = rt.id)
          WHERE rt.basic_type = pBasicType
            AND vw$ut.date_created between vDatumOd AND vDatumDo
            AND vw$ut.status = pStatus;
		
		ELSIF pStatus = mcore.common_pck.cTRPSTS_BA THEN
        SELECT COUNT(vw$ut.id)
          INTO vCount
          FROM mcore.vw$user_tranpays_ba vw$ut JOIN mcore.request_types rt ON (vw$ut.req_type_id = rt.id)
          WHERE rt.basic_type = pBasicType
            AND vw$ut.date_created between vDatumOd AND vDatumDo;

        ELSIF pStatus = mcore.common_pck.cTRPSTS_UC THEN
        SELECT COUNT(vw$ut.id)
          INTO vCount
          FROM mcore.vw$user_tranpays_uc vw$ut JOIN mcore.request_types rt ON (vw$ut.req_type_id = rt.id)
          WHERE rt.basic_type = pBasicType
            AND vw$ut.date_created between vDatumOd AND vDatumDo
            AND vw$ut.status = pStatus;

        ELSE
            -- Invalid status requested !!
            sspkg.raiseError(cERR_InvalidStatus, null, pkgCtxId, myunit);
        END IF;

        RETURN vCount;
    EXCEPTION
      WHEN sspkg.sysException THEN
        RAISE;
      WHEN OTHERS THEN
        RAISE;
    END getTranpaysCount;

    FUNCTION getOrdersCount(pPeriodFrom DATE,
                            pPeriodTo   DATE,
                            pStatusList mcore.table_of_varchar2_60,
                            pBasicType  mcore.request_types.basic_type%TYPE)
    RETURN PLS_INTEGER IS
        myunit CONSTANT VARCHAR2(16) := 'getOrdersCount';

        vCount PLS_INTEGER := 0;

        vDateFrom DATE;
        vDateTo DATE;

    BEGIN
        slog.debug(pkgCtxId, myUnit, pPeriodFrom || ':' || pPeriodTo || ':' || stringify_status_list(pStatusList) || ':' || pBasicType);
        common_pck.CommonSecurityChecks;

        vDateFrom := NVL(pPeriodFrom, mcore.common_pck.cDATE_PAST);
        vDateTo := NVL(pPeriodTo + 1, mcore.common_pck.cDATE_FUTURE);

        SELECT COUNT(vw$ut.id)
          INTO vCount
          FROM mcore.vw$user_orders vw$ut 
          JOIN mcore.request_types rt ON (vw$ut.req_type_id = rt.id)
          WHERE rt.basic_type = pBasicType
            AND (pStatusList IS NULL OR vw$ut.status IN (SELECT * FROM TABLE(pStatusList)))
            AND vw$ut.date_created between vDateFrom AND vDateTo;

        RETURN vCount;
    EXCEPTION
      WHEN sspkg.sysException THEN
        RAISE;
      WHEN OTHERS THEN
        RAISE;
    END getOrdersCount;


FUNCTION getOrdersCount(pPeriodFrom           DATE,
                          pPeriodTo           DATE,
                          pStatusList         mcore.table_of_varchar2_60,
                          pReqTypeId          IN mcore.request_types.id%TYPE,
                          pTranpayId          IN mcore.tranpays.id%TYPE,
                          pSenderAccountId    IN VARCHAR2,
                          pDescription        IN VARCHAR2,
                          pBeneficiaryAccount IN VARCHAR2,
                          pBeneficiaryName    IN VARCHAR2,
                          pAmountFrom         IN NUMBER,
                          pAmountTo           IN NUMBER)
    RETURN PLS_INTEGER IS
        myunit CONSTANT VARCHAR2(17) := 'getOrdersCount2';

        vCount PLS_INTEGER := 0;

        vDateFrom DATE;
        vDateTo DATE;

    BEGIN
        slog.debug(pkgCtxId, myUnit, pPeriodFrom || ':' || pPeriodTo || ':' || stringify_status_list(pStatusList) || ':' || pReqTypeId 
        || ':' || pTranpayId || ':' || pSenderAccountId || ':' || pDescription || ':' || pBeneficiaryAccount 
        || ':' || pBeneficiaryName || ':' || pAmountFrom || ':' || pAmountTo);
        common_pck.CommonSecurityChecks;

        vDateFrom := NVL(pPeriodFrom, mcore.common_pck.cDATE_PAST);                                 
        vDateTo := NVL(pPeriodTo + 1, mcore.common_pck.cDATE_FUTURE);

        SELECT COUNT(vw$ut.id)
          INTO vCount
          FROM mcore.vw$user_tranpays_uc vw$ut 
          WHERE (pStatusList IS NULL OR vw$ut.status IN (SELECT * FROM TABLE(pStatusList)))
            AND vw$ut.req_type_id LIKE pReqTypeId
            AND vw$ut.id = NVL(pTranpayId, vw$ut.id)
            AND vw$ut.account_id = NVL(pSenderAccountId, vw$ut.account_id)
            AND LOWER(vw$ut.description) LIKE LOWER('%' || pDescription || '%')			  			  
            AND NVL(vw$ut.tranval,0) BETWEEN NVL(pAmountFrom,0) AND NVL(pAmountTo, 99999999999999999999999999)			  
            AND 
              CASE 
                WHEN pBeneficiaryAccount IS NOT NULL THEN 
                  CASE 
                      WHEN EXISTS (SELECT NULL
                        FROM mcore.tranpay_details td1 
                        WHERE td1.tranpay_id = vw$ut.id 
                        AND td1.attrib_id = DECODE(td1.req_type_id, 'TRANSFER', 'RACUN', 'RACUN_PRIMAOCA') 
                        AND td1.data_vchar LIKE pBeneficiaryAccount) THEN 'DA' 
                      ELSE 'NE' 
                  END
                ELSE 'DA'
              END = 'DA'
            AND 
              CASE 
                WHEN pBeneficiaryName IS NOT NULL THEN
                  CASE 
                  WHEN EXISTS (SELECT NULL
                    FROM mcore.tranpay_details td1 
                    WHERE td1.tranpay_id = vw$ut.id 
                    AND td1.attrib_id = 'NAZIV_PRIMAOCA'
                    AND LOWER(td1.data_vchar) LIKE LOWER('%' || pBeneficiaryName || '%')) THEN 'DA'
                  ELSE 'NE' END
                ELSE 'DA'
              END = 'DA'
          AND vw$ut.date_created between vDateFrom AND vDateTo;

        RETURN vCount;
    EXCEPTION
      WHEN sspkg.sysException THEN
        RAISE;
      WHEN OTHERS THEN
        RAISE;
    END getOrdersCount;


    FUNCTION getTranpaysCount(pPeriodFrom DATE,
                        pPeriodTo DATE,
                        pStatus mcore.tranpays.status%TYPE,
                        pReqTypeId IN mcore.request_types.id%TYPE,
						pTranpayId IN mcore.tranpays.id%TYPE,
						pSenderAccountId IN VARCHAR2,
						pDescription IN VARCHAR2,
						pBeneficiaryAccount IN VARCHAR2,
						pBeneficiaryName IN VARCHAR2,
						pAmountFrom IN NUMBER,
						pAmountTo IN NUMBER)
    RETURN PLS_INTEGER IS
        myunit CONSTANT VARCHAR2(17) := 'getTranpaysCount2';

        vCount PLS_INTEGER := 0;

        vDatumOd DATE;
        vDatumDo DATE;

    BEGIN
        slog.debug(pkgCtxId, myUnit, pPeriodFrom || ':' || pPeriodTo || ':' || pStatus || ':' || pReqTypeId 
        || ':' || pTranpayId || ':' || pSenderAccountId || ':' || pDescription || ':' || pBeneficiaryAccount 
        || ':' || pBeneficiaryName || ':' || pAmountFrom || ':' || pAmountTo);
        common_pck.CommonSecurityChecks;

        vDatumOd := NVL(pPeriodFrom, mcore.common_pck.cDATE_PAST);                                 
        vDatumDo := NVL(pPeriodTo + 1, mcore.common_pck.cDATE_FUTURE);

        IF pStatus IN (mcore.common_pck.cTRPSTS_UW, mcore.common_pck.cTRPSTS_VB) THEN
            IF mcauth.auth.getAccountOwner = '%' THEN
                SELECT COUNT(vw$ut.id)
                INTO vCount
                FROM mcore.vw$user_tranpays_uw_vb vw$ut 
                WHERE vw$ut.status = pStatus                                                      
                  AND vw$ut.req_type_id LIKE pReqTypeId
                  AND vw$ut.id = NVL(pTranpayId, vw$ut.id)
                  AND vw$ut.account_id = NVL(pSenderAccountId, vw$ut.account_id)
                  AND LOWER(vw$ut.description) LIKE LOWER('%' || pDescription || '%')			  			  
                  AND NVL(vw$ut.tranval,0) BETWEEN NVL(pAmountFrom,0) AND NVL(pAmountTo, 99999999999999999999999999)			  
                  AND 
                    CASE 
                        WHEN pBeneficiaryAccount IS NOT NULL THEN 
                          CASE 
                              WHEN EXISTS (SELECT NULL
                                FROM mcore.tranpay_details td1 
                                WHERE td1.tranpay_id = vw$ut.id 
                                AND td1.attrib_id = DECODE(td1.req_type_id, 'TRANSFER', 'RACUN', 'RACUN_PRIMAOCA') 
                                AND td1.data_vchar LIKE pBeneficiaryAccount) THEN 'DA' 
                              ELSE 'NE' 
                          END
                        ELSE 'DA'
                    END = 'DA'
                  AND 
                      CASE 
                        WHEN pBeneficiaryName IS NOT NULL THEN
                          CASE 
                          WHEN EXISTS (SELECT NULL
                            FROM mcore.tranpay_details td1 
                            WHERE td1.tranpay_id = vw$ut.id 
                            AND td1.attrib_id = 'NAZIV_PRIMAOCA'
                            AND LOWER(td1.data_vchar) LIKE LOWER('%' || pBeneficiaryName || '%')) THEN 'DA'
                          ELSE 'NE' END
                        ELSE 'DA'
                      END = 'DA'
                  AND vw$ut.date_created BETWEEN vDatumOd AND vDatumDo;
            ELSE
                -- Accountant !
                SELECT COUNT(vw$ut.id)
                INTO vCount
                FROM mcore.vw$user_tranpays_uw_vb_acc vw$ut 
                WHERE vw$ut.status = pStatus
                  AND vw$ut.req_type_id LIKE pReqTypeId
                  AND vw$ut.id = NVL(pTranpayId, vw$ut.id)
                  AND vw$ut.account_id = NVL(pSenderAccountId, vw$ut.account_id)
                  AND LOWER(vw$ut.description) LIKE LOWER('%' || pDescription || '%')			  			  
                  AND NVL(vw$ut.tranval,0) BETWEEN NVL(pAmountFrom,0) AND NVL(pAmountTo, 99999999999999999999999999)			  
                  AND 
                    CASE 
                        WHEN pBeneficiaryAccount IS NOT NULL THEN 
                          CASE 
                              WHEN EXISTS (SELECT NULL
                                FROM mcore.tranpay_details td1 
                                WHERE td1.tranpay_id = vw$ut.id 
                                AND td1.attrib_id = DECODE(td1.req_type_id, 'TRANSFER', 'RACUN', 'RACUN_PRIMAOCA') 
                                AND td1.data_vchar LIKE pBeneficiaryAccount) THEN 'DA' 
                              ELSE 'NE' 
                          END
                        ELSE 'DA'
                    END = 'DA'
                  AND 
                      CASE 
                        WHEN pBeneficiaryName IS NOT NULL THEN
                          CASE 
                          WHEN EXISTS (SELECT NULL
                            FROM mcore.tranpay_details td1 
                            WHERE td1.tranpay_id = vw$ut.id 
                            AND td1.attrib_id = 'NAZIV_PRIMAOCA'
                            AND LOWER(td1.data_vchar) LIKE LOWER('%' || pBeneficiaryName || '%')) THEN 'DA'
                          ELSE 'NE' END
                        ELSE 'DA'
                      END = 'DA'
                  AND vw$ut.date_created BETWEEN vDatumOd AND vDatumDo;
            END IF;

        ELSIF pStatus = mcore.common_pck.cTRPSTS_UD THEN
            SELECT COUNT(vw$ut.id)
            INTO vCount
            FROM mcore.vw$user_tranpays_ud vw$ut 
            WHERE vw$ut.status = pStatus
                AND vw$ut.req_type_id LIKE pReqTypeId
                AND vw$ut.id = NVL(pTranpayId, vw$ut.id)
                AND vw$ut.account_id = NVL(pSenderAccountId, vw$ut.account_id)
                AND LOWER(vw$ut.description) LIKE LOWER('%' || pDescription || '%')			  			  
                AND NVL(vw$ut.tranval,0) BETWEEN NVL(pAmountFrom,0) AND NVL(pAmountTo, 99999999999999999999999999)			  
                AND 
                  CASE 
                    WHEN pBeneficiaryAccount IS NOT NULL THEN 
                      CASE 
                          WHEN EXISTS (SELECT NULL
                            FROM mcore.tranpay_details td1 
                            WHERE td1.tranpay_id = vw$ut.id 
                            AND td1.attrib_id = DECODE(td1.req_type_id, 'TRANSFER', 'RACUN', 'RACUN_PRIMAOCA') 
                            AND td1.data_vchar LIKE pBeneficiaryAccount) THEN 'DA' 
                          ELSE 'NE' 
                      END
                    ELSE 'DA'
                  END = 'DA'
                AND 
                  CASE 
                    WHEN pBeneficiaryName IS NOT NULL THEN
                      CASE 
                      WHEN EXISTS (SELECT NULL
                        FROM mcore.tranpay_details td1 
                        WHERE td1.tranpay_id = vw$ut.id 
                        AND td1.attrib_id = 'NAZIV_PRIMAOCA'
                        AND LOWER(td1.data_vchar) LIKE LOWER('%' || pBeneficiaryName || '%')) THEN 'DA'
                      ELSE 'NE' END
                    ELSE 'DA'
                  END = 'DA'
              AND vw$ut.date_created between vDatumOd AND vDatumDo;

        ELSIF pStatus = mcore.common_pck.cTRPSTS_US THEN
        SELECT COUNT(vw$ut.id)
          INTO vCount
          FROM mcore.vw$user_tranpays_us vw$ut 
          WHERE vw$ut.status = pStatus
            AND vw$ut.req_type_id LIKE pReqTypeId
            AND vw$ut.id = NVL(pTranpayId, vw$ut.id)
            AND vw$ut.account_id = NVL(pSenderAccountId, vw$ut.account_id)
            AND LOWER(vw$ut.description) LIKE LOWER('%' || pDescription || '%')			  			  
            AND NVL(vw$ut.tranval,0) BETWEEN NVL(pAmountFrom,0) AND NVL(pAmountTo, 99999999999999999999999999)			  
            AND 
              CASE 
                WHEN pBeneficiaryAccount IS NOT NULL THEN 
                  CASE 
                      WHEN EXISTS (SELECT NULL
                        FROM mcore.tranpay_details td1 
                        WHERE td1.tranpay_id = vw$ut.id 
                        AND td1.attrib_id = DECODE(td1.req_type_id, 'TRANSFER', 'RACUN', 'RACUN_PRIMAOCA') 
                        AND td1.data_vchar LIKE pBeneficiaryAccount) THEN 'DA' 
                      ELSE 'NE' 
                  END
                ELSE 'DA'
              END = 'DA'
            AND 
              CASE 
                WHEN pBeneficiaryName IS NOT NULL THEN
                  CASE 
                  WHEN EXISTS (SELECT NULL
                    FROM mcore.tranpay_details td1 
                    WHERE td1.tranpay_id = vw$ut.id 
                    AND td1.attrib_id = 'NAZIV_PRIMAOCA'
                    AND LOWER(td1.data_vchar) LIKE LOWER('%' || pBeneficiaryName || '%')) THEN 'DA'
                  ELSE 'NE' END
                ELSE 'DA'
              END = 'DA'
            AND vw$ut.date_created between vDatumOd AND vDatumDo;

        ELSIF pStatus IN (mcore.common_pck.cTRPSTS_BP, mcore.common_pck.cTRPSTS_BR) THEN
        SELECT COUNT(vw$ut.id)
          INTO vCount
          FROM mcore.vw$user_tranpays_bx vw$ut 
          WHERE vw$ut.status = pStatus
            AND vw$ut.req_type_id LIKE pReqTypeId
            AND vw$ut.id = NVL(pTranpayId, vw$ut.id)
            AND vw$ut.account_id = NVL(pSenderAccountId, vw$ut.account_id)
            AND LOWER(vw$ut.description) LIKE LOWER('%' || pDescription || '%')			  			  
            AND NVL(vw$ut.tranval,0) BETWEEN NVL(pAmountFrom,0) AND NVL(pAmountTo, 99999999999999999999999999)			  
            AND 
              CASE 
                WHEN pBeneficiaryAccount IS NOT NULL THEN 
                  CASE 
                      WHEN EXISTS (SELECT NULL
                        FROM mcore.tranpay_details td1 
                        WHERE td1.tranpay_id = vw$ut.id 
                        AND td1.attrib_id = DECODE(td1.req_type_id, 'TRANSFER', 'RACUN', 'RACUN_PRIMAOCA') 
                        AND td1.data_vchar LIKE pBeneficiaryAccount) THEN 'DA' 
                      ELSE 'NE' 
                  END
                ELSE 'DA'
              END = 'DA'
            AND 
              CASE 
                WHEN pBeneficiaryName IS NOT NULL THEN
                  CASE 
                  WHEN EXISTS (SELECT NULL
                    FROM mcore.tranpay_details td1 
                    WHERE td1.tranpay_id = vw$ut.id 
                    AND td1.attrib_id = 'NAZIV_PRIMAOCA'
                    AND LOWER(td1.data_vchar) LIKE LOWER('%' || pBeneficiaryName || '%')) THEN 'DA'
                  ELSE 'NE' END
                ELSE 'DA'
              END = 'DA'
          AND vw$ut.date_created between vDatumOd AND vDatumDo;
		  
		ELSIF pStatus = mcore.common_pck.cTRPSTS_BA THEN
        SELECT COUNT(vw$ut.id)
          INTO vCount
          FROM mcore.vw$user_tranpays_ba vw$ut 
          WHERE vw$ut.req_type_id LIKE pReqTypeId
            AND vw$ut.id = NVL(pTranpayId, vw$ut.id)
            AND vw$ut.account_id = NVL(pSenderAccountId, vw$ut.account_id)
            AND LOWER(vw$ut.description) LIKE LOWER('%' || pDescription || '%')			  			  
            AND NVL(vw$ut.tranval,0) BETWEEN NVL(pAmountFrom,0) AND NVL(pAmountTo, 99999999999999999999999999)			  
            AND 
              CASE 
                WHEN pBeneficiaryAccount IS NOT NULL THEN 
                  CASE 
                      WHEN EXISTS (SELECT NULL
                        FROM mcore.tranpay_details td1 
                        WHERE td1.tranpay_id = vw$ut.id 
                        AND td1.attrib_id = DECODE(td1.req_type_id, 'TRANSFER', 'RACUN', 'RACUN_PRIMAOCA') 
                        AND td1.data_vchar LIKE pBeneficiaryAccount) THEN 'DA' 
                      ELSE 'NE' 
                  END
                ELSE 'DA'
              END = 'DA'
            AND 
              CASE 
                WHEN pBeneficiaryName IS NOT NULL THEN
                  CASE 
                  WHEN EXISTS (SELECT NULL
                    FROM mcore.tranpay_details td1 
                    WHERE td1.tranpay_id = vw$ut.id 
                    AND td1.attrib_id = 'NAZIV_PRIMAOCA'
                    AND LOWER(td1.data_vchar) LIKE LOWER('%' || pBeneficiaryName || '%')) THEN 'DA'
                  ELSE 'NE' END
                ELSE 'DA'
              END = 'DA'
          AND vw$ut.date_created between vDatumOd AND vDatumDo;

        ELSIF pStatus = mcore.common_pck.cTRPSTS_UC THEN
        SELECT COUNT(vw$ut.id)
          INTO vCount
          FROM mcore.vw$user_tranpays_uc vw$ut 
          WHERE vw$ut.status = pStatus
            AND vw$ut.req_type_id LIKE pReqTypeId
            AND vw$ut.id = NVL(pTranpayId, vw$ut.id)
            AND vw$ut.account_id = NVL(pSenderAccountId, vw$ut.account_id)
            AND LOWER(vw$ut.description) LIKE LOWER('%' || pDescription || '%')			  			  
            AND NVL(vw$ut.tranval,0) BETWEEN NVL(pAmountFrom,0) AND NVL(pAmountTo, 99999999999999999999999999)			  
            AND 
              CASE 
                WHEN pBeneficiaryAccount IS NOT NULL THEN 
                  CASE 
                      WHEN EXISTS (SELECT NULL
                        FROM mcore.tranpay_details td1 
                        WHERE td1.tranpay_id = vw$ut.id 
                        AND td1.attrib_id = DECODE(td1.req_type_id, 'TRANSFER', 'RACUN', 'RACUN_PRIMAOCA') 
                        AND td1.data_vchar LIKE pBeneficiaryAccount) THEN 'DA' 
                      ELSE 'NE' 
                  END
                ELSE 'DA'
              END = 'DA'
            AND 
              CASE 
                WHEN pBeneficiaryName IS NOT NULL THEN
                  CASE 
                  WHEN EXISTS (SELECT NULL
                    FROM mcore.tranpay_details td1 
                    WHERE td1.tranpay_id = vw$ut.id 
                    AND td1.attrib_id = 'NAZIV_PRIMAOCA'
                    AND LOWER(td1.data_vchar) LIKE LOWER('%' || pBeneficiaryName || '%')) THEN 'DA'
                  ELSE 'NE' END
                ELSE 'DA'
              END = 'DA'
          AND vw$ut.date_created between vDatumOd AND vDatumDo;

        ELSE
            -- Invalid status requested !!
            sspkg.raiseError(cERR_InvalidStatus, null, pkgCtxId, myunit);
        END IF;

        RETURN vCount;
    EXCEPTION
      WHEN sspkg.sysException THEN
        RAISE;
      WHEN OTHERS THEN
        RAISE;
    END getTranpaysCount;

    FUNCTION translateWuErrorMessage(vErrorMessage VARCHAR2)
    RETURN VARCHAR2
    IS
    BEGIN
        RETURN mcore.tranpays_pck.translateWuErrorMessage(vErrorMessage);
    END translateWuErrorMessage;

    FUNCTION getFilteredTranpays(pBasicType mcore.request_types.basic_type%TYPE)
    RETURN sys_refcursor IS
    BEGIN
        RETURN mcore.tranpays_pck.getFilteredTranpays(pBasicType);
    END getFilteredTranpays;

    FUNCTION getTranpaysUW_VB(
                        pStatus IN mcore.tranpays.status%TYPE,
                        vDatumOd IN DATE,
                        vDatumDo IN DATE,
                        pBasicType IN mcore.request_types.basic_type%TYPE,
                        vOffset IN PLS_INTEGER,
                        vArraySize IN PLS_INTEGER)
    RETURN sys_refcursor IS
        myunit CONSTANT VARCHAR2(16) := 'getTranpaysUW_VB';
    BEGIN
        slog.debug(pkgCtxId, myUnit, to_char(vDatumOd,mcore.common_pck.cDATE_MASK) || ':' || to_char(vDatumDo,mcore.common_pck.cDATE_MASK) || ':' || pBasicType || ':' || vOffset || ':' || vArraySize);

        IF mcauth.auth.getAccountOwner = '%' THEN
            INSERT INTO mcore.tmp$tranpays(id, rownumber)
            SELECT tranpay_id, rn FROM
            (SELECT vw$ut.id tranpay_id,
                ROW_NUMBER() OVER (ORDER BY vw$ut.date_modified DESC) rn
            FROM mcore.vw$user_tranpays_uw_vb vw$ut
            JOIN mcore.request_types rt ON (vw$ut.req_type_id = rt.id)
            WHERE vw$ut.status = pStatus
              AND rt.basic_type = pBasicType
              AND vw$ut.date_created between vDatumOd AND vDatumDo)
			WHERE rn BETWEEN vOffset AND (vOffset + vArraySize);

        ELSE
            INSERT INTO mcore.tmp$tranpays(id, rownumber)
            SELECT tranpay_id, rn FROM
            (SELECT vw$ut.id tranpay_id,
                ROW_NUMBER() OVER (ORDER BY vw$ut.date_modified DESC) rn
            FROM mcore.vw$user_tranpays_uw_vb_acc vw$ut
            JOIN mcore.request_types rt ON (vw$ut.req_type_id = rt.id)
            WHERE vw$ut.status = pStatus
              AND rt.basic_type = pBasicType
              AND vw$ut.date_created between vDatumOd AND vDatumDo)
			WHERE rn BETWEEN vOffset AND (vOffset + vArraySize);
        END IF;

        RETURN getFilteredTranpays(pBasicType => pBasicType);

    END getTranpaysUW_VB;

    FUNCTION getTranpaysUW_VB(
                        pStatus IN mcore.tranpays.status%TYPE,
                        vDatumOd IN DATE,
                        vDatumDo IN DATE,
                        pReqTypeId IN mcore.request_types.id%TYPE,
						pTranpayId IN mcore.tranpays.id%TYPE,
						pSenderAccountId IN VARCHAR2,
						pDescription IN VARCHAR2,
						pBeneficiaryAccount IN VARCHAR2,
						pBeneficiaryName IN VARCHAR2,
						pAmountFrom IN NUMBER,
						pAmountTo IN NUMBER,
                        vOffset IN PLS_INTEGER,
                        vArraySize IN PLS_INTEGER)
    RETURN sys_refcursor IS
        myunit CONSTANT VARCHAR2(17) := 'getTranpaysUW_VB2';
    BEGIN
        slog.debug(pkgCtxId, myUnit, to_char(vDatumOd,mcore.common_pck.cDATE_MASK) || ':' || to_char(vDatumDo,mcore.common_pck.cDATE_MASK) || ':' || vOffset || ':' || vArraySize);

        IF mcauth.auth.getAccountOwner = '%' THEN
			INSERT INTO mcore.tmp$tranpays(id, description, tranval, aggr_tranpay, rownumber)
			SELECT tranpay_id, description, tranval, aggr_tranpay, rn FROM
			(SELECT vw$ut.id tranpay_id,
					vw$ut.description,
					vw$ut.tranval,
					vw$ut.aggr_tranpay,
                ROW_NUMBER() OVER (ORDER BY vw$ut.date_modified DESC) rn
            FROM mcore.vw$user_tranpays_uw_vb vw$ut
            WHERE vw$ut.status = pStatus
              AND vw$ut.req_type_id LIKE pReqTypeId
			  AND vw$ut.id = NVL(pTranpayId, vw$ut.id)
			  AND vw$ut.account_id = NVL(pSenderAccountId, vw$ut.account_id)
			  AND NVL(LOWER(vw$ut.description),'%') LIKE LOWER('%' || pDescription || '%')			  			  
			  AND NVL(vw$ut.tranval,0) BETWEEN NVL(pAmountFrom,0) AND NVL(pAmountTo, 99999999999999999999999999)			  
			  AND 
				CASE 
                    WHEN pBeneficiaryAccount IS NOT NULL THEN 
                      CASE 
                          WHEN EXISTS (SELECT NULL
                            FROM mcore.tranpay_details td1 
                            WHERE td1.tranpay_id = vw$ut.id 
                            AND td1.attrib_id = DECODE(td1.req_type_id, 'TRANSFER', 'RACUN', 'RACUN_PRIMAOCA') 
                            AND td1.data_vchar LIKE pBeneficiaryAccount) THEN 'DA' 
                          ELSE 'NE' 
                      END
                    ELSE 'DA'
                END = 'DA'
              AND 
                  CASE 
                    WHEN pBeneficiaryName IS NOT NULL THEN
                      CASE 
                      WHEN EXISTS (SELECT NULL
                        FROM mcore.tranpay_details td1 
                        WHERE td1.tranpay_id = vw$ut.id 
                        AND td1.attrib_id = 'NAZIV_PRIMAOCA'
                        AND LOWER(td1.data_vchar) LIKE LOWER('%' || pBeneficiaryName || '%')) THEN 'DA'
                      ELSE 'NE' END
                    ELSE 'DA'
                  END = 'DA'			  
              AND vw$ut.date_created between NVL(vDatumOd, mcore.common_pck.cDATE_PAST) AND NVL(vDatumDo, mcore.common_pck.cDATE_FUTURE)
			  )
			WHERE rn BETWEEN vOffset AND (vOffset + vArraySize);

        ELSE
			INSERT INTO mcore.tmp$tranpays(id, description, tranval, aggr_tranpay, rownumber)
			SELECT tranpay_id, description, tranval, aggr_tranpay, rn FROM
			(SELECT vw$ut.id tranpay_id,
					vw$ut.description,
					vw$ut.tranval,
					vw$ut.aggr_tranpay,
                ROW_NUMBER() OVER (ORDER BY vw$ut.date_modified DESC) rn
            FROM mcore.vw$user_tranpays_uw_vb_acc vw$ut
            WHERE vw$ut.status = pStatus
              AND vw$ut.req_type_id LIKE pReqTypeId
              AND vw$ut.id = NVL(pTranpayId, vw$ut.id)
              AND vw$ut.account_id = NVL(pSenderAccountId, vw$ut.account_id)
              AND NVL(LOWER(vw$ut.description),'%') LIKE LOWER('%' || pDescription || '%')
              AND NVL(vw$ut.tranval,0) BETWEEN NVL(pAmountFrom,0) AND NVL(pAmountTo, 99999999999999999999999999)
              AND 
              CASE 
                WHEN pBeneficiaryAccount IS NOT NULL THEN 
                  CASE WHEN EXISTS (SELECT NULL
                    FROM mcore.tranpay_details td1 
                    WHERE td1.tranpay_id = vw$ut.id 
                    AND td1.attrib_id = DECODE(td1.req_type_id, 'TRANSFER', 'RACUN', 'RACUN_PRIMAOCA') 
                    AND td1.data_vchar LIKE pBeneficiaryAccount) THEN 'DA' 
                  ELSE 'NE' END
                ELSE 'DA'
              END = 'DA'
              AND 
              CASE 
                WHEN pBeneficiaryName IS NOT NULL THEN
                  CASE 
                  WHEN EXISTS (SELECT NULL
                    FROM mcore.tranpay_details td1 
                    WHERE td1.tranpay_id = vw$ut.id 
                    AND td1.attrib_id = 'NAZIV_PRIMAOCA'
                    AND LOWER(td1.data_vchar) LIKE LOWER('%' || pBeneficiaryName || '%')) THEN 'DA'
                  ELSE 'NE' END
                ELSE 'DA'
              END = 'DA'
              AND vw$ut.date_created BETWEEN NVL(vDatumOd, mcore.common_pck.cDATE_PAST) AND NVL(vDatumDo, mcore.common_pck.cDATE_FUTURE))
			WHERE rn BETWEEN vOffset AND (vOffset + vArraySize);
        END IF;

        RETURN getFilteredTranpays(pBasicType => '%');

    END getTranpaysUW_VB;

    FUNCTION getTranpaysUD(vDatumOd DATE,
                        vDatumDo DATE,
                        pBasicType mcore.request_types.basic_type%TYPE,
                        vOffset PLS_INTEGER,
                        vArraySize PLS_INTEGER)
    RETURN sys_refcursor IS
        myunit CONSTANT VARCHAR2(13) := 'getTranpaysUD';
    BEGIN
        slog.debug(pkgCtxId, myUnit, to_char(vDatumOd,mcore.common_pck.cDATE_MASK) || ':' || to_char(vDatumDo,mcore.common_pck.cDATE_MASK) || ':' || pBasicType || ':' || vOffset || ':' || vArraySize);

        INSERT INTO mcore.tmp$tranpays(id, rownumber)
        SELECT tranpay_id, rn FROM
        (SELECT vw$ut.id tranpay_id,
               ROW_NUMBER() OVER (ORDER BY vw$ut.date_modified DESC) rn
          FROM mcore.vw$user_tranpays_ud vw$ut
                JOIN mcore.request_types rt ON (vw$ut.req_type_id = rt.id)
          WHERE rt.basic_type = pBasicType
            AND vw$ut.date_created between vDatumOd AND vDatumDo)
        WHERE rn BETWEEN vOffset AND (vOffset + vArraySize);

        RETURN getFilteredTranpays(pBasicType => pBasicType);
    END getTranpaysUD;

    FUNCTION getTranpaysUD(vDatumOd IN DATE,
                        vDatumDo IN DATE,
                        pReqTypeId IN mcore.request_types.id%TYPE,
						pTranpayId IN mcore.tranpays.id%TYPE,
						pSenderAccountId IN VARCHAR2,
						pDescription IN VARCHAR2,
						pBeneficiaryAccount IN VARCHAR2,
						pBeneficiaryName IN VARCHAR2,
						pAmountFrom IN NUMBER,
						pAmountTo IN NUMBER,
                        vOffset IN PLS_INTEGER,
                        vArraySize IN PLS_INTEGER)
    RETURN sys_refcursor IS
        myunit CONSTANT VARCHAR2(14) := 'getTranpaysUD2';
    BEGIN
        slog.debug(pkgCtxId, myUnit, to_char(vDatumOd,mcore.common_pck.cDATE_MASK) || ':' || to_char(vDatumDo,mcore.common_pck.cDATE_MASK) || ':' || vOffset || ':' || vArraySize);

        INSERT INTO mcore.tmp$tranpays(id, description, tranval, aggr_tranpay, rownumber)
        SELECT tranpay_id, description, tranval, aggr_tranpay, rn FROM
        (SELECT vw$ut.id tranpay_id,
				vw$ut.description,
				vw$ut.tranval,
				vw$ut.aggr_tranpay,
               ROW_NUMBER() OVER (ORDER BY vw$ut.date_modified DESC) rn
          FROM mcore.vw$user_tranpays_ud vw$ut
          WHERE vw$ut.req_type_id LIKE pReqTypeId
            AND vw$ut.id = NVL(pTranpayId, vw$ut.id)
            AND vw$ut.account_id = NVL(pSenderAccountId, vw$ut.account_id)
            AND NVL(LOWER(vw$ut.description),'%') LIKE LOWER('%' || pDescription || '%')			  			  
            AND NVL(vw$ut.tranval,0) BETWEEN NVL(pAmountFrom,0) AND NVL(pAmountTo, 99999999999999999999999999)			  
            AND 
                CASE 
                WHEN pBeneficiaryAccount IS NOT NULL THEN 
                  CASE WHEN EXISTS (SELECT NULL
                    FROM mcore.tranpay_details td1 
                    WHERE td1.tranpay_id = vw$ut.id 
                    AND td1.attrib_id = DECODE(td1.req_type_id, 'TRANSFER', 'RACUN', 'RACUN_PRIMAOCA') 
                    AND td1.data_vchar LIKE pBeneficiaryAccount) THEN 'DA' 
                  ELSE 'NE' END
                ELSE 'DA'
                END = 'DA'
            AND 
                CASE 
                WHEN pBeneficiaryName IS NOT NULL THEN
                  CASE 
                  WHEN EXISTS (SELECT NULL
                    FROM mcore.tranpay_details td1 
                    WHERE td1.tranpay_id = vw$ut.id 
                    AND td1.attrib_id = 'NAZIV_PRIMAOCA'
                    AND LOWER(td1.data_vchar) LIKE LOWER('%' || pBeneficiaryName || '%')) THEN 'DA'
                  ELSE 'NE' END
                ELSE 'DA'
                END = 'DA'
            AND vw$ut.date_created BETWEEN NVL(vDatumOd, mcore.common_pck.cDATE_PAST) AND NVL(vDatumDo, mcore.common_pck.cDATE_FUTURE))
        WHERE rn BETWEEN vOffset AND (vOffset + vArraySize);

        RETURN getFilteredTranpays(pBasicType => '%');
    END getTranpaysUD;

   FUNCTION getTranpaysUS(vDatumOd DATE,
                        vDatumDo DATE,
                        pBasicType mcore.request_types.basic_type%TYPE,
                        vOffset PLS_INTEGER,
                        vArraySize PLS_INTEGER)
    RETURN sys_refcursor IS
        myunit CONSTANT VARCHAR2(13) := 'getTranpaysUS';
    BEGIN
        slog.debug(pkgCtxId, myUnit, to_char(vDatumOd,mcore.common_pck.cDATE_MASK) || ':' || to_char(vDatumDo,mcore.common_pck.cDATE_MASK) || ':' || pBasicType || ':' || vOffset || ':' || vArraySize);

        INSERT INTO mcore.tmp$tranpays(id, rownumber)
        SELECT tranpay_id, rn FROM
        (SELECT vw$ut.id tranpay_id,
               ROW_NUMBER() OVER (ORDER BY vw$ut.date_signed DESC) rn
          FROM mcore.vw$user_tranpays_us vw$ut
                JOIN mcore.request_types rt ON (vw$ut.req_type_id = rt.id)
          WHERE rt.basic_type = pBasicType
            AND vw$ut.date_created between vDatumOd AND vDatumDo)
        WHERE rn BETWEEN vOffset AND (vOffset + vArraySize);

        RETURN getFilteredTranpays(pBasicType => pBasicType);
    END getTranpaysUS;

    FUNCTION getTranpaysUS(vDatumOd IN DATE,
                        vDatumDo IN DATE,
                        pReqTypeId IN mcore.request_types.id%TYPE,
						pTranpayId IN mcore.tranpays.id%TYPE,
						pSenderAccountId IN VARCHAR2,
						pDescription IN VARCHAR2,
						pBeneficiaryAccount IN VARCHAR2,
						pBeneficiaryName IN VARCHAR2,
						pAmountFrom IN NUMBER,
						pAmountTo IN NUMBER,
                        vOffset IN PLS_INTEGER,
                        vArraySize IN PLS_INTEGER)
    RETURN sys_refcursor IS
        myunit CONSTANT VARCHAR2(14) := 'getTranpaysUS2';
    BEGIN
        slog.debug(pkgCtxId, myUnit, to_char(vDatumOd,mcore.common_pck.cDATE_MASK) || ':' || to_char(vDatumDo,mcore.common_pck.cDATE_MASK) || ':' || vOffset || ':' || vArraySize);

        INSERT INTO mcore.tmp$tranpays(id, description, tranval, aggr_tranpay, rownumber)
        SELECT tranpay_id, description, tranval, aggr_tranpay, rn FROM
        (SELECT vw$ut.id tranpay_id,
				vw$ut.description,
				vw$ut.tranval,
				vw$ut.aggr_tranpay,
               ROW_NUMBER() OVER (ORDER BY vw$ut.date_signed DESC) rn
          FROM mcore.vw$user_tranpays_us vw$ut
          WHERE vw$ut.req_type_id LIKE pReqTypeId
            AND vw$ut.id = NVL(pTranpayId, vw$ut.id)
            AND vw$ut.account_id = NVL(pSenderAccountId, vw$ut.account_id)
            AND NVL(LOWER(vw$ut.description),'%') LIKE LOWER('%' || pDescription || '%')			  			  
            AND NVL(vw$ut.tranval,0) BETWEEN NVL(pAmountFrom,0) AND NVL(pAmountTo, 99999999999999999999999999)			  
            AND 
                CASE 
                WHEN pBeneficiaryAccount IS NOT NULL THEN 
                  CASE WHEN EXISTS (SELECT NULL
                    FROM mcore.tranpay_details td1 
                    WHERE td1.tranpay_id = vw$ut.id 
                    AND td1.attrib_id = DECODE(td1.req_type_id, 'TRANSFER', 'RACUN', 'RACUN_PRIMAOCA') 
                    AND td1.data_vchar LIKE pBeneficiaryAccount) THEN 'DA' 
                  ELSE 'NE' END
                ELSE 'DA'
                END = 'DA'
            AND 
                CASE 
                WHEN pBeneficiaryName IS NOT NULL THEN
                  CASE 
                  WHEN EXISTS (SELECT NULL
                    FROM mcore.tranpay_details td1 
                    WHERE td1.tranpay_id = vw$ut.id 
                    AND td1.attrib_id = 'NAZIV_PRIMAOCA'
                    AND LOWER(td1.data_vchar) LIKE LOWER('%' || pBeneficiaryName || '%')) THEN 'DA'
                  ELSE 'NE' END
                ELSE 'DA'
                END = 'DA'
            AND vw$ut.date_created BETWEEN NVL(vDatumOd, mcore.common_pck.cDATE_PAST) AND NVL(vDatumDo, mcore.common_pck.cDATE_FUTURE))
        WHERE rn BETWEEN vOffset AND (vOffset + vArraySize);

        RETURN getFilteredTranpays(pBasicType => '%');
    END getTranpaysUS;

   FUNCTION getTranpaysBX(vDatumOd DATE,
                        vDatumDo DATE,
                        pBasicType mcore.request_types.basic_type%TYPE,
                        vOffset PLS_INTEGER,
                        vArraySize PLS_INTEGER,
                        pStatus VARCHAR2)
    RETURN sys_refcursor IS
        myunit CONSTANT VARCHAR2(13) := 'getTranpaysBX';
    BEGIN
        slog.debug(pkgCtxId, myUnit, to_char(vDatumOd,mcore.common_pck.cDATE_MASK) || ':' || to_char(vDatumDo,mcore.common_pck.cDATE_MASK) || ':' || pBasicType || ':' || vOffset || ':' || vArraySize || ':' || pStatus);

        INSERT INTO mcore.tmp$tranpays(id, rownumber)
        SELECT tranpay_id, rn FROM
        (SELECT vw$ut.id tranpay_id,
               ROW_NUMBER() OVER (ORDER BY vw$ut.date_processed DESC) rn
          FROM mcore.vw$user_tranpays_bx vw$ut
                JOIN mcore.request_types rt ON (vw$ut.req_type_id = rt.id)
          WHERE vw$ut.status = pStatus
            AND rt.basic_type = pBasicType
            AND vw$ut.date_created between vDatumOd AND vDatumDo)
        WHERE rn BETWEEN vOffset AND (vOffset + vArraySize);

        RETURN getFilteredTranpays(pBasicType => pBasicType);
    END getTranpaysBX;
	
	 FUNCTION getTranpaysBA(vDatumOd DATE,
                        vDatumDo DATE,
                        pBasicType mcore.request_types.basic_type%TYPE,
                        vOffset PLS_INTEGER,
                        vArraySize PLS_INTEGER)
    RETURN sys_refcursor IS
        myunit CONSTANT VARCHAR2(30) := 'getTranpaysBA1';
    BEGIN
        slog.debug(pkgCtxId, myUnit, to_char(vDatumOd,mcore.common_pck.cDATE_MASK) || ':' || to_char(vDatumDo,mcore.common_pck.cDATE_MASK) || ':' || pBasicType || ':' || vOffset || ':' || vArraySize);

        INSERT INTO mcore.tmp$tranpays(id, rownumber)
        SELECT tranpay_id, rn FROM
        (SELECT vw$ut.id tranpay_id,
               ROW_NUMBER() OVER (ORDER BY vw$ut.date_processed DESC) rn
          FROM mcore.vw$user_tranpays_ba vw$ut
                JOIN mcore.request_types rt ON (vw$ut.req_type_id = rt.id)
          WHERE rt.basic_type = pBasicType
            AND vw$ut.date_created between vDatumOd AND vDatumDo)
        WHERE rn BETWEEN vOffset AND (vOffset + vArraySize);

        RETURN getFilteredTranpays(pBasicType => pBasicType);
    END getTranpaysBA;

    FUNCTION getTranpaysBX(vDatumOd IN DATE,
                        vDatumDo IN DATE,
                        pReqTypeId IN mcore.request_types.id%TYPE,
						pTranpayId IN mcore.tranpays.id%TYPE,
						pSenderAccountId IN VARCHAR2,
						pDescription IN VARCHAR2,
						pBeneficiaryAccount IN VARCHAR2,
						pBeneficiaryName IN VARCHAR2,
						pAmountFrom IN NUMBER,
						pAmountTo IN NUMBER,
                        vOffset IN PLS_INTEGER,
                        vArraySize IN PLS_INTEGER,
                        pStatus IN VARCHAR2)
    RETURN sys_refcursor IS
        myunit CONSTANT VARCHAR2(14) := 'getTranpaysBX2';
    BEGIN
        slog.debug(pkgCtxId, myUnit, to_char(vDatumOd,mcore.common_pck.cDATE_MASK) || ':' || to_char(vDatumDo,mcore.common_pck.cDATE_MASK) || ':' || vOffset || ':' || vArraySize || ':' || pStatus);

        INSERT INTO mcore.tmp$tranpays(id, description, tranval, aggr_tranpay, rownumber)
        SELECT tranpay_id, description, tranval, aggr_tranpay, rn FROM
        (SELECT vw$ut.id tranpay_id,
				vw$ut.description,
				vw$ut.tranval,
				vw$ut.aggr_tranpay,
               ROW_NUMBER() OVER (ORDER BY vw$ut.date_processed DESC) rn
          FROM mcore.vw$user_tranpays_bx vw$ut
          WHERE vw$ut.status = pStatus
            AND vw$ut.req_type_id LIKE pReqTypeId
            AND vw$ut.id = NVL(pTranpayId, vw$ut.id)
            AND vw$ut.account_id = NVL(pSenderAccountId, vw$ut.account_id)
            AND ((NVL(LOWER(vw$ut.description),'%') LIKE LOWER('%' || pDescription || '%')) OR (NVL(LOWER(vw$ut.status_message),'%') LIKE LOWER('%' || pDescription || '%')))
            AND NVL(vw$ut.tranval,0) BETWEEN NVL(pAmountFrom,0) AND NVL(pAmountTo, 99999999999999999999999999)			  
            AND 
               CASE 
                WHEN pBeneficiaryAccount IS NOT NULL THEN 
                  CASE 
                  WHEN EXISTS (SELECT NULL
                    FROM mcore.tranpay_details td1 
                    WHERE td1.tranpay_id = vw$ut.id 
                    AND td1.attrib_id = DECODE(td1.req_type_id, 'TRANSFER', 'RACUN', 'RACUN_PRIMAOCA') 
                    AND td1.data_vchar LIKE pBeneficiaryAccount) THEN 'DA' 
                  ELSE 'NE' END
                ELSE 'DA'
              END = 'DA'
            AND 
              CASE 
                WHEN pBeneficiaryName IS NOT NULL THEN
                  CASE 
                  WHEN EXISTS (SELECT NULL
                    FROM mcore.tranpay_details td1 
                    WHERE td1.tranpay_id = vw$ut.id 
                    AND td1.attrib_id = 'NAZIV_PRIMAOCA'
                    AND LOWER(td1.data_vchar) LIKE LOWER('%' || pBeneficiaryName || '%')) THEN 'DA'
                  ELSE 'NE' END
                ELSE 'DA'
              END = 'DA'
            AND vw$ut.date_created BETWEEN NVL(vDatumOd, mcore.common_pck.cDATE_PAST) AND NVL(vDatumDo, mcore.common_pck.cDATE_FUTURE))
        WHERE rn BETWEEN vOffset AND (vOffset + vArraySize);

        RETURN getFilteredTranpays(pBasicType => '%');
    END getTranpaysBX;
	
	 FUNCTION getTranpaysBA(vDatumOd IN DATE,
                        vDatumDo IN DATE,
                        pReqTypeId IN mcore.request_types.id%TYPE,
						pTranpayId IN mcore.tranpays.id%TYPE,
						pSenderAccountId IN VARCHAR2,
						pDescription IN VARCHAR2,
						pBeneficiaryAccount IN VARCHAR2,
						pBeneficiaryName IN VARCHAR2,
						pAmountFrom IN NUMBER,
						pAmountTo IN NUMBER,
                        vOffset IN PLS_INTEGER,
                        vArraySize IN PLS_INTEGER)
    RETURN sys_refcursor IS
        myunit CONSTANT VARCHAR2(30) := 'getTranpaysBA';
    BEGIN
        slog.debug(pkgCtxId, myUnit, to_char(vDatumOd,mcore.common_pck.cDATE_MASK) || ':' || to_char(vDatumDo,mcore.common_pck.cDATE_MASK) || ':' || vOffset || ':' || vArraySize);

        INSERT INTO mcore.tmp$tranpays(id, description, tranval, aggr_tranpay, rownumber)
        SELECT tranpay_id, description, tranval, aggr_tranpay, rn FROM
        (SELECT vw$ut.id tranpay_id,
				vw$ut.description,
				vw$ut.tranval,
				vw$ut.aggr_tranpay,
               ROW_NUMBER() OVER (ORDER BY vw$ut.date_processed DESC) rn
          FROM mcore.vw$user_tranpays_ba vw$ut
          WHERE vw$ut.req_type_id LIKE pReqTypeId
            AND vw$ut.id = NVL(pTranpayId, vw$ut.id)
            AND vw$ut.account_id = NVL(pSenderAccountId, vw$ut.account_id)
            AND ((NVL(LOWER(vw$ut.description),'%') LIKE LOWER('%' || pDescription || '%')) OR (NVL(LOWER(vw$ut.status_message),'%') LIKE LOWER('%' || pDescription || '%')))
            AND NVL(vw$ut.tranval,0) BETWEEN NVL(pAmountFrom,0) AND NVL(pAmountTo, 99999999999999999999999999)			  
            AND 
               CASE 
                WHEN pBeneficiaryAccount IS NOT NULL THEN 
                  CASE 
                  WHEN EXISTS (SELECT NULL
                    FROM mcore.tranpay_details td1 
                    WHERE td1.tranpay_id = vw$ut.id 
                    AND td1.attrib_id = DECODE(td1.req_type_id, 'TRANSFER', 'RACUN', 'RACUN_PRIMAOCA') 
                    AND td1.data_vchar LIKE pBeneficiaryAccount) THEN 'DA' 
                  ELSE 'NE' END
                ELSE 'DA'
              END = 'DA'
            AND 
              CASE 
                WHEN pBeneficiaryName IS NOT NULL THEN
                  CASE 
                  WHEN EXISTS (SELECT NULL
                    FROM mcore.tranpay_details td1 
                    WHERE td1.tranpay_id = vw$ut.id 
                    AND td1.attrib_id = 'NAZIV_PRIMAOCA'
                    AND LOWER(td1.data_vchar) LIKE LOWER('%' || pBeneficiaryName || '%')) THEN 'DA'
                  ELSE 'NE' END
                ELSE 'DA'
              END = 'DA'
            AND vw$ut.date_created BETWEEN NVL(vDatumOd, mcore.common_pck.cDATE_PAST) AND NVL(vDatumDo, mcore.common_pck.cDATE_FUTURE))
        WHERE rn BETWEEN vOffset AND (vOffset + vArraySize);

        RETURN getFilteredTranpays(pBasicType => '%');
    END getTranpaysBA;

   FUNCTION getTranpaysUC(vDatumOd DATE,
                        vDatumDo DATE,
                        pBasicType mcore.request_types.basic_type%TYPE,
                        vOffset PLS_INTEGER,
                        vArraySize PLS_INTEGER)
    RETURN sys_refcursor IS
        myunit CONSTANT VARCHAR2(13) := 'getTranpaysUC';
    BEGIN
       slog.debug(pkgCtxId, myUnit, to_char(vDatumOd,mcore.common_pck.cDATE_MASK) || ':' || to_char(vDatumDo,mcore.common_pck.cDATE_MASK) || ':' || pBasicType || ':' || vOffset || ':' || vArraySize);

       INSERT INTO mcore.tmp$tranpays(id, rownumber)
        SELECT tranpay_id, rn FROM
        (SELECT vw$ut.id tranpay_id,
               ROW_NUMBER() OVER (ORDER BY vw$ut.date_modified DESC) rn
          FROM mcore.vw$user_tranpays_uc vw$ut
                JOIN mcore.request_types rt ON (vw$ut.req_type_id = rt.id)
          WHERE rt.basic_type = pBasicType
            AND vw$ut.date_created between vDatumOd AND vDatumDo)
        WHERE rn BETWEEN vOffset AND (vOffset + vArraySize);

        slog.debug(pkgCtxId, myUnit, 'getFilteredTranpays...');

      RETURN getFilteredTranpays(pBasicType => pBasicType);
  END getTranpaysUC;

    FUNCTION getTranpaysUC(vDatumOd IN DATE,
                        vDatumDo IN DATE,
                        pReqTypeId IN mcore.request_types.id%TYPE,
						pTranpayId IN mcore.tranpays.id%TYPE,
						pSenderAccountId IN VARCHAR2,
						pDescription IN VARCHAR2,
						pBeneficiaryAccount IN VARCHAR2,
						pBeneficiaryName IN VARCHAR2,
						pAmountFrom IN NUMBER,
						pAmountTo IN NUMBER,
                        vOffset IN PLS_INTEGER,
                        vArraySize IN PLS_INTEGER)
    RETURN sys_refcursor IS
        myunit CONSTANT VARCHAR2(14) := 'getTranpaysUC2';
    BEGIN
       slog.debug(pkgCtxId, myUnit, to_char(vDatumOd,mcore.common_pck.cDATE_MASK) || ':' || to_char(vDatumDo,mcore.common_pck.cDATE_MASK) || ':' || vOffset || ':' || vArraySize);

        INSERT INTO mcore.tmp$tranpays(id, description, tranval, aggr_tranpay, rownumber)
        SELECT tranpay_id, description, tranval, aggr_tranpay, rn FROM
        (SELECT vw$ut.id tranpay_id,
				vw$ut.description,
				vw$ut.tranval,
				vw$ut.aggr_tranpay,
               ROW_NUMBER() OVER (ORDER BY vw$ut.date_modified DESC) rn
          FROM mcore.vw$user_tranpays_uc vw$ut
          WHERE vw$ut.req_type_id LIKE pReqTypeId
            AND vw$ut.id = NVL(pTranpayId, vw$ut.id)
            AND vw$ut.account_id = NVL(pSenderAccountId, vw$ut.account_id)
             AND ((NVL(LOWER(vw$ut.description),'%') LIKE LOWER('%' || pDescription || '%')) OR (NVL(LOWER(vw$ut.status_message),'%') LIKE LOWER('%' || pDescription || '%')))
            AND NVL(vw$ut.tranval,0) BETWEEN NVL(pAmountFrom,0) AND NVL(pAmountTo, 99999999999999999999999999)
            AND 
                CASE 
                WHEN pBeneficiaryAccount IS NOT NULL THEN 
                  CASE WHEN EXISTS (SELECT NULL
                    FROM mcore.tranpay_details td1 
                    WHERE td1.tranpay_id = vw$ut.id 
                    AND td1.attrib_id = DECODE(td1.req_type_id, 'TRANSFER', 'RACUN', 'RACUN_PRIMAOCA') 
                    AND td1.data_vchar LIKE pBeneficiaryAccount) THEN 'DA' 
                  ELSE 'NE' END
                ELSE 'DA'
                END = 'DA'
            AND 
                CASE 
                WHEN pBeneficiaryName IS NOT NULL THEN
                  CASE 
                  WHEN EXISTS (SELECT NULL
                    FROM mcore.tranpay_details td1 
                    WHERE td1.tranpay_id = vw$ut.id 
                    AND td1.attrib_id = 'NAZIV_PRIMAOCA'
                    AND LOWER(td1.data_vchar) LIKE LOWER('%' || pBeneficiaryName || '%')) THEN 'DA'
                  ELSE 'NE' END
                ELSE 'DA'
                END = 'DA'
            AND vw$ut.date_created BETWEEN NVL(vDatumOd, mcore.common_pck.cDATE_PAST) AND NVL(vDatumDo, mcore.common_pck.cDATE_FUTURE))
        WHERE rn BETWEEN vOffset AND (vOffset + vArraySize);

        slog.debug(pkgCtxId, myUnit, 'getFilteredTranpays...');

      RETURN getFilteredTranpays(pBasicType => '%');
  END getTranpaysUC;

  FUNCTION getOrders(
                    vDateFrom    DATE,
                    vDateTo      DATE,
                    pStatusList  mcore.table_of_varchar2_60,
                    pBasicType   mcore.request_types.basic_type%TYPE,
                    vOffset      PLS_INTEGER,
                    vArraySize   PLS_INTEGER)
    RETURN sys_refcursor IS
        myunit CONSTANT VARCHAR2(13) := 'getOrders';
    BEGIN
        slog.debug(pkgCtxId, myUnit, to_char(vDateFrom,mcore.common_pck.cDATE_MASK) || ':' || to_char(vDateTo,mcore.common_pck.cDATE_MASK) || ':' || pBasicType || ':' || vOffset || ':' || vArraySize);

        INSERT INTO mcore.tmp$tranpays(id, rownumber)
        SELECT tranpay_id, rn FROM (
            SELECT vw$ut.id tranpay_id,
                   ROW_NUMBER() OVER (ORDER BY vw$ut.date_signed DESC) rn
            FROM mcore.vw$user_orders vw$ut
                 JOIN mcore.request_types rt ON vw$ut.req_type_id = rt.id
            WHERE rt.basic_type = pBasicType
              AND vw$ut.date_created BETWEEN vDateFrom AND vDateTo
              AND (pStatusList IS NULL OR vw$ut.status IN (SELECT * FROM TABLE(pStatusList)))
        )
        WHERE rn BETWEEN vOffset AND (vOffset + vArraySize);

        RETURN getFilteredTranpays(pBasicType => pBasicType);
    END getOrders;

FUNCTION getOrders(vDateFrom            IN DATE,
                    vDateTo             IN DATE,
                    pStatusList         mcore.table_of_varchar2_60,
                    pReqTypeId          IN mcore.request_types.id%TYPE,
                    pTranpayId          IN mcore.tranpays.id%TYPE,
                    pSenderAccountId    IN VARCHAR2,
                    pDescription        IN VARCHAR2,
                    pBeneficiaryAccount IN VARCHAR2,
                    pBeneficiaryName    IN VARCHAR2,
                    pAmountFrom         IN NUMBER,
                    pAmountTo           IN NUMBER,
                    vOffset             IN PLS_INTEGER,
                    vArraySize          IN PLS_INTEGER)
    RETURN sys_refcursor IS
        myunit CONSTANT VARCHAR2(14) := 'getOrders2';
    BEGIN
        slog.debug(pkgCtxId, myUnit, to_char(vDateFrom,mcore.common_pck.cDATE_MASK) || ':' || to_char(vDateTo,mcore.common_pck.cDATE_MASK) || ':' || vOffset || ':' || vArraySize);

        INSERT INTO mcore.tmp$tranpays(id, description, tranval, aggr_tranpay, rownumber)
        SELECT tranpay_id, description, tranval, aggr_tranpay, rn FROM
        (SELECT vw$ut.id tranpay_id,
				vw$ut.description,
				vw$ut.tranval,
				vw$ut.aggr_tranpay,
               ROW_NUMBER() OVER (ORDER BY vw$ut.date_modified DESC) rn
          FROM mcore.vw$user_orders vw$ut
          WHERE vw$ut.req_type_id LIKE pReqTypeId
            AND (pStatusList IS NULL OR vw$ut.status IN (SELECT * FROM TABLE(pStatusList)))
            AND vw$ut.id = NVL(pTranpayId, vw$ut.id)
            AND vw$ut.account_id = NVL(pSenderAccountId, vw$ut.account_id)
            AND NVL(LOWER(vw$ut.description),'%') LIKE LOWER('%' || pDescription || '%')			  			  
            AND NVL(vw$ut.tranval,0) BETWEEN NVL(pAmountFrom,0) AND NVL(pAmountTo, 99999999999999999999999999)			  
            AND 
                CASE 
                WHEN pBeneficiaryAccount IS NOT NULL THEN 
                  CASE WHEN EXISTS (SELECT NULL
                    FROM mcore.tranpay_details td1 
                    WHERE td1.tranpay_id = vw$ut.id 
                    AND td1.attrib_id = DECODE(td1.req_type_id, 'TRANSFER', 'RACUN', 'RACUN_PRIMAOCA') 
                    AND td1.data_vchar LIKE pBeneficiaryAccount) THEN 'DA' 
                  ELSE 'NE' END
                ELSE 'DA'
                END = 'DA'
            AND 
                CASE 
                WHEN pBeneficiaryName IS NOT NULL THEN
                  CASE 
                  WHEN EXISTS (SELECT NULL
                    FROM mcore.tranpay_details td1 
                    WHERE td1.tranpay_id = vw$ut.id 
                    AND td1.attrib_id = 'NAZIV_PRIMAOCA'
                    AND LOWER(td1.data_vchar) LIKE LOWER('%' || pBeneficiaryName || '%')) THEN 'DA'
                  ELSE 'NE' END
                ELSE 'DA'
                END = 'DA'
            AND vw$ut.date_created BETWEEN NVL(vDateFrom, mcore.common_pck.cDATE_PAST) AND NVL(vDateTo, mcore.common_pck.cDATE_FUTURE))
        WHERE rn BETWEEN vOffset AND (vOffset + vArraySize);

        RETURN getFilteredTranpays(pBasicType => '%');
    END getOrders;

    FUNCTION getTranpays(pPeriodFrom DATE,
                        pPeriodTo DATE,
                        pStatus mcore.tranpays.status%TYPE,
                        pBasicType mcore.request_types.basic_type%TYPE,
                        pOffset PLS_INTEGER := 1,
                        pArraySize PLS_INTEGER := 10)
    RETURN sys_refcursor IS
        myunit CONSTANT VARCHAR2(11) := 'getTranpays';
        vOffset PLS_INTEGER;
        vArraySize PLS_INTEGER;

        vDatumOd DATE;
        vDatumDo DATE;
        rez sys_refcursor;

       PROCEDURE writeActionLog(
          pLogMessage IN VARCHAR2,
          pRefObject IN VARCHAR2 DEFAULT NULL
        ) IS
        BEGIN
          mcore.authorization_pck.writeActionLog(
              pActions => mcore.actions_list(mcore.common_pck.cACT_ViewTranpays),
              pLogMessage => TO_CHAR(vDatumOd, mcore.common_pck.cDATE_MASK) || ':' || TO_CHAR(vDatumDo, mcore.common_pck.cDATE_MASK) || ':' ||
                             pStatus || ':' || pBasicType || ':' || pOffset || ':' || pArraySize || ':-' || pLogMessage,
              pRefClass => cREF_CLASS|| myunit,
              pRefObject => pRefObject);
        END writeActionLog;

    BEGIN
        slog.debug(pkgCtxId, myUnit,  to_char(pPeriodFrom,mcore.common_pck.cDATE_MASK) || ':' || to_char(pPeriodTo,mcore.common_pck.cDATE_MASK) || ':' || pStatus ||':' || pBasicType || ':' || pOffset || ':' || pArraySize);
        -- Execute for :::UD:ORD::
        common_pck.CommonSecurityChecks;

        vDatumOd := NVL(pPeriodFrom, mcore.common_pck.cDATE_PAST);
        vDatumDo := NVL(pPeriodTo + 1, mcore.common_pck.cDATE_FUTURE);

        common_pck.SetOffsetArraySize(pkgCtxId, myunit,
                                      pOffset, pArraySize,
                                      vOffset, vArraySize);

        IF pStatus IN (mcore.common_pck.cTRPSTS_UW, mcore.common_pck.cTRPSTS_VB) THEN
            rez := getTranpaysUW_VB(pStatus => pStatus,
                        vDatumOd => vDatumOd,
                        vDatumDo => vDatumDo,
                        pBasicType => pBasicType,
                        vOffset => vOffset,
                        vArraySize => vArraySize);
        ELSIF pStatus = mcore.common_pck.cTRPSTS_UD THEN
            rez := getTranpaysUD(vDatumOd => vDatumOd,
                        vDatumDo => vDatumDo,
                        pBasicType => pBasicType,
                        vOffset => vOffset,
                        vArraySize => vArraySize);
        ELSIF pStatus = mcore.common_pck.cTRPSTS_US THEN
            rez := getTranpaysUS(vDatumOd => vDatumOd,
                        vDatumDo => vDatumDo,
                        pBasicType => pBasicType,
                        vOffset => vOffset,
                        vArraySize => vArraySize);
        ELSIF pStatus IN (mcore.common_pck.cTRPSTS_BP, mcore.common_pck.cTRPSTS_BR) THEN
            rez := getTranpaysBX(vDatumOd => vDatumOd,
                        vDatumDo => vDatumDo,
                        pBasicType => pBasicType,
                        vOffset => vOffset,
                        vArraySize => vArraySize,
                        pStatus => pStatus);
        ELSIF pStatus = mcore.common_pck.cTRPSTS_BA THEN
            rez := getTranpaysBA(vDatumOd => vDatumOd,
                        vDatumDo => vDatumDo,
                        pBasicType => pBasicType,
                        vOffset => vOffset,
                        vArraySize => vArraySize);
        ELSIF pStatus IN (mcore.common_pck.cTRPSTS_UC) THEN
            rez := getTranpaysUC(vDatumOd => vDatumOd,
                        vDatumDo => vDatumDo,
                        pBasicType => pBasicType,
                        vOffset => vOffset,
                        vArraySize => vArraySize);
        ELSE
            -- Invalid status requested !!
            sspkg.raiseError(cERR_InvalidStatus, null, pkgCtxId, myunit);

        END IF;
        slog.debug(pkgCtxId, myUnit, 'writeActionLog-FINISH');
        writeActionLog(
          pLogMessage => 'FINISH',
          pRefObject => NULL);

        RETURN rez;
    EXCEPTION
      WHEN sspkg.sysException THEN
        writeActionLog(
          pLogMessage => sspkg.getErrorMessage,
          pRefObject => NULL);
        RAISE;
      WHEN OTHERS THEN
        writeActionLog(
          pLogMessage => sqlerrm,
          pRefObject => NULL);
        RAISE;
    END getTranpays;

    FUNCTION getTranpays(pPeriodFrom IN DATE,
                        pPeriodTo IN DATE,
                        pStatus IN mcore.tranpays.status%TYPE,
                        pReqTypeId IN mcore.request_types.id%TYPE,
						pTranpayId IN mcore.tranpays.id%TYPE,
						pSenderAccountId IN VARCHAR2,
						pDescription IN VARCHAR2,
						pBeneficiaryAccount IN VARCHAR2,
						pBeneficiaryName IN VARCHAR2,
						pAmountFrom IN NUMBER,
						pAmountTo IN NUMBER,
						pOffset IN PLS_INTEGER := 1,
                        pArraySize IN PLS_INTEGER := 10)
    RETURN sys_refcursor IS
        myunit CONSTANT VARCHAR2(12) := 'getTranpays2';
        vOffset PLS_INTEGER;
        vArraySize PLS_INTEGER;

        vDatumOd DATE;
        vDatumDo DATE;
        rez sys_refcursor;

       PROCEDURE writeActionLog(
          pLogMessage IN VARCHAR2,
          pRefObject IN VARCHAR2 DEFAULT NULL
        ) IS
        BEGIN
          mcore.authorization_pck.writeActionLog(
              pActions => mcore.actions_list(mcore.common_pck.cACT_ViewTranpays),
              pLogMessage => TO_CHAR(vDatumOd, mcore.common_pck.cDATE_MASK) || ':' || TO_CHAR(vDatumDo, mcore.common_pck.cDATE_MASK) || ':' ||
                             pStatus || ':' || pOffset || ':' || pArraySize || ':-' || pLogMessage,
              pRefClass => cREF_CLASS|| myunit,
              pRefObject => pRefObject);
        END writeActionLog;

    BEGIN
        slog.debug(pkgCtxId, myUnit,  to_char(pPeriodFrom,mcore.common_pck.cDATE_MASK) || ':' || to_char(pPeriodTo,mcore.common_pck.cDATE_MASK) || ':' || pStatus ||':' || pOffset || ':' || pArraySize
            || ':' || pReqTypeId || ':' || pTranpayId || ':' || pSenderAccountId || ':' || pDescription || ':' || pBeneficiaryAccount || ':' || pBeneficiaryName || ':' || pAmountFrom || ':' || pAmountTo);
        -- Execute for :::UD:ORD::
        common_pck.CommonSecurityChecks;

        vDatumOd := NVL(pPeriodFrom, mcore.common_pck.cDATE_PAST);
        vDatumDo := NVL(pPeriodTo + 1, mcore.common_pck.cDATE_FUTURE);

        common_pck.SetOffsetArraySize(pkgCtxId, myunit,
                                      pOffset, pArraySize,
                                      vOffset, vArraySize);

        IF pStatus IN (mcore.common_pck.cTRPSTS_UW, mcore.common_pck.cTRPSTS_VB) THEN
            rez := getTranpaysUW_VB(pStatus => pStatus,
                        vDatumOd => vDatumOd,
                        vDatumDo => vDatumDo,
                        pReqTypeId => pReqTypeId,
						pTranpayId => pTranpayId,
						pSenderAccountId => pSenderAccountId,
						pDescription => pDescription,
						pBeneficiaryAccount => pBeneficiaryAccount,
						pBeneficiaryName => pBeneficiaryName,
						pAmountFrom => pAmountFrom,
						pAmountTo => pAmountTo,
                        vOffset => vOffset,
                        vArraySize => vArraySize);
        ELSIF pStatus = mcore.common_pck.cTRPSTS_UD THEN
            rez := getTranpaysUD(vDatumOd => vDatumOd,
                        vDatumDo => vDatumDo,
                        pReqTypeId => pReqTypeId,
						pTranpayId => pTranpayId,
						pSenderAccountId => pSenderAccountId,
						pDescription => pDescription,
						pBeneficiaryAccount => pBeneficiaryAccount,
						pBeneficiaryName => pBeneficiaryName,
						pAmountFrom => pAmountFrom,
						pAmountTo => pAmountTo,
                        vOffset => vOffset,
                        vArraySize => vArraySize);
        ELSIF pStatus = mcore.common_pck.cTRPSTS_US THEN
            rez := getTranpaysUS(vDatumOd => vDatumOd,
                        vDatumDo => vDatumDo,
                        pReqTypeId => pReqTypeId,
						pTranpayId => pTranpayId,
						pSenderAccountId => pSenderAccountId,
						pDescription => pDescription,
						pBeneficiaryAccount => pBeneficiaryAccount,
						pBeneficiaryName => pBeneficiaryName,
						pAmountFrom => pAmountFrom,
						pAmountTo => pAmountTo,
                        vOffset => vOffset,
                        vArraySize => vArraySize);
        ELSIF pStatus IN (mcore.common_pck.cTRPSTS_BP, mcore.common_pck.cTRPSTS_BR) THEN
            rez := getTranpaysBX(vDatumOd => vDatumOd,
                        vDatumDo => vDatumDo,
                        pReqTypeId => pReqTypeId,
						pTranpayId => pTranpayId,
						pSenderAccountId => pSenderAccountId,
						pDescription => pDescription,
						pBeneficiaryAccount => pBeneficiaryAccount,
						pBeneficiaryName => pBeneficiaryName,
						pAmountFrom => pAmountFrom,
						pAmountTo => pAmountTo,
                        vOffset => vOffset,
                        vArraySize => vArraySize,
                        pStatus => pStatus);
		ELSIF pStatus = mcore.common_pck.cTRPSTS_BA THEN
            rez := getTranpaysBA(vDatumOd => vDatumOd,
                        vDatumDo => vDatumDo,
                        pReqTypeId => pReqTypeId,
						pTranpayId => pTranpayId,
						pSenderAccountId => pSenderAccountId,
						pDescription => pDescription,
						pBeneficiaryAccount => pBeneficiaryAccount,
						pBeneficiaryName => pBeneficiaryName,
						pAmountFrom => pAmountFrom,
						pAmountTo => pAmountTo,
                        vOffset => vOffset,
                        vArraySize => vArraySize);
        ELSIF pStatus IN (mcore.common_pck.cTRPSTS_UC) THEN
            rez := getTranpaysUC(vDatumOd => vDatumOd,
                        vDatumDo => vDatumDo,
                        pReqTypeId => pReqTypeId,
						pTranpayId => pTranpayId,
						pSenderAccountId => pSenderAccountId,
						pDescription => pDescription,
						pBeneficiaryAccount => pBeneficiaryAccount,
						pBeneficiaryName => pBeneficiaryName,
						pAmountFrom => pAmountFrom,
						pAmountTo => pAmountTo,
                        vOffset => vOffset,
                        vArraySize => vArraySize);
        ELSE
            -- Invalid status requested !!
            sspkg.raiseError(cERR_InvalidStatus, null, pkgCtxId, myunit);

        END IF;
        slog.debug(pkgCtxId, myUnit, 'writeActionLog-FINISH');
        writeActionLog(
          pLogMessage => 'FINISH',
          pRefObject => NULL);

        RETURN rez;
    EXCEPTION
      WHEN sspkg.sysException THEN
        writeActionLog(
          pLogMessage => sspkg.getErrorMessage,
          pRefObject => NULL);
        RAISE;
      WHEN OTHERS THEN
        writeActionLog(
          pLogMessage => sqlerrm,
          pRefObject => NULL);
        RAISE;
    END getTranpays;

    FUNCTION getTranpayUW_VB(pTranpayId IN mcore.tranpays.id%TYPE, pStatus IN mcore.tranpays.status%TYPE)
    RETURN sys_refcursor IS
        rez sys_refcursor;
        myunit CONSTANT VARCHAR2(15) := 'getTranpayUW_VB';
        vAccOwner CONSTANT mcore.account_owners.id%TYPE := mcauth.auth.getAccountOwner;
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pTranpayId || ':' || pStatus || ':' || vAccOwner);
        IF vAccOwner = '%' THEN
            OPEN rez FOR
                SELECT vw$ut.id                     AS  id,
                    vw$ut.req_type_id            AS  req_type_id,
                    vw$ut.status                 AS  status,
                    (CASE
                        WHEN rt.basic_type = mcore.common_pck.cBRT_WUT THEN tranpays_pck.translateWuErrorMessage(status_message)
                        ELSE status_message
                        END)                       AS status_message,
                    vw$ut.status_code            AS  status_code,
                    vw$ut.description            AS  description,
                    vw$ut.internal_description   AS  internal_description,
                    vw$ut.tranval                AS  tranval,
                        vw$ut.tranval_currency_id   AS  tranval_currency_id,
                    vw$ut.cost                   AS  cost,
                    vw$ut.cost_currency_id       AS  cost_currency_id,
                        vw$ut.date_created          AS  date_created,
                        vw$ut.date_signed           AS  date_signed,
                        vw$ut.date_processed        AS  date_processed,
                        vw$ut.tranpay_group_id      AS  tranpay_group_id,
                        tg.name                     AS  tg_name,
                        tg.description              AS  tg_description,
                        vw$ut.account_id            AS  account_id,
                        vw$ut.chart_of_accounts_id  AS  chart_of_accounts_id,
                        vw$ut.schedule_id           AS  schedule_id,
                        vw$ut.execute_at            AS  execute_at,
                        vw$ut.date_modified         AS  date_modified,
                        vw$ut.retry_counter         AS  retry_counter,
                        vw$ut.ref_code              AS  ref_code,
                        vw$ut.user_modified         AS  user_modified,
                        ec.first_name || ', ' || ec.last_name user_created_name,
                        NVL(em.first_name || ', ' || em.last_name, ec.first_name || ', ' || ec.last_name) user_modified_name
                FROM mcore.vw$user_tranpays_uw_vb vw$ut
                    JOIN mcore.request_types rt ON (vw$ut.req_type_id = rt.id)
                    JOIN mcore.end_users ec ON (ec.id = vw$ut.user_id)
                    JOIN mcore.end_users em ON (em.id = vw$ut.user_modified)
                    LEFT JOIN mcore.tranpay_groups tg ON (tg.id = vw$ut.tranpay_group_id)
                WHERE vw$ut.id = pTranpayId AND vw$ut.status = pStatus;
        ELSE
            OPEN rez FOR
                SELECT vw$ut.id                     AS  id,
                    vw$ut.req_type_id            AS  req_type_id,
                    vw$ut.status                 AS  status,
                    (CASE
                        WHEN rt.basic_type = mcore.common_pck.cBRT_WUT THEN tranpays_pck.translateWuErrorMessage(status_message)
                        ELSE status_message
                        END)                       AS status_message,
                    vw$ut.status_code            AS  status_code,
                    vw$ut.description            AS  description,
                    vw$ut.internal_description   AS  internal_description,
                    vw$ut.tranval                AS  tranval,
                        vw$ut.tranval_currency_id   AS  tranval_currency_id,
                    vw$ut.cost                   AS  cost,
                    vw$ut.cost_currency_id       AS  cost_currency_id,
                        vw$ut.date_created          AS  date_created,
                        vw$ut.date_signed           AS  date_signed,
                        vw$ut.date_processed        AS  date_processed,
                        vw$ut.tranpay_group_id      AS  tranpay_group_id,
                        tg.name                     AS  tg_name,
                        tg.description              AS  tg_description,
                        vw$ut.account_id            AS  account_id,
                        vw$ut.chart_of_accounts_id  AS  chart_of_accounts_id,
                        vw$ut.schedule_id           AS  schedule_id,
                        vw$ut.execute_at            AS  execute_at,
                        vw$ut.date_modified         AS  date_modified,
                        vw$ut.retry_counter         AS  retry_counter,
                        vw$ut.ref_code              AS  ref_code,
                        vw$ut.user_modified         AS  user_modified,
                        ec.first_name || ', ' || ec.last_name user_created_name,
                        NVL(em.first_name || ', ' || em.last_name, ec.first_name || ', ' || ec.last_name) user_modified_name
                FROM mcore.vw$user_tranpays_uw_vb_acc vw$ut
                    JOIN mcore.request_types rt ON (vw$ut.req_type_id = rt.id)
                    JOIN mcore.end_users ec ON (ec.id = vw$ut.user_id)
                    JOIN mcore.end_users em ON (em.id = vw$ut.user_modified)
                    LEFT JOIN mcore.tranpay_groups tg ON (tg.id = vw$ut.tranpay_group_id)
                WHERE vw$ut.id = pTranpayId AND vw$ut.status = pStatus;
        END IF;

        RETURN rez;
    END getTranpayUW_VB;

    FUNCTION getTranpayUD(pTranpayId mcore.tranpays.id%TYPE)
    RETURN sys_refcursor IS
		myunit CONSTANT VARCHAR2(12) := 'getTranpayUD';
		rez sys_refcursor;
    BEGIN
		slog.debug(pkgCtxId, myUnit,  pTranpayId);
      OPEN rez FOR
        SELECT vw$ut.id                     AS  id,
               vw$ut.req_type_id            AS  req_type_id,
               vw$ut.status                 AS  status,
               (CASE
                 WHEN rt.basic_type = mcore.common_pck.cBRT_WUT THEN tranpays_pck.translateWuErrorMessage(status_message)
                 ELSE status_message
                 END)                       AS status_message,
               vw$ut.status_code            AS  status_code,
               vw$ut.description            AS  description,
               vw$ut.internal_description   AS  internal_description,
               vw$ut.tranval                AS  tranval,
                vw$ut.tranval_currency_id   AS  tranval_currency_id,
               vw$ut.cost                   AS  cost,
               vw$ut.cost_currency_id       AS  cost_currency_id,
                vw$ut.date_created          AS  date_created,
                vw$ut.date_signed           AS  date_signed,
                vw$ut.date_processed        AS  date_processed,
                vw$ut.tranpay_group_id      AS  tranpay_group_id,
                tg.name                     AS  tg_name,
                tg.description              AS  tg_description,
                vw$ut.account_id            AS  account_id,
                vw$ut.chart_of_accounts_id  AS  chart_of_accounts_id,
                vw$ut.schedule_id           AS  schedule_id,
                vw$ut.execute_at            AS  execute_at,
                vw$ut.date_modified         AS  date_modified,
                vw$ut.retry_counter         AS  retry_counter,
                vw$ut.ref_code              AS  ref_code,
                vw$ut.user_modified         AS  user_modified,
                ec.first_name || ', ' || ec.last_name user_created_name,
                NVL(em.first_name || ', ' || em.last_name, ec.first_name || ', ' || ec.last_name) user_modified_name
          FROM mcore.vw$user_tranpays_ud vw$ut
            JOIN mcore.request_types rt ON (vw$ut.req_type_id = rt.id)
            JOIN mcore.end_users ec ON (ec.id = vw$ut.user_id)
            JOIN mcore.end_users em ON (em.id = vw$ut.user_modified)
               LEFT JOIN mcore.tranpay_groups tg ON (tg.id = vw$ut.tranpay_group_id)
          WHERE vw$ut.id = pTranpayId;
      RETURN rez;
    END getTranpayUD;

    FUNCTION getTranpayUS(pTranpayId mcore.tranpays.id%TYPE)
    RETURN sys_refcursor IS
		myunit CONSTANT VARCHAR2(12) := 'getTranpayUS';
		rez sys_refcursor;
    BEGIN
		slog.debug(pkgCtxId, myUnit,  pTranpayId);
		OPEN rez FOR
        SELECT vw$ut.id                     AS  id,
               vw$ut.req_type_id            AS  req_type_id,
               vw$ut.status                 AS  status,
               (CASE
                 WHEN rt.basic_type = mcore.common_pck.cBRT_WUT THEN tranpays_pck.translateWuErrorMessage(status_message)
                 ELSE status_message
                 END)                       AS status_message,
               vw$ut.status_code            AS  status_code,
               vw$ut.description            AS  description,
               vw$ut.internal_description   AS  internal_description,
               vw$ut.tranval                AS  tranval,
                vw$ut.tranval_currency_id   AS  tranval_currency_id,
               vw$ut.cost                   AS  cost,
               vw$ut.cost_currency_id       AS  cost_currency_id,
                vw$ut.date_created          AS  date_created,
                vw$ut.date_signed           AS  date_signed,
                vw$ut.date_processed        AS  date_processed,
                vw$ut.tranpay_group_id      AS  tranpay_group_id,
                tg.name                     AS  tg_name,
                tg.description              AS  tg_description,
                vw$ut.account_id            AS  account_id,
                vw$ut.chart_of_accounts_id  AS  chart_of_accounts_id,
                vw$ut.schedule_id           AS  schedule_id,
                vw$ut.execute_at            AS  execute_at,
                vw$ut.date_modified         AS  date_modified,
                vw$ut.retry_counter         AS  retry_counter,
                vw$ut.ref_code              AS  ref_code,
                vw$ut.user_modified         AS  user_modified,
                ec.first_name || ', ' || ec.last_name user_created_name,
                NVL(em.first_name || ', ' || em.last_name, ec.first_name || ', ' || ec.last_name) user_modified_name
          FROM mcore.vw$user_tranpays_us vw$ut
            JOIN mcore.request_types rt ON (vw$ut.req_type_id = rt.id)
            JOIN mcore.end_users ec ON (ec.id = vw$ut.user_id)
            JOIN mcore.end_users em ON (em.id = vw$ut.user_modified)
               LEFT JOIN mcore.tranpay_groups tg ON (tg.id = vw$ut.tranpay_group_id)
          WHERE vw$ut.id = pTranpayId;

      RETURN rez;
    END getTranpayUS;

    FUNCTION getTranpayUC(pTranpayId mcore.tranpays.id%TYPE)
    RETURN sys_refcursor IS
		myunit CONSTANT VARCHAR2(12) := 'getTranpayUC';
		rez sys_refcursor;
    BEGIN
		slog.debug(pkgCtxId, myUnit,  pTranpayId);
      OPEN rez FOR
        SELECT vw$ut.id                     AS  id,
               vw$ut.req_type_id            AS  req_type_id,
               vw$ut.status                 AS  status,
               (CASE
                 WHEN rt.basic_type = mcore.common_pck.cBRT_WUT THEN tranpays_pck.translateWuErrorMessage(status_message)
                 ELSE status_message
                 END)                       AS status_message,
               vw$ut.status_code            AS  status_code,
               vw$ut.description            AS  description,
               vw$ut.internal_description   AS  internal_description,
               vw$ut.tranval                AS  tranval,
                vw$ut.tranval_currency_id   AS  tranval_currency_id,
               vw$ut.cost                   AS  cost,
               vw$ut.cost_currency_id       AS  cost_currency_id,
                vw$ut.date_created          AS  date_created,
                vw$ut.date_signed           AS  date_signed,
                vw$ut.date_processed        AS  date_processed,
                vw$ut.tranpay_group_id      AS  tranpay_group_id,
                tg.name                     AS  tg_name,
                tg.description              AS  tg_description,
                vw$ut.account_id            AS  account_id,
                vw$ut.chart_of_accounts_id  AS  chart_of_accounts_id,
                vw$ut.schedule_id           AS  schedule_id,
                vw$ut.execute_at            AS  execute_at,
                vw$ut.date_modified         AS  date_modified,
                vw$ut.retry_counter         AS  retry_counter,
                vw$ut.ref_code              AS  ref_code,
                vw$ut.user_modified         AS  user_modified,
                ec.first_name || ', ' || ec.last_name user_created_name,
                NVL(em.first_name || ', ' || em.last_name, ec.first_name || ', ' || ec.last_name) user_modified_name
          FROM mcore.vw$user_tranpays_uc vw$ut
            JOIN mcore.request_types rt ON (vw$ut.req_type_id = rt.id)
            JOIN mcore.end_users ec ON (ec.id = vw$ut.user_id)
            JOIN mcore.end_users em ON (em.id = vw$ut.user_modified)
               LEFT JOIN mcore.tranpay_groups tg ON (tg.id = vw$ut.tranpay_group_id)
          WHERE vw$ut.id = pTranpayId;

      RETURN rez;
    END getTranpayUC;
    
    FUNCTION getTranpayBA(pTranpayId mcore.tranpays.id%TYPE, pTranpayStatus mcore.tranpays.status%TYPE)
    RETURN sys_refcursor IS
		myunit CONSTANT VARCHAR2(12) := 'getTranpayBA';
		rez sys_refcursor;
    BEGIN
		slog.debug(pkgCtxId, myUnit,  pTranpayId || ':' || pTranpayStatus);
      OPEN rez FOR
        SELECT vw$ut.id                     AS  id,
               vw$ut.req_type_id            AS  req_type_id,
               vw$ut.status                 AS  status,
               (CASE
                 WHEN rt.basic_type = mcore.common_pck.cBRT_WUT THEN tranpays_pck.translateWuErrorMessage(status_message)
                 ELSE status_message
                 END)                       AS status_message,
               vw$ut.status_code            AS  status_code,
               vw$ut.description            AS  description,
               vw$ut.internal_description   AS  internal_description,
               vw$ut.tranval                AS  tranval,
                vw$ut.tranval_currency_id   AS  tranval_currency_id,
               vw$ut.cost                   AS  cost,
               vw$ut.cost_currency_id       AS  cost_currency_id,
                vw$ut.date_created          AS  date_created,
                vw$ut.date_signed           AS  date_signed,
                vw$ut.date_processed        AS  date_processed,
                vw$ut.tranpay_group_id      AS  tranpay_group_id,
                tg.name                     AS  tg_name,
                tg.description              AS  tg_description,
                vw$ut.account_id            AS  account_id,
                vw$ut.chart_of_accounts_id  AS  chart_of_accounts_id,
                vw$ut.schedule_id           AS  schedule_id,
                vw$ut.execute_at            AS  execute_at,
                vw$ut.date_modified         AS  date_modified,
                vw$ut.retry_counter         AS  retry_counter,
                vw$ut.ref_code              AS  ref_code,
                vw$ut.user_modified         AS  user_modified,
                ec.first_name || ', ' || ec.last_name user_created_name,
                NVL(em.first_name || ', ' || em.last_name, ec.first_name || ', ' || ec.last_name) user_modified_name
          FROM mcore.vw$user_tranpays_ba vw$ut
            JOIN mcore.request_types rt ON (vw$ut.req_type_id = rt.id)
            JOIN mcore.end_users ec ON (ec.id = vw$ut.user_id)
            JOIN mcore.end_users em ON (em.id = vw$ut.user_modified)
               LEFT JOIN mcore.tranpay_groups tg ON (tg.id = vw$ut.tranpay_group_id)
          WHERE vw$ut.id = pTranpayId
            AND vw$ut.status = pTranpayStatus;
      RETURN rez;
    END getTranpayBA;

    FUNCTION getTranpayBX(pTranpayId mcore.tranpays.id%TYPE, pTranpayStatus mcore.tranpays.status%TYPE)
    RETURN sys_refcursor IS
		myunit CONSTANT VARCHAR2(12) := 'getTranpayBX';
		rez sys_refcursor;
    BEGIN
		slog.debug(pkgCtxId, myUnit,  pTranpayId || ':' || pTranpayStatus);
      OPEN rez FOR
        SELECT vw$ut.id                     AS  id,
               vw$ut.req_type_id            AS  req_type_id,
               vw$ut.status                 AS  status,
               (CASE
                 WHEN rt.basic_type = mcore.common_pck.cBRT_WUT THEN tranpays_pck.translateWuErrorMessage(status_message)
                 ELSE status_message
                 END)                       AS status_message,
               vw$ut.status_code            AS  status_code,
               vw$ut.description            AS  description,
               vw$ut.internal_description   AS  internal_description,
               vw$ut.tranval                AS  tranval,
                vw$ut.tranval_currency_id   AS  tranval_currency_id,
               vw$ut.cost                   AS  cost,
               vw$ut.cost_currency_id       AS  cost_currency_id,
                vw$ut.date_created          AS  date_created,
                vw$ut.date_signed           AS  date_signed,
                vw$ut.date_processed        AS  date_processed,
                vw$ut.tranpay_group_id      AS  tranpay_group_id,
                tg.name                     AS  tg_name,
                tg.description              AS  tg_description,
                vw$ut.account_id            AS  account_id,
                vw$ut.chart_of_accounts_id  AS  chart_of_accounts_id,
                vw$ut.schedule_id           AS  schedule_id,
                vw$ut.execute_at            AS  execute_at,
                vw$ut.date_modified         AS  date_modified,
                vw$ut.retry_counter         AS  retry_counter,
                vw$ut.ref_code              AS  ref_code,
                vw$ut.user_modified         AS  user_modified,
                ec.first_name || ', ' || ec.last_name user_created_name,
                NVL(em.first_name || ', ' || em.last_name, ec.first_name || ', ' || ec.last_name) user_modified_name
          FROM mcore.vw$user_tranpays_bx vw$ut
            JOIN mcore.request_types rt ON (vw$ut.req_type_id = rt.id)
            JOIN mcore.end_users ec ON (ec.id = vw$ut.user_id)
            JOIN mcore.end_users em ON (em.id = vw$ut.user_modified)
               LEFT JOIN mcore.tranpay_groups tg ON (tg.id = vw$ut.tranpay_group_id)
          WHERE vw$ut.id = pTranpayId
            AND vw$ut.status = pTranpayStatus;
      RETURN rez;
    END getTranpayBX;

    FUNCTION getTranpay(pTranpayId mcore.tranpays.id%TYPE)
    RETURN sys_refcursor IS
        myunit CONSTANT VARCHAR2(10) := 'getTranpay';
        vTranpayStatus VARCHAR2(20);
        rez sys_refcursor;

       PROCEDURE writeActionLog(
          pLogMessage IN VARCHAR2,
          pRefObject IN VARCHAR2 DEFAULT NULL
        ) IS
        BEGIN
          mcore.authorization_pck.writeActionLog(
              pActions => mcore.actions_list(mcore.common_pck.cACT_ViewTranpays),
              pLogMessage => pTranpayId || ':-' || pLogMessage,
              pRefClass => cREF_CLASS|| myunit,
              pRefObject => pRefObject);
        END writeActionLog;
    BEGIN
        slog.debug(pkgCtxId, myUnit, pTranpayId);

        common_pck.CommonSecurityChecks;

        vTranpayStatus := getTranpayCurrentStatus(pTranpayId);

        IF vTranpayStatus IN (mcore.common_pck.cTRPSTS_UW, mcore.common_pck.cTRPSTS_VB)  THEN
            RETURN getTranpayUW_VB(pTranpayId, vTranpayStatus);

        ELSIF vTranpayStatus = mcore.common_pck.cTRPSTS_UD THEN
          RETURN getTranpayUD(pTranpayId);

        ELSIF vTranpayStatus = mcore.common_pck.cTRPSTS_US THEN
          RETURN getTranpayUS(pTranpayId);

        ELSIF vTranpayStatus = mcore.common_pck.cTRPSTS_UC THEN
          RETURN getTranpayUC(pTranpayId);

        ELSIF vTranpayStatus IN (mcore.common_pck.cTRPSTS_BA) THEN
          RETURN getTranpayBA(pTranpayId, vTranpayStatus);
        
        ELSIF vTranpayStatus IN (mcore.common_pck.cTRPSTS_BP,mcore.common_pck.cTRPSTS_BR) THEN
          RETURN getTranpayBX(pTranpayId, vTranpayStatus);

        ELSE
        -- Invalid status requested !!
            sspkg.raiseError(cERR_InvalidStatus, null, pkgCtxId, myunit);
        END IF;

        writeActionLog(pLogMessage => 'FINISH', pRefObject => NULL);

        RETURN rez;
    EXCEPTION
      WHEN sspkg.sysException THEN
        writeActionLog(
          pLogMessage => sspkg.getErrorMessage,
          pRefObject => NULL);
        RAISE;
      WHEN OTHERS THEN
        writeActionLog(
          pLogMessage => sqlerrm,
          pRefObject => NULL);
        RAISE;
    END getTranpay;

    FUNCTION getTranpaySignatures(pTranpayId mcore.tranpays.id%TYPE)
    RETURN sys_refcursor
    IS
    BEGIN
        RETURN mcore.tranpays_pck.getTranpaySignatures(pTranpayId);
    END getTranpaySignatures;

    FUNCTION getTranpaySignatures
    RETURN sys_refcursor
    IS
    BEGIN
        RETURN mcore.tranpays_pck.getTranpaySignatures;
    END getTranpaySignatures;

    FUNCTION getScheduledTranpays(pScheduleId mcore.tranpays.schedule_id%TYPE,
                        pOffset PLS_INTEGER := 1,
                        pArraySize PLS_INTEGER := 10)
    RETURN sys_refcursor IS
        myunit CONSTANT VARCHAR2(20) := 'getScheduledTranpays';
        vOffset PLS_INTEGER;
        vArraySize PLS_INTEGER;

        rez sys_refcursor;

       PROCEDURE writeActionLog(
          pLogMessage IN VARCHAR2,
          pRefObject IN VARCHAR2 DEFAULT NULL
        ) IS
        BEGIN
          mcore.authorization_pck.writeActionLog(
              pActions => mcore.actions_list(mcore.common_pck.cACT_ViewTranpays),
              pLogMessage => pScheduleId || ':' || pOffset || ':' || pArraySize || ':-' || pLogMessage,
              pRefClass => cREF_CLASS|| myunit,
              pRefObject => pRefObject);
        END writeActionLog;
    BEGIN
        slog.debug(pkgCtxId, myUnit, pScheduleId ||':' || pOffset || ':' || pArraySize);
        common_pck.CommonSecurityChecks;

        common_pck.SetOffsetArraySize(pkgCtxId, myunit,
                                      pOffset, pArraySize,
                                      vOffset, vArraySize);

        OPEN rez FOR
        SELECT  id,
                req_type_id,
                status,
                status_message,
                status_code,
                description,
                internal_description,
                tranval,
                tranval_currency_id,
                cost,
                cost_currency_id,
                date_signed,
                date_processed,
                tranpay_group_id,
                tg_name,
                tg_description,
                account_id,
                chart_of_accounts_id,
                execute_at
       FROM(
        SELECT vw$ut.id id, vw$ut.req_type_id req_type_id, vw$ut.status status,
            vw$ut.status_message status_message, vw$ut.status_code status_code,
            vw$ut.description description, vw$ut.internal_description internal_description,
            vw$ut.tranval tranval,
            vw$ut.tranval_currency_id tranval_currency_id, vw$ut.cost cost, vw$ut.cost_currency_id cost_currency_id,
            vw$ut.date_signed date_signed,
            vw$ut.date_processed date_processed, vw$ut.tranpay_group_id tranpay_group_id,
            tg.name tg_name, tg.description tg_description,
            vw$ut.account_id account_id,
            vw$ut.chart_of_accounts_id chart_of_accounts_id,
            vw$ut.execute_at execute_at,
            ROW_NUMBER() OVER (ORDER BY vw$ut.parent_id ASC) rn
          FROM mcore.tranpays vw$ut,
               mcore.tranpay_groups tg,
                 (SELECT id FROM
                    (SELECT ut.id
                       FROM mcore.vw$user_tranpays_us_bx ut,
                            mcore.schedules s
                      WHERE ut.schedule_id = s.id
                        AND s.active = 1
                        AND ut.schedule_id = pScheduleId
                    ORDER BY ut.date_modified DESC)
                    WHERE ROWNUM < 2) s
          WHERE vw$ut.id = s.id
            AND tg.id (+) = vw$ut.tranpay_group_id)
        WHERE rn BETWEEN vOffset AND (vOffset + vArraySize)
        ORDER BY rn ASC;

        writeActionLog(pLogMessage => 'FINISH', pRefObject => NULL);

        RETURN rez;
    EXCEPTION
      WHEN sspkg.sysException THEN
        writeActionLog(
          pLogMessage => sspkg.getErrorMessage,
          pRefObject => NULL);
        RAISE;
      WHEN OTHERS THEN
        writeActionLog(
          pLogMessage => sqlerrm,
          pRefObject => NULL);
        RAISE;
    END getScheduledTranpays;

    FUNCTION getTranpayCurrentStatus(pTranpayId mcore.tranpays.id%TYPE)
    RETURN mcore.tranpays.status%TYPE IS
    BEGIN
        RETURN mcore.tranpays_pck.getTranpayCurrentStatus(pTranpayId);

    END getTranpayCurrentStatus;

    PROCEDURE getTranpayCurrentStatus(
                    pTranpayId IN mcore.tranpays.id%TYPE,
                    pRequestType OUT mcore.tranpays.req_type_id%TYPE,
                    pTranpayStatus OUT mcore.tranpays.status%TYPE
    )
    IS
    BEGIN
        mcore.tranpays_pck.getTranpayCurrentStatus(pTranpayId, pRequestType, pTranpayStatus);
    END getTranpayCurrentStatus;

    FUNCTION getTranpayAccount(pTranpayId mcore.tranpays.id%TYPE)
    RETURN mcore.tranpays.account_id%TYPE IS
    BEGIN
        RETURN mcore.tranpays_pck.getTranpayAccount(pTranpayId);
    END getTranpayAccount;

    FUNCTION prepareTranpaySenderData(pAccountID mcore.bank_accounts.id%TYPE, pRequestTypeId mcore.tranpays.req_type_id%TYPE DEFAULT NULL)
    RETURN mcore.tranpay_details.data_vchar%TYPE IS
    BEGIN
    	common_pck.CommonSecurityChecks;
        RETURN mcore.tranpays_pck.prepareTranpaySenderData (pUserID => auth.getSCID, pAccountID => pAccountID, pRequestTypeId => pRequestTypeId);
    END prepareTranpaySenderData;

    FUNCTION CopyTranpay(pTranpayId mcore.tranpays.id%TYPE)
    RETURN mcore.tranpays.id%TYPE IS
    BEGIN
        RETURN mcore.tranpays_pck.CopyTranpay(pTranpayId);
    END CopyTranpay;

    FUNCTION CopyTranpay(pTranpayList mcore.table_of_integer)
    RETURN mcore.table_of_integer IS
    BEGIN
        RETURN mcore.tranpays_pck.CopyTranpay(pTranpayList);
    END CopyTranpay;

    -- TRANPAY DETAILS
    PROCEDURE AppendTranPayDetail(pTranpayId       mcore.tranpays.id%TYPE,
                                  pRequestTypeId    mcore.tranpays.req_type_id%TYPE,
                                  pAttribId         mcore.tranpay_details.attrib_id%TYPE,
                                  pDescription      mcore.tranpay_details.description%TYPE,
                                  pDataVCHAR        mcore.tranpay_details.data_vchar%TYPE,
                                  pDataBLOB         mcore.tranpay_details.data_blob%TYPE)
    IS
        myunit CONSTANT VARCHAR2(19) := 'AppendTranPayDetail';
        vRequestTypeId mcore.tranpays.req_type_id%TYPE;
        vTranpayStatus mcore.tranpays.status%TYPE;
        vAccountId mcore.tranpays.account_id%TYPE;
    BEGIN
        slog.debug(pkgCtxId, myUnit,  pTranpayId || ':' || pRequestTypeId || ':' ||pAttribId|| ':' ||pDescription|| ':' ||pDataVCHAR);
        common_pck.CommonSecurityChecks;

        mcore.tranpays_pck.getTranpayData(pTranpayId, vRequestTypeId, vTranpayStatus, vAccountId);

        IF vAccountId IS NULL THEN
          sspkg.raiseError(cERR_NoAccount, null, pkgCtxId, myunit);
        END IF;

        IF NOT mcore.authorization_pck.hasGrantOnCreateTranpay(vAccountId, vRequestTypeId) THEN
	  slog.error(pkgCtxId, myUnit, cERR_NoCreatePrivilege, vAccountId);
          sspkg.raiseError(cERR_NoCreatePrivilege, null, pkgCtxId, myunit);
        END IF;

        mcore.tranpays_pck.AppendTranPayDetail(pTranpayId, pRequestTypeId, pAttribId, pDescription, pDataVCHAR, pDataBLOB);

    END AppendTranPayDetail;

    FUNCTION getTranpayDetails(pTranpayId mcore.tranpays.id%TYPE)
    RETURN sys_refcursor IS
    BEGIN
        RETURN mcore.tranpays_pck.getTranpayDetails(pTranpayId);
    END getTranpayDetails;

    FUNCTION getTranpayDetails
    RETURN sys_refcursor IS
    BEGIN
        RETURN mcore.tranpays_pck.getTranpayDetails;
    END getTranpayDetails;

    FUNCTION getTranpayAttributeVCHARData(pTranpayId mcore.tranpays.id%TYPE,
                                          pAttributeId mcore.tranpay_attribs.id%TYPE)
    RETURN mcore.tranpay_details.data_vchar%TYPE
    IS
    BEGIN
        RETURN mcore.tranpays_pck.getTranpayAttributeVCHARData(pTranpayId, pAttributeId);
    END getTranpayAttributeVCHARData;

    PROCEDURE MarkForExport(pTranpayId mcore.tranpays.id%TYPE,
        pTranpayGroupId mcore.tranpay_groups.id%TYPE := NULL)
    IS
        myunit CONSTANT VARCHAR2(13) := 'MarkForExport';

        PROCEDURE writeActionLog(
          pLogMessage IN VARCHAR2,
          pRefObject IN VARCHAR2 DEFAULT NULL
        ) IS
        BEGIN
          mcore.authorization_pck.writeActionLog(
              pActions => mcore.actions_list(mcore.common_pck.cACT_ViewTranpays),
              pLogMessage => pTranpayId || ':' || pLogMessage,
              pRefClass => cREF_CLASS|| myunit,
              pRefObject => pRefObject);
        END writeActionLog;

    BEGIN
        slog.debug(pkgCtxId, myUnit, pTranpayId || ':' || pTranpayGroupId);

        common_pck.CommonSecurityChecks;

        IF NOT tranpayExists(pTranpayId) THEN
            sspkg.raiseError(cERR_InvalidTranpay, null, pkgCtxId, myunit);
        END IF;

        INSERT INTO tmp$export_candidates(tranpay_id)
        VALUES(pTranpayId);

        writeActionLog(pLogMessage => 'FINISH', pRefObject => NULL);

    EXCEPTION
      WHEN sspkg.sysException THEN
        writeActionLog(
          pLogMessage => sspkg.getErrorMessage,
          pRefObject => NULL);
        RAISE;
      WHEN OTHERS THEN
        writeActionLog(
          pLogMessage => sqlerrm,
          pRefObject => NULL);
        RAISE;
    END MarkForExport;

    FUNCTION ExportTranpays(pRequestTypeId mcore.request_types.id%TYPE)
    RETURN sys_refcursor
    IS
        myunit CONSTANT VARCHAR2(14) := 'ExportTranpays';
        rez sys_refcursor;
    BEGIN
        slog.debug(pkgCtxId, myUnit, pRequestTypeId);
        common_pck.CommonSecurityChecks;

        IF pRequestTypeId = mcore.common_pck.cRTI_UPP THEN
        OPEN rez FOR
        SELECT ROW_NUMBER() OVER (ORDER BY id ASC) RBR_NALOGA,
                   NAZIV_POSILJAOCA, RACUN_PRIMAOCA, NAZIV_PRIMAOCA,
                   IZNOS, OPIS_PLACANJA,
                   HITNO, IDPOROBV, VRSTA_UPLATE, VRSTA_PRIHODA, PORPERIOD_OD, PORPERIOD_DO,
                   OPCINA, BUDZETSKA_ORGANIZACIJA, POZIV_NA_BROJ, REFERENCA_PLACANJA, TRANS_RACUN FROM
            (
                SELECT t.id, t.tranval IZNOS, t.description OPIS_PLACANJA,
                    max (decode(td.attrib_id, 'NAZIV_POSILJAOCA', td.data_vchar, NULL)) NAZIV_POSILJAOCA,
                    max (decode(td.attrib_id, 'RACUN_PRIMAOCA', td.data_vchar, NULL)) RACUN_PRIMAOCA,
                    max (decode(td.attrib_id, 'NAZIV_PRIMAOCA', td.data_vchar, NULL)) NAZIV_PRIMAOCA,
                    max (decode(td.attrib_id, 'HITNO', td.data_vchar, NULL)) HITNO,
                    max (decode(td.attrib_id, 'IDPOROBV', td.data_vchar, NULL)) IDPOROBV,
                    max (decode(td.attrib_id, 'VRSTA_UPLATE', td.data_vchar, NULL)) VRSTA_UPLATE,
                    max (decode(td.attrib_id, 'VRSTA_PRIHODA', td.data_vchar, NULL)) VRSTA_PRIHODA,
                    max (decode(td.attrib_id, 'PORPERIOD_OD', td.data_vchar, NULL)) PORPERIOD_OD,
                    max (decode(td.attrib_id, 'PORPERIOD_DO', td.data_vchar, NULL)) PORPERIOD_DO,
                    max (decode(td.attrib_id, 'OPCINA', td.data_vchar, NULL)) OPCINA,
                    max (decode(td.attrib_id, 'BUDZETSKA_ORGANIZACIJA', td.data_vchar, NULL)) BUDZETSKA_ORGANIZACIJA,
                    max (decode(td.attrib_id, 'POZIV_NA_BROJ', td.data_vchar, NULL)) POZIV_NA_BROJ,
                    max (decode(td.attrib_id, 'REFERENCA_PLACANJA', td.data_vchar, NULL)) REFERENCA_PLACANJA,
                    accounts_pck.getAccountDomPaymentId(t.account_id) TRANS_RACUN
                   FROM mcore.tranpays t, mcore.tranpay_details td, tmp$export_candidates tmp$ec
                  WHERE td.tranpay_id = t.id AND t.id = tmp$ec.tranpay_id AND tmp$ec.tranpay_group_id IS NULL
                  GROUP BY t.id, t.tranval, t.description, t.account_id
                UNION
                SELECT t.id, t.tranval IZNOS, t.description OPIS_PLACANJA,
                    max (decode(td.attrib_id, 'NAZIV_POSILJAOCA', td.data_vchar, NULL)) NAZIV_POSILJAOCA,
                    max (decode(td.attrib_id, 'RACUN_PRIMAOCA', td.data_vchar, NULL)) RACUN_PRIMAOCA,
                    max (decode(td.attrib_id, 'NAZIV_PRIMAOCA', td.data_vchar, NULL)) NAZIV_PRIMAOCA,
                    max (decode(td.attrib_id, 'HITNO', td.data_vchar, NULL)) HITNO,
                    max (decode(td.attrib_id, 'IDPOROBV', td.data_vchar, NULL)) IDPOROBV,
                    max (decode(td.attrib_id, 'VRSTA_UPLATE', td.data_vchar, NULL)) VRSTA_UPLATE,
                    max (decode(td.attrib_id, 'VRSTA_PRIHODA', td.data_vchar, NULL)) VRSTA_PRIHODA,
                    max (decode(td.attrib_id, 'PORPERIOD_OD', td.data_vchar, NULL)) PORPERIOD_OD,
                    max (decode(td.attrib_id, 'PORPERIOD_DO', td.data_vchar, NULL)) PORPERIOD_DO,
                    max (decode(td.attrib_id, 'OPCINA', td.data_vchar, NULL)) OPCINA,
                    max (decode(td.attrib_id, 'BUDZETSKA_ORGANIZACIJA', td.data_vchar, NULL)) BUDZETSKA_ORGANIZACIJA,
                    max (decode(td.attrib_id, 'POZIV_NA_BROJ', td.data_vchar, NULL)) POZIV_NA_BROJ,
                    max (decode(td.attrib_id, 'REFERENCA_PLACANJA', td.data_vchar, NULL)) REFERENCA_PLACANJA,
                    accounts_pck.getAccountDomPaymentId(t.account_id) TRANS_RACUN
                   FROM mcore.tranpays t, mcore.tranpay_details td, tmp$export_candidates tmp$ec
                  WHERE td.tranpay_id = t.id AND t.tranpay_group_id = tmp$ec.tranpay_group_id AND tmp$ec.tranpay_group_id IS NOT NULL
                GROUP BY t.id, t.tranval, t.description, t.account_id);
        ELSIF pRequestTypeId = mcore.common_pck.cRTI_PPI THEN
            OPEN rez FOR
                SELECT ROW_NUMBER() OVER (ORDER BY id ASC) RBR_NALOGA,
                   NAZIV_POSILJAOCA, IZVORNA_VALUTA, ODREDISNA_VALUTA, IZLAZNI_IZNOS, OPIS_PLACANJA,
                   TROSKOVI, RACUN_PRIMAOCA, NAZIV_PRIMAOCA, BANKA_ID, BANKA_NAZ, NULL REFERENCA_PLACANJA,
                   TRANS_RACUN, ADRESA, ADRESA2, OSNOV_PLACANJA FROM (
                SELECT t.id,
                    max (decode(td.attrib_id, 'NAZIV_POSILJAOCA', td.data_vchar, NULL)) NAZIV_POSILJAOCA,
                       accounts_pck.getCurrency(t.account_id) IZVORNA_VALUTA,
                       t.tranval_currency_id ODREDISNA_VALUTA, t.tranval IZLAZNI_IZNOS,
                       t.description OPIS_PLACANJA,
                    max (decode(td.attrib_id, 'TROSAK', td.data_vchar, NULL)) TROSKOVI,
                    max (decode(td.attrib_id, 'RACUN_PRIMAOCA', td.data_vchar, NULL)) RACUN_PRIMAOCA,
                    max (decode(td.attrib_id, 'NAZIV_PRIMAOCA', td.data_vchar, NULL)) NAZIV_PRIMAOCA,
                    max (decode(td.attrib_id, 'BIC_KOD_BANKE', td.data_vchar, NULL)) BANKA_ID,
                    max (decode(td.attrib_id, 'NAZIV_BANKE', td.data_vchar, NULL)) BANKA_NAZ,
                    max (decode(td.attrib_id, 'ADRESA', td.data_vchar, NULL)) ADRESA,
                    max (decode(td.attrib_id, 'ADRESA2', td.data_vchar, NULL)) ADRESA2,
					max (decode(td.attrib_id, 'OSNOV_PLACANJA_ALIAS', td.data_vchar, NULL)) OSNOV_PLACANJA,
                    accounts_pck.getAccountDomPaymentId(t.account_id) TRANS_RACUN
                   FROM mcore.tranpays t, mcore.tranpay_details td, tmp$export_candidates tmp$ec
                  WHERE td.tranpay_id = t.id AND t.id = tmp$ec.tranpay_id AND tmp$ec.tranpay_group_id IS NULL
                  GROUP BY t.id, t.account_id, t.tranval_currency_id, t.tranval, t.description
                UNION
                SELECT t.id,
                    max (decode(td.attrib_id, 'NAZIV_POSILJAOCA', td.data_vchar, NULL)) NAZIV_POSILJAOCA,
                       accounts_pck.getCurrency(t.account_id) IZVORNA_VALUTA,
                       t.tranval_currency_id ODREDISNA_VALUTA, t.tranval IZLAZNI_IZNOS,
                       t.description OPIS_PLACANJA,
                    max (decode(td.attrib_id, 'TROSAK', td.data_vchar, NULL)) TROSKOVI,
                    max (decode(td.attrib_id, 'RACUN_PRIMAOCA', td.data_vchar, NULL)) RACUN_PRIMAOCA,
                    max (decode(td.attrib_id, 'NAZIV_PRIMAOCA', td.data_vchar, NULL)) NAZIV_PRIMAOCA,
                    max (decode(td.attrib_id, 'BIC_KOD_BANKE', td.data_vchar, NULL)) BANKA_ID,
                    max (decode(td.attrib_id, 'NAZIV_BANKE', td.data_vchar, NULL)) BANKA_NAZ,
                    max (decode(td.attrib_id, 'ADRESA', td.data_vchar, NULL)) ADRESA,
                    max (decode(td.attrib_id, 'ADRESA2', td.data_vchar, NULL)) ADRESA2,
					max (decode(td.attrib_id, 'OSNOV_PLACANJA_ALIAS', td.data_vchar, NULL)) OSNOV_PLACANJA,
                    accounts_pck.getAccountDomPaymentId(t.account_id) TRANS_RACUN
                   FROM mcore.tranpays t, mcore.tranpay_details td, tmp$export_candidates tmp$ec
                  WHERE td.tranpay_id = t.id AND t.tranpay_group_id = tmp$ec.tranpay_group_id AND tmp$ec.tranpay_group_id IS NOT NULL
                GROUP BY t.id, t.account_id, t.tranval_currency_id, t.tranval, t.description);
        END IF;
        RETURN rez;
    END ExportTranpays;

   FUNCTION getLastTranpays(pNumberOfRecords PLS_INTEGER)
   RETURN sys_refcursor IS
        myunit CONSTANT VARCHAR2(15) := 'getLastTranpays';
        rez sys_refcursor;

        PROCEDURE writeActionLog(
          pLogMessage IN VARCHAR2,
          pRefObject IN VARCHAR2 DEFAULT NULL
        ) IS
        BEGIN
          mcore.authorization_pck.writeActionLog(
              pActions => mcore.actions_list(mcore.common_pck.cACT_ViewTranpays),
              pLogMessage => pNumberOfRecords || ':-' || pLogMessage,
              pRefClass => cREF_CLASS|| myunit,
              pRefObject => pRefObject);
        END writeActionLog;
    BEGIN
        slog.debug(pkgCtxId, myUnit, pNumberOfRecords);

        common_pck.CommonSecurityChecks;

        OPEN rez FOR
        SELECT  id,
                req_type_id,
                status,
                status_message,
                status_code,
                description,
                internal_description,
                tranval,
                tranval_currency_id,
                cost,
                cost_currency_id,
                date_created,
                date_signed,
                date_processed,
                tranpay_group_id,
                account_id,
                chart_of_accounts_id,
                schedule_id,
                execute_at,
                date_modified,
				aggr_tranpay
       FROM(
        SELECT vw$ut.id, vw$ut.req_type_id, vw$ut.status, vw$ut.status_message, vw$ut.status_code,
            vw$ut.description, vw$ut.internal_description, vw$ut.tranval,
            vw$ut.tranval_currency_id, vw$ut.cost, vw$ut.cost_currency_id,
            vw$ut.date_created, vw$ut.date_signed,
            vw$ut.date_processed, vw$ut.tranpay_group_id, vw$ut.account_id,
            vw$ut.chart_of_accounts_id, vw$ut.schedule_id, vw$ut.execute_at, vw$ut.date_modified, aggr_tranpay,
            ROW_NUMBER() OVER (ORDER BY DECODE(vw$ut.status,
                                         mcore.common_pck.cTRPSTS_US, vw$ut.date_signed,
                                    vw$ut.date_processed) DESC) rn
          FROM mcore.vw$user_tranpays_us_bx vw$ut)
          WHERE rn <= pNumberOfRecords
        ORDER BY rn ASC;

        writeActionLog(pLogMessage => 'FINISH', pRefObject => NULL);

        RETURN rez;
    EXCEPTION
      WHEN sspkg.sysException THEN
        writeActionLog(
          pLogMessage => sspkg.getErrorMessage,
          pRefObject => NULL);
        RAISE;
      WHEN OTHERS THEN
        writeActionLog(
          pLogMessage => sqlerrm,
          pRefObject => NULL);
        RAISE;
    END getLastTranpays;

    PROCEDURE createRequiredDetails(pTranpayId mcore.tranpays.id%TYPE)
    IS
    BEGIN
        mcore.tranpays_pck.createRequiredDetails(pTranpayId);
    END createRequiredDetails;

    FUNCTION getCommonReqTypeForTemplPck(pTemplatePackage mcore.tranpay_templates.template_package_id%TYPE)
    RETURN mcore.tranpay_templates.request_type_id%TYPE
    IS
    BEGIN
        RETURN mcore.tranpays_pck.getCommonReqTypeForTemplPck(pTemplatePackage);
    END getCommonReqTypeForTemplPck;

    FUNCTION createCOA(pAccountId mcore.bank_accounts.id%TYPE,
                       pShortDescription mcore.charts_of_accounts.short_description%TYPE,
                       pDescription mcore.charts_of_accounts.description%TYPE,
                       pSortCode mcore.charts_of_accounts.sort_code%TYPE,
                       pParentId mcore.charts_of_accounts.parent_id%TYPE)
    RETURN mcore.charts_of_accounts.id%TYPE
    IS
        myunit CONSTANT VARCHAR2(9) := 'createCOA';
        vAccOwnerId mcore.charts_of_accounts.acc_owner_id%TYPE;
    BEGIN
        slog.debug(pkgCtxId, myUnit, pAccountId || ':' || pShortDescription || ':' || pDescription || ':' || pSortCode || ':' || pParentId);
        common_pck.CommonSecurityChecks;

        vAccOwnerId := mcore.accounts_pck.getAccountOwner(pAccountId);

        IF vAccOwnerId IS NULL THEN
            sspkg.raiseError(cERR_NoPrivsForAccOwner, null, pkgCtxId,myunit);
        END IF;

       RETURN mcore.tranpays_pck.createCOA(vAccOwnerId, pShortDescription, pDescription, pSortCode, pParentId);

    END createCOA;

    PROCEDURE updateCOA(pCoaId mcore.charts_of_accounts.id%TYPE,
                       pShortDescription mcore.charts_of_accounts.short_description%TYPE,
                       pDescription mcore.charts_of_accounts.description%TYPE,
                       pSortCode mcore.charts_of_accounts.sort_code%TYPE,
                       pParentId mcore.charts_of_accounts.parent_id%TYPE)
    IS
    BEGIN
        mcore.tranpays_pck.updateCOA(pCoaId, pShortDescription, pDescription, pSortCode, pParentId);
    END updateCOA;

    PROCEDURE deleteCOA(pCoaId mcore.charts_of_accounts.id%TYPE)
    IS
    BEGIN
        mcore.tranpays_pck.deleteCOA(pCoaId);
    END deleteCOA;

    FUNCTION getTranpayRefCode(pTranpayId mcore.tranpays.id%TYPE)
    RETURN mcore.tranpays.ref_code%TYPE
    IS
    BEGIN
        RETURN mcore.tranpays_pck.getTranpayRefCode(pTranpayId);
    END getTranpayRefCode;

    FUNCTION getTranpaysExpensesBearerDesc(pCbVal0 mcore.generic_code_books.cb_val_0%TYPE)
    RETURN mcore.generic_code_books.cb_key_description%TYPE
    IS
        myunit CONSTANT VARCHAR2(29) := 'getTranpaysExpensesBearerDesc';
        vCbKeyDescription mcore.generic_code_books.cb_key_description%TYPE;
    BEGIN
        slog.debug(pkgCtxId, myUnit, pCbVal0);

        IF pCbVal0 IS NULL THEN
            RETURN NULL;
        END IF;

        SELECT cb_key_description
          INTO vCbKeyDescription
        FROM mcore.generic_code_books
        WHERE cb_table = 'TROSKOVI_INO'
        AND cb_val_0 = pCbVal0;

        RETURN vCbKeyDescription;
    EXCEPTION
        WHEN no_data_found THEN
             RETURN NULL;
    END getTranpaysExpensesBearerDesc;

    FUNCTION getTranpaysForRefCode(pTranpayRefCode mcore.tranpays.ref_code%TYPE)
    RETURN sys_refcursor
    IS
        myunit CONSTANT VARCHAR2(21) := 'getTranpaysForRefCode';

        vTranpayRefCode mcore.tranpays.ref_code%TYPE;
        vNoOfRecords PLS_INTEGER := 0;
        rez sys_refcursor;

        vRefCodeMinLength PLS_INTEGER;

    BEGIN
        slog.debug(pkgCtxId, myUnit, pTranpayRefCode);

        vRefCodeMinLength := NVL(sspkg.readInt(pkgCtxId || '/refCodeMinLength'), 10);

        vTranpayRefCode := REPLACE(REPLACE(SUBSTR(pTranpayRefCode,1,255), '%'),'_');

        IF LENGTH(vTranpayRefCode) < vRefCodeMinLength THEN
            sspkg.raiseError(pkgCtxId || '/err/RefCodeTooShort', null, pkgCtxId,myunit);
        END IF;

        SELECT COUNT(id)
        INTO vNoOfRecords
        FROM mcore.tranpays t
        WHERE t.ref_code like vTranpayRefCode || '%';

        IF vNoOfRecords > 1 THEN
            sspkg.raiseError(pkgCtxId || '/err/RefCodeAmbigious', null, pkgCtxId,myunit);
        ELSIF vNoOfRecords = 0 THEN
            sspkg.raiseError(pkgCtxId || '/err/InvalidRefCode', null, pkgCtxId,myunit);
        END IF;

        OPEN rez FOR
        SELECT ID, IZNOS,
               RACUN_POSILJAOCA,
               OPIS_PLACANJA, NAZIV_POSILJAOCA, RACUN_PRIMAOCA, NAZIV_PRIMAOCA,
               IZVORNA_VALUTA, ODREDISNA_VALUTA,
               REFERENCA_PLACANJA,
                   HITNO, IDPOROBV, VRSTA_UPLATE, VRSTA_PRIHODA, PORPERIOD_OD, PORPERIOD_DO,
                   OPCINA, BUDZETSKA_ORGANIZACIJA, POZIV_NA_BROJ, CLEARING_DATE,
                   TROSKOVI,
                   TROSKOVI_DESC,
                   BANKA_ID, BANKA_NAZ, ADRESA, VALUTA_POKRICA, IZNOS_POKRICA, ADRESA2, VRIJEME_IZVRSENJA_ALIAS,
                   FIKSNO, RACUN_POKRICA, REQ_TYPE_ID,
                   DATUM_OBRADE, OSNOV_PLACANJA, IZNOS_KUPOVINE, IZVORNA_REFERENCA
                   FROM (
                -- UPP
                SELECT t.id, t.req_type_id, t.tranval IZNOS,
                    max (decode(td.attrib_id, 'EXT_ID', td.data_vchar, NULL)) RACUN_POSILJAOCA,
                    t.description OPIS_PLACANJA, t.date_processed DATUM_OBRADE,
                    max (decode(td.attrib_id, 'REFERENCA_PLACANJA', td.data_vchar, NULL)) REFERENCA_PLACANJA,
                    max (decode(td.attrib_id, 'NAZIV_POSILJAOCA', td.data_vchar, NULL)) NAZIV_POSILJAOCA,
                    max (decode(td.attrib_id, 'VALUTA_POKRICA_ALIAS', td.data_vchar, NULL)) IZVORNA_VALUTA,
                    t.tranval_currency_id ODREDISNA_VALUTA,
                    max (decode(td.attrib_id, 'RACUN_PRIMAOCA', td.data_vchar, NULL)) RACUN_PRIMAOCA,
                    max (decode(td.attrib_id, 'NAZIV_PRIMAOCA', td.data_vchar, NULL)) NAZIV_PRIMAOCA,
                    max (decode(td.attrib_id, 'HITNO', td.data_vchar, NULL)) HITNO,
                    max (decode(td.attrib_id, 'IDPOROBV', td.data_vchar, NULL)) IDPOROBV,
                    max (decode(td.attrib_id, 'VRSTA_UPLATE', td.data_vchar, NULL)) VRSTA_UPLATE,
                    max (decode(td.attrib_id, 'VRSTA_PRIHODA', td.data_vchar, NULL)) VRSTA_PRIHODA,
                    max (decode(td.attrib_id, 'PORPERIOD_OD', td.data_vchar, NULL)) PORPERIOD_OD,
                    max (decode(td.attrib_id, 'PORPERIOD_DO', td.data_vchar, NULL)) PORPERIOD_DO,
                    max (decode(td.attrib_id, 'OPCINA', td.data_vchar, NULL)) OPCINA,
                    max (decode(td.attrib_id, 'BUDZETSKA_ORGANIZACIJA', td.data_vchar, NULL)) BUDZETSKA_ORGANIZACIJA,
                    max (decode(td.attrib_id, 'POZIV_NA_BROJ', td.data_vchar, NULL)) POZIV_NA_BROJ,
                    max (decode(td.attrib_id, 'CLEARING_DATE', td.data_vchar, NULL)) CLEARING_DATE,
                    NULL TROSKOVI,
                    NULL TROSKOVI_DESC,
                    NULL BANKA_ID,
                    NULL BANKA_NAZ,
                    NULL ADRESA,
                    NULL VALUTA_POKRICA,
                    NULL IZNOS_POKRICA,
                    NULL ADRESA2,
                    NULL VRIJEME_IZVRSENJA_ALIAS,
                    NULL FIKSNO,
                    NULL RACUN_POKRICA,
                    NULL OSNOV_PLACANJA,
                    NULL IZNOS_KUPOVINE,
                    max (decode(td.attrib_id, 'COREID', td.data_vchar, NULL)) IZVORNA_REFERENCA
                   FROM mcore.tranpays t LEFT OUTER JOIN mcore.tranpay_details td ON (td.tranpay_id = t.id)
                  WHERE t.req_type_id = mcore.common_pck.cRTI_UPP
                    AND t.ref_code like vTranpayRefCode || '%'
                  GROUP BY t.id, t.req_type_id, t.tranval, t.account_id, t.description, t.date_processed, t.tranval_currency_id
                UNION
                 -- RACUN POSILJAOCA (IBAN), NAZIV UZ SIFRU TROSKA NALOGA,
                 -- PPI
                 SELECT t.id, t.req_type_id, t.tranval IZNOS,
                    max (decode(td.attrib_id, 'EXT_ID', td.data_vchar, NULL)) RACUN_POSILJAOCA,
                    t.description OPIS_PLACANJA, t.date_processed DATUM_OBRADE,
                    NULL REFERENCA_PLACANJA,
                    max (decode(td.attrib_id, 'NAZIV_POSILJAOCA', td.data_vchar, NULL)) NAZIV_POSILJAOCA,
                    max (decode(td.attrib_id, 'VALUTA_POKRICA_ALIAS', td.data_vchar, NULL)) IZVORNA_VALUTA,
                    t.tranval_currency_id ODREDISNA_VALUTA,
                    max (decode(td.attrib_id, 'RACUN_PRIMAOCA', td.data_vchar, NULL)) RACUN_PRIMAOCA,
                    max (decode(td.attrib_id, 'NAZIV_PRIMAOCA', td.data_vchar, NULL)) NAZIV_PRIMAOCA,
                    NULL HITNO,
                    NULL IDPOROBV,
                    NULL VRSTA_UPLATE,
                    NULL VRSTA_PRIHODA,
                    NULL PORPERIOD_OD,
                    NULL PORPERIOD_DO,
                    NULL OPCINA,
                    NULL BUDZETSKA_ORGANIZACIJA,
                    NULL POZIV_NA_BROJ,
                    max (decode(td.attrib_id, 'CLEARING_DATE', td.data_vchar, NULL)) CLEARING_DATE,
                    max (decode(td.attrib_id, 'TROSKOVI', td.data_vchar, NULL)) TROSKOVI,
                    max (decode(td.attrib_id, 'TROSAK_ALIAS', td.data_vchar, NULL)) TROSKOVI_DESC,
                    max (decode(td.attrib_id, 'BIC_KOD_BANKE', td.data_vchar, NULL)) BANKA_ID,
                    max (decode(td.attrib_id, 'NAZIV_BANKE', td.data_vchar, NULL)) BANKA_NAZ,
                    max (decode(td.attrib_id, 'ADRESA', td.data_vchar, NULL)) ADRESA,
                    max (decode(td.attrib_id, 'VALUTA_POKRICA_ALIAS', td.data_vchar, NULL)) VALUTA_POKRICA,
                    max (decode(td.attrib_id, 'IZNOS_POKRICA', td.data_vchar, NULL)) IZNOS_POKRICA,
                    max (decode(td.attrib_id, 'ADRESA2', td.data_vchar, NULL)) ADRESA2,
                    max (decode(td.attrib_id, 'VRIJEME_IZVRSENJA_ALIAS', td.data_vchar, NULL)) VRIJEME_IZVRSENJA_ALIAS,
                    NULL FIKSNO,
                    NULL RACUN_POKRICA,
                    max (decode(td.attrib_id, 'OSNOV_PLACANJA_ALIAS', td.data_vchar, NULL)) OSNOV_PLACANJA,
                    NULL IZNOS_KUPOVINE,
                    max (decode(td.attrib_id, 'COREID', td.data_vchar, NULL)) IZVORNA_REFERENCA
                   FROM mcore.tranpays t LEFT OUTER JOIN mcore.tranpay_details td ON (td.tranpay_id = t.id)
                  WHERE t.req_type_id = mcore.common_pck.cRTI_PPI
                    AND t.ref_code like vTranpayRefCode || '%'
                  GROUP BY t.id, t.req_type_id, t.account_id, t.tranval_currency_id, t.tranval, t.account_id, t.description, t.date_processed
                  UNION
                 -- TRANSFER
                 SELECT t.id, t.req_type_id, t.tranval IZNOS,
                    max (decode(td.attrib_id, 'RACUN_POKRICA', td.data_vchar, NULL)) RACUN_POSILJAOCA,
                    t.description OPIS_PLACANJA, t.date_processed DATUM_OBRADE,
                    NULL REFERENCA_PLACANJA,
                    max (decode(td.attrib_id, 'ACCOUNT_ALIAS', td.data_vchar, NULL)) NAZIV_POSILJAOCA,
                    max (decode(td.attrib_id, 'ACCOUNT_VALUTA', td.data_vchar, NULL)) IZVORNA_VALUTA,
                    max (decode(td.attrib_id, 'RACUN_VALUTA', td.data_vchar, NULL)) ODREDISNA_VALUTA,
                    max (decode(td.attrib_id, 'RACUN', td.data_vchar, NULL)) RACUN_PRIMAOCA,
                    max (decode(td.attrib_id, 'RACUN_ALIAS', td.data_vchar, NULL)) NAZIV_PRIMAOCA,
                    NULL HITNO,
                    NULL IDPOROBV,
                    NULL VRSTA_UPLATE,
                    NULL VRSTA_PRIHODA,
                    NULL PORPERIOD_OD,
                    NULL PORPERIOD_DO,
                    NULL OPCINA,
                    NULL BUDZETSKA_ORGANIZACIJA,
                    NULL POZIV_NA_BROJ,
                    max (decode(td.attrib_id, 'CLEARING_DATE', td.data_vchar, NULL)) CLEARING_DATE,
                    max (decode(td.attrib_id, 'TROSKOVI', td.data_vchar, NULL)) TROSKOVI,
                    max (decode(td.attrib_id, 'TROSAK_ALIAS', td.data_vchar, NULL)) TROSKOVI_DESC,
                    max (decode(td.attrib_id, 'BIC_KOD_BANKE', td.data_vchar, NULL)) BANKA_ID,
                    max (decode(td.attrib_id, 'NAZIV_BANKE', td.data_vchar, NULL)) BANKA_NAZ,
                    max (decode(td.attrib_id, 'ADRESA', td.data_vchar, NULL)) ADRESA,
                    max (decode(td.attrib_id, 'VALUTA_POKRICA', td.data_vchar, NULL)) VALUTA_POKRICA,
                    max (decode(td.attrib_id, 'IZNOS_POKRICA', td.data_vchar, NULL)) IZNOS_POKRICA,
                    max (decode(td.attrib_id, 'ADRESA2', td.data_vchar, NULL)) ADRESA2,
                    max (decode(td.attrib_id, 'VRIJEME_IZVRSENJA_ALIAS', td.data_vchar, NULL)) VRIJEME_IZVRSENJA_ALIAS,
                    max (decode(td.attrib_id, 'FIKSNO', td.data_vchar, NULL)) FIKSNO,
                    max (decode(td.attrib_id, 'RACUN_POKRICA_ALIAS', td.data_vchar, NULL)) RACUN_POKRICA,
                    max (decode(td.attrib_id, 'OSNOV_PLACANJA_ALIAS', td.data_vchar, NULL)) OSNOV_PLACANJA,
                    max (decode(td.attrib_id, 'IZNOS_KUPOVINE', td.data_vchar, NULL)) IZNOS_KUPOVINE,
                    max (decode(td.attrib_id, 'COREID', td.data_vchar, NULL)) IZVORNA_REFERENCA
                   FROM mcore.tranpays t LEFT OUTER JOIN mcore.tranpay_details td ON (td.tranpay_id = t.id)
                  WHERE t.req_type_id = mcore.common_pck.cRTI_TRANSFER
                    AND t.ref_code like vTranpayRefCode || '%'
                  GROUP BY t.id, t.req_type_id, t.account_id, t.tranval_currency_id, t.tranval, t.account_id, t.description, t.date_processed);

        RETURN rez;
    END getTranpaysForRefCode;

    PROCEDURE getTranpaysForRefCode(pTranpayRefCode IN mcore.tranpays.ref_code%TYPE, pTranpay OUT sys_refcursor)
    IS
        myunit CONSTANT VARCHAR2(22) := 'getTranpaysForRefCode2';
    BEGIN
        slog.debug(pkgCtxId, myUnit, pTranpayRefCode);

        pTranpay := getTranpaysForRefCode(pTranpayRefCode);
    END getTranpaysForRefCode;

    FUNCTION getLastTranpayForSchedule(pScheduleId mcore.tranpays.schedule_id%TYPE)
    RETURN mcore.tranpays.id%TYPE
    IS
        myunit CONSTANT VARCHAR2(25) := 'getLastTranpayForSchedule';
        vTranpayId mcore.tranpays.id%TYPE;
    BEGIN
        slog.debug(pkgCtxId, myUnit, pScheduleId);
        common_pck.CommonSecurityChecks;

        IF pScheduleId IS NULL THEN
            RETURN NULL;
        END IF;

        vTranpayId := mcore.tranpays_pck.getLastTranpayForSchedule(pScheduleId);

        RETURN vTranpayId;
    END getLastTranpayForSchedule;

  PROCEDURE getTranpayParts(pPeriodFrom DATE,
                               pPeriodTo DATE,
                               pStatus mcore.tranpays.status%TYPE,
                               pBasicType mcore.request_types.basic_type%TYPE,
                               pAllTranpays out sys_refcursor,
                               pAllTranpayDetails out sys_refcursor,
                               pAllTranpaySchedules out sys_refcursor,
                               pAllTranpaySignatures out sys_refcursor,
                               pOffset PLS_INTEGER := 1,
                               pArraySize PLS_INTEGER := 10)
  IS
       myunit CONSTANT VARCHAR2(15) := 'getTranpayParts';
  BEGIN
    slog.debug(pkgCtxId, myUnit, to_char(pPeriodFrom,mcore.common_pck.cDATE_MASK) || ':' || to_char(pPeriodTo,mcore.common_pck.cDATE_MASK) || ':' || pStatus ||':' || pBasicType || ':' || pOffset || ':' || pArraySize);
    ROLLBACK;
    common_pck.CommonSecurityChecks;

    pAllTranpays := getTranpays(pPeriodFrom => pPeriodFrom,
                          pPeriodTo => pPeriodTo,
                          pStatus => pStatus,
                          pBasicType => pBasicType,
                          pOffset => pOffset,
                          pArraySize => pArraySize);


    pAllTranpayDetails := getTranpayDetails;

    pAllTranpaySchedules := getTranpaySchedule;
    pAllTranpaySignatures := getTranpaySignatures;

  END getTranpayParts;

 PROCEDURE getOrderParts(pPeriodFrom          DATE,
                          pPeriodTo           DATE,
                          pStatusList         mcore.table_of_varchar2_60,
                          pBasicType          mcore.request_types.basic_type%TYPE,
                          pAllOrders          out sys_refcursor,
                          pAllOrderDetails    out sys_refcursor,
                          pAllOrderSchedules  out sys_refcursor,
                          pAllOrderSignatures out sys_refcursor,
                          pOffset             PLS_INTEGER := 1,
                          pArraySize          PLS_INTEGER := 10)
  IS
       myunit CONSTANT VARCHAR2(15) := 'getOrderParts';
  BEGIN
    slog.debug(pkgCtxId, myUnit, to_char(pPeriodFrom,mcore.common_pck.cDATE_MASK) || ':' || to_char(pPeriodTo,mcore.common_pck.cDATE_MASK) || ':' || stringify_status_list(pStatusList) ||':' || pBasicType || ':' || pOffset || ':' || pArraySize);
    ROLLBACK;
    common_pck.CommonSecurityChecks;

    pAllOrders := getOrders(vDateFrom   => pPeriodFrom,
                            vDateTo     => pPeriodTo,
                            pStatusList => pStatusList,
                            pBasicType  => pBasicType,
                            vOffset     => pOffset,
                            vArraySize  => pArraySize);

    pAllOrderDetails := getTranpayDetails;
    pAllOrderSchedules := getTranpaySchedule;
    pAllOrderSignatures := getTranpaySignatures;

  END getOrderParts;
  
  FUNCTION getSelectedTranpays(pStatus IN mcore.tranpays.status%TYPE, pTranpaysSequence mcore.table_of_integer)
  RETURN sys_refcursor IS
  BEGIN
	RETURN mcore.tranpays_pck.getSelectedTranpays(pStatus, pTranpaysSequence);
  END;

  FUNCTION getTranpayAttachmentList
  RETURN sys_refcursor IS
    myunit CONSTANT VARCHAR2(24) := 'getTranpayAttachmentList';
    rez sys_refcursor;
  BEGIN
    slog.debug(pkgCtxId, myUnit);

    OPEN rez FOR
      SELECT at.tranpay_id TRANPAY_ID,
              at.id ATTACHMENT_ID,
              at.description DESCRIPTION,
              eu.last_name || ' ' || eu.first_name USER_CREATED,
              at.date_created DATE_CREATED,
              at.mime_type MIME_TYPE
      FROM mcore.attachments at join tmp$tranpays tmp ON (tmp.id = at.tranpay_id)
        JOIN mcore.end_users eu ON (at.user_id = eu.id);

    RETURN rez;

  END getTranpayAttachmentList;
							   
  PROCEDURE getTranpayParts(pPeriodFrom DATE,
                               pPeriodTo DATE,
                               pStatus mcore.tranpays.status%TYPE,
                               pBasicType mcore.request_types.basic_type%TYPE,
                               pAllTranpays out sys_refcursor,
                               pAllTranpayDetails out sys_refcursor,
                               pAllTranpaySchedules out sys_refcursor,
                               pAllTranpaySignatures out sys_refcursor,
                               pAllTranpayAttachments out sys_refcursor,
                               pOffset PLS_INTEGER := 1,
                               pArraySize PLS_INTEGER := 10)
  IS
        myunit CONSTANT VARCHAR2(16) := 'getTranpayParts2';
  BEGIN
    slog.debug(pkgCtxId, myUnit, to_char(pPeriodFrom,mcore.common_pck.cDATE_MASK) || ':' || to_char(pPeriodTo,mcore.common_pck.cDATE_MASK) || ':' || pStatus ||':' || pBasicType || ':' || pOffset || ':' || pArraySize);
    ROLLBACK;
    common_pck.CommonSecurityChecks;

    pAllTranpays := getTranpays(pPeriodFrom => pPeriodFrom,
                          pPeriodTo => pPeriodTo,
                          pStatus => pStatus,
                          pBasicType => pBasicType,
                          pOffset => pOffset,
                          pArraySize => pArraySize);


    pAllTranpayDetails := getTranpayDetails;

    pAllTranpaySchedules := getTranpaySchedule;
    pAllTranpaySignatures := getTranpaySignatures;

    pAllTranpayAttachments := getTranpayAttachmentList;

  END getTranpayParts;

  PROCEDURE getOrderParts(pPeriodFrom          DATE,
                          pPeriodTo            DATE,
                          pStatusList          mcore.table_of_varchar2_60,
                          pBasicType           mcore.request_types.basic_type%TYPE,
                          pAllOrders           out sys_refcursor,
                          pAllOrderDetails     out sys_refcursor,
                          pAllOrderSchedules   out sys_refcursor,
                          pAllOrderSignatures  out sys_refcursor,
                          pAllOrderAttachments out sys_refcursor,
                          pOffset              PLS_INTEGER := 1,
                          pArraySize           PLS_INTEGER := 10)
  IS
        myunit CONSTANT VARCHAR2(16) := 'getOrderParts2';
  BEGIN
    slog.debug(pkgCtxId, myUnit, to_char(pPeriodFrom,mcore.common_pck.cDATE_MASK) || ':' || to_char(pPeriodTo,mcore.common_pck.cDATE_MASK) || ':' || stringify_status_list(pStatusList) ||':' || pBasicType || ':' || pOffset || ':' || pArraySize);
    ROLLBACK;
    common_pck.CommonSecurityChecks;

    pAllOrders := getOrders(vDateFrom   => pPeriodFrom,
                            vDateTo     => pPeriodTo,
                            pStatusList => pStatusList,
                            pBasicType  => pBasicType,
                            vOffset     => pOffset,
                            vArraySize  => pArraySize);


    pAllOrderDetails := getTranpayDetails;
    pAllOrderSchedules := getTranpaySchedule;
    pAllOrderSignatures := getTranpaySignatures;
    pAllOrderAttachments := getTranpayAttachmentList;

  END getOrderParts;

  PROCEDURE getTranpayParts(pPeriodFrom IN DATE,
                               pPeriodTo IN DATE,
                               pStatus IN mcore.tranpays.status%TYPE,
                               pReqTypeId IN mcore.request_types.id%TYPE,
                               pTranpayId IN mcore.tranpays.id%TYPE,
						       pSenderAccountId IN VARCHAR2,
						       pDescription IN VARCHAR2,
						       pBeneficiaryAccount IN VARCHAR2,
						       pBeneficiaryName IN VARCHAR2,
						       pAmountFrom IN NUMBER,
						       pAmountTo IN NUMBER,
                               pAllTranpays out sys_refcursor,
                               pAllTranpayDetails out sys_refcursor,
                               pAllTranpaySchedules out sys_refcursor,
                               pAllTranpaySignatures out sys_refcursor,
                               pAllTranpayAttachments out sys_refcursor,
                               pOffset PLS_INTEGER := 1,
                               pArraySize PLS_INTEGER := 10)
  IS
        myunit CONSTANT VARCHAR2(16) := 'getTranpayParts3';
  BEGIN
    slog.debug(pkgCtxId, myUnit, to_char(pPeriodFrom,mcore.common_pck.cDATE_MASK) || ':' || to_char(pPeriodTo,mcore.common_pck.cDATE_MASK) || ':' || pStatus ||':' || pOffset || ':' || pArraySize);
    ROLLBACK;
    common_pck.CommonSecurityChecks;

    pAllTranpays := getTranpays(pPeriodFrom => pPeriodFrom,
                          pPeriodTo => pPeriodTo,
                          pStatus => pStatus,
                          pReqTypeId => pReqTypeId,
                          pTranpayId => pTranpayId,
                          pSenderAccountId => pSenderAccountId,
                          pDescription => pDescription,
                          pBeneficiaryAccount => pBeneficiaryAccount,
                          pBeneficiaryName => pBeneficiaryName,
                          pAmountFrom => pAmountFrom,
                          pAmountTo => pAmountTo,
                          pOffset => pOffset,
                          pArraySize => pArraySize);


    pAllTranpayDetails := getTranpayDetails;

    pAllTranpaySchedules := getTranpaySchedule;
    pAllTranpaySignatures := getTranpaySignatures;

    pAllTranpayAttachments := getTranpayAttachmentList;

  END getTranpayParts;

  PROCEDURE getOrderParts(pPeriodFrom            IN DATE,
                          pPeriodTo              IN DATE,
                          pStatusList            mcore.table_of_varchar2_60,
                          pReqTypeId             IN mcore.request_types.id%TYPE,
                          pTranpayId             IN mcore.tranpays.id%TYPE,
                          pSenderAccountId       IN VARCHAR2,
                          pDescription           IN VARCHAR2,
                          pBeneficiaryAccount    IN VARCHAR2,
                          pBeneficiaryName       IN VARCHAR2,
                          pAmountFrom            IN NUMBER,
                          pAmountTo              IN NUMBER,
                          pAllOrders           out sys_refcursor,
                          pAllOrderDetails     out sys_refcursor,
                          pAllOrderSchedules   out sys_refcursor,
                          pAllOrderSignatures  out sys_refcursor,
                          pAllOrderAttachments out sys_refcursor,
                          pOffset                PLS_INTEGER := 1,
                          pArraySize             PLS_INTEGER := 10)
  IS
        myunit CONSTANT VARCHAR2(16) := 'getOrderParts3';
  BEGIN
    slog.debug(pkgCtxId, myUnit, to_char(pPeriodFrom,mcore.common_pck.cDATE_MASK) || ':' || to_char(pPeriodTo,mcore.common_pck.cDATE_MASK) || ':' || stringify_status_list(pStatusList) ||':' || pOffset || ':' || pArraySize);
    ROLLBACK;
    common_pck.CommonSecurityChecks;

    pAllOrders := getOrders(
                          vDateFrom           => pPeriodFrom,
                          vDateTo             => pPeriodTo,
                          pStatusList         => pStatusList,
                          pReqTypeId          => pReqTypeId,
                          pTranpayId          => pTranpayId,
                          pSenderAccountId    => pSenderAccountId,
                          pDescription        => pDescription,
                          pBeneficiaryAccount => pBeneficiaryAccount,
                          pBeneficiaryName    => pBeneficiaryName,
                          pAmountFrom         => pAmountFrom,
                          pAmountTo           => pAmountTo,
                          vOffset             => pOffset,
                          vArraySize          => pArraySize);


    pAllOrderDetails := getTranpayDetails;
    pAllOrderSchedules := getTranpaySchedule;
    pAllOrderSignatures := getTranpaySignatures;
    pAllOrderAttachments := getTranpayAttachmentList;

  END getOrderParts;

  PROCEDURE getTranpaysAttachments(pTranpayId NUMBER, pStatus VARCHAR2, pAttachmentId NUMBER, pAttachment OUT BLOB, pMimeType OUT VARCHAR2)
  IS
    myunit CONSTANT VARCHAR2(22) := 'getTranpaysAttachments';
    vBfile BFILE;
  BEGIN
    slog.debug(pkgCtxId, myUnit,  pTranpayId || ':' || pStatus || ':' || pAttachmentId);

    -- pTranpayId and pStatus must match to pass next control
    IF pStatus NOT IN (
        mcore.common_pck.cTRPSTS_UW,
        mcore.common_pck.cTRPSTS_UD,
        mcore.common_pck.cTRPSTS_US,
        mcore.common_pck.cTRPSTS_BP)
    THEN
        sspkg.raiseError(cERR_InvalidStatus, null, pkgCtxId, myunit);
    END IF;
    IF p_tranpayExists(pTranpayId => pTranpayId, pTranpayStatus => pStatus) = 0 THEN
        sspkg.raiseError(cERR_InvalidTranpay, null, pkgCtxId, myunit);
    END IF;

    -- pTranpayId and pAttachmentId must match to pass next control
    BEGIN
      SELECT content, mime_type
      INTO vBfile, pMimeType
      FROM mcore.attachments
      WHERE id = pAttachmentId and tranpay_id = pTranpayId;
    EXCEPTION
      WHEN no_data_found THEN
        sspkg.raiseError(cERR_InvalidAttachmentId, null, pkgCtxId, myunit);
    END;

    pAttachment := mcore.util.readBlobFromBfile(vBfile);
  END getTranpaysAttachments;

  FUNCTION addTranpayAttachment(pTranpayId NUMBER, pDescription VARCHAR2, pSourceFileName VARCHAR2, pBlobData BLOB, pBlob_MimeType  VARCHAR2)
  RETURN NUMBER IS
    myunit CONSTANT VARCHAR2(30) := 'addTranpayAttachment';
  BEGIN
    slog.debug(pkgCtxId, myUnit,  pTranpayId || ':' || SUBSTR(pDescription, 1, 30) || ':' || pSourceFileName ||':'|| pBlob_MimeType);
    RETURN mcore.tranpays_pck.addTranpayAttachment(pTranpayId, pDescription, pSourceFileName, pBlobData, pBlob_MimeType);
  END addTranpayAttachment;

  PROCEDURE removeTranpayAttachment(pTranpayId NUMBER, pAttachmentId NUMBER)
  IS
    myunit CONSTANT VARCHAR2(30) := 'removeTranpayAttachment';
  BEGIN
    slog.debug(pkgCtxId, myUnit,  pTranpayId || ':' || pAttachmentId);
	mcore.tranpays_pck.removeTranpayAttachment(pTranpayId, pAttachmentId);
  END removeTranpayAttachment;

  FUNCTION getTranpayAttachmentList(pTranpayId NUMBER, pStatus VARCHAR2)
  RETURN sys_refcursor IS
    myunit CONSTANT VARCHAR2(25) := 'getTranpayAttachmentList2';
    rez sys_refcursor;
  BEGIN
    slog.debug(pkgCtxId, myUnit,  pTranpayId ||':'||pStatus);

    -- pTranpayId and pStatus must match to pass next control
    IF pStatus NOT IN (
        mcore.common_pck.cTRPSTS_UW,
        mcore.common_pck.cTRPSTS_UD,
        mcore.common_pck.cTRPSTS_US,
        mcore.common_pck.cTRPSTS_BP)
    THEN
        sspkg.raiseError(cERR_InvalidStatus, null, pkgCtxId, myunit);
    END IF;
    IF p_tranpayExists(pTranpayId => pTranpayId, pTranpayStatus => pStatus) = 0 THEN
        sspkg.raiseError(cERR_InvalidTranpay, null, pkgCtxId, myunit);
    END IF;

    OPEN rez FOR
      SELECT at.tranpay_id TRANPAY_ID,
              at.id ATTACHMENT_ID,
              at.description DESCRIPTION,
              eu.last_name || ' ' || eu.first_name USER_CREATED,
              at.date_created DATE_CREATED,
              at.mime_type MIME_TYPE
      FROM mcore.attachments at JOIN mcore.end_users eu ON (at.user_id = eu.id)
      WHERE at.tranpay_id = pTranpayId;

    RETURN rez;

  END getTranpayAttachmentList;
  
	PROCEDURE assignExtRefId2TRanpay(
		pTranpaysId NUMBER,
		pExtRef_Id VARCHAR2,
		pExtRef_Name VARCHAR2)
	IS
		myunit CONSTANT VARCHAR2(22) := 'assignExtRefId2TRanpay';
	BEGIN
		slog.debug(pkgCtxId, myUnit,  pTranpaysId || ':' || pExtRef_Id || ':' || pExtRef_Name);
        common_pck.CommonSecurityChecks;
		
		IF NOT tranpayExists(pTranpayId => pTranpaysId) THEN
			sspkg.raiseError(cERR_InvalidTranpay, null, pkgCtxId, myunit);
		END IF;
		
		mcore.tranpays_pck.assignExtRefId2TRanpay(
			pTranpaysId => pTranpaysId,
			pExtRef_Id => pExtRef_Id,
			pExtRef_Name => pExtRef_Name,
			pExtRef_Status => 'NEW');
		
	END assignExtRefId2TRanpay;  
	
    FUNCTION UploadTranpays(pTranpayList mcore.obj$tranpay_table) 
	RETURN mcore.tranpay_import_result_list IS
	BEGIN
		RETURN mcore.tranpays_pck.UploadTranpays(pTranpayList);
	END UploadTranpays;
	
	FUNCTION getTranpayGroupTypes(pGranteePermissionName mcore.grantee_permissions.name%TYPE DEFAULT NULL)
	RETURN sys_refcursor 
	IS
		myunit CONSTANT VARCHAR2(30) := 'getTranpayGroupTypes';
	BEGIN
		slog.debug(pkgCtxId, myUnit);
		RETURN mcore.tranpays_pck.getTranpayGroupTypes(pGranteePermissionName);
	END getTranpayGroupTypes;
	
	FUNCTION insertTranpayGroupType(pName mcore.tranpay_group_type.name%TYPE, pValid mcore.tranpay_group_type.valid%TYPE)
	RETURN mcore.tranpay_group_type.id%TYPE
	IS 
		myunit CONSTANT VARCHAR2(30) := 'insertTranpayGroupType';
	BEGIN
		slog.debug(pkgCtxId, myUnit);
		RETURN mcore.tranpays_pck.insertTranpayGroupType(pName, pValid);
	END insertTranpayGroupType;
	
	PROCEDURE updateTranpayGroupType(pTranpayGroupTypeId mcore.tranpay_group_type.id%TYPE, pName mcore.tranpay_group_type.name%TYPE, pValid mcore.tranpay_group_type.valid%TYPE)
	IS
		myunit CONSTANT VARCHAR2(30) := 'updateTranpayGroupType';
	BEGIN 
		slog.debug(pkgCtxId, myUnit);
		mcore.tranpays_pck.updateTranpayGroupType(pTranpayGroupTypeId, pName, pValid);
	END updateTranpayGroupType;

END;
/
show error

